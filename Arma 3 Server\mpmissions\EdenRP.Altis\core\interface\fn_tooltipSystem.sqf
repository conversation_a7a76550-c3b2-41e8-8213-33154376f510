/*
    File: fn_tooltipSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages tooltip system.
*/

params [["_player", player, [objNull]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_tooltipsEnabled", true, true];
        true
    };
    case "showTooltip": {
        params ["", "", ["_text", "", [""]], ["_duration", 3, [0]]];
        
        if (!(_player getVariable ["eden_tooltipsEnabled", true])) exitWith { false };
        if (_text == "") exitWith { false };
        
        [_text, 0.5, 0.8, _duration, 0] spawn BIS_fnc_dynamicText;
        true
    };
    case "showItemTooltip": {
        params ["", "", ["_item", "", [""]]];
        
        _itemInfo = "";
        {
            if ((_x select 0) == _item) then {
                _itemInfo = format["Item: %1\nType: %2\nWeight: %3kg\nValue: $%4", 
                    (_x select 0), (_x select 1), (_x select 2), (_x select 3)];
            };
        } forEach eden_itemDatabase;
        
        if (_itemInfo != "") then {
            [_itemInfo, 0.7, 0.3, 5, 0] spawn BIS_fnc_dynamicText;
        };
        
        true
    };
    case "showPlayerTooltip": {
        params ["", "", ["_target", objNull, [objNull]]];
        
        if (isNull _target) exitWith { false };
        
        _info = format["Player: %1\nJob: %2\nLevel: %3", 
            name _target,
            _target getVariable ["eden_job", "civilian"],
            _target getVariable ["eden_level", 1]
        ];
        
        [_info, 0.7, 0.3, 3, 0] spawn BIS_fnc_dynamicText;
        true
    };
    case "toggleTooltips": {
        _enabled = _player getVariable ["eden_tooltipsEnabled", true];
        _player setVariable ["eden_tooltipsEnabled", !_enabled, true];
        
        if (!_enabled) then {
            ["Tooltips enabled"] call EDEN_fnc_showHint;
        } else {
            ["Tooltips disabled"] call EDEN_fnc_showHint;
        };
        true
    };
    default { false };
};
