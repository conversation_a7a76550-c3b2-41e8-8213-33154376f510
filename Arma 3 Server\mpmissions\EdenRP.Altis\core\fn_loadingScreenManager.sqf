/*
    File: fn_loadingScreenManager.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages the loading screen for EdenRP
*/

// Initialize loading screen
if (hasInterface) then {
    // Create loading screen
    disableUserInput true;
    
    // Show loading message
    titleText ["Loading EdenRP...", "BLACK FADED", 0.5];
    
    // Set initial progress
    EDEN_LoadingProgress = 0;
    EDEN_LoadingText = "Initializing...";
    
    // Start progress display
    [] spawn {
        while {EDEN_LoadingProgress < 100} do {
            titleText [format["%1 (%2%%)", EDEN_LoadingText, EDEN_LoadingProgress], "PLAIN"];
            sleep 0.1;
        };
        
        // Finish loading
        titleText ["", "BLACK IN", 2];
        disableUserInput false;
    };
};

true
