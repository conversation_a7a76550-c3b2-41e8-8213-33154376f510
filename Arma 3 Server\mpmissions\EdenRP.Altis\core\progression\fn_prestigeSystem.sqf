/*
    File: fn_prestigeSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages prestige system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_prestigeLevel", 0, true];
        _player setVariable ["eden_prestigePoints", 0, true];
        _player setVariable ["eden_prestigeBonuses", [], true];
        true
    };
    case "canPrestige": {
        _level = _player getVariable ["eden_level", 1];
        _level >= 50
    };
    case "prestige": {
        _canPrestige = [_player, "canPrestige"] call EDEN_fnc_prestigeSystem;
        if (!_canPrestige) exitWith {
            ["You must reach level 50 to prestige"] call EDEN_fnc_showHint;
            false
        };
        
        _currentPrestige = _player getVariable ["eden_prestigeLevel", 0];
        _newPrestige = _currentPrestige + 1;
        
        // Reset player stats but keep prestige bonuses
        _player setVariable ["eden_level", 1, true];
        _player setVariable ["eden_experience", 0, true];
        _player setVariable ["eden_skillPoints", 5, true];
        _player setVariable ["eden_skills", [], true];
        
        // Award prestige
        _player setVariable ["eden_prestigeLevel", _newPrestige, true];
        _prestigePoints = _player getVariable ["eden_prestigePoints", 0];
        _player setVariable ["eden_prestigePoints", (_prestigePoints + 10), true];
        
        [format ["🌟 PRESTIGE %1 ACHIEVED! (+10 Prestige Points)", _newPrestige]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "buyPrestigeBonus": {
        params ["", "", ["_bonusType", "xp_multiplier", [""]]];
        
        _prestigePoints = _player getVariable ["eden_prestigePoints", 0];
        _bonuses = _player getVariable ["eden_prestigeBonuses", []];
        
        _cost = switch (_bonusType) do {
            case "xp_multiplier": { 5 };
            case "cash_bonus": { 8 };
            case "skill_efficiency": { 10 };
            case "job_mastery": { 12 };
            case "social_boost": { 6 };
            default { 999 };
        };
        
        if (_prestigePoints < _cost) exitWith {
            [format ["Not enough prestige points! Need %1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        if (_bonusType in _bonuses) exitWith {
            ["Bonus already purchased"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_prestigePoints", (_prestigePoints - _cost), true];
        _bonuses pushBack _bonusType;
        _player setVariable ["eden_prestigeBonuses", _bonuses, true];
        
        _bonusName = switch (_bonusType) do {
            case "xp_multiplier": { "XP Multiplier (+25%)" };
            case "cash_bonus": { "Cash Bonus (+20%)" };
            case "skill_efficiency": { "Skill Efficiency (+30%)" };
            case "job_mastery": { "Job Mastery (+15%)" };
            case "social_boost": { "Social Boost (+10%)" };
            default { "Unknown Bonus" };
        };
        
        [format ["Purchased prestige bonus: %1", _bonusName]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "hasBonus": {
        params ["", "", ["_bonusType", "xp_multiplier", [""]]];
        
        _bonuses = _player getVariable ["eden_prestigeBonuses", []];
        _bonusType in _bonuses
    };
    case "getPrestigeInfo": {
        _prestigeLevel = _player getVariable ["eden_prestigeLevel", 0];
        _prestigePoints = _player getVariable ["eden_prestigePoints", 0];
        _bonuses = _player getVariable ["eden_prestigeBonuses", []];
        
        [_prestigeLevel, _prestigePoints, _bonuses]
    };
    case "showPrestigeShop": {
        _prestigePoints = _player getVariable ["eden_prestigePoints", 0];
        _bonuses = _player getVariable ["eden_prestigeBonuses", []];
        
        _shopText = format["=== PRESTIGE SHOP ===\nPrestige Points: %1\n\n", _prestigePoints];
        
        _items = [
            ["xp_multiplier", "XP Multiplier (+25%)", 5],
            ["cash_bonus", "Cash Bonus (+20%)", 8],
            ["skill_efficiency", "Skill Efficiency (+30%)", 10],
            ["job_mastery", "Job Mastery (+15%)", 12],
            ["social_boost", "Social Boost (+10%)", 6]
        ];
        
        {
            _status = if ((_x select 0) in _bonuses) then { "✓ OWNED" } else { format["Cost: %1 PP", (_x select 2)] };
            _shopText = _shopText + format["%1 - %2\n", (_x select 1), _status];
        } forEach _items;
        
        [_shopText] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
