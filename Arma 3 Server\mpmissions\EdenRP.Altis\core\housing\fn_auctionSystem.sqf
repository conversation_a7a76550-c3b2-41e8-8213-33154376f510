/*
    File: fn_auctionSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages property auction system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_propertyId", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_propertyAuctions") then {
            eden_propertyAuctions = [];
            publicVariable "eden_propertyAuctions";
        };
        _player setVariable ["eden_auctionBids", [], true];
        true
    };
    case "createAuction": {
        params ["", "", "", ["_startingBid", 25000, [0]], ["_duration", 3600, [0]]];
        
        _owned = _player getVariable ["eden_ownedProperties", []];
        if !(_propertyId in _owned) exitWith {
            ["You don't own this property!"] call EDEN_fnc_showHint;
            false
        };
        
        _auction = [_propertyId, getPlayerUID _player, name _player, _startingBid, _startingBid, "", time, (time + _duration), "Active"];
        eden_propertyAuctions pushBack _auction;
        publicVariable "eden_propertyAuctions";
        
        [format ["Property auction created - starting bid: $%1", _startingBid]] call EDEN_fnc_showHint;
        true
    };
    case "placeBid": {
        params ["", "", "", ["_bidAmount", 0, [0]]];
        
        _auction = [];
        _auctionIndex = -1;
        
        {
            if (((_x select 0) == _propertyId) && ((_x select 8) == "Active") && (time < (_x select 7))) then {
                _auction = _x;
                _auctionIndex = _forEachIndex;
            };
        } forEach eden_propertyAuctions;
        
        if (count _auction == 0) exitWith {
            ["No active auction for this property!"] call EDEN_fnc_showHint;
            false
        };
        
        _currentBid = _auction select 4;
        if (_bidAmount <= _currentBid) exitWith {
            [format ["Bid must be higher than $%1", _currentBid]] call EDEN_fnc_showHint;
            false
        };
        
        _cash = _player getVariable ["eden_cash", 0];
        if (_cash < _bidAmount) exitWith {
            [format ["Not enough money! Need $%1", _bidAmount]] call EDEN_fnc_showHint;
            false
        };
        
        _auction set [4, _bidAmount];
        _auction set [5, getPlayerUID _player];
        eden_propertyAuctions set [_auctionIndex, _auction];
        publicVariable "eden_propertyAuctions";
        
        _bids = _player getVariable ["eden_auctionBids", []];
        _bids pushBack [_propertyId, _bidAmount, time];
        _player setVariable ["eden_auctionBids", _bids, true];
        
        [format ["Bid placed: $%1", _bidAmount]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
