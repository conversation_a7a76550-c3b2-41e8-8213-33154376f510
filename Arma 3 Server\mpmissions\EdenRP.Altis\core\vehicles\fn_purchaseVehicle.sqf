/*
    EdenRP Purchase Vehicle Function
    Enhanced vehicle purchasing with validation
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_vehicleClass", "", [""]],
    ["_data", [], [[]]]
];

// Get vehicle configuration
private _vehicleConfig = [_vehicleClass] call EDEN_fnc_getVehicleConfig;
if (count _vehicleConfig == 0) exitWith {
    ["Invalid vehicle class", "ERROR", "VEHICLES"] call EDEN_fnc_systemLogger;
    ["Invalid vehicle selected", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Extract vehicle data
private _vehicleName = _vehicleConfig select 0;
private _basePrice = _vehicleConfig select 1;
private _vehicleType = _vehicleConfig select 2;
private _requiredLicense = _vehicleConfig select 3;
private _factionRestriction = _vehicleConfig select 4;
private _levelRequirement = _vehicleConfig select 5;

// Check faction restriction
if (_factionRestriction != "any" && str (side _player) != _factionRestriction) exitWith {
    ["You don't have access to this vehicle", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Check level requirement
private _playerLevel = _player getVariable ["EDEN_Level", 1];
if (_playerLevel < _levelRequirement) exitWith {
    [format["You need to be level %1 to purchase this vehicle", _levelRequirement], "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Check license requirement
if (_requiredLicense != "") then {
    private _licenses = _player getVariable ["EDEN_CivLicenses", []];
    if !(_requiredLicense in _licenses) exitWith {
        [format["You need a %1 license to purchase this vehicle", _requiredLicense], "error"] remoteExec ["EDEN_fnc_showNotification", _player];
        false
    };
};

// Check vehicle limit
private _ownedVehicles = _player getVariable ["EDEN_OwnedVehicles", []];
private _maxVehicles = _player getVariable ["EDEN_MaxVehicles", 5];
if (count _ownedVehicles >= _maxVehicles) exitWith {
    [format["You can only own %1 vehicles at a time", _maxVehicles], "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Calculate final price with customizations
private _finalPrice = _basePrice;
private _customizations = if (count _data > 0) then {_data select 0} else {[]};
{
    private _customPrice = [_x] call EDEN_fnc_getCustomizationPrice;
    _finalPrice = _finalPrice + _customPrice;
} forEach _customizations;

// Apply discounts
private _donatorLevel = _player getVariable ["EDEN_DonatorLevel", 0];
private _discount = _donatorLevel * 0.05; // 5% per donator level
_finalPrice = _finalPrice * (1 - _discount);
_finalPrice = round _finalPrice;

// Check player funds
private _playerCash = _player getVariable ["EDEN_Cash", 0];
private _playerBank = _player getVariable ["EDEN_Bank", 0];
private _totalFunds = _playerCash + _playerBank;

if (_totalFunds < _finalPrice) exitWith {
    [format["You need $%1 to purchase this vehicle", [_finalPrice] call EDEN_fnc_formatMoney], "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Deduct payment (prefer bank over cash)
if (_playerBank >= _finalPrice) then {
    _player setVariable ["EDEN_Bank", _playerBank - _finalPrice, false];
} else {
    private _bankAmount = _playerBank;
    private _cashAmount = _finalPrice - _bankAmount;
    _player setVariable ["EDEN_Bank", 0, false];
    _player setVariable ["EDEN_Cash", _playerCash - _cashAmount, false];
};

// Generate unique plate number
private _plateNumber = [6] call EDEN_fnc_generatePlateNumber;

// Create vehicle record in database
private _uid = getPlayerUID _player;
private _query = format [
    "EDEN_Vehicles:insertVehicle:%1:%2:%3:%4:%5:%6:%7:%8:%9:%10:%11:%12:%13:%14:%15:%16:%17",
    _uid,
    _vehicleClass,
    _vehicleType,
    str (side _player),
    _plateNumber,
    str _customizations,
    "[]", // inventory
    1.0, // fuel
    "[]", // damage
    "[]", // position (will be set when spawned)
    0, // direction
    1, // locked
    1, // alive
    1, // active
    -1, // gang_id
    _vehicleName, // custom_name
    str _customizations // modifications
];

private _queryId = [_query, 1] call EDEN_fnc_asyncCall;

// Store query info for callback
if (isNil "EDEN_PendingQueries") then {
    EDEN_PendingQueries = [];
};

EDEN_PendingQueries pushBack [
    _queryId,
    "insertVehicle",
    [_uid, _vehicleClass, _plateNumber, _finalPrice],
    time
];

// Update player's owned vehicles list
_ownedVehicles pushBack [_vehicleClass, _plateNumber, time];
_player setVariable ["EDEN_OwnedVehicles", _ownedVehicles, false];

// Log transaction
[_uid, "VEHICLE_PURCHASE", -_finalPrice, _totalFunds, _totalFunds - _finalPrice, format["Purchased %1 (%2)", _vehicleName, _plateNumber]] call EDEN_fnc_logTransaction;

// Notify player
[format["Successfully purchased %1 for $%2", _vehicleName, [_finalPrice] call EDEN_fnc_formatMoney], "success"] remoteExec ["EDEN_fnc_showNotification", _player];
[format["Vehicle plate number: %1", _plateNumber], "info"] remoteExec ["EDEN_fnc_showNotification", _player];

// Update client money display
[_player getVariable ["EDEN_Cash", 0], _player getVariable ["EDEN_Bank", 0]] remoteExec ["EDEN_fnc_updateMoneyDisplay", _player];

[format["Vehicle purchased: %1 (%2) by %3 for $%4", _vehicleName, _plateNumber, name _player, _finalPrice], "INFO", "VEHICLES"] call EDEN_fnc_systemLogger;

true
