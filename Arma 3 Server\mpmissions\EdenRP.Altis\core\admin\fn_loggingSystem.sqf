/*
    File: fn_loggingSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages comprehensive logging system.
*/

params [["_message", "", [""]], ["_level", "INFO", [""]], ["_category", "GENERAL", [""]]];

if (_message == "") exitWith { false };

if (isNil "eden_serverLogs") then {
    eden_serverLogs = [];
    publicVariable "eden_serverLogs";
};

_timestamp = format["%1-%2-%3 %4:%5:%6", 
    date select 0, date select 1, date select 2,
    date select 3, date select 4, floor(date select 5)
];

_logEntry = [_timestamp, _level, _category, _message];
eden_serverLogs pushBack _logEntry;

// Keep only last 1000 entries
if (count eden_serverLogs > 1000) then {
    eden_serverLogs deleteAt 0;
};

publicVariable "eden_serverLogs";

// Write to RPT file
diag_log format["[EDEN][%1][%2] %3", _level, _category, _message];

true
