/*
    File: fn_banSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages player ban system.
*/

params [["_admin", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON><PERSON>]]];

if (isNull _admin) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_banList") then {
            eden_banList = [];
            publicVariable "eden_banList";
        };
        true
    };
    case "banPlayer": {
        params ["", "", "", ["_reason", "Admin ban", [""]], ["_duration", 86400, [0]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        if (isNull _target) exitWith { false };
        
        _targetUID = getPlayerUID _target;
        _banEntry = [_targetUID, name _target, _reason, name _admin, time, (time + _duration)];
        eden_banList pushBack _banEntry;
        publicVariable "eden_banList";
        
        [format ["You have been banned. Reason: %1. Duration: %2 hours", _reason, (_duration/3600)]] remoteExec ["EDEN_fnc_showHint", _target];
        
        [_target] spawn {
            sleep 5;
            kickPlayer (_this select 0);
        };
        
        [format ["Banned %1 for %2 hours. Reason: %3", name _target, (_duration/3600), _reason]] call EDEN_fnc_showHint;
        
        [format ["[EDEN] Player %1 (%2) banned by %3. Reason: %4", name _target, _targetUID, name _admin, _reason], "ADMIN", "BAN"] call EDEN_fnc_systemLogger;
        true
    };
    case "unbanPlayer": {
        params ["", "", "", ["_playerUID", "", [""]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _banIndex = -1;
        {
            if ((_x select 0) == _playerUID) then { _banIndex = _forEachIndex; };
        } forEach eden_banList;
        
        if (_banIndex == -1) exitWith {
            ["Player not found in ban list"] call EDEN_fnc_showHint;
            false
        };
        
        _banEntry = eden_banList select _banIndex;
        eden_banList deleteAt _banIndex;
        publicVariable "eden_banList";
        
        [format ["Unbanned %1", (_banEntry select 1)]] call EDEN_fnc_showHint;
        
        [format ["[EDEN] Player %1 (%2) unbanned by %3", (_banEntry select 1), _playerUID, name _admin], "ADMIN", "UNBAN"] call EDEN_fnc_systemLogger;
        true
    };
    case "checkBan": {
        params ["", "", "", ["_playerUID", "", [""]]];
        
        _isBanned = false;
        _banReason = "";
        _banExpiry = 0;
        
        {
            if ((_x select 0) == _playerUID) then {
                if (time < (_x select 5)) then {
                    _isBanned = true;
                    _banReason = _x select 2;
                    _banExpiry = _x select 5;
                } else {
                    // Ban expired, remove it
                    eden_banList deleteAt _forEachIndex;
                    publicVariable "eden_banList";
                };
            };
        } forEach eden_banList;
        
        [_isBanned, _banReason, _banExpiry]
    };
    case "listBans": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _activeBans = [];
        {
            if (time < (_x select 5)) then { _activeBans pushBack _x; };
        } forEach eden_banList;
        
        [format ["Active bans: %1", count _activeBans]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
