/*
    File: fn_marketplaceSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages player marketplace system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_item", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_marketplace") then {
            eden_marketplace = [];
            publicVariable "eden_marketplace";
        };
        _player setVariable ["eden_marketSales", [], true];
        true
    };
    case "listItem": {
        params ["", "", "", ["_quantity", 1, [0]], ["_price", 100, [0]]];
        
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _hasItem = false;
        _playerQuantity = 0;
        
        {
            if ((_x select 0) == _item) then {
                _hasItem = true;
                _playerQuantity = _x select 1;
            };
        } forEach _virtualItems;
        
        if (!_hasItem || _playerQuantity < _quantity) exitWith {
            ["Insufficient items to list"] call EDEN_fnc_showHint;
            false
        };
        
        _listingFee = floor(_price * 0.05); // 5% listing fee
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _listingFee) exitWith {
            [format ["Not enough money for listing fee: $%1", _listingFee]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _listingFee), true];
        
        // Remove item from player
        [_player, "removeItem", _item, _quantity] call EDEN_fnc_itemSystem;
        
        _listing = [_item, _quantity, _price, getPlayerUID _player, name _player, time];
        eden_marketplace pushBack _listing;
        publicVariable "eden_marketplace";
        
        [format ["Listed %1x %2 for $%3 (Fee: $%4)", _quantity, _item, _price, _listingFee]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "buyItem": {
        params ["", "", "", ["_listingIndex", 0, [0]]];
        
        if (_listingIndex >= count eden_marketplace) exitWith {
            ["Invalid listing"] call EDEN_fnc_showHint;
            false
        };
        
        _listing = eden_marketplace select _listingIndex;
        _itemName = _listing select 0;
        _quantity = _listing select 1;
        _price = _listing select 2;
        _sellerUID = _listing select 3;
        
        if (_sellerUID == getPlayerUID _player) exitWith {
            ["Cannot buy your own listing"] call EDEN_fnc_showHint;
            false
        };
        
        _cash = _player getVariable ["eden_cash", 0];
        if (_cash < _price) exitWith {
            [format ["Not enough money! Need $%1", _price]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _price), true];
        
        // Add item to buyer
        [_player, "addItem", _itemName, _quantity] call EDEN_fnc_itemSystem;
        
        // Pay seller
        _seller = objNull;
        {
            if (getPlayerUID _x == _sellerUID) then { _seller = _x; };
        } forEach allPlayers;
        
        if (!isNull _seller) then {
            _sellerCash = _seller getVariable ["eden_cash", 0];
            _commission = floor(_price * 0.1); // 10% marketplace commission
            _sellerEarnings = _price - _commission;
            _seller setVariable ["eden_cash", (_sellerCash + _sellerEarnings), true];
            
            _sales = _seller getVariable ["eden_marketSales", []];
            _sales pushBack [_itemName, _quantity, _sellerEarnings, time];
            _seller setVariable ["eden_marketSales", _sales, true];
            
            [format ["Sold %1x %2 for $%3", _quantity, _itemName, _sellerEarnings]] remoteExec ["EDEN_fnc_showHint", _seller];
            [_seller] call EDEN_fnc_savePlayerData;
        };
        
        eden_marketplace deleteAt _listingIndex;
        publicVariable "eden_marketplace";
        
        [format ["Purchased %1x %2 for $%3", _quantity, _itemName, _price]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "cancelListing": {
        params ["", "", "", ["_listingIndex", 0, [0]]];
        
        if (_listingIndex >= count eden_marketplace) exitWith { false };
        
        _listing = eden_marketplace select _listingIndex;
        _sellerUID = _listing select 3;
        
        if (_sellerUID != getPlayerUID _player) exitWith {
            ["Not your listing"] call EDEN_fnc_showHint;
            false
        };
        
        _itemName = _listing select 0;
        _quantity = _listing select 1;
        
        // Return item to player
        [_player, "addItem", _itemName, _quantity] call EDEN_fnc_itemSystem;
        
        eden_marketplace deleteAt _listingIndex;
        publicVariable "eden_marketplace";
        
        [format ["Cancelled listing for %1x %2", _quantity, _itemName]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
