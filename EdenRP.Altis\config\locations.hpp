/*
    EdenRP Locations Configuration
    Enhanced location definitions for Altis
*/

// Cities and Towns
EDEN_CITIES = [
    ["Kavala", [3560, 13175, 0], 800, "major"],
    ["Athira", [14000, 18700, 0], 600, "major"],
    ["Pyrgos", [16800, 12400, 0], 500, "major"],
    ["Sofia", [25900, 21400, 0], 400, "minor"],
    ["Paros", [23300, 19600, 0], 300, "minor"],
    ["Zaros", [26800, 24400, 0], 300, "minor"],
    ["Kalochori", [21100, 16000, 0], 250, "minor"],
    ["Frini", [23100, 20200, 0], 200, "village"],
    ["Dorida", [12900, 14500, 0], 200, "village"],
    ["Aggelochori", [12100, 15900, 0], 150, "village"],
    ["Chalkeia", [16200, 16800, 0], 150, "village"],
    ["Galati", [18900, 15400, 0], 150, "village"]
];

// Police Stations
EDEN_POLICE_STATIONS = [
    ["Kavala Police Station", [3540, 13100, 0], "main"],
    ["Athira Police Station", [14000, 18650, 0], "main"],
    ["Pyrgos Police Station", [16750, 12350, 0], "main"],
    ["Sofia Police Outpost", [25850, 21350, 0], "outpost"],
    ["Airport Police Station", [14800, 16700, 0], "outpost"],
    ["Highway Patrol Station", [18500, 14200, 0], "patrol"]
];

// Hospitals
EDEN_HOSPITALS = [
    ["Kavala General Hospital", [3520, 13200, 0], "major"],
    ["Athira Medical Center", [13950, 18750, 0], "major"],
    ["Pyrgos Regional Hospital", [16850, 12450, 0], "major"],
    ["Sofia Medical Clinic", [25900, 21450, 0], "clinic"],
    ["Field Hospital Alpha", [20000, 20000, 0], "field"],
    ["Emergency Medical Station", [15000, 17000, 0], "emergency"]
];

// Banks and ATMs
EDEN_BANKS = [
    ["Kavala Central Bank", [3594, 13100, 0], 500000, 4, 0],
    ["Athira National Bank", [14050, 18700, 0], 750000, 5, 0],
    ["Pyrgos Federal Bank", [16800, 12500, 0], 1000000, 5, 0],
    ["Sofia Community Bank", [25950, 21400, 0], 250000, 3, 0]
];

EDEN_ATMS = [
    [3594, 13100, 0], [3520, 13200, 0], [3540, 13100, 0], // Kavala
    [14050, 18700, 0], [13950, 18750, 0], [14000, 18650, 0], // Athira
    [16800, 12500, 0], [16850, 12450, 0], [16750, 12350, 0], // Pyrgos
    [25950, 21400, 0], [25900, 21450, 0], [25850, 21350, 0], // Sofia
    [23300, 19600, 0], [26800, 24400, 0], [21100, 16000, 0] // Other towns
];

// Vehicle Shops
EDEN_VEHICLE_SHOPS = [
    ["Kavala Motors", [3500, 13300, 0], "civilian"],
    ["Athira Auto Sales", [13900, 18800, 0], "civilian"],
    ["Pyrgos Vehicle Center", [16900, 12300, 0], "civilian"],
    ["Sofia Car Lot", [26000, 21300, 0], "civilian"],
    ["Luxury Motors Kavala", [3400, 13400, 0], "luxury"],
    ["Truck Depot Athira", [13800, 18900, 0], "industrial"],
    ["Police Motor Pool", [3540, 13050, 0], "police"],
    ["Medical Vehicle Bay", [3520, 13250, 0], "medical"]
];

// Aircraft Shops and Airports
EDEN_AIRCRAFT_SHOPS = [
    ["Altis International Airport", [14800, 16700, 0], "civilian"],
    ["Kavala Airfield", [3200, 12800, 0], "civilian"],
    ["Pyrgos Airstrip", [17000, 12000, 0], "civilian"],
    ["Police Aviation Unit", [14850, 16650, 0], "police"],
    ["Medical Air Rescue", [14750, 16750, 0], "medical"]
];

// Boat Shops and Marinas
EDEN_BOAT_SHOPS = [
    ["Kavala Marina", [3700, 13500, 0], "civilian"],
    ["Athira Boat Rental", [13500, 19200, 0], "civilian"],
    ["Pyrgos Harbor", [17200, 12800, 0], "civilian"],
    ["Coast Guard Station", [3750, 13450, 0], "police"]
];

// General Stores
EDEN_GENERAL_STORES = [
    ["Kavala General Store", [3580, 13250, 0], "general"],
    ["Athira Market", [14100, 18600, 0], "general"],
    ["Pyrgos Shop", [16700, 12400, 0], "general"],
    ["Sofia Mini Mart", [25800, 21500, 0], "general"],
    ["Highway Stop", [18000, 15000, 0], "convenience"],
    ["Airport Shop", [14900, 16600, 0], "convenience"]
];

// Clothing Stores
EDEN_CLOTHING_STORES = [
    ["Kavala Outfitters", [3600, 13150, 0], "civilian"],
    ["Athira Fashion", [14150, 18650, 0], "civilian"],
    ["Pyrgos Clothing", [16650, 12450, 0], "civilian"],
    ["Police Uniform Supply", [3530, 13080, 0], "police"],
    ["Medical Scrubs & More", [3510, 13220, 0], "medical"]
];

// Gas Stations
EDEN_GAS_STATIONS = [
    ["Kavala Fuel", [3650, 13000, 0]],
    ["Athira Gas", [13700, 18500, 0]],
    ["Pyrgos Petrol", [17100, 12200, 0]],
    ["Sofia Station", [26100, 21200, 0]],
    ["Highway Fuel Stop", [18200, 14800, 0]],
    ["Airport Fuel", [15000, 16500, 0]],
    ["Industrial Fuel Depot", [20000, 15000, 0]]
];

// Mining Locations
EDEN_MINING_LOCATIONS = [
    ["Iron Mine", [17000, 21000, 0], ["iron_ore"], 2, 0],
    ["Copper Mine", [21000, 16000, 0], ["copper_ore"], 1, 0],
    ["Diamond Mine", [18500, 19500, 0], ["diamond_uncut"], 4, 0],
    ["Salt Flats", [26000, 23000, 0], ["salt_unrefined"], 1, 0],
    ["Quarry", [22000, 18000, 0], ["stone", "sand"], 2, 0],
    ["Coal Mine", [19000, 17000, 0], ["coal"], 2, 0]
];

// Processing Locations
EDEN_PROCESSING_LOCATIONS = [
    ["Iron Processing", [16500, 20500, 0], "iron_ore", "iron_ingot"],
    ["Copper Smelter", [20500, 15500, 0], "copper_ore", "copper_ingot"],
    ["Diamond Cutting", [18000, 19000, 0], "diamond_uncut", "diamond"],
    ["Salt Refinery", [25500, 22500, 0], "salt_unrefined", "salt"],
    ["Oil Refinery", [21500, 18500, 0], "oil_unprocessed", "oil"],
    ["Glass Factory", [22500, 17500, 0], "sand", "glass"]
];

// Drug Locations (Illegal)
EDEN_DRUG_LOCATIONS = [
    ["Marijuana Field", [6000, 16000, 0], "marijuana", 3],
    ["Cocaine Lab", [25000, 6000, 0], "cocaine", 4],
    ["Heroin Lab", [5000, 21000, 0], "heroin", 5],
    ["Meth Lab", [27000, 26000, 0], "meth", 4],
    ["Drug Dealer", [3800, 13600, 0], "dealer", 2]
];

// Fishing Locations
EDEN_FISHING_LOCATIONS = [
    ["Kavala Pier", [3700, 13400, 0], ["fish_tuna", "fish_salmon"], 1],
    ["Athira Docks", [13400, 19100, 0], ["fish_mackerel", "fish_salmon"], 1],
    ["Pyrgos Harbor", [17100, 12700, 0], ["fish_tuna", "fish_mackerel"], 1],
    ["Deep Sea Fishing", [15000, 25000, 0], ["fish_tuna", "turtle_raw"], 3],
    ["Lake Fishing", [20000, 20000, 0], ["fish_salmon", "fish_mackerel"], 2]
];

// Gang Territories
EDEN_GANG_TERRITORIES = [
    ["Downtown Kavala", [3600, 13200, 0], 200, 300, 2000],
    ["Athira Industrial", [14200, 18500, 0], 250, 360, 2500],
    ["Pyrgos Docks", [17000, 12600, 0], 180, 300, 1800],
    ["Sofia Outskirts", [26000, 21500, 0], 150, 240, 1500],
    ["Highway Junction", [18000, 15000, 0], 300, 420, 3000],
    ["Airport Perimeter", [15000, 16500, 0], 400, 480, 4000],
    ["Industrial Zone", [20000, 15000, 0], 350, 450, 3500],
    ["Northern Hills", [15000, 22000, 0], 500, 600, 5000]
];

// Robbery Locations
EDEN_ROBBERY_LOCATIONS = [
    ["Federal Reserve", [16000, 16500, 0], 2000000, 5, 0],
    ["Jewelry Store Kavala", [3620, 13180, 0], 150000, 3, 0],
    ["Jewelry Store Athira", [14080, 18680, 0], 200000, 3, 0],
    ["Jewelry Store Pyrgos", [16780, 12480, 0], 175000, 3, 0],
    ["Gas Station Alpha", [3650, 13000, 0], 25000, 1, 0],
    ["Gas Station Beta", [13700, 18500, 0], 30000, 1, 0],
    ["Gas Station Gamma", [17100, 12200, 0], 28000, 1, 0]
];

// Spawn Points
EDEN_SPAWN_POINTS = [
    // Civilian Spawns
    ["Kavala", [3560, 13175, 0], "civilian"],
    ["Athira", [14000, 18700, 0], "civilian"],
    ["Pyrgos", [16800, 12400, 0], "civilian"],
    ["Sofia", [25900, 21400, 0], "civilian"],
    
    // Police Spawns
    ["Kavala PD", [3540, 13100, 0], "police"],
    ["Athira PD", [14000, 18650, 0], "police"],
    ["Pyrgos PD", [16750, 12350, 0], "police"],
    ["HQ", [16000, 16500, 0], "police"],
    
    // Medical Spawns
    ["Kavala Hospital", [3520, 13200, 0], "medical"],
    ["Athira Medical", [13950, 18750, 0], "medical"],
    ["Pyrgos Hospital", [16850, 12450, 0], "medical"],
    ["Central Medical", [15000, 17000, 0], "medical"]
];

// Safe Zones
EDEN_SAFE_ZONES = [
    ["Kavala Green Zone", [3560, 13175, 0], 300],
    ["Athira Green Zone", [14000, 18700, 0], 300],
    ["Pyrgos Green Zone", [16800, 12400, 0], 300],
    ["Airport Green Zone", [14800, 16700, 0], 400],
    ["Prison Green Zone", [16000, 16000, 0], 200]
];

// No-Fly Zones
EDEN_NO_FLY_ZONES = [
    ["Prison Airspace", [16000, 16000, 0], 1000, 500],
    ["Military Base", [26000, 26000, 0], 2000, 1000],
    ["Government Complex", [16500, 16500, 0], 800, 300]
];
