/*
    EdenRP Main Initialization Script
    Enhanced Altis Life framework initialization
    
    This script handles the main initialization sequence for EdenRP
    with improved security, performance, and modularity
*/

// Initialize core variables
EDEN_Debug = false; // Set to true for development
EDEN_Version = "1.0.0";
EDEN_BuildDate = "2025-01-29";

// Performance monitoring
EDEN_InitStartTime = diag_tickTime;

// Core system initialization
if (hasInterface) then {
    // Client initialization
    [] spawn {
        // Wait for player to be ready
        waitUntil {!isNull player && player == player};
        waitUntil {!isNil "bis_fnc_init"};
        
        // Initialize loading screen
        [] call EDEN_fnc_loadingScreenManager;
        
        // Set initial loading progress
        ["Initializing EdenRP Systems...", 0] call EDEN_fnc_loadingScreenProgress;
        
        // Initialize core client systems
        ["Loading Core Systems...", 10] call EDEN_fnc_loadingScreenProgress;
        [] call EDEN_fnc_initializeClient;
        
        // Initialize security validation
        ["Validating Security...", 20] call EDEN_fnc_loadingScreenProgress;
        [] call EDEN_fnc_securityValidator;
        
        // Setup event handlers
        ["Setting up Event Handlers...", 30] call EDEN_fnc_loadingScreenProgress;
        [] call EDEN_fnc_setupEventHandlers;
        
        // Setup player actions
        ["Configuring Player Actions...", 40] call EDEN_fnc_loadingScreenProgress;
        [] call EDEN_fnc_setupPlayerActions;
        
        // Initialize faction-specific systems
        ["Loading Faction Systems...", 50] call EDEN_fnc_loadingScreenProgress;
        switch (playerSide) do {
            case civilian: {
                [] call EDEN_fnc_initializeCivilian;
            };
            case west: {
                [] call EDEN_fnc_initializePolice;
            };
            case independent: {
                [] call EDEN_fnc_initializeMedical;
            };
        };
        
        // Initialize UI systems
        ["Loading User Interface...", 60] call EDEN_fnc_loadingScreenProgress;
        [] call EDEN_fnc_hudManager;
        [] call EDEN_fnc_menuSystem;
        [] call EDEN_fnc_notificationSystem;
        
        // Initialize communication systems
        ["Setting up Communications...", 70] call EDEN_fnc_loadingScreenProgress;
        [] call EDEN_fnc_phoneSystem;
        [] call EDEN_fnc_radioSystem;
        [] call EDEN_fnc_messagingSystem;
        
        // Initialize progression systems
        ["Loading Progression Systems...", 80] call EDEN_fnc_loadingScreenProgress;
        [] call EDEN_fnc_experienceSystem;
        [] call EDEN_fnc_skillTrees;
        [] call EDEN_fnc_achievementSystem;
        
        // Initialize economy systems
        ["Connecting to Economy...", 90] call EDEN_fnc_loadingScreenProgress;
        [] call EDEN_fnc_marketSystem;
        [] call EDEN_fnc_bankingSystem;
        
        // Final initialization
        ["Finalizing Setup...", 95] call EDEN_fnc_loadingScreenProgress;
        [] call EDEN_fnc_performanceMonitor;
        
        // Complete initialization
        ["Welcome to EdenRP!", 100] call EDEN_fnc_loadingScreenProgress;
        
        // Log initialization completion
        private _initTime = diag_tickTime - EDEN_InitStartTime;
        [format["Client initialization completed in %1 seconds", _initTime]] call EDEN_fnc_systemLogger;
        
        // Show welcome message
        sleep 2;
        ["Welcome to EdenRP - Enhanced Altis Life Experience", "success"] call EDEN_fnc_showNotification;
        
        // Start briefing system
        [] call EDEN_fnc_briefingSystem;
    };
};

if (isServer) then {
    // Server initialization
    [] spawn {
        // Initialize server systems
        [format["EdenRP Server v%1 starting initialization...", EDEN_Version]] call EDEN_fnc_systemLogger;
        
        // Initialize database connection
        [] call EDEN_fnc_initializeDatabase;
        
        // Initialize economy system
        [] call EDEN_fnc_initializeEconomy;
        
        // Initialize security systems
        [] call EDEN_fnc_initializeSecurity;
        
        // Initialize cleanup systems
        [] call EDEN_fnc_initializeCleanup;
        
        // Initialize admin systems
        [] call EDEN_fnc_initializeAdmin;
        
        // Initialize gang systems
        [] call EDEN_fnc_initializeGangs;
        
        // Initialize housing systems
        [] call EDEN_fnc_initializeHousing;
        
        // Initialize vehicle systems
        [] call EDEN_fnc_initializeVehicles;
        
        // Initialize job systems
        [] call EDEN_fnc_initializeJobs;
        
        // Initialize event systems
        [] call EDEN_fnc_initializeEvents;
        
        // Start performance monitoring
        [] call EDEN_fnc_serverPerformanceMonitor;
        
        // Log server ready
        private _initTime = diag_tickTime - EDEN_InitStartTime;
        [format["EdenRP Server initialization completed in %1 seconds", _initTime]] call EDEN_fnc_systemLogger;
        
        // Set server ready flag
        EDEN_ServerReady = true;
        publicVariable "EDEN_ServerReady";
    };
};

// Global variables initialization
EDEN_PlayerCount = 0;
EDEN_PoliceCount = 0;
EDEN_MedicCount = 0;
EDEN_CivilianCount = 0;

// Economy variables
EDEN_EconomyMultiplier = 1.0;
EDEN_XPMultiplier = 1.0;

// Security variables
EDEN_AntiCheatEnabled = true;
EDEN_LoggingEnabled = true;
EDEN_ValidationEnabled = true;

// Performance variables
EDEN_MaxViewDistance = 3000;
EDEN_MaxObjectDistance = 2000;
EDEN_TerrainGrid = 25;

// Gang system variables
EDEN_MaxGangMembers = 12;
EDEN_MaxGangs = 50;
EDEN_GangTerritories = [];

// Housing system variables
EDEN_MaxHousesPerPlayer = 3;
EDEN_HouseCleanupTime = 2592000; // 30 days in seconds

// Vehicle system variables
EDEN_MaxVehiclesPerPlayer = 5;
EDEN_VehicleCleanupTime = 604800; // 7 days in seconds

// Job system variables
EDEN_JobCooldownTime = 300; // 5 minutes
EDEN_MaxJobsPerPlayer = 3;

// Communication system variables
EDEN_MaxMessageLength = 255;
EDEN_MessageCooldown = 5; // seconds

// Admin system variables
EDEN_AdminLevels = [
    "Player",      // 0
    "Supporter",   // 1
    "Moderator",   // 2
    "Admin",       // 3
    "Senior Admin",// 4
    "Developer"    // 5
];

// Initialize mission parameters
if (isServer) then {
    // Apply server parameters
    if (!isNil "paramsArray") then {
        if (count paramsArray > 0) then {
            EDEN_EconomyMultiplier = paramsArray select 0;
        };
        if (count paramsArray > 1) then {
            EDEN_XPMultiplier = paramsArray select 1;
        };
        if (count paramsArray > 2) then {
            EDEN_MaxGangMembers = paramsArray select 2;
        };
    };
    
    // Broadcast parameters to clients
    publicVariable "EDEN_EconomyMultiplier";
    publicVariable "EDEN_XPMultiplier";
    publicVariable "EDEN_MaxGangMembers";
};

// Initialize cleanup system
if (isServer) then {
    [] spawn {
        while {true} do {
            sleep 300; // Run every 5 minutes
            [] call EDEN_fnc_cleanupSystem;
        };
    };
};

// Initialize performance monitoring
[] spawn {
    while {true} do {
        sleep 60; // Check every minute
        [] call EDEN_fnc_performanceCheck;
    };
};

// Initialize security monitoring
if (EDEN_AntiCheatEnabled) then {
    [] spawn {
        while {true} do {
            sleep 30; // Check every 30 seconds
            [] call EDEN_fnc_antiCheatMonitor;
        };
    };
};

// Log initialization start
[format["EdenRP v%1 initialization started on %2", EDEN_Version, EDEN_BuildDate]] call EDEN_fnc_systemLogger;
