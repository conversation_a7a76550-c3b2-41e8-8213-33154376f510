/*
    File: fn_buyVehicle.sqf
    Author: EdenRP Development Team
    
    Description:
    Purchases a vehicle from a dealership.
    
    Parameters:
    0: STRING - Vehicle class to buy
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if purchase was successful
*/

params [
    ["_vehicleClass", "", [""]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (_vehicleClass == "" || isNull _player) exitWith { false };

// Vehicle prices
_vehiclePrices = createHashMap;
// Civilian Cars
_vehiclePrices set ["C_Offroad_01_F", 15000];
_vehiclePrices set ["C_SUV_01_F", 25000];
_vehiclePrices set ["C_Hatchback_01_F", 12000];
_vehiclePrices set ["C_Van_01_transport_F", 35000];
_vehiclePrices set ["B_Quadbike_01_F", 8000];
// Trucks
_vehiclePrices set ["C_Truck_02_transport_F", 75000];
_vehiclePrices set ["C_Truck_02_covered_F", 85000];
// Boats
_vehiclePrices set ["C_Boat_Civil_01_F", 20000];
_vehiclePrices set ["C_Rubberboat_02_F", 5000];
// Aircraft
_vehiclePrices set ["B_Heli_Light_01_civil_F", 250000];
_vehiclePrices set ["C_Plane_Civil_01_F", 500000];

_price = _vehiclePrices getOrDefault [_vehicleClass, 0];
if (_price == 0) exitWith {
    ["This vehicle is not available for purchase!"] call EDEN_fnc_showHint;
    false
};

// Check if player has enough money
_playerMoney = _player getVariable ["eden_cash", 0];
_playerBank = _player getVariable ["eden_bankAccount", 0];
_totalMoney = _playerMoney + _playerBank;

if (_totalMoney < _price) exitWith {
    [format ["Vehicle costs $%1, but you only have $%2!", _price, _totalMoney]] call EDEN_fnc_showHint;
    false
};

// Check license requirements
_requiredLicense = switch (true) do {
    case (_vehicleClass isKindOf "Car"): { "driver" };
    case (_vehicleClass isKindOf "Air"): { "pilot" };
    case (_vehicleClass isKindOf "Ship"): { "boat" };
    default { "" };
};

if (_requiredLicense != "" && !(_player getVariable [format ["eden_license_%1", _requiredLicense], false])) exitWith {
    [format ["You need a %1 license to buy this vehicle!", _requiredLicense]] call EDEN_fnc_showHint;
    false
};

// Check if near dealership
_nearDealerships = nearestObjects [_player, ["Land_CarService_F", "Land_Garage_V1_F"], 25];
if (count _nearDealerships == 0) exitWith {
    ["You must be at a vehicle dealership!"] call EDEN_fnc_showHint;
    false
};

// Payment processing (prefer bank over cash)
if (_playerBank >= _price) then {
    _player setVariable ["eden_bankAccount", (_playerBank - _price), true];
} else {
    _remainingCost = _price - _playerBank;
    _player setVariable ["eden_bankAccount", 0, true];
    _player setVariable ["eden_cash", (_playerMoney - _remainingCost), true];
};

// Find spawn position
_spawnPos = [getPos _player, 10, 50, 5, 0, 20, 0] call BIS_fnc_findSafePos;

// Spawn vehicle
_vehicle = createVehicle [_vehicleClass, _spawnPos, [], 0, "NONE"];
_vehicle setDir (getDir _player);
_vehicle setFuel 1;

// Set ownership
_vehicle setVariable ["eden_ownerUID", getPlayerUID _player, true];
_vehicle setVariable ["eden_ownerName", name _player, true];
_vehicle setVariable ["eden_purchasePrice", _price, true];
_vehicle setVariable ["eden_purchaseTime", time, true];

// Lock vehicle to owner
_vehicle lock 2;

[format ["Vehicle purchased for $%1! Use your key to unlock it.", _price]] call EDEN_fnc_showHint;

// Log purchase
[format ["[EDEN] Player %1 purchased vehicle %2 for $%3", name _player, _vehicleClass, _price], "INFO", "VEHICLE"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
