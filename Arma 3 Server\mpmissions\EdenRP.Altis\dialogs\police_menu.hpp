/*
    File: police_menu.hpp
    Author: EdenRP Development Team
    
    Description:
    Police menu dialog definitions for EdenRP
*/

class EdenRP_PoliceMenu {
    idd = 3200;
    name = "EdenRP_PoliceMenu";
    movingEnable = 1;
    enableSimulation = 1;
    
    class controlsBackground {
        class Background: RscText {
            idc = -1;
            x = 0.15;
            y = 0.15;
            w = 0.7;
            h = 0.7;
            colorBackground[] = {0, 0, 0, 0.8};
        };
        
        class Title: RscText {
            idc = -1;
            text = "Police Command Center";
            x = 0.15;
            y = 0.15;
            w = 0.7;
            h = 0.05;
            colorText[] = {1, 1, 1, 1};
            sizeEx = 0.04;
        };
    };
    
    class controls {
        class WantedList: RscListBox {
            idc = 3201;
            x = 0.2;
            y = 0.25;
            w = 0.25;
            h = 0.5;
        };
        
        class PlayerList: RscListBox {
            idc = 3202;
            x = 0.5;
            y = 0.25;
            w = 0.25;
            h = 0.5;
        };
        
        class ArrestButton: RscButton {
            idc = 3203;
            text = "Arrest Player";
            x = 0.2;
            y = 0.77;
            w = 0.1;
            h = 0.04;
            action = "[] call EDEN_fnc_arrestPlayer;";
        };
        
        class TicketButton: RscButton {
            idc = 3204;
            text = "Issue Ticket";
            x = 0.32;
            y = 0.77;
            w = 0.1;
            h = 0.04;
            action = "[] call EDEN_fnc_issueTicket;";
        };
        
        class SearchButton: RscButton {
            idc = 3205;
            text = "Search Player";
            x = 0.44;
            y = 0.77;
            w = 0.1;
            h = 0.04;
            action = "[] call EDEN_fnc_searchPlayer;";
        };
        
        class ImpoundButton: RscButton {
            idc = 3206;
            text = "Impound Vehicle";
            x = 0.56;
            y = 0.77;
            w = 0.1;
            h = 0.04;
            action = "[] call EDEN_fnc_impoundVehicle;";
        };
        
        class CloseButton: RscButton {
            idc = 3207;
            text = "Close";
            x = 0.75;
            y = 0.8;
            w = 0.08;
            h = 0.04;
            action = "closeDialog 0;";
        };
    };
};
