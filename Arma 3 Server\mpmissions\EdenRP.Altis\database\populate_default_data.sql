-- --------------------------------------------------------
-- EdenRP Default Data Population Script
-- Populates new tables with default data
-- Version: 2.0.0
-- Date: 2025-07-29
-- --------------------------------------------------------

USE `edenrp`;

-- Insert default stock market data
INSERT IGNORE INTO `eden_stock_market` (`symbol`, `company_name`, `current_price`, `previous_price`, `volume`, `market_cap`) VALUES
('ALTIS', 'Altis Mining Corp', 125.50, 120.00, 15000, 5000000),
('KAVALA', 'Kavala Industries', 89.25, 92.10, 8500, 3200000),
('PYRGOS', 'Pyrgos Energy', 156.75, 155.20, 12000, 7800000),
('SOFIA', 'Sofia Transport', 67.40, 65.80, 6200, 2100000),
('ATHIRA', 'Athira Tech', 234.60, 228.90, 18500, 12500000),
('NEOCHORI', 'Neochori Foods', 45.30, 44.80, 4500, 1800000),
('ZAROS', 'Zaros Manufacturing', 78.90, 76.50, 7200, 2900000);

-- Insert default seasonal events
INSERT IGNORE INTO `eden_seasonal_events` (`event_name`, `description`, `start_date`, `end_date`, `rewards`, `active`) VALUES
('Summer Festival', 'Special summer activities with bonus rewards', '2025-06-01 00:00:00', '2025-08-31 23:59:59', '{"xp_bonus": 1.5, "cash_bonus": 1.2}', 0),
('Winter Holidays', 'Holiday themed events and special items', '2025-12-01 00:00:00', '2025-12-31 23:59:59', '{"special_items": ["gift_box", "holiday_hat"], "xp_bonus": 2.0}', 0),
('Spring Cleanup', 'Environmental cleanup activities', '2025-03-01 00:00:00', '2025-05-31 23:59:59', '{"reputation_bonus": 50, "cash_bonus": 1.1}', 0),
('Autumn Harvest', 'Special farming and gathering bonuses', '2025-09-01 00:00:00', '2025-11-30 23:59:59', '{"farming_bonus": 1.8, "gathering_bonus": 1.5}', 0);

-- Insert sample news articles
INSERT IGNORE INTO `eden_news_articles` (`author_id`, `title`, `content`, `category`, `published`) VALUES
('SYSTEM', 'Welcome to EdenRP!', 'Welcome to the enhanced EdenRP Altis Life server! We have many new features including stock trading, advanced progression systems, and enhanced security.', 'announcement', 1),
('SYSTEM', 'Stock Market Now Open', 'The Altis Stock Exchange is now open for trading! Invest in local companies and watch your portfolio grow.', 'economy', 1),
('SYSTEM', 'New Achievement System', 'Unlock achievements by completing various tasks and challenges. Check your progress in the player menu!', 'features', 1),
('SYSTEM', 'Enhanced Security Measures', 'We have implemented advanced anti-cheat and security systems to ensure fair gameplay for everyone.', 'security', 1);

-- Insert default business types (for reference)
INSERT IGNORE INTO `eden_config` (`config_key`, `config_value`, `description`) VALUES
('business_types', '["restaurant", "shop", "garage", "warehouse", "office", "factory"]', 'Available business types'),
('stock_market_enabled', '1', 'Enable stock market trading'),
('auction_house_enabled', '1', 'Enable auction house system'),
('daily_tasks_enabled', '1', 'Enable daily task system'),
('achievements_enabled', '1', 'Enable achievement system'),
('social_media_enabled', '1', 'Enable social media system'),
('email_system_enabled', '1', 'Enable email system'),
('news_system_enabled', '1', 'Enable news system'),
('security_monitoring_enabled', '1', 'Enable security monitoring'),
('risk_assessment_enabled', '1', 'Enable risk assessment system'),
('forensic_system_enabled', '1', 'Enable forensic analysis system');

-- Create additional performance indexes
CREATE INDEX IF NOT EXISTS `idx_players_level` ON `eden_players` (`level`);
CREATE INDEX IF NOT EXISTS `idx_players_experience` ON `eden_players` (`experience`);
CREATE INDEX IF NOT EXISTS `idx_players_reputation` ON `eden_players` (`reputation`);
CREATE INDEX IF NOT EXISTS `idx_players_playtime` ON `eden_players` (`playtime`);
CREATE INDEX IF NOT EXISTS `idx_players_gang` ON `eden_players` (`gang_id`);
CREATE INDEX IF NOT EXISTS `idx_players_prestige` ON `eden_players` (`prestige_level`);

CREATE INDEX IF NOT EXISTS `idx_vehicles_owner_type` ON `eden_vehicles` (`owner_id`, `type`);
CREATE INDEX IF NOT EXISTS `idx_vehicles_active` ON `eden_vehicles` (`active`);
CREATE INDEX IF NOT EXISTS `idx_vehicles_impounded` ON `eden_vehicles` (`impounded`);

CREATE INDEX IF NOT EXISTS `idx_houses_owner` ON `eden_houses` (`owner_id`);
CREATE INDEX IF NOT EXISTS `idx_houses_price` ON `eden_houses` (`price`);

CREATE INDEX IF NOT EXISTS `idx_gangs_leader` ON `eden_gangs` (`leader_id`);
CREATE INDEX IF NOT EXISTS `idx_gangs_active` ON `eden_gangs` (`active`);

CREATE INDEX IF NOT EXISTS `idx_logs_timestamp_category` ON `eden_logs` (`timestamp`, `category`);
CREATE INDEX IF NOT EXISTS `idx_logs_player_category` ON `eden_logs` (`player_id`, `category`);

CREATE INDEX IF NOT EXISTS `idx_stock_market_symbol` ON `eden_stock_market` (`symbol`);
CREATE INDEX IF NOT EXISTS `idx_player_stocks_player_symbol` ON `eden_player_stocks` (`player_id`, `symbol`);
CREATE INDEX IF NOT EXISTS `idx_loans_player_status` ON `eden_loans` (`player_id`, `status`);
CREATE INDEX IF NOT EXISTS `idx_businesses_owner_type` ON `eden_businesses` (`owner_id`, `type`);
CREATE INDEX IF NOT EXISTS `idx_containers_owner_type` ON `eden_containers` (`owner_id`, `type`);
CREATE INDEX IF NOT EXISTS `idx_auctions_status_end` ON `eden_auctions` (`status`, `end_time`);
CREATE INDEX IF NOT EXISTS `idx_emails_recipient_read` ON `eden_emails` (`recipient_id`, `read_status`);
CREATE INDEX IF NOT EXISTS `idx_social_posts_timestamp` ON `eden_social_posts` (`timestamp` DESC);
CREATE INDEX IF NOT EXISTS `idx_news_published_category` ON `eden_news_articles` (`published`, `category`);
CREATE INDEX IF NOT EXISTS `idx_reports_status_timestamp` ON `eden_reports` (`status`, `timestamp`);
CREATE INDEX IF NOT EXISTS `idx_bans_player_active` ON `eden_bans` (`player_id`, `active`);
CREATE INDEX IF NOT EXISTS `idx_warnings_player_date` ON `eden_warnings` (`player_id`, `warning_date`);
CREATE INDEX IF NOT EXISTS `idx_achievements_player_completed` ON `eden_achievements` (`player_id`, `completed`);
CREATE INDEX IF NOT EXISTS `idx_daily_tasks_player_date` ON `eden_daily_tasks` (`player_id`, `assigned_date`);
CREATE INDEX IF NOT EXISTS `idx_leaderboards_category_rank` ON `eden_leaderboards` (`category`, `rank_position`);
CREATE INDEX IF NOT EXISTS `idx_incidents_type_status` ON `eden_security_incidents` (`incident_type`, `status`);
CREATE INDEX IF NOT EXISTS `idx_evidence_type_player` ON `eden_forensic_evidence` (`evidence_type`, `player_id`);
CREATE INDEX IF NOT EXISTS `idx_violations_player_type` ON `eden_player_violations` (`player_id`, `violation_type`);
CREATE INDEX IF NOT EXISTS `idx_risk_assessments_player_level` ON `eden_risk_assessments` (`player_id`, `risk_level`);

-- Create useful views for common queries
CREATE OR REPLACE VIEW `view_player_stats` AS
SELECT 
    p.player_id,
    p.name,
    p.level,
    p.experience,
    p.reputation,
    p.cash + p.bank AS total_money,
    p.playtime,
    p.prestige_level,
    p.prestige_points,
    g.name AS gang_name,
    COUNT(DISTINCT v.id) AS vehicle_count,
    COUNT(DISTINCT h.id) AS house_count,
    COUNT(DISTINCT b.id) AS business_count
FROM eden_players p
LEFT JOIN eden_gangs g ON p.gang_id = g.id
LEFT JOIN eden_vehicles v ON p.player_id = v.owner_id AND v.active = 1
LEFT JOIN eden_houses h ON p.player_id = h.owner_id
LEFT JOIN eden_businesses b ON p.player_id = b.owner_id
GROUP BY p.player_id;

CREATE OR REPLACE VIEW `view_active_violations` AS
SELECT 
    pv.player_id,
    p.name,
    pv.violation_type,
    pv.severity,
    COUNT(*) AS violation_count,
    MAX(pv.timestamp) AS last_violation
FROM eden_player_violations pv
JOIN eden_players p ON pv.player_id = p.player_id
WHERE pv.timestamp > DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY pv.player_id, pv.violation_type, pv.severity;

CREATE OR REPLACE VIEW `view_stock_portfolio` AS
SELECT 
    ps.player_id,
    p.name,
    ps.symbol,
    sm.company_name,
    ps.shares,
    ps.purchase_price,
    sm.current_price,
    (sm.current_price - ps.purchase_price) * ps.shares AS profit_loss,
    ps.purchase_date
FROM eden_player_stocks ps
JOIN eden_players p ON ps.player_id = p.player_id
JOIN eden_stock_market sm ON ps.symbol = sm.symbol;

CREATE OR REPLACE VIEW `view_leaderboard_summary` AS
SELECT 
    l.category,
    p.name,
    l.score,
    l.rank_position,
    l.last_update
FROM eden_leaderboards l
JOIN eden_players p ON l.player_id = p.player_id
WHERE l.rank_position <= 10
ORDER BY l.category, l.rank_position;

CREATE OR REPLACE VIEW `view_active_auctions` AS
SELECT 
    a.id,
    a.item_name,
    a.quantity,
    a.current_bid,
    seller.name AS seller_name,
    bidder.name AS highest_bidder_name,
    a.end_time,
    TIMESTAMPDIFF(MINUTE, NOW(), a.end_time) AS minutes_remaining
FROM eden_auctions a
JOIN eden_players seller ON a.seller_id = seller.player_id
LEFT JOIN eden_players bidder ON a.highest_bidder = bidder.player_id
WHERE a.status = 'active' AND a.end_time > NOW()
ORDER BY a.end_time;

-- Insert sample achievement definitions
INSERT IGNORE INTO `eden_config` (`config_key`, `config_value`, `description`) VALUES
('achievement_definitions', '{
  "first_steps": {"name": "First Steps", "description": "Complete your first job", "reward": 500},
  "money_maker": {"name": "Money Maker", "description": "Earn $50,000", "reward": 1000},
  "social_butterfly": {"name": "Social Butterfly", "description": "Add 10 friends", "reward": 750},
  "law_enforcer": {"name": "Law Enforcer", "description": "Make 25 arrests", "reward": 1500},
  "life_saver": {"name": "Life Saver", "description": "Revive 50 players", "reward": 2000},
  "entrepreneur": {"name": "Entrepreneur", "description": "Own 3 businesses", "reward": 5000},
  "stock_trader": {"name": "Stock Trader", "description": "Make 100 stock trades", "reward": 3000},
  "house_collector": {"name": "House Collector", "description": "Own 5 houses", "reward": 4000}
}', 'Achievement definitions and rewards');

SELECT 'Default data population completed successfully!' AS Status;
