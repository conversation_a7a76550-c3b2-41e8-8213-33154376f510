/*
    File: fn_surrenderToPolice.sqf
    Author: EdenRP Development Team
    
    Description:
    Allows a player to surrender to police.
    
    Parameters:
    0: OBJECT - Player surrendering (optional, default: player)
    
    Returns:
    BOOLEAN - True if surrender was successful
*/

params [
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (isNull _player) exitWith { false };

// Check if player has wanted level
_wantedLevel = _player getVariable ["eden_wantedLevel", 0];
if (_wantedLevel <= 0) exitWith {
    ["You are not wanted by police!"] call EDEN_fnc_showHint;
    false
};

// Check if there are police nearby
_nearbyPolice = [];
{
    if (_x getVariable ["eden_isPolice", false] && _x distance _player < 100) then {
        _nearbyPolice pushBack _x;
    };
} forEach allPlayers;

if (count _nearbyPolice == 0) exitWith {
    ["No police officers nearby to surrender to!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_player, "legal_action", [_player, "surrender"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Start surrender process
[_player, "Acts_Executioner_Squat"] remoteExec ["switchMove"];
["Surrendering to police..."] call EDEN_fnc_showHint;

// Alert nearby police
{
    [
        "SUSPECT SURRENDER",
        format ["%1 is surrendering at %2!", name _player, mapGridPosition _player],
        15,
        "info"
    ] remoteExec ["EDEN_fnc_showNotification", _x];
} forEach _nearbyPolice;

sleep 3;

// Calculate reduced sentence for voluntary surrender
_criminalRecord = _player getVariable ["eden_criminalRecord", []];
_baseFine = _wantedLevel * 500;
_baseJailTime = _wantedLevel * 60; // 1 minute per wanted level

// Surrender reduces penalties by 50%
_surrenderDiscount = 0.5;
_finalFine = round(_baseFine * _surrenderDiscount);
_finalJailTime = round(_baseJailTime * _surrenderDiscount);

// Process surrender
_playerMoney = _player getVariable ["eden_cash", 0];
_playerBank = _player getVariable ["eden_bankAccount", 0];
_totalMoney = _playerMoney + _playerBank;

// Pay fine if possible
if (_totalMoney >= _finalFine) then {
    if (_playerMoney >= _finalFine) then {
        _player setVariable ["eden_cash", (_playerMoney - _finalFine), true];
    } else {
        _remainingFine = _finalFine - _playerMoney;
        _player setVariable ["eden_cash", 0, true];
        _player setVariable ["eden_bankAccount", (_playerBank - _remainingFine), true];
    };
    
    // No jail time if fine is paid
    _finalJailTime = 0;
    [format ["Fine paid: $%1. No jail time due to voluntary surrender.", _finalFine]] call EDEN_fnc_showHint;
} else {
    // Can't pay fine, must serve time
    [format ["Insufficient funds for fine. Jail time: %1 minutes.", round(_finalJailTime/60)]] call EDEN_fnc_showHint;
};

// Clear wanted level
_player setVariable ["eden_wantedLevel", 0, true];

// Add surrender to record
_surrenderRecord = _player getVariable ["eden_surrenderHistory", []];
_surrenderEntry = [
    time,
    _wantedLevel,
    _finalFine,
    _finalJailTime,
    "Voluntary surrender"
];
_surrenderRecord pushBack _surrenderEntry;
_player setVariable ["eden_surrenderHistory", _surrenderRecord, true];

// Process jail time if needed
if (_finalJailTime > 0) then {
    _player setVariable ["eden_isJailed", true, true];
    _player setVariable ["eden_jailTime", _finalJailTime, true];
    
    // Move to jail
    _jailPositions = [
        [3688.5, 13092.5, 0.1],
        [3690.5, 13092.5, 0.1],
        [3692.5, 13092.5, 0.1]
    ];
    _jailPos = selectRandom _jailPositions;
    _player setPosATL _jailPos;
    
    // Start jail timer
    [_player, _finalJailTime] spawn {
        params ["_jailedPlayer", "_time"];
        sleep _time;
        
        if (_jailedPlayer getVariable ["eden_isJailed", false]) then {
            _jailedPlayer setVariable ["eden_isJailed", false, true];
            _jailedPlayer setVariable ["eden_jailTime", 0, true];
            
            // Move to jail exit
            _jailedPlayer setPosATL [3695, 13095, 0.1];
            ["Your sentence is complete. You are free to go."] remoteExec ["EDEN_fnc_showHint", _jailedPlayer];
        };
    };
};

// Update statistics
_surrenders = _player getVariable ["eden_surrenderCount", 0];
_player setVariable ["eden_surrenderCount", (_surrenders + 1), true];

// Add experience for good behavior
_expGained = _wantedLevel * 10; // XP for surrendering
_currentExp = _player getVariable ["eden_experience", 0];
_player setVariable ["eden_experience", (_currentExp + _expGained), true];

// Government receives fine money
if (_finalFine > 0) then {
    _governmentFunds = missionNamespace getVariable ["eden_governmentFunds", 0];
    missionNamespace setVariable ["eden_governmentFunds", (_governmentFunds + _finalFine), true];
};

// Notify all police of surrender
{
    if (_x getVariable ["eden_isPolice", false]) then {
        [
            "Suspect Processed",
            format ["%1 surrendered voluntarily. Fine: $%2, Jail: %3min", name _player, _finalFine, round(_finalJailTime/60)],
            12,
            "info"
        ] remoteExec ["EDEN_fnc_showNotification", _x];
    };
} forEach allPlayers;

[_player, ""] remoteExec ["switchMove"];

// Final message
if (_finalJailTime > 0) then {
    [format ["Processed for surrender. Serving %1 minutes in jail. (+%2 XP)", round(_finalJailTime/60), _expGained]] call EDEN_fnc_showHint;
} else {
    [format ["Surrender processed. Fine paid, no jail time. (+%1 XP)", _expGained]] call EDEN_fnc_showHint;
};

// Log surrender
[format ["[EDEN] Player %1 surrendered to police - Fine: $%2, Jail: %3min", name _player, _finalFine, round(_finalJailTime/60)], "INFO", "LEGAL"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
