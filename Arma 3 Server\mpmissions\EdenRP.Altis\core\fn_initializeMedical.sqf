/*
    File: fn_initializeMedical.sqf
    Author: EdenRP Development Team
    
    Description:
    Initializes a player as a medical personnel with proper equipment, variables, and settings.
    
    Parameters:
    0: OBJECT - Player object
    1: STRING - Medical rank (optional, default: "Paramedic")
    
    Returns:
    BOOLEAN - True if initialization was successful
    
    Example:
    [player] call EDEN_fnc_initializeMedical;
    [player, "Doctor"] call EDEN_fnc_initializeMedical;
*/

params [
    ["_player", player, [obj<PERSON><PERSON>]],
    ["_rank", "Paramedic", [""]]
];

// Validate parameters
if (isNull _player) exitWith {
    ["[EDEN] fn_initializeMedical: Invalid player object"] call EDEN_fnc_systemLogger;
    false
};

// Set player role and faction
_player setVariable ["eden_playerRole", "medical", true];
_player setVariable ["eden_playerFaction", "medical", true];
_player setVariable ["eden_medicalRank", _rank, true];

// Set medical-specific flags
_player setVariable ["eden_isPolice", false, true];
_player setVariable ["eden_isMedic", true, true];
_player setVariable ["eden_isAdmin", false, true];
_player setVariable ["eden_onDuty", true, true];

// Initialize medical stats
_player setVariable ["eden_patientsRevived", 0, true];
_player setVariable ["eden_patientsTreated", 0, true];
_player setVariable ["eden_emergencyCalls", 0, true];
_player setVariable ["eden_serviceTime", 0, true];

// Initialize equipment access based on rank
_accessLevel = switch (toLower _rank) do {
    case "chief medical officer": { 4 };
    case "doctor": { 3 };
    case "nurse": { 2 };
    default { 1 }; // Paramedic
};
_player setVariable ["eden_medicalAccessLevel", _accessLevel, true];

// Remove all gear first
removeAllWeapons _player;
removeAllItems _player;
removeAllAssignedItems _player;
removeUniform _player;
removeVest _player;
removeBackpack _player;
removeHeadgear _player;
removeGoggles _player;

// Give medical uniform based on rank
switch (toLower _rank) do {
    case "chief medical officer": {
        _player forceAddUniform "U_C_Scientist";
        _player addVest "V_Safety_yellow_F";
        _player addHeadgear "H_Cap_headphones";
    };
    case "doctor": {
        _player forceAddUniform "U_C_Scientist";
        _player addVest "V_Safety_blue_F";
    };
    case "nurse": {
        _player forceAddUniform "U_C_WorkerCoveralls";
        _player addVest "V_Safety_blue_F";
    };
    default {
        _player forceAddUniform "U_C_WorkerCoveralls";
        _player addVest "V_Safety_orange_F";
    };
};

// Add basic equipment
_player addItem "ItemMap";
_player addItem "ItemCompass";
_player addItem "ItemWatch";
_player addItem "ItemRadio";
_player addItem "ItemGPS";

// Assign items
_player assignItem "ItemMap";
_player assignItem "ItemCompass";
_player assignItem "ItemWatch";
_player assignItem "ItemRadio";
_player assignItem "ItemGPS";

// Add medical equipment based on access level
if (_accessLevel >= 1) then {
    _player addItem "FirstAidKit";
    _player addItem "FirstAidKit";
    _player addItem "FirstAidKit";
    _player addItem "Medikit";
};

if (_accessLevel >= 2) then {
    _player addItem "Medikit";
    _player addItem "ToolKit";
};

if (_accessLevel >= 3) then {
    _player addItem "Medikit";
    _player addItem "Medikit";
};

// Initialize medical inventory
_player setVariable ["eden_medicalEquipment", [
    ["defibrillator", 1],
    ["morphine", 10],
    ["epinephrine", 5],
    ["bandage", 20],
    ["blood_bag", 5],
    ["stretcher", 1],
    ["oxygen_tank", 2]
], true];

// Set medical permissions
_player setVariable ["eden_canRevive", true, true];
_player setVariable ["eden_canTreat", true, true];
_player setVariable ["eden_canHeal", true, true];
_player setVariable ["eden_canTransport", true, true];

// Initialize emergency response variables
_player setVariable ["eden_currentPatient", objNull, true];
_player setVariable ["eden_emergencyCall", "", true];
_player setVariable ["eden_ambulanceAssigned", objNull, true];

// Set captive status (medical personnel are protected)
_player setCaptive true;

// Initialize medical actions
[_player] call EDEN_fnc_setupPlayerActions;

// Setup medical-specific event handlers
_player addEventHandler ["Killed", {
    params ["_unit", "_killer"];
    [_unit, _killer] call EDEN_fnc_onMedicalKilled;
}];

_player addEventHandler ["Respawn", {
    params ["_unit", "_corpse"];
    [_unit, (_unit getVariable ["eden_medicalRank", "Paramedic"])] call EDEN_fnc_initializeMedical;
}];

// Setup medical HUD
[] call EDEN_fnc_setupMedicalHUD;

// Create medical briefing
[_player, "medical"] call EDEN_fnc_briefingSystem;

// Add to medical group
_medicalGroup = createGroup independent;
[_player] joinSilent _medicalGroup;
_player setVariable ["eden_medicalGroup", _medicalGroup, true];

// Initialize medical protocols
_player setVariable ["eden_medicalProtocols", [
    "Always prioritize patient safety",
    "Maintain medical neutrality",
    "Document all treatments",
    "Follow triage procedures",
    "Coordinate with emergency services"
], true];

// Set medical clearance levels
_player setVariable ["eden_canPerformSurgery", (_accessLevel >= 3), true];
_player setVariable ["eden_canPrescribeMedication", (_accessLevel >= 2), true];
_player setVariable ["eden_canDeclareDeceased", (_accessLevel >= 3), true];

// Sync player data
[_player] call EDEN_fnc_savePlayerData;

// Welcome message
[
    format ["Welcome %1 %2!", _rank, name _player],
    "You are now on duty. Respond to medical emergencies and save lives!",
    5
] call EDEN_fnc_showNotification;

// Log successful initialization
[format ["[EDEN] Player %1 initialized as medical personnel (Rank: %2)", name _player, _rank]] call EDEN_fnc_systemLogger;

// Return success
true
