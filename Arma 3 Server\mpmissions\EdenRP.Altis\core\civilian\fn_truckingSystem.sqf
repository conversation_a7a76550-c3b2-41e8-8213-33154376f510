/*
    File: fn_truckingSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages trucking and delivery system for civilians.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_vehicle", objNull, [objNull]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_truckingLevel", 1, true];
        _player setVariable ["eden_deliveriesMade", 0, true];
        _player setVariable ["eden_currentDelivery", [], true];
        true
    };
    case "startDelivery": {
        if (!(_player getVariable ["eden_truckingLicense", false])) exitWith {
            ["You need a trucking license!"] call EDEN_fnc_showHint;
            false
        };
        
        if (isNull _vehicle || !(_player in _vehicle)) exitWith {
            ["You need to be in a truck!"] call EDEN_fnc_showHint;
            false
        };
        
        _currentDelivery = _player getVariable ["eden_currentDelivery", []];
        if (count _currentDelivery > 0) exitWith {
            ["You already have an active delivery!"] call EDEN_fnc_showHint;
            false
        };
        
        _deliveryTypes = [
            ["Food Supplies", 500, [3000, 4000, 5000]],
            ["Construction Materials", 750, [4000, 5000, 6000]],
            ["Electronics", 1000, [5000, 6000, 7000]],
            ["Medical Supplies", 1250, [6000, 7000, 8000]]
        ];
        
        _delivery = selectRandom _deliveryTypes;
        _name = _delivery select 0;
        _weight = _delivery select 1;
        _distances = _delivery select 2;
        _distance = selectRandom _distances;
        
        _level = _player getVariable ["eden_truckingLevel", 1];
        _basePay = 200 + (_distance / 10) + (_level * 50);
        _pay = _basePay + (random 100);
        
        _destinations = ["Kavala", "Athira", "Pyrgos", "Sofia", "Zaros"];
        _destination = selectRandom _destinations;
        
        _deliveryData = [_name, _weight, _distance, _pay, _destination, time];
        _player setVariable ["eden_currentDelivery", _deliveryData, true];
        
        [format ["Delivery started: %1 to %2 for $%3", _name, _destination, floor _pay]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "completeDelivery": {
        _currentDelivery = _player getVariable ["eden_currentDelivery", []];
        if (count _currentDelivery == 0) exitWith {
            ["No active delivery!"] call EDEN_fnc_showHint;
            false
        };
        
        _pay = _currentDelivery select 3;
        _startTime = _currentDelivery select 5;
        _timeBonus = 0;
        
        if ((time - _startTime) < 600) then { // Completed in under 10 minutes
            _timeBonus = _pay * 0.2;
            ["Time bonus earned!"] call EDEN_fnc_showHint;
        };
        
        _totalPay = _pay + _timeBonus;
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _totalPay), true];
        
        _deliveries = _player getVariable ["eden_deliveriesMade", 0];
        _player setVariable ["eden_deliveriesMade", (_deliveries + 1), true];
        
        if ((_deliveries % 5) == 0) then {
            _newLevel = _player getVariable ["eden_truckingLevel", 1];
            _player setVariable ["eden_truckingLevel", (_newLevel + 1), true];
            [format ["Trucking level increased to %1!", _newLevel + 1]] call EDEN_fnc_showHint;
        };
        
        _player setVariable ["eden_currentDelivery", [], true];
        
        [format ["Delivery completed! Earned $%1", floor _totalPay]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "cancelDelivery": {
        _currentDelivery = _player getVariable ["eden_currentDelivery", []];
        if (count _currentDelivery == 0) exitWith {
            ["No active delivery to cancel!"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_currentDelivery", [], true];
        ["Delivery cancelled."] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
