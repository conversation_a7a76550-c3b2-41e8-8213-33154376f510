/*
    File: fn_storageSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages storage and warehouse system.
*/

params [["_player", player, [objNull]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_storageUnits", [], true];
        _player setVariable ["eden_storageCapacity", 200, true];
        true
    };
    case "rentStorage": {
        params ["", "", ["_storageType", "small", [""]]];
        
        _storageCosts = [
            ["small", 100, 500],
            ["medium", 200, 1000],
            ["large", 500, 2500]
        ];
        
        _storageData = [];
        {
            if ((_x select 0) == _storageType) then { _storageData = _x; };
        } forEach _storageCosts;
        
        _capacity = _storageData select 1;
        _cost = _storageData select 2;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        _player setVariable ["eden_storageCapacity", _capacity, true];
        
        _units = _player getVariable ["eden_storageUnits", []];
        _units pushBack [_storageType, _capacity, time];
        _player setVariable ["eden_storageUnits", _units, true];
        
        [format ["Rented %1 storage unit for $%2", _storageType, _cost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "organizeStorage": {
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _sortedItems = [];
        
        // Sort by item type
        {
            _itemType = "misc";
            {
                if ((_x select 0) == ((_virtualItems select _forEachIndex) select 0)) then {
                    _itemType = _x select 1;
                };
            } forEach eden_itemDatabase;
            
            _sortedItems pushBack [(_x select 0), (_x select 1), _itemType];
        } forEach _virtualItems;
        
        _sortedItems sort true;
        
        _newVirtualItems = [];
        {
            _newVirtualItems pushBack [(_x select 0), (_x select 1)];
        } forEach _sortedItems;
        
        _player setVariable ["eden_virtualItems", _newVirtualItems, true];
        
        ["Storage organized by item type"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
