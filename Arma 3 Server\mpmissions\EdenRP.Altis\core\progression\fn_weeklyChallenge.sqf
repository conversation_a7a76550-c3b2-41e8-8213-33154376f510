/*
    File: fn_weeklyChallenge.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages weekly challenge system.
*/

params [["_action", "init", [""]]];

switch (_action) do {
    case "init": {
        if (isServer) then {
            if (isNil "eden_weeklyChallenges") then {
                eden_weeklyChallenges = [
                    ["community_goal", "Server earns $10,000,000 collectively", 10000000, 1000],
                    ["arrest_challenge", "Police make 100 arrests", 100, 800],
                    ["medical_saves", "EMS save 50 lives", 50, 600],
                    ["vehicle_repairs", "Mechanics repair 75 vehicles", 75, 700],
                    ["resource_gathering", "Gather 1000 resources", 1000, 500]
                ];
                publicVariable "eden_weeklyChallenges";
            };
            eden_currentWeeklyChallenge = [];
            eden_weeklyProgress = 0;
            publicVariable "eden_currentWeeklyChallenge";
            publicVariable "eden_weeklyProgress";
        };
        true
    };
    case "selectWeeklyChallenge": {
        if (!isServer) exitWith { false };
        
        _randomChallenge = selectRandom eden_weeklyChallenges;
        eden_currentWeeklyChallenge = _randomChallenge;
        eden_weeklyProgress = 0;
        publicVariable "eden_currentWeeklyChallenge";
        publicVariable "eden_weeklyProgress";
        
        {
            [format ["🏆 NEW WEEKLY CHALLENGE: %1", (_randomChallenge select 1)]] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach allPlayers;
        
        true
    };
    case "updateProgress": {
        params ["", ["_amount", 1, [0]]];
        
        if (!isServer) exitWith { false };
        if (count eden_currentWeeklyChallenge == 0) exitWith { false };
        
        eden_weeklyProgress = eden_weeklyProgress + _amount;
        publicVariable "eden_weeklyProgress";
        
        _target = eden_currentWeeklyChallenge select 2;
        _percentage = floor((eden_weeklyProgress / _target) * 100);
        
        if (eden_weeklyProgress >= _target) then {
            ["completeWeeklyChallenge"] call EDEN_fnc_weeklyChallenge;
        } else {
            if (_percentage % 25 == 0 && _percentage > 0) then {
                {
                    [format ["Weekly Challenge Progress: %1%% complete!", _percentage]] remoteExec ["EDEN_fnc_showHint", _x];
                } forEach allPlayers;
            };
        };
        
        true
    };
    case "completeWeeklyChallenge": {
        if (!isServer) exitWith { false };
        if (count eden_currentWeeklyChallenge == 0) exitWith { false };
        
        _reward = eden_currentWeeklyChallenge select 3;
        
        {
            [_x, "addExperience", _reward, "Weekly Challenge"] call EDEN_fnc_experienceSystem;
            [format ["🎉 WEEKLY CHALLENGE COMPLETED! Everyone receives %1 XP!", _reward]] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach allPlayers;
        
        eden_currentWeeklyChallenge = [];
        eden_weeklyProgress = 0;
        publicVariable "eden_currentWeeklyChallenge";
        publicVariable "eden_weeklyProgress";
        
        true
    };
    case "getProgress": {
        if (count eden_currentWeeklyChallenge == 0) exitWith { [[], 0, 0] };
        
        _target = eden_currentWeeklyChallenge select 2;
        [eden_currentWeeklyChallenge, eden_weeklyProgress, _target]
    };
    case "showProgress": {
        params ["", ["_player", player, [objNull]]];
        
        if (count eden_currentWeeklyChallenge == 0) exitWith {
            ["No weekly challenge active"] call EDEN_fnc_showHint;
            false
        };
        
        _challenge = eden_currentWeeklyChallenge select 1;
        _target = eden_currentWeeklyChallenge select 2;
        _percentage = floor((eden_weeklyProgress / _target) * 100);
        
        _progressText = format["=== WEEKLY CHALLENGE ===\n%1\nProgress: %2/%3 (%4%%)", 
            _challenge, eden_weeklyProgress, _target, _percentage];
        
        if (!isNull _player) then {
            [_progressText] remoteExec ["EDEN_fnc_showHint", _player];
        } else {
            [_progressText] call EDEN_fnc_showHint;
        };
        
        true
    };
    default { false };
};
