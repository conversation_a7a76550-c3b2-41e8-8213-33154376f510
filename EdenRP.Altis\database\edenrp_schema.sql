-- --------------------------------------------------------
-- EdenRP Enhanced Database Schema
-- Enhanced Altis Life framework database structure
-- Version: 1.0.0
-- Date: 2025-01-29
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8mb4 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;

-- Create database
CREATE DATABASE IF NOT EXISTS `edenrp` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci */;
USE `edenrp`;

-- Enhanced Players Table with additional features
CREATE TABLE IF NOT EXISTS `eden_players` (
  `uid` int(12) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `name` varchar(64) NOT NULL,
  `cash` bigint(20) NOT NULL DEFAULT 5000,
  `bank` bigint(20) NOT NULL DEFAULT 15000,
  `experience` bigint(20) NOT NULL DEFAULT 0,
  `level` int(11) NOT NULL DEFAULT 1,
  `reputation` int(11) NOT NULL DEFAULT 0,
  `playtime` int(11) NOT NULL DEFAULT 0,
  `last_seen` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `first_join` timestamp NOT NULL DEFAULT current_timestamp(),
  `total_logins` int(11) NOT NULL DEFAULT 0,
  `cop_level` tinyint(4) NOT NULL DEFAULT 0,
  `medic_level` tinyint(4) NOT NULL DEFAULT 0,
  `admin_level` tinyint(4) NOT NULL DEFAULT 0,
  `donator_level` tinyint(4) NOT NULL DEFAULT 0,
  `cop_licenses` text DEFAULT NULL,
  `civ_licenses` text DEFAULT NULL,
  `med_licenses` text DEFAULT NULL,
  `cop_gear` text DEFAULT NULL,
  `med_gear` text DEFAULT NULL,
  `civ_gear` text DEFAULT NULL,
  `virtual_inventory` text DEFAULT NULL,
  `physical_inventory` text DEFAULT NULL,
  `skills` text DEFAULT NULL,
  `achievements` text DEFAULT NULL,
  `settings` text DEFAULT NULL,
  `arrested` tinyint(1) NOT NULL DEFAULT 0,
  `jail_time` int(11) NOT NULL DEFAULT 0,
  `wanted_level` tinyint(4) NOT NULL DEFAULT 0,
  `bounty` int(11) NOT NULL DEFAULT 0,
  `gang_id` int(11) DEFAULT NULL,
  `gang_rank` tinyint(4) NOT NULL DEFAULT 0,
  `phone_number` varchar(20) DEFAULT NULL,
  `contacts` text DEFAULT NULL,
  `messages` text DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `banned` tinyint(1) NOT NULL DEFAULT 0,
  `ban_reason` text DEFAULT NULL,
  `ban_expires` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`uid`),
  UNIQUE KEY `player_id` (`player_id`),
  KEY `name` (`name`),
  KEY `gang_id` (`gang_id`),
  KEY `last_seen` (`last_seen`),
  KEY `active` (`active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced Gangs Table with additional features
CREATE TABLE IF NOT EXISTS `eden_gangs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) NOT NULL,
  `tag` varchar(8) DEFAULT NULL,
  `leader_id` varchar(50) NOT NULL,
  `bank` bigint(20) NOT NULL DEFAULT 0,
  `experience` bigint(20) NOT NULL DEFAULT 0,
  `level` int(11) NOT NULL DEFAULT 1,
  `reputation` int(11) NOT NULL DEFAULT 0,
  `max_members` int(11) NOT NULL DEFAULT 12,
  `territory_count` int(11) NOT NULL DEFAULT 0,
  `kills` int(11) NOT NULL DEFAULT 0,
  `deaths` int(11) NOT NULL DEFAULT 0,
  `created` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_active` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `color` varchar(7) DEFAULT '#FFFFFF',
  `description` text DEFAULT NULL,
  `rules` text DEFAULT NULL,
  `perks` text DEFAULT NULL,
  `allies` text DEFAULT NULL,
  `enemies` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `tag` (`tag`),
  KEY `leader_id` (`leader_id`),
  KEY `active` (`active`),
  KEY `last_active` (`last_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced Gang Members Table
CREATE TABLE IF NOT EXISTS `eden_gang_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `gang_id` int(11) NOT NULL,
  `player_id` varchar(50) NOT NULL,
  `rank` tinyint(4) NOT NULL DEFAULT 0,
  `joined` timestamp NOT NULL DEFAULT current_timestamp(),
  `contribution` bigint(20) NOT NULL DEFAULT 0,
  `permissions` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `gang_player` (`gang_id`, `player_id`),
  KEY `player_id` (`player_id`),
  CONSTRAINT `fk_gang_members_gang` FOREIGN KEY (`gang_id`) REFERENCES `eden_gangs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced Vehicles Table with additional features
CREATE TABLE IF NOT EXISTS `eden_vehicles` (
  `id` int(12) NOT NULL AUTO_INCREMENT,
  `owner_id` varchar(50) NOT NULL,
  `classname` varchar(64) NOT NULL,
  `type` varchar(32) NOT NULL,
  `side` varchar(16) NOT NULL,
  `plate` varchar(16) DEFAULT NULL,
  `color` varchar(64) DEFAULT NULL,
  `inventory` text DEFAULT NULL,
  `fuel` float NOT NULL DEFAULT 1,
  `damage` text DEFAULT NULL,
  `position` varchar(128) DEFAULT NULL,
  `direction` float NOT NULL DEFAULT 0,
  `locked` tinyint(1) NOT NULL DEFAULT 1,
  `alive` tinyint(1) NOT NULL DEFAULT 1,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `impounded` tinyint(1) NOT NULL DEFAULT 0,
  `impound_fee` int(11) NOT NULL DEFAULT 0,
  `insurance` tinyint(1) NOT NULL DEFAULT 0,
  `insurance_expires` timestamp NULL DEFAULT NULL,
  `mileage` float NOT NULL DEFAULT 0,
  `last_used` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `purchased` timestamp NOT NULL DEFAULT current_timestamp(),
  `gang_id` int(11) DEFAULT NULL,
  `custom_name` varchar(64) DEFAULT NULL,
  `modifications` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `owner_id` (`owner_id`),
  KEY `classname` (`classname`),
  KEY `alive` (`alive`),
  KEY `gang_id` (`gang_id`),
  KEY `last_used` (`last_used`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced Houses Table with additional features
CREATE TABLE IF NOT EXISTS `eden_houses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `owner_id` varchar(50) DEFAULT NULL,
  `position` varchar(128) NOT NULL,
  `price` int(11) NOT NULL,
  `inventory` text DEFAULT NULL,
  `locked` tinyint(1) NOT NULL DEFAULT 1,
  `alarm` tinyint(1) NOT NULL DEFAULT 0,
  `security_level` tinyint(4) NOT NULL DEFAULT 0,
  `garage` tinyint(1) NOT NULL DEFAULT 0,
  `garage_size` tinyint(4) NOT NULL DEFAULT 0,
  `last_payment` timestamp NOT NULL DEFAULT current_timestamp(),
  `next_payment` timestamp NOT NULL DEFAULT (current_timestamp() + INTERVAL 30 DAY),
  `purchased` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_accessed` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `for_sale` tinyint(1) NOT NULL DEFAULT 0,
  `sale_price` int(11) DEFAULT NULL,
  `house_type` varchar(32) DEFAULT 'small',
  `decorations` text DEFAULT NULL,
  `utilities` text DEFAULT NULL,
  `insurance` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `owner_id` (`owner_id`),
  KEY `active` (`active`),
  KEY `for_sale` (`for_sale`),
  KEY `last_accessed` (`last_accessed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced Market System
CREATE TABLE IF NOT EXISTS `eden_market` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item` varchar(64) NOT NULL,
  `buy_price` int(11) NOT NULL DEFAULT 0,
  `sell_price` int(11) NOT NULL DEFAULT 0,
  `stock` int(11) NOT NULL DEFAULT 1000,
  `demand` float NOT NULL DEFAULT 1.0,
  `last_update` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `price_history` text DEFAULT NULL,
  `category` varchar(32) DEFAULT 'general',
  `location` varchar(64) DEFAULT 'global',
  PRIMARY KEY (`id`),
  UNIQUE KEY `item_location` (`item`, `location`),
  KEY `category` (`category`),
  KEY `last_update` (`last_update`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced Logging System
CREATE TABLE IF NOT EXISTS `eden_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `level` varchar(16) NOT NULL,
  `category` varchar(32) NOT NULL,
  `message` text NOT NULL,
  `player_id` varchar(50) DEFAULT NULL,
  `server_time` float DEFAULT NULL,
  `additional_data` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `timestamp` (`timestamp`),
  KEY `level` (`level`),
  KEY `category` (`category`),
  KEY `player_id` (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced Admin Actions Log
CREATE TABLE IF NOT EXISTS `eden_admin_actions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `admin_id` varchar(50) NOT NULL,
  `target_id` varchar(50) DEFAULT NULL,
  `action` varchar(64) NOT NULL,
  `reason` text DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `additional_data` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `admin_id` (`admin_id`),
  KEY `target_id` (`target_id`),
  KEY `timestamp` (`timestamp`),
  KEY `action` (`action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced Economy Transactions
CREATE TABLE IF NOT EXISTS `eden_transactions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `type` varchar(32) NOT NULL,
  `amount` bigint(20) NOT NULL,
  `balance_before` bigint(20) NOT NULL,
  `balance_after` bigint(20) NOT NULL,
  `description` text DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `location` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `type` (`type`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced Job System
CREATE TABLE IF NOT EXISTS `eden_jobs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `job_type` varchar(32) NOT NULL,
  `start_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `end_time` timestamp NULL DEFAULT NULL,
  `status` varchar(16) NOT NULL DEFAULT 'active',
  `progress` float NOT NULL DEFAULT 0,
  `payout` int(11) NOT NULL DEFAULT 0,
  `experience_gained` int(11) NOT NULL DEFAULT 0,
  `location` varchar(64) DEFAULT NULL,
  `additional_data` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `job_type` (`job_type`),
  KEY `status` (`status`),
  KEY `start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced Police System
CREATE TABLE IF NOT EXISTS `eden_arrests` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `officer_id` varchar(50) NOT NULL,
  `suspect_id` varchar(50) NOT NULL,
  `charges` text NOT NULL,
  `fine_amount` int(11) NOT NULL DEFAULT 0,
  `jail_time` int(11) NOT NULL DEFAULT 0,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `location` varchar(64) DEFAULT NULL,
  `evidence` text DEFAULT NULL,
  `status` varchar(16) NOT NULL DEFAULT 'active',
  PRIMARY KEY (`id`),
  KEY `officer_id` (`officer_id`),
  KEY `suspect_id` (`suspect_id`),
  KEY `timestamp` (`timestamp`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `eden_tickets` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `officer_id` varchar(50) NOT NULL,
  `player_id` varchar(50) NOT NULL,
  `violation` varchar(128) NOT NULL,
  `fine_amount` int(11) NOT NULL,
  `paid` tinyint(1) NOT NULL DEFAULT 0,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `location` varchar(64) DEFAULT NULL,
  `due_date` timestamp NOT NULL DEFAULT (current_timestamp() + INTERVAL 7 DAY),
  PRIMARY KEY (`id`),
  KEY `officer_id` (`officer_id`),
  KEY `player_id` (`player_id`),
  KEY `paid` (`paid`),
  KEY `due_date` (`due_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `eden_wanted` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `crimes` text NOT NULL,
  `bounty` int(11) NOT NULL DEFAULT 0,
  `wanted_level` tinyint(4) NOT NULL DEFAULT 1,
  `issued_by` varchar(50) NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `player_id` (`player_id`),
  KEY `active` (`active`),
  KEY `wanted_level` (`wanted_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced Medical System
CREATE TABLE IF NOT EXISTS `eden_medical_records` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `patient_id` varchar(50) NOT NULL,
  `medic_id` varchar(50) DEFAULT NULL,
  `treatment_type` varchar(64) NOT NULL,
  `injury_description` text DEFAULT NULL,
  `treatment_description` text DEFAULT NULL,
  `cost` int(11) NOT NULL DEFAULT 0,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `location` varchar(64) DEFAULT NULL,
  `outcome` varchar(32) DEFAULT 'successful',
  PRIMARY KEY (`id`),
  KEY `patient_id` (`patient_id`),
  KEY `medic_id` (`medic_id`),
  KEY `timestamp` (`timestamp`),
  KEY `treatment_type` (`treatment_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced Communication System
CREATE TABLE IF NOT EXISTS `eden_messages` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `sender_id` varchar(50) NOT NULL,
  `recipient_id` varchar(50) NOT NULL,
  `message` text NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `read_status` tinyint(1) NOT NULL DEFAULT 0,
  `message_type` varchar(16) NOT NULL DEFAULT 'text',
  `encrypted` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `sender_id` (`sender_id`),
  KEY `recipient_id` (`recipient_id`),
  KEY `timestamp` (`timestamp`),
  KEY `read_status` (`read_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced Gang Territories
CREATE TABLE IF NOT EXISTS `eden_gang_territories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `gang_id` int(11) NOT NULL,
  `name` varchar(64) NOT NULL,
  `position` varchar(128) NOT NULL,
  `radius` float NOT NULL DEFAULT 100,
  `capture_time` int(11) NOT NULL DEFAULT 300,
  `payout_rate` int(11) NOT NULL DEFAULT 1000,
  `last_captured` timestamp NOT NULL DEFAULT current_timestamp(),
  `capture_count` int(11) NOT NULL DEFAULT 0,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `contested` tinyint(1) NOT NULL DEFAULT 0,
  `contested_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `gang_id` (`gang_id`),
  KEY `active` (`active`),
  KEY `contested` (`contested`),
  CONSTRAINT `fk_territory_gang` FOREIGN KEY (`gang_id`) REFERENCES `eden_gangs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced Server Configuration
CREATE TABLE IF NOT EXISTS `eden_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(64) NOT NULL,
  `config_value` text NOT NULL,
  `description` text DEFAULT NULL,
  `last_modified` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `modified_by` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default configuration values
INSERT INTO `eden_config` (`config_key`, `config_value`, `description`) VALUES
('economy_multiplier', '1.0', 'Global economy multiplier'),
('xp_multiplier', '1.0', 'Global XP gain multiplier'),
('max_gang_members', '12', 'Maximum members per gang'),
('house_cleanup_days', '30', 'Days before inactive houses are cleaned'),
('vehicle_cleanup_days', '7', 'Days before inactive vehicles are cleaned'),
('server_name', 'EdenRP - Enhanced Altis Life', 'Server display name'),
('max_players', '120', 'Maximum concurrent players'),
('restart_interval', '240', 'Server restart interval in minutes'),
('backup_interval', '60', 'Database backup interval in minutes');

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
