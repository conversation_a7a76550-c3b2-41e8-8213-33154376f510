/*
    File: main_menu.hpp
    Author: EdenRP Development Team
    
    Description:
    Main menu dialog definitions for EdenRP
*/

class EdenRP_MainMenu {
    idd = 2800;
    name = "EdenRP_MainMenu";
    movingEnable = 0;
    enableSimulation = 1;
    
    class controlsBackground {
        class Background: RscText {
            idc = -1;
            x = 0;
            y = 0;
            w = 1;
            h = 1;
            colorBackground[] = {0, 0, 0, 0.8};
        };
        
        class Title: RscText {
            idc = -1;
            text = "EdenRP - Enhanced Altis Life";
            x = 0.3;
            y = 0.2;
            w = 0.4;
            h = 0.1;
            colorText[] = {1, 1, 1, 1};
            sizeEx = 0.04;
        };
    };
    
    class controls {
        class CivilianButton: RscButton {
            idc = 2801;
            text = "Civilian";
            x = 0.4;
            y = 0.4;
            w = 0.2;
            h = 0.05;
            action = "[] call EDEN_fnc_selectCivilian; closeDialog 0;";
        };
        
        class PoliceButton: RscButton {
            idc = 2802;
            text = "Police";
            x = 0.4;
            y = 0.5;
            w = 0.2;
            h = 0.05;
            action = "[] call EDEN_fnc_selectPolice; closeDialog 0;";
        };
        
        class MedicButton: RscButton {
            idc = 2803;
            text = "Medic";
            x = 0.4;
            y = 0.6;
            w = 0.2;
            h = 0.05;
            action = "[] call EDEN_fnc_selectMedic; closeDialog 0;";
        };
    };
};
