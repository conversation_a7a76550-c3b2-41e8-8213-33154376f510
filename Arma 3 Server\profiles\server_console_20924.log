﻿ 7:25:27 BattlEye Server: Initialized (v1.220, Arma 3 2.20.152984)
 7:25:27 Game Port: 2302, Steam Query Port: 2303
 7:25:27 Warning: Current Steam AppId: 233780 doesn't match expected value: 107410
 7:25:27 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:25:27 Host identity created.
 7:25:27 Roles assigned.
 7:25:27 Reading mission ...
 7:25:32 Script core\fn_briefingSystem.sqf not found
 7:25:32 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:25:32 Roles assigned.
 7:25:33 Reading mission ...
 7:25:33 Script core\fn_briefingSystem.sqf not found
 7:25:33 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:25:33 Roles assigned.
 7:25:33 Reading mission ...
 7:25:33 Script core\fn_briefingSystem.sqf not found
 7:25:34 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:25:34 Roles assigned.
 7:25:34 Reading mission ...
 7:25:34 Script core\fn_briefingSystem.sqf not found
 7:25:34 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:25:34 Roles assigned.
 7:25:34 Reading mission ...
 7:25:35 Script core\fn_briefingSystem.sqf not found
 7:25:35 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:25:35 Roles assigned.
 7:25:35 Reading mission ...
 7:25:35 Script core\fn_briefingSystem.sqf not found
 7:25:36 Mission EdenRP.Altis read from directory.
 7:25:36 Roles assigned.
 7:25:36 Reading mission ...
 7:25:36 Script core\fn_briefingSystem.sqf not found
 7:25:36 Mission EdenRP.Altis read from directory.
 7:25:36 Roles assigned.
 7:25:36 Reading mission ...
 7:25:37 Script core\fn_briefingSystem.sqf not found
 7:25:37 Mission EdenRP.Altis read from directory.
 7:25:37 Roles assigned.
 7:25:37 Reading mission ...
 7:25:37 Script core\fn_briefingSystem.sqf not found
 7:25:38 Mission EdenRP.Altis read from directory.
 7:25:38 Roles assigned.
 7:25:38 Reading mission ...
 7:25:38 Script core\fn_briefingSystem.sqf not found
 7:25:38 Mission EdenRP.Altis read from directory.
 7:25:38 Roles assigned.
 7:25:39 Reading mission ...
 7:25:39 Script core\fn_briefingSystem.sqf not found
 7:25:39 Mission EdenRP.Altis read from directory.
 7:25:39 Roles assigned.
 7:25:39 Reading mission ...
 7:25:39 Script core\fn_briefingSystem.sqf not found
 7:25:40 Mission EdenRP.Altis read from directory.
 7:25:40 Roles assigned.
 7:25:40 Reading mission ...
 7:25:40 Script core\fn_briefingSystem.sqf not found
 7:25:40 Mission EdenRP.Altis read from directory.
 7:25:41 Roles assigned.
 7:25:41 Reading mission ...
 7:25:41 Script core\fn_briefingSystem.sqf not found
 7:25:41 Mission EdenRP.Altis read from directory.
 7:25:41 Roles assigned.
 7:25:41 Reading mission ...
 7:25:42 Script core\fn_briefingSystem.sqf not found
 7:25:42 Mission EdenRP.Altis read from directory.
 7:25:42 Roles assigned.
 7:25:42 Reading mission ...
 7:25:42 Script core\fn_briefingSystem.sqf not found
 7:25:42 Mission EdenRP.Altis read from directory.
 7:25:43 Roles assigned.
 7:25:43 Reading mission ...
 7:25:43 Script core\fn_briefingSystem.sqf not found
 7:25:43 Mission EdenRP.Altis read from directory.
 7:25:43 Roles assigned.
 7:25:43 Reading mission ...
 7:25:44 Script core\fn_briefingSystem.sqf not found
 7:25:44 Mission EdenRP.Altis read from directory.
 7:25:44 Roles assigned.
 7:25:44 Reading mission ...
 7:25:44 Script core\fn_briefingSystem.sqf not found
 7:25:44 Mission EdenRP.Altis read from directory.
 7:25:45 Roles assigned.
 7:25:45 Reading mission ...
 7:25:45 Script core\fn_briefingSystem.sqf not found
 7:25:45 Mission EdenRP.Altis read from directory.
 7:25:45 Roles assigned.
 7:25:45 Reading mission ...
 7:25:46 Script core\fn_briefingSystem.sqf not found
 7:25:46 Mission EdenRP.Altis read from directory.
 7:25:46 Roles assigned.
 7:25:46 Reading mission ...
 7:25:46 Script core\fn_briefingSystem.sqf not found
 7:25:47 Mission EdenRP.Altis read from directory.
 7:25:47 Roles assigned.
 7:25:47 Reading mission ...
 7:25:47 Script core\fn_briefingSystem.sqf not found
 7:25:47 Mission EdenRP.Altis read from directory.
 7:25:47 Roles assigned.
 7:25:47 Reading mission ...
 7:25:48 Script core\fn_briefingSystem.sqf not found
 7:25:48 Mission EdenRP.Altis read from directory.
 7:25:48 Roles assigned.
 7:25:48 Reading mission ...
 7:25:48 Script core\fn_briefingSystem.sqf not found
 7:25:49 Mission EdenRP.Altis read from directory.
 7:25:49 Roles assigned.
 7:25:49 Reading mission ...
 7:25:49 Script core\fn_briefingSystem.sqf not found
 7:25:49 Mission EdenRP.Altis read from directory.
 7:25:49 Roles assigned.
 7:25:49 Reading mission ...
 7:25:50 Script core\fn_briefingSystem.sqf not found
 7:25:50 Mission EdenRP.Altis read from directory.
 7:25:50 Roles assigned.
 7:25:50 Reading mission ...
 7:25:50 Script core\fn_briefingSystem.sqf not found
 7:25:51 Mission EdenRP.Altis read from directory.
 7:25:51 Roles assigned.
 7:25:51 Reading mission ...
 7:25:51 Script core\fn_briefingSystem.sqf not found
 7:25:51 Mission EdenRP.Altis read from directory.
 7:25:52 Roles assigned.
 7:25:52 Reading mission ...
 7:25:52 Script core\fn_briefingSystem.sqf not found
 7:25:52 Mission EdenRP.Altis read from directory.
 7:25:52 Roles assigned.
 7:25:52 Reading mission ...
 7:25:53 Script core\fn_briefingSystem.sqf not found
 7:25:53 Mission EdenRP.Altis read from directory.
 7:25:53 Roles assigned.
 7:25:53 Reading mission ...
 7:25:54 Script core\fn_briefingSystem.sqf not found
 7:25:54 Mission EdenRP.Altis read from directory.
 7:25:54 Roles assigned.
 7:25:54 Reading mission ...
 7:25:54 Script core\fn_briefingSystem.sqf not found
