/*
    File: fn_restrainPlayer.sqf
    Author: EdenRP Development Team
    
    Description:
    Restrains or unrestrains a player with handcuffs.
    
    Parameters:
    0: OBJECT - Target player to restrain/unrestrain
    1: OBJECT - Officer performing action (optional, default: player)
    
    Returns:
    BOOLEAN - True if action was successful
    
    Example:
    [cursorTarget] call EDEN_fnc_restrainPlayer;
    [_target, player] call EDEN_fnc_restrainPlayer;
*/

params [
    ["_target", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_officer", player, [objN<PERSON>]]
];

// Validate parameters
if (isNull _target || isNull _officer) exitWith {
    ["[EDEN] fn_restrainPlayer: Invalid target or officer"] call EDEN_fnc_systemLogger;
    false
};

// Check if officer is police
if (!(_officer getVariable ["eden_isPolice", false])) exitWith {
    ["Only police officers can restrain players!"] call EDEN_fnc_showHint;
    false
};

// Check distance
if (_officer distance _target > 3) exitWith {
    ["Target is too far away!"] call EDEN_fnc_showHint;
    false
};

// Check if target is alive
if (!alive _target) exitWith {
    ["Cannot restrain unconscious players!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_officer, "player_action", [_target, "restrain"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Get current restraint status
_isRestrained = _target getVariable ["eden_isRestrained", false];

if (_isRestrained) then {
    // UNRESTRAIN PLAYER
    _target setVariable ["eden_isRestrained", false, true];
    _target setVariable ["eden_restrainedBy", "", true];
    _target setVariable ["eden_restrainTime", 0, true];
    
    // Remove handcuff animation
    [_target, ""] remoteExec ["switchMove"];
    
    // Re-enable player actions
    _target enableSimulation true;
    
    // Notifications
    [
        "Player Unrestrained",
        format ["%1 has been unrestrained", name _target],
        3,
        "success"
    ] remoteExec ["EDEN_fnc_showNotification", _officer];
    
    [
        "Restraints Removed",
        format ["Officer %1 has removed your restraints", name _officer],
        3,
        "success"
    ] remoteExec ["EDEN_fnc_showNotification", _target];
    
    // Log the action
    [format ["[EDEN] Officer %1 unrestrained %2", name _officer, name _target], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
    
} else {
    // RESTRAIN PLAYER
    _target setVariable ["eden_isRestrained", true, true];
    _target setVariable ["eden_restrainedBy", name _officer, true];
    _target setVariable ["eden_restrainTime", time, true];
    
    // Add handcuff animation
    [_target, "Acts_AidlPsitMstpSsurWnonDnon_loop"] remoteExec ["switchMove"];
    
    // Disable some player actions (but keep simulation for movement)
    _target enableSimulation true;
    
    // Remove weapons from restrained player
    {
        _target removeWeapon _x;
    } forEach weapons _target;
    
    // Prevent weapon pickup
    _target addEventHandler ["Take", {
        params ["_unit", "_container", "_item"];
        if (_unit getVariable ["eden_isRestrained", false]) then {
            ["You cannot pick up items while restrained!"] call EDEN_fnc_showHint;
            false
        } else {
            true
        };
    }];
    
    // Notifications
    [
        "Player Restrained",
        format ["%1 has been restrained", name _target],
        3,
        "success"
    ] remoteExec ["EDEN_fnc_showNotification", _officer];
    
    [
        "You Have Been Restrained",
        format ["Officer %1 has restrained you with handcuffs", name _officer],
        5,
        "warning"
    ] remoteExec ["EDEN_fnc_showNotification", _target];
    
    // Auto-release after 30 minutes if not arrested
    [_target] spawn {
        params ["_prisoner"];
        sleep 1800; // 30 minutes
        
        if (_prisoner getVariable ["eden_isRestrained", false] && !(_prisoner getVariable ["eden_isArrested", false])) then {
            _prisoner setVariable ["eden_isRestrained", false, true];
            _prisoner setVariable ["eden_restrainedBy", "", true];
            _prisoner setVariable ["eden_restrainTime", 0, true];
            
            [_prisoner, ""] remoteExec ["switchMove"];
            _prisoner enableSimulation true;
            
            [
                "Restraints Removed",
                "Your restraints have been automatically removed after 30 minutes",
                5,
                "info"
            ] remoteExec ["EDEN_fnc_showNotification", _prisoner];
            
            [format ["[EDEN] Auto-released restraints for %1 after 30 minutes", name _prisoner], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
        };
    };
    
    // Log the action
    [format ["[EDEN] Officer %1 restrained %2", name _officer, name _target], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
};

// Update police statistics
if (_isRestrained) then {
    _unrestrains = _officer getVariable ["eden_unrestrains", 0];
    _officer setVariable ["eden_unrestrains", (_unrestrains + 1), true];
} else {
    _restrains = _officer getVariable ["eden_restrains", 0];
    _officer setVariable ["eden_restrains", (_restrains + 1), true];
};

// Save player data
[_target] call EDEN_fnc_savePlayerData;
[_officer] call EDEN_fnc_savePlayerData;

// Return success
true
