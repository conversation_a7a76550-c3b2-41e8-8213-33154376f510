/*
    EdenRP UI Elements
    Enhanced HUD and interface elements
*/

class EDEN_HUD {
    idd = 2400;
    duration = 999999;
    fadeIn = 0;
    fadeOut = 0;
    name = "EDEN_HUD";
    
    class controls {
        class MoneyDisplay: EDEN_RscText {
            idc = 2401;
            text = "$0";
            x = 0.85;
            y = 0.02;
            w = 0.14;
            h = 0.04;
            colorText[] = {0, 1, 0, 1};
            font = "RobotoCondensedBold";
            sizeEx = 0.035;
            style = 2; // Right aligned
        };
        
        class LevelDisplay: EDEN_RscText {
            idc = 2402;
            text = "Level 1";
            x = 0.85;
            y = 0.06;
            w = 0.14;
            h = 0.04;
            colorText[] = {1, 1, 0, 1};
            font = "RobotoCondensed";
            sizeEx = 0.03;
            style = 2; // Right aligned
        };
        
        class XPBar: RscText {
            idc = 2403;
            x = 0.85;
            y = 0.10;
            w = 0.14;
            h = 0.01;
            colorFrame[] = {0, 0, 0, 0.5};
            colorBar[] = {0, 0.8, 1, 0.8};
        };
        
        class HealthBar: RscText {
            idc = 2404;
            x = 0.02;
            y = 0.88;
            w = 0.15;
            h = 0.02;
            colorFrame[] = {0, 0, 0, 0.5};
            colorBar[] = {1, 0, 0, 0.8};
        };
        
        class FoodBar: RscText {
            idc = 2405;
            x = 0.02;
            y = 0.91;
            w = 0.15;
            h = 0.02;
            colorFrame[] = {0, 0, 0, 0.5};
            colorBar[] = {1, 0.5, 0, 0.8};
        };
        
        class WaterBar: RscText {
            idc = 2406;
            x = 0.02;
            y = 0.94;
            w = 0.15;
            h = 0.02;
            colorFrame[] = {0, 0, 0, 0.5};
            colorBar[] = {0, 0.5, 1, 0.8};
        };
        
        class WeightDisplay: EDEN_RscText {
            idc = 2407;
            text = "0/50 kg";
            x = 0.02;
            y = 0.84;
            w = 0.15;
            h = 0.03;
            colorText[] = {1, 1, 1, 0.8};
            font = "RobotoCondensed";
            sizeEx = 0.025;
        };
        
        class JobDisplay: EDEN_RscText {
            idc = 2408;
            text = "Unemployed";
            x = 0.02;
            y = 0.02;
            w = 0.2;
            h = 0.04;
            colorText[] = {0.8, 0.8, 0.8, 1};
            font = "RobotoCondensed";
            sizeEx = 0.03;
        };
        
        class GangDisplay: EDEN_RscText {
            idc = 2409;
            text = "";
            x = 0.02;
            y = 0.06;
            w = 0.2;
            h = 0.04;
            colorText[] = {1, 0.5, 0, 1};
            font = "RobotoCondensedBold";
            sizeEx = 0.03;
        };
        
        class WantedDisplay: EDEN_RscText {
            idc = 2410;
            text = "";
            x = 0.4;
            y = 0.02;
            w = 0.2;
            h = 0.04;
            colorText[] = {1, 0, 0, 1};
            font = "RobotoCondensedBold";
            sizeEx = 0.035;
            style = 2; // Center aligned
        };
        
        class SpeedDisplay: EDEN_RscText {
            idc = 2411;
            text = "0 km/h";
            x = 0.85;
            y = 0.85;
            w = 0.14;
            h = 0.04;
            colorText[] = {1, 1, 1, 0.8};
            font = "RobotoCondensed";
            sizeEx = 0.03;
            style = 2; // Right aligned
        };
        
        class FuelDisplay: EDEN_RscText {
            idc = 2412;
            text = "";
            x = 0.85;
            y = 0.89;
            w = 0.14;
            h = 0.04;
            colorText[] = {1, 1, 0, 0.8};
            font = "RobotoCondensed";
            sizeEx = 0.03;
            style = 2; // Right aligned
        };
        
        class CompassDisplay: EDEN_RscText {
            idc = 2413;
            text = "N";
            x = 0.48;
            y = 0.02;
            w = 0.04;
            h = 0.04;
            colorText[] = {1, 1, 1, 0.8};
            font = "RobotoCondensedBold";
            sizeEx = 0.04;
            style = 2; // Center aligned
        };
        
        class TimeDisplay: EDEN_RscText {
            idc = 2414;
            text = "12:00";
            x = 0.85;
            y = 0.93;
            w = 0.14;
            h = 0.04;
            colorText[] = {1, 1, 1, 0.8};
            font = "RobotoCondensed";
            sizeEx = 0.03;
            style = 2; // Right aligned
        };
    };
};

class EDEN_ProgressBar {
    idd = 2500;
    duration = 999999;
    fadeIn = 0.3;
    fadeOut = 0.3;
    name = "EDEN_ProgressBar";
    
    class controls {
        class Background: RscBackground {
            x = 0.35;
            y = 0.45;
            w = 0.3;
            h = 0.1;
            colorBackground[] = {0, 0, 0, 0.7};
        };
        
        class Title: EDEN_RscText {
            idc = 2501;
            text = "Processing...";
            x = 0.36;
            y = 0.46;
            w = 0.28;
            h = 0.04;
            colorText[] = {1, 1, 1, 1};
            font = "RobotoCondensedBold";
            sizeEx = 0.04;
            style = 2; // Center aligned
        };
        
        class ProgressBar: RscText {
            idc = 2502;
            x = 0.36;
            y = 0.51;
            w = 0.28;
            h = 0.03;
            colorFrame[] = {0.3, 0.3, 0.3, 1};
            colorBar[] = {0.2, 0.8, 0.2, 1};
        };
    };
};

class EDEN_Notification {
    idd = 2600;
    duration = 5;
    fadeIn = 0.3;
    fadeOut = 0.3;
    name = "EDEN_Notification";
    
    class controls {
        class Background: RscBackground {
            x = 0.7;
            y = 0.15;
            w = 0.28;
            h = 0.08;
            colorBackground[] = {0, 0, 0, 0.8};
        };
        
        class Icon: RscPicture {
            idc = 2601;
            text = "images\icon_info.paa";
            x = 0.71;
            y = 0.16;
            w = 0.04;
            h = 0.06;
        };
        
        class Title: EDEN_RscText {
            idc = 2602;
            text = "Information";
            x = 0.76;
            y = 0.16;
            w = 0.21;
            h = 0.03;
            colorText[] = {1, 1, 1, 1};
            font = "RobotoCondensedBold";
            sizeEx = 0.035;
        };
        
        class Message: EDEN_RscText {
            idc = 2603;
            text = "This is a notification message";
            x = 0.76;
            y = 0.19;
            w = 0.21;
            h = 0.03;
            colorText[] = {0.9, 0.9, 0.9, 1};
            font = "RobotoCondensed";
            sizeEx = 0.03;
        };
    };
};

class EDEN_InteractionMenu {
    idd = 2700;
    duration = 999999;
    fadeIn = 0.2;
    fadeOut = 0.2;
    name = "EDEN_InteractionMenu";
    
    class controls {
        class Background: RscBackground {
            x = 0.4;
            y = 0.3;
            w = 0.2;
            h = 0.4;
            colorBackground[] = {0, 0, 0, 0.8};
        };
        
        class Title: EDEN_RscText {
            text = "Interaction Menu";
            x = 0.41;
            y = 0.31;
            w = 0.18;
            h = 0.04;
            colorText[] = {1, 1, 1, 1};
            font = "RobotoCondensedBold";
            sizeEx = 0.04;
            style = 2; // Center aligned
        };
        
        class ActionList: EDEN_RscListBox {
            idc = 2701;
            x = 0.41;
            y = 0.36;
            w = 0.18;
            h = 0.3;
            sizeEx = 0.035;
        };
        
        class CloseButton: EDEN_RscButtonRed {
            text = "Close";
            x = 0.41;
            y = 0.67;
            w = 0.18;
            h = 0.04;
            action = "closeDialog 2700;";
        };
    };
};
