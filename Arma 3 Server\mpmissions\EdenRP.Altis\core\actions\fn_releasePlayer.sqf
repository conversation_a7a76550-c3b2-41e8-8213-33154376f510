/*
    File: fn_releasePlayer.sqf
    Author: EdenRP Development Team
    
    Description:
    Releases a player from jail or restraints.
    
    Parameters:
    0: OBJECT - Player to release
    1: OBJECT - Releasing officer (optional)
    
    Returns:
    BOOLEAN - True if release was successful
    
    Example:
    [cursorTarget] call EDEN_fnc_releasePlayer;
    [_prisoner, player] call EDEN_fnc_releasePlayer;
*/

params [
    ["_target", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_officer", player, [obj<PERSON><PERSON>]]
];

// Validate parameters
if (isNull _target) exitWith {
    ["[EDEN] fn_releasePlayer: Invalid target"] call EDEN_fnc_systemLogger;
    false
};

// Check if player is actually arrested or restrained
if (!(_target getVariable ["eden_isArrested", false]) && !(_target getVariable ["eden_isRestrained", false])) exitWith {
    ["Player is not arrested or restrained!"] call EDEN_fnc_showHint;
    false
};

// Remove arrest status
_target setVariable ["eden_isArrested", false, true];
_target setVariable ["eden_isRestrained", false, true];
_target setVariable ["eden_arrestedBy", "", true];
_target setVariable ["eden_arrestReason", "", true];
_target setVariable ["eden_arrestTime", 0, true];
_target setVariable ["eden_jailTime", 0, true];

// Re-enable damage
_target allowDamage true;

// Remove handcuff animation
[_target, ""] remoteExec ["switchMove"];

// Teleport to release location (outside jail)
_releasePosition = [1681.5, 1315.2, 0]; // Outside Altis jail
_target setPos _releasePosition;

// Remove jail marker
_markerName = format ["jail_%1", getPlayerUID _target];
deleteMarker _markerName;

// Give basic civilian equipment back
_target addItem "ItemMap";
_target addItem "ItemCompass";
_target addItem "ItemWatch";
_target addItem "ItemRadio";
_target addItem "ItemGPS";

// Assign items
_target assignItem "ItemMap";
_target assignItem "ItemCompass";
_target assignItem "ItemWatch";
_target assignItem "ItemRadio";
_target assignItem "ItemGPS";

// Notifications
if (!isNull _officer && _officer != _target) then {
    [
        "Player Released",
        format ["%1 has been released from custody", name _target],
        5,
        "success"
    ] remoteExec ["EDEN_fnc_showNotification", _officer];
    
    // Log the release
    [format ["[EDEN] Player %1 released by %2", name _target, name _officer], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
} else {
    // Automatic release (time served)
    [format ["[EDEN] Player %1 automatically released (time served)", name _target], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
};

[
    "Released from Custody",
    "You have been released. Please follow the law in the future.",
    5,
    "success"
] remoteExec ["EDEN_fnc_showNotification", _target];

// Save player data
[_target] call EDEN_fnc_savePlayerData;

// Return success
true
