/*
    File: fn_safeHouseSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages gang safe house system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_safeHouses") then {
            eden_safeHouses = [];
            publicVariable "eden_safeHouses";
        };
        true
    };
    case "establishSafeHouse": {
        _gang = _player getVariable ["eden_gang", ""];
        _rank = _player getVariable ["eden_gangRank", ""];
        
        if (_gang == "" || _rank != "Leader") exitWith {
            ["Only gang leaders can establish safe houses!"] call EDEN_fnc_showHint;
            false
        };
        
        _cost = 10000;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _pos = getPos _player;
        _safeHouse = [_gang, _pos, time, "Active"];
        eden_safeHouses pushBack _safeHouse;
        publicVariable "eden_safeHouses";
        
        ["Safe house established!"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "enterSafeHouse": {
        _gang = _player getVariable ["eden_gang", ""];
        if (_gang == "") exitWith {
            ["You must be in a gang!"] call EDEN_fnc_showHint;
            false
        };
        
        ["Entered safe house - you are protected here"] call EDEN_fnc_showHint;
        _player setVariable ["eden_inSafeHouse", true, true];
        true
    };
    case "leaveSafeHouse": {
        _player setVariable ["eden_inSafeHouse", false, true];
        ["Left safe house"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
