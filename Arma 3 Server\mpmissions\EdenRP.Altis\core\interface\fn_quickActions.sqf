/*
    File: fn_quickActions.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages quick action system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_quickActions", ["inventory", "phone", "map", "settings"], true];
        true
    };
    case "executeQuickAction": {
        params ["", "", ["_actionIndex", 0, [0]]];
        
        _actions = _player getVariable ["eden_quickActions", []];
        if (_actionIndex >= count _actions) exitWith { false };
        
        _actionName = _actions select _actionIndex;
        
        switch (_actionName) do {
            case "inventory": {
                [_player, "openMainMenu"] call EDEN_fnc_menuSystem;
            };
            case "phone": {
                [_player, "openMainMenu"] call EDEN_fnc_menuSystem;
            };
            case "map": {
                openMap true;
            };
            case "settings": {
                [_player, "openMainMenu"] call EDEN_fnc_menuSystem;
            };
            case "help": {
                ["Quick Actions:\n1 - Inventory\n2 - Phone\n3 - Map\n4 - Settings"] call EDEN_fnc_showHint;
            };
        };
        
        true
    };
    case "customizeActions": {
        params ["", "", ["_newActions", [], [[]]]];
        
        if (count _newActions > 0) then {
            _player setVariable ["eden_quickActions", _newActions, true];
            ["Quick actions customized"] call EDEN_fnc_showHint;
        };
        
        true
    };
    case "showHelp": {
        _actions = _player getVariable ["eden_quickActions", []];
        _helpText = "Quick Actions:\n";
        
        for "_i" from 0 to (count _actions - 1) do {
            _helpText = _helpText + format["%1 - %2\n", (_i + 1), (_actions select _i)];
        };
        
        [_helpText] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
