/*
    EdenRP Arrest Player Function
    Enhanced arrest system with comprehensive logging
*/

params [
    ["_officer", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_suspect", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_charges", [], [[]]],
    ["_evidence", [], [[]]]
];

// Validate parameters
if (isNull _officer || isNull _suspect) exitWith {
    ["Invalid officer or suspect provided to arrest<PERSON><PERSON>", "ERROR", "POLICE"] call EDEN_fnc_systemLogger;
    false
};

if (!isPlayer _officer || !isPlayer _suspect) exitWith {
    ["Non-player provided to arrestPlayer", "ERROR", "POLICE"] call EDEN_fnc_systemLogger;
    false
};

// Check if officer is police
if (side _officer != west) exitWith {
    ["Non-police officer attempted arrest", "WARNING", "POLICE"] call EDEN_fnc_systemLogger;
    ["You are not authorized to make arrests", "error"] remoteExec ["EDEN_fnc_showNotification", _officer];
    false
};

// Check if officer is on duty
if !(_officer getVariable ["EDEN_OnDuty", false]) exitWith {
    ["Off-duty officer attempted arrest", "WARNING", "POLICE"] call EDEN_fnc_systemLogger;
    ["You must be on duty to make arrests", "error"] remoteExec ["EDEN_fnc_showNotification", _officer];
    false
};

// Check if suspect is already arrested
if (_suspect getVariable ["EDEN_IsArrested", false]) exitWith {
    ["Suspect is already arrested", "warning"] remoteExec ["EDEN_fnc_showNotification", _officer];
    false
};

// Check distance
if (_officer distance _suspect > 5) exitWith {
    ["You must be closer to the suspect to arrest them", "error"] remoteExec ["EDEN_fnc_showNotification", _officer];
    false
};

// Validate charges
if (count _charges == 0) exitWith {
    ["No charges specified for arrest", "error"] remoteExec ["EDEN_fnc_showNotification", _officer];
    false
};

// Calculate total fine and jail time
private _totalFine = 0;
private _totalJailTime = 0;
private _chargeDescriptions = [];

{
    private _chargeConfig = [_x] call EDEN_fnc_getChargeConfig;
    if (count _chargeConfig > 0) then {
        _totalFine = _totalFine + (_chargeConfig select 1);
        _totalJailTime = _totalJailTime + (_chargeConfig select 2);
        _chargeDescriptions pushBack (_chargeConfig select 0);
    };
} forEach _charges;

// Apply officer rank multiplier for fines
private _officerRank = _officer getVariable ["EDEN_CopLevel", 1];
private _rankMultiplier = 1 + (_officerRank * 0.1); // 10% increase per rank
_totalFine = round (_totalFine * _rankMultiplier);

// Set suspect as arrested
_suspect setVariable ["EDEN_IsArrested", true, true];
_suspect setVariable ["EDEN_JailTime", _totalJailTime, false];
_suspect setVariable ["EDEN_ArrestingOfficer", getPlayerUID _officer, false];
_suspect setVariable ["EDEN_ArrestTime", time, false];

// Remove suspect's weapons and illegal items
[_suspect] call EDEN_fnc_stripIllegalItems;

// Teleport suspect to jail
private _jailPosition = [16000, 16000, 0]; // Default jail position
[_suspect, _jailPosition] call EDEN_fnc_teleportPlayer;

// Create arrest record in database
private _officerUID = getPlayerUID _officer;
private _suspectUID = getPlayerUID _suspect;
private _chargesStr = str _charges;
private _evidenceStr = str _evidence;
private _location = [getPosATL _suspect] call EDEN_fnc_getLocationName;

private _query = format [
    "EDEN_Police:insertArrest:%1:%2:%3:%4:%5:%6:%7",
    _officerUID,
    _suspectUID,
    _chargesStr,
    _totalFine,
    _totalJailTime,
    _location,
    _evidenceStr
];

private _queryId = [_query, 1] call EDEN_fnc_asyncCall;

// Update wanted status
[_suspectUID, _charges, 0] call EDEN_fnc_updateWantedStatus;

// Log arrest
[format["ARREST: %1 arrested %2 for %3 charges. Fine: $%4, Jail: %5min", 
    name _officer, name _suspect, count _charges, _totalFine, _totalJailTime], "INFO", "POLICE"] call EDEN_fnc_systemLogger;

// Notify all police officers
private _arrestMessage = format ["%1 has arrested %2 for: %3", 
    name _officer, name _suspect, _chargeDescriptions joinString ", "];
{
    if (side _x == west && _x getVariable ["EDEN_OnDuty", false]) then {
        [_arrestMessage, "police"] remoteExec ["EDEN_fnc_showNotification", _x];
    };
} forEach allPlayers;

// Notify suspect
[format["You have been arrested by %1", name _officer], "error"] remoteExec ["EDEN_fnc_showNotification", _suspect];
[format["Charges: %1", _chargeDescriptions joinString ", "], "info"] remoteExec ["EDEN_fnc_showNotification", _suspect];
[format["Fine: $%1, Jail Time: %2 minutes", _totalFine, _totalJailTime], "info"] remoteExec ["EDEN_fnc_showNotification", _suspect];

// Start jail timer
[_suspect, _totalJailTime] spawn EDEN_fnc_startJailTimer;

// Award XP to officer
private _xpReward = _totalJailTime * 10; // 10 XP per minute of jail time
[_officer, _xpReward, "ARREST"] call EDEN_fnc_awardExperience;

// Update officer statistics
private _arrests = _officer getVariable ["EDEN_TotalArrests", 0];
_officer setVariable ["EDEN_TotalArrests", _arrests + 1, false];

true
