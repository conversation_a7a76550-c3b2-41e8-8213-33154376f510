/*
    File: fn_seizeMoney.sqf
    Author: EdenRP Development Team
    
    Description:
    Seizes money from a player (police only).
    
    Parameters:
    0: OBJECT - Target player
    1: NUMBER - Amount to seize (optional, seizes all cash if 0)
    2: OBJECT - Officer (optional, default: player)
    
    Returns:
    BOOLEAN - True if money was seized successfully
*/

params [
    ["_target", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_amount", 0, [0]],
    ["_officer", player, [obj<PERSON><PERSON>]]
];

if (isNull _target || isNull _officer) exitWith { false };

if (!(_officer getVariable ["eden_isPolice", false])) exitWith {
    ["Only police officers can seize money!"] call EDEN_fnc_showHint;
    false
};

if (_officer distance _target > 5) exitWith {
    ["Target is too far away!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_officer, "police_action", [_target, "seize_money"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

_targetCash = _target getVariable ["eden_cash", 0];

if (_targetCash <= 0) exitWith {
    ["Target has no cash to seize!"] call EDEN_fnc_showHint;
    false
};

// Determine amount to seize
_seizeAmount = if (_amount <= 0) then { _targetCash } else { _amount min _targetCash };

if (_seizeAmount <= 0) exitWith {
    ["No money to seize!"] call EDEN_fnc_showHint;
    false
};

// Seize the money
_target setVariable ["eden_cash", (_targetCash - _seizeAmount), true];

// Add to police department funds
_policeFunds = missionNamespace getVariable ["eden_policeFunds", 0];
missionNamespace setVariable ["eden_policeFunds", (_policeFunds + _seizeAmount), true];

// Add to evidence log
_evidenceLocker = missionNamespace getVariable ["eden_evidenceLocker", []];
_evidenceEntry = [
    time,
    name _officer,
    name _target,
    [["cash", _seizeAmount]],
    format ["Money seized during arrest/search by %1", name _officer]
];
_evidenceLocker pushBack _evidenceEntry;
missionNamespace setVariable ["eden_evidenceLocker", _evidenceLocker, true];

// Update police statistics
_moneySeized = _officer getVariable ["eden_moneySeized", 0];
_officer setVariable ["eden_moneySeized", (_moneySeized + _seizeAmount), true];

// Notifications
[format ["Seized $%1 from %2", _seizeAmount, name _target]] call EDEN_fnc_showHint;
[format ["Officer %1 seized $%2 from you", name _officer, _seizeAmount]] remoteExec ["EDEN_fnc_showHint", _target];

// Log the seizure
[format ["[EDEN] Officer %1 seized $%2 from %3", name _officer, _seizeAmount, name _target], "INFO", "POLICE"] call EDEN_fnc_systemLogger;

[_target] call EDEN_fnc_savePlayerData;
[_officer] call EDEN_fnc_savePlayerData;

true
