﻿ 7:35:15 BattlEye Server: Initialized (v1.220, Arma 3 2.20.152984)
 7:35:15 Game Port: 2302, Steam Query Port: 2303
 7:35:15 Warning: Current Steam AppId: 233780 doesn't match expected value: 107410
 7:35:15 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:35:15 Host identity created.
 7:35:15 Roles assigned.
 7:35:15 Reading mission ...
 7:35:20 Script core\actions\fn_arrestPlayer.sqf not found
 7:35:20 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:35:20 Roles assigned.
 7:35:20 Reading mission ...
 7:35:20 Script core\actions\fn_arrestPlayer.sqf not found
 7:35:21 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:35:21 Roles assigned.
 7:35:21 Reading mission ...
 7:35:21 Script core\actions\fn_arrestPlayer.sqf not found
 7:35:21 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:35:21 Roles assigned.
 7:35:21 Reading mission ...
 7:35:22 Script core\actions\fn_arrestPlayer.sqf not found
 7:35:22 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:35:22 Roles assigned.
 7:35:22 Reading mission ...
 7:35:22 Script core\actions\fn_arrestPlayer.sqf not found
 7:35:23 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:35:23 Roles assigned.
 7:35:23 Reading mission ...
 7:35:23 Script core\actions\fn_arrestPlayer.sqf not found
 7:35:23 Mission EdenRP.Altis read from directory.
 7:35:23 Roles assigned.
 7:35:24 Reading mission ...
 7:35:24 Script core\actions\fn_arrestPlayer.sqf not found
 7:35:24 Mission EdenRP.Altis read from directory.
 7:35:24 Roles assigned.
 7:35:24 Reading mission ...
 7:35:24 Script core\actions\fn_arrestPlayer.sqf not found
 7:35:25 Mission EdenRP.Altis read from directory.
 7:35:25 Roles assigned.
 7:35:25 Reading mission ...
 7:35:25 Script core\actions\fn_arrestPlayer.sqf not found
 7:35:25 Mission EdenRP.Altis read from directory.
 7:35:25 Roles assigned.
 7:35:25 Reading mission ...
 7:35:26 Script core\actions\fn_arrestPlayer.sqf not found
 7:35:26 Mission EdenRP.Altis read from directory.
 7:35:26 Roles assigned.
 7:35:26 Reading mission ...
 7:35:26 Script core\actions\fn_arrestPlayer.sqf not found
 7:35:27 Mission EdenRP.Altis read from directory.
 7:35:27 Roles assigned.
 7:35:27 Reading mission ...
 7:35:27 Script core\actions\fn_arrestPlayer.sqf not found
 7:35:27 Mission EdenRP.Altis read from directory.
