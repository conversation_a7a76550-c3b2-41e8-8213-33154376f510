/*
    File: fn_stockMarket.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages stock market trading system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_stock", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_stockPrices") then {
            eden_stockPrices = [
                ["EDEN_TECH", 100, 0],
                ["ALTIS_MINING", 75, 0],
                ["KAVALA_BANK", 150, 0],
                ["PYRGOS_OIL", 120, 0]
            ];
            publicVariable "eden_stockPrices";
        };
        _player setVariable ["eden_portfolio", [], true];
        _player setVariable ["eden_tradingHistory", [], true];
        true
    };
    case "buyStock": {
        params ["", "", "", ["_shares", 1, [0]]];
        
        _stockData = [];
        _stockIndex = -1;
        {
            if ((_x select 0) == _stock) then {
                _stockData = _x;
                _stockIndex = _forEachIndex;
            };
        } forEach eden_stockPrices;
        
        if (count _stockData == 0) exitWith {
            ["Stock not found"] call EDEN_fnc_showHint;
            false
        };
        
        _price = _stockData select 1;
        _totalCost = _price * _shares;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _totalCost) exitWith {
            [format ["Not enough money! Need $%1", _totalCost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _totalCost), true];
        
        _portfolio = _player getVariable ["eden_portfolio", []];
        _found = false;
        {
            if ((_x select 0) == _stock) then {
                _x set [1, ((_x select 1) + _shares)];
                _portfolio set [_forEachIndex, _x];
                _found = true;
            };
        } forEach _portfolio;
        
        if (!_found) then {
            _portfolio pushBack [_stock, _shares, _price];
        };
        _player setVariable ["eden_portfolio", _portfolio, true];
        
        _history = _player getVariable ["eden_tradingHistory", []];
        _history pushBack [_stock, "BUY", _shares, _price, time];
        _player setVariable ["eden_tradingHistory", _history, true];
        
        [format ["Bought %1 shares of %2 at $%3 each", _shares, _stock, _price]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "sellStock": {
        params ["", "", "", ["_shares", 1, [0]]];
        
        _portfolio = _player getVariable ["eden_portfolio", []];
        _holding = [];
        _holdingIndex = -1;
        
        {
            if ((_x select 0) == _stock) then {
                _holding = _x;
                _holdingIndex = _forEachIndex;
            };
        } forEach _portfolio;
        
        if (count _holding == 0 || (_holding select 1) < _shares) exitWith {
            ["Insufficient shares to sell"] call EDEN_fnc_showHint;
            false
        };
        
        _stockData = [];
        {
            if ((_x select 0) == _stock) then { _stockData = _x; };
        } forEach eden_stockPrices;
        
        _currentPrice = _stockData select 1;
        _totalValue = _currentPrice * _shares;
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _totalValue), true];
        
        _holding set [1, ((_holding select 1) - _shares)];
        if ((_holding select 1) <= 0) then {
            _portfolio deleteAt _holdingIndex;
        } else {
            _portfolio set [_holdingIndex, _holding];
        };
        _player setVariable ["eden_portfolio", _portfolio, true];
        
        [format ["Sold %1 shares of %2 at $%3 each", _shares, _stock, _currentPrice]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
