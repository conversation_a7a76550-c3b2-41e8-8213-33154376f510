/*
    File: fn_trackingSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages vehicle tracking and GPS system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_vehicle", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_trackedVehicles", [], true];
        true
    };
    case "installTracker": {
        if (isNull _vehicle) exitWith { false };
        
        _cost = 200;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _vehicle setVariable ["eden_hasTracker", true, true];
        _vehicle setVariable ["eden_trackerOwner", getPlayerUID _player, true];
        
        _tracked = _player getVariable ["eden_trackedVehicles", []];
        _tracked pushBack [typeOf _vehicle, netId _vehicle, time];
        _player setVariable ["eden_trackedVehicles", _tracked, true];
        
        [format ["GPS tracker installed for $%1", _cost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "locateVehicle": {
        if (isNull _vehicle) exitWith { false };
        
        _hasTracker = _vehicle getVariable ["eden_hasTracker", false];
        _trackerOwner = _vehicle getVariable ["eden_trackerOwner", ""];
        
        if (!_hasTracker || _trackerOwner != getPlayerUID _player) exitWith {
            ["No tracker installed or not authorized"] call EDEN_fnc_showHint;
            false
        };
        
        _pos = getPos _vehicle;
        _marker = createMarkerLocal [format ["vehicle_tracker_%1", random 1000], _pos];
        _marker setMarkerTypeLocal "mil_dot";
        _marker setMarkerColorLocal "ColorBlue";
        _marker setMarkerTextLocal "Tracked Vehicle";
        
        ["Vehicle located on map"] call EDEN_fnc_showHint;
        
        [{
            deleteMarkerLocal (_this select 0);
        }, [_marker], 30] call CBA_fnc_waitAndExecute;
        
        true
    };
    case "disableTracker": {
        if (isNull _vehicle) exitWith { false };
        
        _vehicle setVariable ["eden_hasTracker", false, true];
        _vehicle setVariable ["eden_trackerOwner", "", true];
        
        ["Vehicle tracker disabled"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
