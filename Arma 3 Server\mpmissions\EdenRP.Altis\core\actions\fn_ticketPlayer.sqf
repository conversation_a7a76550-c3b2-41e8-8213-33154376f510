/*
    File: fn_ticketPlayer.sqf
    Author: EdenRP Development Team
    
    Description:
    Issues a traffic ticket or fine to a player.
    
    Parameters:
    0: OBJECT - Target player to ticket
    1: OBJECT - Issuing officer (optional, default: player)
    2: STRING - Violation reason (optional)
    3: NUMBER - Fine amount (optional, default: 1000)
    
    Returns:
    BOOLEAN - True if ticket was issued successfully
    
    Example:
    [cursorTarget] call EDEN_fnc_ticketPlayer;
    [_target, player, "Speeding", 500] call EDEN_fnc_ticketPlayer;
*/

params [
    ["_target", objNull, [objNull]],
    ["_officer", player, [objNull]],
    ["_violation", "Traffic violation", [""]],
    ["_fineAmount", 1000, [0]]
];

// Validate parameters
if (isNull _target || isNull _officer) exitWith {
    ["[EDEN] fn_ticketPlayer: Invalid target or officer"] call EDEN_fnc_systemLogger;
    false
};

// Check if officer is police
if (!(_officer getVariable ["eden_isPolice", false])) exitWith {
    ["Only police officers can issue tickets!"] call EDEN_fnc_showHint;
    false
};

// Check distance
if (_officer distance _target > 10) exitWith {
    ["Target is too far away!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_officer, "player_action", [_target, "ticket"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Validate fine amount
_fineAmount = _fineAmount max 100 min 50000;

// Generate ticket ID
_ticketID = format ["TKT-%1-%2", floor(time), floor(random 9999)];

// Get player's current money
_playerCash = _target getVariable ["eden_cash", 0];
_playerBank = _target getVariable ["eden_bankAccount", 0];
_totalMoney = _playerCash + _playerBank;

// Check if player can afford the fine
_canAfford = _totalMoney >= _fineAmount;

// Create ticket record
_ticketRecord = [
    _ticketID,
    time,
    name _officer,
    name _target,
    _violation,
    _fineAmount,
    false, // paid status
    time + (7 * 24 * 60 * 60) // due date (7 days)
];

// Add to player's tickets
_playerTickets = _target getVariable ["eden_tickets", []];
_playerTickets pushBack _ticketRecord;
_target setVariable ["eden_tickets", _playerTickets, true];

// Add to officer's issued tickets
_officerTickets = _officer getVariable ["eden_issuedTickets", []];
_officerTickets pushBack _ticketRecord;
_officer setVariable ["eden_issuedTickets", _officerTickets, true];

// Update police statistics
_ticketsIssued = _officer getVariable ["eden_ticketsIssued", 0];
_officer setVariable ["eden_ticketsIssued", (_ticketsIssued + 1), true];

// Create detailed ticket information
_ticketDetails = format [
    "=== TRAFFIC CITATION ===\n" +
    "Ticket ID: %1\n" +
    "Date: %2\n" +
    "Officer: %3\n" +
    "Violator: %4\n" +
    "Violation: %5\n" +
    "Fine Amount: $%6\n" +
    "Due Date: %7 days\n" +
    "Status: %8\n" +
    "========================",
    _ticketID,
    [time, "YYYY-MM-DD HH:MM"] call BIS_fnc_timeToString,
    name _officer,
    name _target,
    _violation,
    _fineAmount,
    7,
    if (_canAfford) then {"UNPAID"} else {"UNPAID - INSUFFICIENT FUNDS"}
];

// Notifications
[
    "Ticket Issued",
    format ["Ticket issued to %1 for %2 ($%3)", name _target, _violation, _fineAmount],
    5,
    "success"
] remoteExec ["EDEN_fnc_showNotification", _officer];

[
    "Traffic Citation Received",
    format ["You have been issued a ticket for %1. Fine: $%2 (ID: %3)", _violation, _fineAmount, _ticketID],
    10,
    "warning"
] remoteExec ["EDEN_fnc_showNotification", _target];

// Show payment options if player can afford
if (_canAfford) then {
    [
        "Payment Options",
        "You can pay this ticket now or within 7 days. Unpaid tickets may result in arrest.",
        8,
        "info"
    ] remoteExec ["EDEN_fnc_showNotification", _target];
} else {
    [
        "Insufficient Funds",
        "You cannot afford to pay this ticket right now. You have 7 days to pay or face arrest.",
        10,
        "error"
    ] remoteExec ["EDEN_fnc_showNotification", _target];
};

// Add to criminal record if significant violation
if (_fineAmount > 2000) then {
    _criminalRecord = _target getVariable ["eden_criminalRecord", []];
    _violationRecord = [
        time,
        name _officer,
        format ["Traffic violation: %1", _violation],
        _fineAmount
    ];
    _criminalRecord pushBack _violationRecord;
    _target setVariable ["eden_criminalRecord", _criminalRecord, true];
    
    // Increase wanted level for serious violations
    _wantedLevel = _target getVariable ["eden_wantedLevel", 0];
    _target setVariable ["eden_wantedLevel", (_wantedLevel + 1), true];
};

// Schedule automatic warrant for unpaid tickets
[_target, _ticketID, _fineAmount] spawn {
    params ["_player", "_id", "_amount"];
    
    sleep (7 * 24 * 60 * 60); // Wait 7 days
    
    // Check if ticket is still unpaid
    _tickets = _player getVariable ["eden_tickets", []];
    _unpaidTicket = _tickets select {(_x select 0) == _id && !(_x select 6)};
    
    if (count _unpaidTicket > 0) then {
        // Issue warrant for unpaid ticket
        _wantedLevel = _player getVariable ["eden_wantedLevel", 0];
        _player setVariable ["eden_wantedLevel", (_wantedLevel + 2), true];
        
        _bounty = _player getVariable ["eden_bounty", 0];
        _player setVariable ["eden_bounty", (_bounty + _amount), true];
        
        [format ["[EDEN] Warrant issued for %1 - unpaid ticket %2", name _player, _id], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
        
        // Notify all police
        {
            if (_x getVariable ["eden_isPolice", false]) then {
                [
                    "Warrant Issued",
                    format ["Warrant issued for %1 - unpaid ticket ($%2)", name _player, _amount],
                    5,
                    "warning"
                ] remoteExec ["EDEN_fnc_showNotification", _x];
            };
        } forEach allPlayers;
    };
};

// Log the ticket
[format ["[EDEN] Officer %1 issued ticket to %2 for %3 ($%4) - ID: %5", 
    name _officer, name _target, _violation, _fineAmount, _ticketID], "INFO", "POLICE"] call EDEN_fnc_systemLogger;

// Save player data
[_target] call EDEN_fnc_savePlayerData;
[_officer] call EDEN_fnc_savePlayerData;

// Return success
true
