/*
    File: fn_defuseBomb.sqf
    Author: EdenRP Development Team
    
    Description:
    Defuses an explosive device (police/bomb squad only).
    
    Parameters:
    0: OBJECT - Bomb to defuse
    1: OBJECT - Defuser (optional, default: player)
    
    Returns:
    BOOLEAN - True if bomb was defused successfully
*/

params [
    ["_bomb", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_defuser", player, [obj<PERSON><PERSON>]]
];

if (isNull _bomb || isNull _defuser) exitWith { false };

if (_defuser distance _bomb > 3) exitWith {
    ["You must be very close to defuse the bomb!"] call EDEN_fnc_showHint;
    false
};

// Check if defuser is authorized (police or bomb squad)
if (!(_defuser getVariable ["eden_isPolice", false]) && !(_defuser getVariable ["eden_isBombSquad", false])) exitWith {
    ["Only police or bomb squad personnel can defuse bombs!"] call EDEN_fnc_showHint;
    false
};

// Check if bomb is actually armed
if (!(_bomb getVariable ["eden_isArmed", true])) exitWith {
    ["This device is not armed!"] call EDEN_fnc_showHint;
    false
};

// Check if defuser has bomb defusal kit
_virtualItems = _defuser getVariable ["eden_virtualItems", []];
_hasDefusalKit = false;

{
    if ((_x select 0) == "bomb_defusal_kit") then {
        _hasDefusalKit = true;
    };
} forEach _virtualItems;

if (!_hasDefusalKit) exitWith {
    ["You need a bomb defusal kit to safely defuse explosives!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_defuser, "police_action", [_bomb, "bomb_defusal"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Get bomb information
_bombID = _bomb getVariable ["eden_bombID", ""];
_bomber = _bomb getVariable ["eden_bomber", "Unknown"];
_detonationTime = _bomb getVariable ["eden_detonationTime", time + 60];
_timeRemaining = _detonationTime - time;

if (_timeRemaining <= 0) exitWith {
    ["WARNING: Bomb timer has expired! EVACUATE IMMEDIATELY!"] call EDEN_fnc_showHint;
    false
};

// Start defusal process
[_defuser, "Acts_carFixingWheel"] remoteExec ["switchMove"];
[format ["Defusing bomb... %1 seconds remaining on timer!", round _timeRemaining]] call EDEN_fnc_showHint;

// Defusal takes time and has risk
_defusalTime = 15 + random 10; // 15-25 seconds
_riskFactor = if (_timeRemaining < 60) then { 0.3 } else { 0.1 }; // Higher risk if timer is low

// Check if there's enough time
if (_defusalTime >= _timeRemaining) exitWith {
    [_defuser, ""] remoteExec ["switchMove"];
    ["NOT ENOUGH TIME TO DEFUSE! EVACUATE NOW!"] call EDEN_fnc_showHint;
    false
};

sleep _defusalTime;

[_defuser, ""] remoteExec ["switchMove"];

// Determine defusal success
_playerLevel = _defuser getVariable ["eden_playerLevel", 1];
_baseChance = 85; // 85% base success rate
_levelBonus = _playerLevel * 2; // +2% per level
_riskPenalty = _riskFactor * 30; // Penalty for high-risk situations
_successChance = (_baseChance + _levelBonus - _riskPenalty) max 50; // Minimum 50% chance

_success = (random 100) < _successChance;

if (_success) then {
    // Disarm the bomb
    _bomb setVariable ["eden_isArmed", false, true];
    
    // Remove from active bombs list
    _activeBombs = missionNamespace getVariable ["eden_activeBombs", []];
    _newBombs = [];
    {
        if ((_x select 1) != _bombID) then {
            _newBombs pushBack _x;
        };
    } forEach _activeBombs;
    missionNamespace setVariable ["eden_activeBombs", _newBombs, true];
    
    // Create evidence
    _evidenceLocker = missionNamespace getVariable ["eden_evidenceLocker", []];
    _evidenceEntry = [
        time,
        name _defuser,
        _bomber,
        [["defused_bomb", 1]],
        format ["Bomb defused by %1 - planted by %2", name _defuser, _bomber]
    ];
    _evidenceLocker pushBack _evidenceEntry;
    missionNamespace setVariable ["eden_evidenceLocker", _evidenceLocker, true];
    
    // Update police statistics
    _bombsDefused = _defuser getVariable ["eden_bombsDefused", 0];
    _defuser setVariable ["eden_bombsDefused", (_bombsDefused + 1), true];
    
    // Add experience
    _expGained = 500; // Large XP reward for defusing bombs
    _currentExp = _defuser getVariable ["eden_experience", 0];
    _defuser setVariable ["eden_experience", (_currentExp + _expGained), true];
    
    // Alert all players of successful defusal
    {
        [
            "BOMB DEFUSED",
            format ["Explosive device successfully defused by %1! Area is now safe.", name _defuser],
            15,
            "info"
        ] remoteExec ["EDEN_fnc_showNotification", _x];
    } forEach allPlayers;
    
    // Remove bomb object after delay
    [_bomb] spawn {
        params ["_bombObj"];
        sleep 5;
        deleteVehicle _bombObj;
    };
    
    [format ["BOMB SUCCESSFULLY DEFUSED! (+%1 XP)", _expGained]] call EDEN_fnc_showHint;
    
} else {
    // Defusal failed - bomb might detonate early or become more dangerous
    if (random 100 < 50) then {
        // Early detonation
        ["DEFUSAL FAILED! BOMB DETONATING EARLY!"] call EDEN_fnc_showHint;
        
        // Trigger immediate explosion
        _bomb setVariable ["eden_detonationTime", time + 3, true];
        
        // Alert everyone
        {
            [
                "BOMB DETONATION",
                "DEFUSAL FAILED! BOMB DETONATING! EVACUATE IMMEDIATELY!",
                10,
                "error"
            ] remoteExec ["EDEN_fnc_showNotification", _x];
        } forEach allPlayers;
        
    } else {
        // Defusal failed but bomb timer continues
        ["Defusal failed! Bomb is still armed and dangerous!"] call EDEN_fnc_showHint;
        
        // Add some time penalty (bomb becomes harder to defuse)
        _newDetonationTime = _detonationTime - 30; // Reduce timer by 30 seconds
        _bomb setVariable ["eden_detonationTime", _newDetonationTime, true];
    };
};

// Log defusal attempt
[format ["[EDEN] Bomb defusal attempt by %1 on bomb planted by %2 - %3", name _defuser, _bomber, if (_success) then {"successful"} else {"failed"}], "CRITICAL", "BOMB_SQUAD"] call EDEN_fnc_systemLogger;

[_defuser] call EDEN_fnc_savePlayerData;

_success
