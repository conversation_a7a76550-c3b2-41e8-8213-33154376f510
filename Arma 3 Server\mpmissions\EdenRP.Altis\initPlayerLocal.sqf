/*
    EdenRP Player Local Initialization
    Enhanced player-specific initialization for EdenRP
    
    This script runs locally for each player when they join
    Handles player-specific setup and configuration
*/

params ["_player", "_didJIP"];

// Only run for actual players, not AI
if (!hasInterface) exitWith {};

// Wait for essential systems
waitUntil {!isNull player};
waitUntil {!isNil "bis_fnc_init"};

// Initialize player variables
player setVariable ["EDEN_PlayerID", getPlayerUID player, true];
player setVariable ["EDEN_PlayerName", name player, true];
player setVariable ["EDEN_JoinTime", time, true];
player setVariable ["EDEN_IsJIP", _didJIP, true];

// Initialize player stats
player setVariable ["EDEN_Cash", 0, false];
player setVariable ["EDEN_Bank", 5000, false]; // Starting money
player setVariable ["EDEN_XP", 0, false];
player setVariable ["EDEN_Level", 1, false];
player setVariable ["EDEN_Faction", civilian, false];
player setVariable ["EDEN_Job", "Unemployed", false];
player setVariable ["EDEN_Licenses", [], false];
player setVariable ["EDEN_Skills", [], false];
player setVariable ["EDEN_Achievements", [], false];
player setVariable ["EDEN_Reputation", 0, false];
player setVariable ["EDEN_PlayTime", 0, false];

// Initialize player status
player setVariable ["EDEN_IsArrested", false, true];
player setVariable ["EDEN_IsRestrained", false, true];
player setVariable ["EDEN_IsEscorted", false, true];
player setVariable ["EDEN_IsDead", false, true];
player setVariable ["EDEN_IsUnconscious", false, true];
player setVariable ["EDEN_IsInVehicle", false, false];
player setVariable ["EDEN_IsInCombat", false, false];

// Initialize player inventory
player setVariable ["EDEN_VirtualInventory", [], false];
player setVariable ["EDEN_PhysicalInventory", [], false];
player setVariable ["EDEN_MaxWeight", 50, false];
player setVariable ["EDEN_CurrentWeight", 0, false];

// Initialize player vehicles
player setVariable ["EDEN_OwnedVehicles", [], false];
player setVariable ["EDEN_VehicleKeys", [], false];

// Initialize player properties
player setVariable ["EDEN_OwnedProperties", [], false];
player setVariable ["EDEN_PropertyKeys", [], false];

// Initialize player gang
player setVariable ["EDEN_GangID", -1, false];
player setVariable ["EDEN_GangRank", 0, false];
player setVariable ["EDEN_GangName", "", false];

// Initialize player communication
player setVariable ["EDEN_PhoneNumber", "", false];
player setVariable ["EDEN_RadioChannel", 0, false];
player setVariable ["EDEN_Messages", [], false];
player setVariable ["EDEN_Contacts", [], false];

// Initialize player settings
player setVariable ["EDEN_Settings", [
    ["HUD_Enabled", true],
    ["Notifications_Enabled", true],
    ["Sound_Enabled", true],
    ["Music_Enabled", true],
    ["Chat_Enabled", true],
    ["Radio_Enabled", true],
    ["Phone_Enabled", true],
    ["GPS_Enabled", true],
    ["Compass_Enabled", true],
    ["Speedometer_Enabled", true]
], false];

// Initialize player admin level
player setVariable ["EDEN_AdminLevel", 0, false];
player setVariable ["EDEN_IsModerator", false, false];
player setVariable ["EDEN_IsAdmin", false, false];
player setVariable ["EDEN_IsDeveloper", false, false];

// Initialize player security
player setVariable ["EDEN_LastValidation", time, false];
player setVariable ["EDEN_SecurityFlags", [], false];
player setVariable ["EDEN_SuspiciousActivity", 0, false];

// Setup player event handlers
player addEventHandler ["Killed", {
    params ["_unit", "_killer", "_instigator", "_useEffects"];
    [_unit, _killer, _instigator] call EDEN_fnc_playerKilled;
}];

player addEventHandler ["Respawn", {
    params ["_unit", "_corpse"];
    [_unit, _corpse] call EDEN_fnc_playerRespawn;
}];

player addEventHandler ["InventoryOpened", {
    params ["_unit", "_container"];
    [_unit, _container] call EDEN_fnc_inventoryOpened;
}];

player addEventHandler ["InventoryClosed", {
    params ["_unit", "_container"];
    [_unit, _container] call EDEN_fnc_inventoryClosed;
}];

player addEventHandler ["GetInMan", {
    params ["_unit", "_role", "_vehicle", "_turret"];
    [_unit, _role, _vehicle, _turret] call EDEN_fnc_playerGetIn;
}];

player addEventHandler ["GetOutMan", {
    params ["_unit", "_role", "_vehicle", "_turret"];
    [_unit, _role, _vehicle, _turret] call EDEN_fnc_playerGetOut;
}];

player addEventHandler ["HandleDamage", {
    params ["_unit", "_selection", "_damage", "_source", "_projectile", "_hitIndex", "_instigator", "_hitPoint"];
    [_unit, _selection, _damage, _source, _projectile, _hitIndex, _instigator, _hitPoint] call EDEN_fnc_playerHandleDamage;
}];

// Setup key bindings
player addEventHandler ["KeyDown", {
    params ["_displayOrControl", "_key", "_shift", "_ctrl", "_alt"];
    [_displayOrControl, _key, _shift, _ctrl, _alt] call EDEN_fnc_keyHandler;
}];

// Setup mouse bindings
player addEventHandler ["MouseButtonDown", {
    params ["_control", "_button", "_xPos", "_yPos", "_shift", "_ctrl", "_alt"];
    [_control, _button, _xPos, _yPos, _shift, _ctrl, _alt] call EDEN_fnc_mouseHandler;
}];

// Initialize faction-specific systems
switch (playerSide) do {
    case civilian: {
        // Civilian initialization
        player setVariable ["EDEN_CivilianRank", 0, false];
        player setVariable ["EDEN_CriminalRecord", [], false];
        player setVariable ["EDEN_Wanted", false, true];
        player setVariable ["EDEN_WantedLevel", 0, true];
        player setVariable ["EDEN_Bounty", 0, true];
        
        // Setup civilian-specific event handlers
        player addEventHandler ["FiredMan", {
            params ["_unit", "_weapon", "_muzzle", "_mode", "_ammo", "_magazine", "_projectile", "_gunner"];
            [_unit, _weapon, _muzzle, _mode, _ammo, _magazine, _projectile, _gunner] call EDEN_fnc_civilianFired;
        }];
    };
    
    case west: {
        // Police initialization
        player setVariable ["EDEN_PoliceRank", 0, false];
        player setVariable ["EDEN_BadgeNumber", "", false];
        player setVariable ["EDEN_Department", "", false];
        player setVariable ["EDEN_OnDuty", false, true];
        player setVariable ["EDEN_PatrolArea", "", false];
        player setVariable ["EDEN_Partner", objNull, false];
        
        // Setup police-specific event handlers
        player addEventHandler ["FiredMan", {
            params ["_unit", "_weapon", "_muzzle", "_mode", "_ammo", "_magazine", "_projectile", "_gunner"];
            [_unit, _weapon, _muzzle, _mode, _ammo, _magazine, _projectile, _gunner] call EDEN_fnc_policeFired;
        }];
    };
    
    case independent: {
        // Medical initialization
        player setVariable ["EDEN_MedicalRank", 0, false];
        player setVariable ["EDEN_MedicalID", "", false];
        player setVariable ["EDEN_Specialization", "", false];
        player setVariable ["EDEN_OnDuty", false, true];
        player setVariable ["EDEN_AssignedHospital", "", false];
        player setVariable ["EDEN_Partner", objNull, false];
        
        // Setup medical-specific event handlers
        player addEventHandler ["Medical", {
            params ["_unit", "_selection", "_damage", "_source", "_projectile", "_hitIndex", "_instigator", "_hitPoint"];
            [_unit, _selection, _damage, _source, _projectile, _hitIndex, _instigator, _hitPoint] call EDEN_fnc_medicalTreatment;
        }];
    };
};

// Initialize player HUD
[] spawn {
    waitUntil {!isNull (findDisplay 46)};
    [] call EDEN_fnc_initializeHUD;
};

// Initialize player UI
[] spawn {
    waitUntil {!isNull (findDisplay 46)};
    [] call EDEN_fnc_initializeUI;
};

// Load player data from server
if (!_didJIP) then {
    // New player
    ["Loading new player data..."] call EDEN_fnc_systemLogger;
    [getPlayerUID player, name player] remoteExec ["EDEN_fnc_createNewPlayer", 2];
} else {
    // Returning player
    ["Loading existing player data..."] call EDEN_fnc_systemLogger;
    [getPlayerUID player] remoteExec ["EDEN_fnc_loadPlayerData", 2];
};

// Start player monitoring systems
[] spawn {
    while {alive player} do {
        sleep 5;
        [] call EDEN_fnc_playerMonitor;
    };
};

// Start player auto-save system
[] spawn {
    while {alive player} do {
        sleep 300; // Save every 5 minutes
        [] call EDEN_fnc_autoSavePlayer;
    };
};

// Start player validation system
if (EDEN_ValidationEnabled) then {
    [] spawn {
        while {alive player} do {
            sleep 30; // Validate every 30 seconds
            [] call EDEN_fnc_validatePlayer;
        };
    };
};

// Initialize player communication systems
[] spawn {
    sleep 2; // Wait for other systems
    [] call EDEN_fnc_initializePlayerCommunication;
};

// Show welcome message
[] spawn {
    sleep 5; // Wait for loading to complete
    private _welcomeText = format ["Welcome to EdenRP, %1!", name player];
    [_welcomeText, "success"] call EDEN_fnc_showNotification;
    
    if (_didJIP) then {
        ["You have joined an ongoing session.", "info"] call EDEN_fnc_showNotification;
    };
};

// Log player initialization
[format["Player %1 (%2) initialized successfully. JIP: %3", name player, getPlayerUID player, _didJIP]] call EDEN_fnc_systemLogger;
