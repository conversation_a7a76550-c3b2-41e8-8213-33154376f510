/*
    File: fn_customizationSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages vehicle customization system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_vehicle", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_vehicleCustomizations", [], true];
        true
    };
    case "paintVehicle": {
        params ["", "", "", ["_color", [1,1,1,1], [[]]]];
        
        if (isNull _vehicle) exitWith { false };
        
        _cost = 500;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _vehicle setObjectTexture [0, "#(rgb,8,8,3)color(" + str(_color select 0) + "," + str(_color select 1) + "," + str(_color select 2) + ",1)"];
        
        _customizations = _player getVariable ["eden_vehicleCustomizations", []];
        _customizations pushBack [typeOf _vehicle, "paint", _color, time];
        _player setVariable ["eden_vehicleCustomizations", _customizations, true];
        
        [format ["Vehicle painted for $%1", _cost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "upgradeEngine": {
        if (isNull _vehicle) exitWith { false };
        
        _cost = 2000;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _vehicle setVariable ["eden_engineUpgrade", true, true];
        
        _customizations = _player getVariable ["eden_vehicleCustomizations", []];
        _customizations pushBack [typeOf _vehicle, "engine", true, time];
        _player setVariable ["eden_vehicleCustomizations", _customizations, true];
        
        [format ["Engine upgraded for $%1", _cost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "addNitrous": {
        if (isNull _vehicle) exitWith { false };
        
        _cost = 1500;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _vehicle setVariable ["eden_nitrous", 100, true];
        
        ["Nitrous system installed"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
