/*
    File: fn_dropItem.sqf
    Author: EdenRP Development Team
    
    Description:
    Drops an item from the player's virtual inventory onto the ground.
    
    Parameters:
    0: STRING - Item name to drop
    1: NUMBER - Quantity to drop (optional, default: 1)
    2: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if item was dropped successfully
*/

params [
    ["_itemName", "", [""]],
    ["_quantity", 1, [0]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (_itemName == "" || _quantity <= 0 || isNull _player) exitWith { false };

// Get player's virtual inventory
_virtualItems = _player getVariable ["eden_virtualItems", []];
_itemIndex = -1;
_itemQuantity = 0;

// Find the item
{
    if ((_x select 0) == _itemName) then {
        _itemIndex = _forEachIndex;
        _itemQuantity = _x select 1;
    };
} forEach _virtualItems;

if (_itemIndex == -1 || _itemQuantity < _quantity) exitWith {
    ["You don't have enough of this item!"] call EDEN_fnc_showHint;
    false
};

// Create physical item on ground
_playerPos = getPosATL _player;
_dropPos = [
    (_playerPos select 0) + (random 2 - 1),
    (_playerPos select 1) + (random 2 - 1),
    _playerPos select 2
];

// Create appropriate object based on item type
_objectClass = switch (_itemName) do {
    case "water_bottle": { "Land_BottlePlastic_V1_F" };
    case "apple": { "Land_Canteen_F" }; // Placeholder
    case "bread": { "Land_Canteen_F" }; // Placeholder
    case "toolkit": { "Land_ToolKit_01_F" };
    case "first_aid_kit": { "Land_FirstAidKit_01_F" };
    case "lockpick": { "Land_Screwdriver_V1_F" };
    case "rope": { "Land_Rope_01_F" };
    case "zipties": { "Land_Rope_01_F" }; // Placeholder
    case "phone": { "Land_MobilePhone_smart_F" };
    case "radio": { "Land_HandyCam_F" }; // Placeholder
    default { "Land_Money_F" }; // Default object for unknown items
};

// Create the object
_droppedObject = createVehicle [_objectClass, _dropPos, [], 0, "CAN_COLLIDE"];

// Set object properties
_droppedObject setVariable ["eden_droppedItem", true, true];
_droppedObject setVariable ["eden_itemName", _itemName, true];
_droppedObject setVariable ["eden_itemQuantity", _quantity, true];
_droppedObject setVariable ["eden_droppedBy", name _player, true];
_droppedObject setVariable ["eden_dropTime", time, true];

// Add pickup action
_droppedObject addAction [
    format ["<t color='#00FF00'>Pick up %1 x%2</t>", _itemName, _quantity],
    {
        params ["_target", "_caller"];
        
        _itemName = _target getVariable ["eden_itemName", ""];
        _itemQuantity = _target getVariable ["eden_itemQuantity", 1];
        
        if (_itemName != "") then {
            // Add to player's inventory
            _virtualItems = _caller getVariable ["eden_virtualItems", []];
            _found = false;
            
            {
                if ((_x select 0) == _itemName) then {
                    _x set [1, ((_x select 1) + _itemQuantity)];
                    _found = true;
                };
            } forEach _virtualItems;
            
            if (!_found) then {
                _virtualItems pushBack [_itemName, _itemQuantity];
            };
            
            _caller setVariable ["eden_virtualItems", _virtualItems, true];
            
            // Update weight
            _currentWeight = _caller getVariable ["eden_currentWeight", 0];
            _caller setVariable ["eden_currentWeight", (_currentWeight + _itemQuantity), true];
            
            [format ["Picked up %1 x%2", _itemName, _itemQuantity]] call EDEN_fnc_showHint;
            
            // Save player data
            [_caller] call EDEN_fnc_savePlayerData;
            
            // Delete the object
            deleteVehicle _target;
        };
    },
    [],
    10,
    true,
    true,
    "",
    "_this distance _target < 3",
    3
];

// Remove item from player's inventory
if (_itemQuantity <= _quantity) then {
    _virtualItems deleteAt _itemIndex;
} else {
    (_virtualItems select _itemIndex) set [1, (_itemQuantity - _quantity)];
};

_player setVariable ["eden_virtualItems", _virtualItems, true];

// Update weight
_currentWeight = _player getVariable ["eden_currentWeight", 0];
_player setVariable ["eden_currentWeight", (_currentWeight - _quantity), true];

// Auto-cleanup after 10 minutes
[_droppedObject] spawn {
    params ["_object"];
    sleep 600; // 10 minutes
    
    if (!isNull _object) then {
        deleteVehicle _object;
    };
};

[format ["Dropped %1 x%2", _itemName, _quantity]] call EDEN_fnc_showHint;

// Log item drop
[format ["[EDEN] Player %1 dropped %2 x%3", name _player, _itemName, _quantity], "DEBUG", "INVENTORY"] call EDEN_fnc_systemLogger;

// Save player data
[_player] call EDEN_fnc_savePlayerData;

true
