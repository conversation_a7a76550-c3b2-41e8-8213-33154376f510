# EdenRP Automated Download and Installation Script
# PowerShell script to download and install required components

param(
    [string]$ServerPath = "C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server"
)

Write-Host "===============================================" -ForegroundColor Green
Write-Host "    EdenRP Automated Download & Install" -ForegroundColor Green
Write-Host "    Enhanced Altis Life Server Setup" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""

# Function to download files
function Download-File {
    param(
        [string]$Url,
        [string]$OutputPath
    )
    
    try {
        Write-Host "[DOWNLOAD] Downloading from: $Url" -ForegroundColor Yellow
        Invoke-WebRequest -Uri $Url -OutFile $OutputPath -UseBasicParsing
        Write-Host "[OK] Downloaded: $OutputPath" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "[ERROR] Failed to download: $Url" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to extract ZIP files
function Extract-ZipFile {
    param(
        [string]$ZipPath,
        [string]$ExtractPath
    )
    
    try {
        Write-Host "[EXTRACT] Extracting: $ZipPath" -ForegroundColor Yellow
        Expand-Archive -Path $ZipPath -DestinationPath $ExtractPath -Force
        Write-Host "[OK] Extracted to: $ExtractPath" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "[ERROR] Failed to extract: $ZipPath" -ForegroundColor Red
        return $false
    }
}

# Create temporary download directory
$TempDir = "$env:TEMP\EdenRP_Setup"
if (!(Test-Path $TempDir)) {
    New-Item -ItemType Directory -Path $TempDir -Force | Out-Null
}

Write-Host "[INFO] Temporary directory: $TempDir" -ForegroundColor Cyan
Write-Host "[INFO] Server directory: $ServerPath" -ForegroundColor Cyan
Write-Host ""

# Check if server directory exists
if (!(Test-Path $ServerPath)) {
    Write-Host "[ERROR] Server directory not found: $ServerPath" -ForegroundColor Red
    Write-Host "[INFO] Please ensure your Arma 3 Server is installed at this location" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Download URLs (these may need to be updated)
$Downloads = @{
    "extDB3" = @{
        "Url" = "https://github.com/SteezCram/extDB3/releases/download/v1033/extDB3-v1033-Windows-Latest.zip"
        "File" = "$TempDir\extDB3.zip"
        "Extract" = "$TempDir\extDB3"
    }
    "VCRedist" = @{
        "Url" = "https://aka.ms/vs/16/release/vc_redist.x64.exe"
        "File" = "$TempDir\vc_redist.x64.exe"
    }
}

Write-Host "[STEP 1] Downloading required components..." -ForegroundColor Magenta
Write-Host ""

# Download extDB3
if (Download-File -Url $Downloads.extDB3.Url -OutputPath $Downloads.extDB3.File) {
    if (Extract-ZipFile -ZipPath $Downloads.extDB3.File -ExtractPath $Downloads.extDB3.Extract) {
        
        # Copy extDB3 files to server directory
        $ExtDB3Files = @(
            "extDB3_x64.dll",
            "tbbmalloc_x64.dll"
        )
        
        foreach ($File in $ExtDB3Files) {
            $SourcePath = Get-ChildItem -Path $Downloads.extDB3.Extract -Name $File -Recurse | Select-Object -First 1
            if ($SourcePath) {
                $FullSourcePath = Join-Path $Downloads.extDB3.Extract $SourcePath
                $DestPath = Join-Path $ServerPath "extDB3\$File"
                Copy-Item -Path $FullSourcePath -Destination $DestPath -Force
                Write-Host "[OK] Copied: $File" -ForegroundColor Green
            }
        }
        
        # Copy @extDB3 mod folder
        $ModSource = Get-ChildItem -Path $Downloads.extDB3.Extract -Name "@extDB3" -Recurse -Directory | Select-Object -First 1
        if ($ModSource) {
            $FullModPath = $ModSource.FullName
            $ModDest = Join-Path $ServerPath "@extDB3"
            Copy-Item -Path $FullModPath -Destination $ModDest -Recurse -Force
            Write-Host "[OK] Copied: @extDB3 mod folder" -ForegroundColor Green
        }
    }
}

# Download Visual C++ Redistributable
Write-Host ""
Write-Host "[STEP 2] Downloading Visual C++ Redistributable..." -ForegroundColor Magenta
if (Download-File -Url $Downloads.VCRedist.Url -OutputPath $Downloads.VCRedist.File) {
    Write-Host "[INFO] Installing Visual C++ Redistributable..." -ForegroundColor Yellow
    Start-Process -FilePath $Downloads.VCRedist.File -ArgumentList "/quiet" -Wait
    Write-Host "[OK] Visual C++ Redistributable installed" -ForegroundColor Green
}

Write-Host ""
Write-Host "[STEP 3] Setting up configuration files..." -ForegroundColor Magenta

# Create extDB3 configuration
$ExtDB3Config = @"
[Database]
Type = MySQL
Name = Database

Host = 127.0.0.1
Port = 3306
Username = edenrp_user
Password = edenrp_password
Database = edenrp

Compress = true
Secure Auth = true

[Logging]
Version = 1
Flush = true

[Log]
Flush = true
"@

$ConfigPath = Join-Path $ServerPath "extDB3\extdb3-conf.ini"
$ExtDB3Config | Out-File -FilePath $ConfigPath -Encoding UTF8
Write-Host "[OK] Created: extdb3-conf.ini" -ForegroundColor Green

Write-Host ""
Write-Host "[STEP 4] MySQL Installation..." -ForegroundColor Magenta
Write-Host "[INFO] Opening MySQL download page..." -ForegroundColor Yellow
Write-Host "[INFO] Please download and install MySQL manually" -ForegroundColor Yellow
Start-Process "https://dev.mysql.com/downloads/mysql/"

Write-Host ""
Write-Host "===============================================" -ForegroundColor Green
Write-Host "    AUTOMATED SETUP COMPLETE!" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host ""
Write-Host "COMPLETED AUTOMATICALLY:" -ForegroundColor Green
Write-Host "✓ Downloaded and installed extDB3" -ForegroundColor Green
Write-Host "✓ Downloaded and installed Visual C++ Redistributable" -ForegroundColor Green
Write-Host "✓ Created server configuration files" -ForegroundColor Green
Write-Host "✓ Set up directory structure" -ForegroundColor Green
Write-Host ""
Write-Host "MANUAL STEPS REMAINING:" -ForegroundColor Yellow
Write-Host "1. Install MySQL from the opened browser page" -ForegroundColor Yellow
Write-Host "2. Run the database setup script: setup_database.sql" -ForegroundColor Yellow
Write-Host "3. Import the full database schema from EdenRP.Altis\database\edenrp_schema.sql" -ForegroundColor Yellow
Write-Host "4. Start your server with: start_server.bat" -ForegroundColor Yellow
Write-Host ""
Write-Host "Your EdenRP server is almost ready!" -ForegroundColor Cyan
Write-Host ""

# Cleanup
Remove-Item -Path $TempDir -Recurse -Force -ErrorAction SilentlyContinue

Read-Host "Press Enter to exit"
