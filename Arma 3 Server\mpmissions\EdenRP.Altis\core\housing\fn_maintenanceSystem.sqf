/*
    File: fn_maintenanceSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages property maintenance system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_propertyId", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_propertyCondition", [], true];
        _player setVariable ["eden_maintenanceHistory", [], true];
        true
    };
    case "inspectProperty": {
        _owned = _player getVariable ["eden_ownedProperties", []];
        if !(_propertyId in _owned) exitWith {
            ["You don't own this property!"] call EDEN_fnc_showHint;
            false
        };
        
        _condition = random 100;
        _conditions = _player getVariable ["eden_propertyCondition", []];
        
        _found = false;
        {
            if ((_x select 0) == _propertyId) then {
                _x set [1, _condition];
                _conditions set [_forEachIndex, _x];
                _found = true;
            };
        } forEach _conditions;
        
        if (!_found) then {
            _conditions pushBack [_propertyId, _condition];
        };
        
        _player setVariable ["eden_propertyCondition", _conditions, true];
        
        [format ["Property condition: %1%%", floor _condition]] call EDEN_fnc_showHint;
        true
    };
    case "repairProperty": {
        _owned = _player getVariable ["eden_ownedProperties", []];
        if !(_propertyId in _owned) exitWith {
            ["You don't own this property!"] call EDEN_fnc_showHint;
            false
        };
        
        _repairCost = 500;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _repairCost) exitWith {
            [format ["Not enough money! Need $%1", _repairCost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _repairCost), true];
        
        _conditions = _player getVariable ["eden_propertyCondition", []];
        {
            if ((_x select 0) == _propertyId) then {
                _x set [1, 100];
                _conditions set [_forEachIndex, _x];
            };
        } forEach _conditions;
        _player setVariable ["eden_propertyCondition", _conditions, true];
        
        _history = _player getVariable ["eden_maintenanceHistory", []];
        _history pushBack [_propertyId, "Repair", _repairCost, time];
        _player setVariable ["eden_maintenanceHistory", _history, true];
        
        [format ["Property repaired for $%1", _repairCost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
