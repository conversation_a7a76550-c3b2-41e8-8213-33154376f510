/*
    File: fn_mapSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages map system and markers.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_mapMarkers", [], true];
        _player setVariable ["eden_mapSettings", ["showJobs", true], true];
        true
    };
    case "addMarker": {
        params ["", "", ["_position", [0,0,0], [[]]], ["_text", "", [""]], ["_type", "mil_dot", [""]]];
        
        _markerName = format["eden_marker_%1_%2", getPlayerUID _player, random 9999];
        _marker = createMarkerLocal [_markerName, _position];
        _marker setMarkerTypeLocal _type;
        _marker setMarkerTextLocal _text;
        _marker setMarkerColorLocal "ColorBlue";
        
        _markers = _player getVariable ["eden_mapMarkers", []];
        _markers pushBack [_markerName, _position, _text, _type];
        _player setVariable ["eden_mapMarkers", _markers, true];
        
        [format ["Marker added: %1", _text]] call EDEN_fnc_showHint;
        true
    };
    case "removeMarker": {
        params ["", "", ["_markerName", "", [""]]];
        
        deleteMarkerLocal _markerName;
        
        _markers = _player getVariable ["eden_mapMarkers", []];
        for "_i" from (count _markers - 1) to 0 step -1 do {
            if ((_markers select _i select 0) == _markerName) then {
                _markers deleteAt _i;
            };
        };
        _player setVariable ["eden_mapMarkers", _markers, true];
        
        ["Marker removed"] call EDEN_fnc_showHint;
        true
    };
    case "showJobLocations": {
        _jobLocations = [
            ["Police Station", [3540, 13100, 0], "b_hq"],
            ["Hospital", [3545, 13270, 0], "b_med"],
            ["Mechanic Shop", [3525, 13200, 0], "b_maint"],
            ["Bank", [3560, 13180, 0], "b_unknown"]
        ];
        
        {
            _markerName = format["job_%1", _forEachIndex];
            _marker = createMarkerLocal [_markerName, (_x select 1)];
            _marker setMarkerTypeLocal (_x select 2);
            _marker setMarkerTextLocal (_x select 0);
            _marker setMarkerColorLocal "ColorGreen";
        } forEach _jobLocations;
        
        ["Job locations shown on map"] call EDEN_fnc_showHint;
        true
    };
    case "clearMarkers": {
        _markers = _player getVariable ["eden_mapMarkers", []];
        {
            deleteMarkerLocal (_x select 0);
        } forEach _markers;
        
        _player setVariable ["eden_mapMarkers", [], true];
        ["All markers cleared"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
