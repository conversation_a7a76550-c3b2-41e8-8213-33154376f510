/*
    File: fn_inflationSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages inflation and economic adjustment system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_inflationRate") then {
            eden_inflationRate = 0.02; // 2% base inflation
            publicVariable "eden_inflationRate";
        };
        if (isNil "eden_lastInflationUpdate") then {
            eden_lastInflationUpdate = time;
            publicVariable "eden_lastInflationUpdate";
        };
        true
    };
    case "updatePrices": {
        if ((time - eden_lastInflationUpdate) < 3600) exitWith { false }; // Update hourly
        
        _inflationFactor = 1 + eden_inflationRate;
        
        // Update commodity prices
        {
            _price = _x select 1;
            _newPrice = floor(_price * _inflationFactor);
            _x set [1, _newPrice];
            eden_commodityPrices set [_forEachIndex, _x];
        } forEach eden_commodityPrices;
        publicVariable "eden_commodityPrices";
        
        // Update stock prices
        {
            _price = _x select 1;
            _volatility = (random 0.1) - 0.05; // -5% to +5% volatility
            _newPrice = floor(_price * (_inflationFactor + _volatility));
            _x set [1, _newPrice max 10]; // Minimum price of $10
            eden_stockPrices set [_forEachIndex, _x];
        } forEach eden_stockPrices;
        publicVariable "eden_stockPrices";
        
        eden_lastInflationUpdate = time;
        publicVariable "eden_lastInflationUpdate";
        
        {
            [format ["Economic update: Inflation rate %1%%", floor(eden_inflationRate * 100)]] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach allPlayers;
        
        true
    };
    case "adjustInflation": {
        params ["", "", ["_newRate", 0.02, [0]]];
        
        eden_inflationRate = _newRate max 0 min 0.1; // 0% to 10% max
        publicVariable "eden_inflationRate";
        
        [format ["Inflation rate adjusted to %1%%", floor(eden_inflationRate * 100)]] call EDEN_fnc_showHint;
        true
    };
    case "economicCrisis": {
        eden_inflationRate = 0.08; // 8% crisis inflation
        publicVariable "eden_inflationRate";
        
        // Crash stock prices
        {
            _price = _x select 1;
            _newPrice = floor(_price * 0.7); // 30% crash
            _x set [1, _newPrice max 10];
            eden_stockPrices set [_forEachIndex, _x];
        } forEach eden_stockPrices;
        publicVariable "eden_stockPrices";
        
        {
            ["ECONOMIC CRISIS: Markets crashed!"] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach allPlayers;
        
        true
    };
    case "economicBoom": {
        eden_inflationRate = 0.01; // 1% low inflation
        publicVariable "eden_inflationRate";
        
        // Boost stock prices
        {
            _price = _x select 1;
            _newPrice = floor(_price * 1.3); // 30% boom
            _x set [1, _newPrice];
            eden_stockPrices set [_forEachIndex, _x];
        } forEach eden_stockPrices;
        publicVariable "eden_stockPrices";
        
        {
            ["ECONOMIC BOOM: Markets surging!"] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach allPlayers;
        
        true
    };
    default { false };
};
