/*
    File: fn_territorySystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages gang territory system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_territories") then {
            eden_territories = [];
            publicVariable "eden_territories";
        };
        true
    };
    case "claimTerritory": {
        _gang = _player getVariable ["eden_gang", ""];
        if (_gang == "") exitWith {
            ["You must be in a gang!"] call EDEN_fnc_showHint;
            false
        };
        
        _pos = getPos _player;
        _territory = [_pos, _gang, time];
        eden_territories pushBack _territory;
        publicVariable "eden_territories";
        
        [format ["Territory claimed for %1!", _gang]] call EDEN_fnc_showHint;
        true
    };
    case "defendTerritory": {
        ["Defending territory!"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
