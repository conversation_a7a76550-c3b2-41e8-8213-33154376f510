/*
    File: fn_reportSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages player report system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON>ull]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_reports") then {
            eden_reports = [];
            publicVariable "eden_reports";
        };
        _player setVariable ["eden_reportHistory", [], true];
        true
    };
    case "submitReport": {
        params ["", "", "", ["_reason", "", [""]], ["_description", "", [""]]];
        
        if (isNull _target || _reason == "") exitWith { false };
        
        _reportID = str(random 999999);
        _report = [_reportID, getPlayerUID _player, name _player, getPlayerUID _target, name _target, _reason, _description, time, "Open"];
        eden_reports pushBack _report;
        publicVariable "eden_reports";
        
        _history = _player getVariable ["eden_reportHistory", []];
        _history pushBack [_reportID, name _target, _reason, time];
        _player setVariable ["eden_reportHistory", _history, true];
        
        [format ["Report submitted against %1 (ID: %2)", name _target, _reportID]] call EDEN_fnc_showHint;
        
        // Notify admins
        {
            if (getPlayerUID _x in eden_adminList) then {
                [format ["New report: %1 reported %2 for %3", name _player, name _target, _reason]] remoteExec ["EDEN_fnc_showHint", _x];
            };
        } forEach allPlayers;
        
        true
    };
    case "handleReport": {
        params ["", "", "", ["_reportID", "", [""]], ["_action", "close", [""]]];
        
        if (!(getPlayerUID _player in eden_adminList)) exitWith { false };
        
        _reportIndex = -1;
        {
            if ((_x select 0) == _reportID) then { _reportIndex = _forEachIndex; };
        } forEach eden_reports;
        
        if (_reportIndex == -1) exitWith {
            ["Report not found"] call EDEN_fnc_showHint;
            false
        };
        
        _report = eden_reports select _reportIndex;
        _report set [8, _action];
        eden_reports set [_reportIndex, _report];
        publicVariable "eden_reports";
        
        [format ["Report %1 %2", _reportID, _action]] call EDEN_fnc_showHint;
        true
    };
    case "viewReports": {
        if (!(getPlayerUID _player in eden_adminList)) exitWith { false };
        
        _openReports = [];
        {
            if ((_x select 8) == "Open") then { _openReports pushBack _x; };
        } forEach eden_reports;
        
        [format ["Open reports: %1", count _openReports]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
