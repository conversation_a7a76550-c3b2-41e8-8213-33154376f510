/*
    File: fn_taxationSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages taxation system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_taxRates") then {
            eden_taxRates = [
                ["income", 0.15],
                ["property", 0.02],
                ["sales", 0.08],
                ["business", 0.20]
            ];
            publicVariable "eden_taxRates";
        };
        _player setVariable ["eden_taxesOwed", 0, true];
        _player setVariable ["eden_taxHistory", [], true];
        true
    };
    case "calculateIncomeTax": {
        params ["", "", ["_income", 0, [0]]];
        
        _taxRate = 0.15;
        {
            if ((_x select 0) == "income") then { _taxRate = _x select 1; };
        } forEach eden_taxRates;
        
        _taxOwed = floor(_income * _taxRate);
        _currentTax = _player getVariable ["eden_taxesOwed", 0];
        _player setVariable ["eden_taxesOwed", (_currentTax + _taxOwed), true];
        
        [format ["Income tax calculated: $%1 (%2%%)", _taxOwed, floor(_taxRate * 100)]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "payTaxes": {
        _taxesOwed = _player getVariable ["eden_taxesOwed", 0];
        if (_taxesOwed <= 0) exitWith {
            ["No taxes owed"] call EDEN_fnc_showHint;
            false
        };
        
        _cash = _player getVariable ["eden_cash", 0];
        if (_cash < _taxesOwed) exitWith {
            [format ["Not enough money! Need $%1", _taxesOwed]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _taxesOwed), true];
        
        _history = _player getVariable ["eden_taxHistory", []];
        _history pushBack ["payment", _taxesOwed, time];
        _player setVariable ["eden_taxHistory", _history, true];
        
        _player setVariable ["eden_taxesOwed", 0, true];
        
        [format ["Taxes paid: $%1", _taxesOwed]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "propertyTax": {
        _properties = _player getVariable ["eden_ownedProperties", []];
        _totalTax = 0;
        
        _taxRate = 0.02;
        {
            if ((_x select 0) == "property") then { _taxRate = _x select 1; };
        } forEach eden_taxRates;
        
        {
            _propertyValue = 50000; // Base property value
            _tax = floor(_propertyValue * _taxRate);
            _totalTax = _totalTax + _tax;
        } forEach _properties;
        
        if (_totalTax > 0) then {
            _currentTax = _player getVariable ["eden_taxesOwed", 0];
            _player setVariable ["eden_taxesOwed", (_currentTax + _totalTax), true];
            
            [format ["Property tax assessed: $%1", _totalTax]] call EDEN_fnc_showHint;
            [_player] call EDEN_fnc_savePlayerData;
        };
        
        true
    };
    case "businessTax": {
        _businessIncome = _player getVariable ["eden_businessIncome", 0];
        if (_businessIncome <= 0) exitWith { false };
        
        _taxRate = 0.20;
        {
            if ((_x select 0) == "business") then { _taxRate = _x select 1; };
        } forEach eden_taxRates;
        
        _taxOwed = floor(_businessIncome * _taxRate);
        _currentTax = _player getVariable ["eden_taxesOwed", 0];
        _player setVariable ["eden_taxesOwed", (_currentTax + _taxOwed), true];
        
        [format ["Business tax calculated: $%1", _taxOwed]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
