/*
    File: fn_chopShop.sqf
    Author: EdenRP Development Team
    
    Description:
    Allows players to sell stolen vehicles at chop shops.
    
    Parameters:
    0: OBJECT - Vehicle to chop
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if chop was successful
*/

params [
    ["_vehicle", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (isNull _vehicle || isNull _player) exitWith { false };

if (_player distance _vehicle > 10) exitWith {
    ["Vehicle is too far away!"] call EDEN_fnc_showHint;
    false
};

if (!(_vehicle isKindOf "LandVehicle") && !(_vehicle isKindOf "Air")) exitWith {
    ["This vehicle cannot be chopped!"] call EDEN_fnc_showHint;
    false
};

if (!alive _vehicle) exitWith {
    ["Cannot chop destroyed vehicles!"] call EDEN_fnc_showHint;
    false
};

// Check if near chop shop (specific locations)
_chopShopLocations = [
    [4500, 4500, 0], // Example chop shop location 1
    [8500, 8500, 0], // Example chop shop location 2
    [12500, 12500, 0] // Example chop shop location 3
];

_nearChopShop = false;
{
    if (_player distance _x < 50) then {
        _nearChopShop = true;
    };
} forEach _chopShopLocations;

if (!_nearChopShop) exitWith {
    ["You must be at a chop shop to sell stolen vehicles!"] call EDEN_fnc_showHint;
    false
};

// Check if vehicle is stolen (not owned by player)
_vehicleOwner = _vehicle getVariable ["eden_ownerUID", ""];
_playerUID = getPlayerUID _player;

if (_vehicleOwner == _playerUID) exitWith {
    ["You cannot chop your own vehicle!"] call EDEN_fnc_showHint;
    false
};

// Check if it's a police/medical vehicle
if (_vehicle getVariable ["eden_isPoliceVehicle", false] || _vehicle getVariable ["eden_isMedicalVehicle", false]) exitWith {
    ["Emergency service vehicles cannot be chopped!"] call EDEN_fnc_showHint;
    false
};

// Calculate chop value based on vehicle type
_vehicleType = typeOf _vehicle;
_baseValue = switch (true) do {
    case (_vehicle isKindOf "Car"): { 5000 };
    case (_vehicle isKindOf "Truck"): { 8000 };
    case (_vehicle isKindOf "Tank"): { 25000 };
    case (_vehicle isKindOf "Helicopter"): { 50000 };
    case (_vehicle isKindOf "Plane"): { 75000 };
    default { 2000 };
};

// Apply condition modifier
_condition = 1 - (damage _vehicle);
_chopValue = round(_baseValue * _condition * 0.3); // 30% of base value

// Eject all passengers
{
    _x action ["GetOut", _vehicle];
} forEach crew _vehicle;

sleep 1;

// Start chopping process
[_player, "Acts_carFixingWheel"] remoteExec ["switchMove"];
[format ["Chopping vehicle... Estimated value: $%1", _chopValue]] call EDEN_fnc_showHint;

// Chopping takes time based on vehicle size
_chopTime = switch (true) do {
    case (_vehicle isKindOf "Car"): { 15 };
    case (_vehicle isKindOf "Truck"): { 25 };
    case (_vehicle isKindOf "Tank"): { 45 };
    case (_vehicle isKindOf "Helicopter"): { 35 };
    case (_vehicle isKindOf "Plane"): { 60 };
    default { 10 };
};

// Progress updates during chopping
_startTime = time;
while {(time - _startTime) < _chopTime} do {
    _progress = ((time - _startTime) / _chopTime) * 100;
    [format ["Chopping progress: %1%%", round _progress]] call EDEN_fnc_showHint;
    sleep 2;
};

[_player, ""] remoteExec ["switchMove"];

// Complete the chop
deleteVehicle _vehicle;

// Pay the player
_playerMoney = _player getVariable ["eden_cash", 0];
_player setVariable ["eden_cash", (_playerMoney + _chopValue), true];

// Add criminal activity
_wantedLevel = _player getVariable ["eden_wantedLevel", 0];
_player setVariable ["eden_wantedLevel", (_wantedLevel + 3), true]; // Major crime

// Add to criminal record
_criminalRecord = _player getVariable ["eden_criminalRecord", []];
_crimeRecord = [
    time,
    "Chop Shop",
    "Vehicle theft and destruction",
    _vehicleType
];
_criminalRecord pushBack _crimeRecord;
_player setVariable ["eden_criminalRecord", _criminalRecord, true];

// Add experience
_expGained = 100;
_currentExp = _player getVariable ["eden_experience", 0];
_player setVariable ["eden_experience", (_currentExp + _expGained), true];

// Check for level up
_newLevel = floor((_currentExp + _expGained) / 1000) + 1;
_currentLevel = _player getVariable ["eden_playerLevel", 1];
if (_newLevel > _currentLevel) then {
    _player setVariable ["eden_playerLevel", _newLevel, true];
    [format ["Level up! You are now level %1", _newLevel], 5] call EDEN_fnc_showNotification;
};

// Alert police about chop shop activity
{
    if (_x getVariable ["eden_isPolice", false]) then {
        [
            "Chop Shop Activity",
            format ["Suspicious vehicle activity reported near %1", mapGridPosition _player],
            10,
            "warning"
        ] remoteExec ["EDEN_fnc_showNotification", _x];
    };
} forEach allPlayers;

[format ["Vehicle chopped successfully! Received $%1 (+%2 XP)", _chopValue, _expGained]] call EDEN_fnc_showHint;

// Log the crime
[format ["[EDEN] Player %1 chopped vehicle %2 for $%3", name _player, _vehicleType, _chopValue], "WARN", "CRIME"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
