/*
    File: fn_anomalyDetection.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages anomaly detection system.
*/

params [["_action", "init", [""]]];

switch (_action) do {
    case "init": {
        if (isServer) then {
            if (isNil "eden_playerBaselines") then {
                eden_playerBaselines = [];
            };
            if (isNil "eden_anomalyThresholds") then {
                eden_anomalyThresholds = [
                    ["movement_speed", 2.0],
                    ["money_gain_rate", 3.0],
                    ["action_frequency", 2.5],
                    ["location_changes", 4.0]
                ];
                publicVariable "eden_anomalyThresholds";
            };
        };
        true
    };
    case "updateBaseline": {
        params ["", ["_player", objNull, [objNull]]];
        
        if (!isServer || isNull _player) exitWith { false };
        
        _playerUID = getPlayerUID _player;
        _currentTime = time;
        
        // Calculate current metrics
        _currentPos = getPos _player;
        _lastPos = _player getVariable ["eden_lastAnomalyPos", _currentPos];
        _distance = _lastPos distance _currentPos;
        _timeDiff = _currentTime - (_player getVariable ["eden_lastAnomalyCheck", _currentTime]);
        
        if (_timeDiff > 0) then {
            _speed = _distance / _timeDiff;
            _moneyGain = (_player getVariable ["eden_cash", 0]) - (_player getVariable ["eden_lastCashCheck", 0]);
            _moneyRate = if (_timeDiff > 0) then { _moneyGain / _timeDiff } else { 0 };
            
            // Find or create baseline record
            _baselineRecord = [];
            _recordIndex = -1;
            
            {
                if ((_x select 0) == _playerUID) then {
                    _baselineRecord = _x;
                    _recordIndex = _forEachIndex;
                };
            } forEach eden_playerBaselines;
            
            if (count _baselineRecord == 0) then {
                _baselineRecord = [_playerUID, [_speed], [_moneyRate], 1];
                eden_playerBaselines pushBack _baselineRecord;
            } else {
                _speedHistory = _baselineRecord select 1;
                _moneyHistory = _baselineRecord select 2;
                _sampleCount = _baselineRecord select 3;
                
                _speedHistory pushBack _speed;
                _moneyHistory pushBack _moneyRate;
                
                // Keep only last 50 samples
                if (count _speedHistory > 50) then { _speedHistory deleteAt 0; };
                if (count _moneyHistory > 50) then { _moneyHistory deleteAt 0; };
                
                _baselineRecord set [1, _speedHistory];
                _baselineRecord set [2, _moneyHistory];
                _baselineRecord set [3, _sampleCount + 1];
                
                eden_playerBaselines set [_recordIndex, _baselineRecord];
            };
            
            _player setVariable ["eden_lastAnomalyPos", _currentPos, false];
            _player setVariable ["eden_lastAnomalyCheck", _currentTime, false];
            _player setVariable ["eden_lastCashCheck", _player getVariable ["eden_cash", 0], false];
        };
        
        true
    };
    case "detectAnomalies": {
        params ["", ["_player", objNull, [objNull]]];
        
        if (!isServer || isNull _player) exitWith { false };
        
        _playerUID = getPlayerUID _player;
        
        // Find baseline record
        _baselineRecord = [];
        {
            if ((_x select 0) == _playerUID) then { _baselineRecord = _x; };
        } forEach eden_playerBaselines;
        
        if (count _baselineRecord == 0) exitWith { false };
        
        _speedHistory = _baselineRecord select 1;
        _moneyHistory = _baselineRecord select 2;
        
        if (count _speedHistory < 10) exitWith { false }; // Need enough samples
        
        // Calculate averages and standard deviations
        _avgSpeed = 0;
        { _avgSpeed = _avgSpeed + _x; } forEach _speedHistory;
        _avgSpeed = _avgSpeed / (count _speedHistory);
        
        _avgMoney = 0;
        { _avgMoney = _avgMoney + _x; } forEach _moneyHistory;
        _avgMoney = _avgMoney / (count _moneyHistory);
        
        // Check current values against baseline
        _currentSpeed = _speedHistory select ((count _speedHistory) - 1);
        _currentMoney = _moneyHistory select ((count _moneyHistory) - 1);
        
        _speedDeviation = if (_avgSpeed > 0) then { _currentSpeed / _avgSpeed } else { 1 };
        _moneyDeviation = if (_avgMoney > 0) then { _currentMoney / _avgMoney } else { 1 };
        
        // Check thresholds
        {
            _anomalyType = _x select 0;
            _threshold = _x select 1;
            
            _isAnomaly = switch (_anomalyType) do {
                case "movement_speed": { _speedDeviation > _threshold };
                case "money_gain_rate": { _moneyDeviation > _threshold };
                default { false };
            };
            
            if (_isAnomaly) then {
                ["reportAnomaly", _player, _anomalyType, _speedDeviation max _moneyDeviation] call EDEN_fnc_anomalyDetection;
            };
        } forEach eden_anomalyThresholds;
        
        true
    };
    case "reportAnomaly": {
        params ["", ["_player", objNull, [objNull]], ["_anomalyType", "", [""]], ["_severity", 1.0, [0]]];
        
        if (!isServer) exitWith { false };
        
        _severityLevel = switch (true) do {
            case (_severity > 5.0): { "CRITICAL" };
            case (_severity > 3.0): { "HIGH" };
            case (_severity > 2.0): { "MEDIUM" };
            default { "LOW" };
        };
        
        // Log the anomaly
        ["logSecurityEvent", _player, "anomaly_detected", _severityLevel, format["Type: %1, Severity: %2x", _anomalyType, _severity]] call EDEN_fnc_auditSystem;
        
        // Notify admins for high severity anomalies
        if (_severityLevel in ["HIGH", "CRITICAL"]) then {
            {
                if (getPlayerUID _x in eden_adminList) then {
                    [format ["⚠️ ANOMALY DETECTED: %1 - %2 (%3x normal)", name _player, _anomalyType, _severity]] remoteExec ["EDEN_fnc_showHint", _x];
                };
            } forEach allPlayers;
        };
        
        // Take action for critical anomalies
        if (_severityLevel == "CRITICAL") then {
            ["detectIntrusion", _player, "critical_anomaly"] call EDEN_fnc_intrusionDetection;
        };
        
        true
    };
    case "getPlayerBaseline": {
        params ["", ["_playerUID", "", [""]]];
        
        if (!isServer) exitWith { [] };
        
        _baseline = [];
        {
            if ((_x select 0) == _playerUID) then { _baseline = _x; };
        } forEach eden_playerBaselines;
        
        _baseline
    };
    case "resetBaseline": {
        params ["", ["_playerUID", "", [""]]];
        
        if (!isServer) exitWith { false };
        
        for "_i" from (count eden_playerBaselines - 1) to 0 step -1 do {
            if (((eden_playerBaselines select _i) select 0) == _playerUID) then {
                eden_playerBaselines deleteAt _i;
            };
        };
        
        true
    };
    default { false };
};
