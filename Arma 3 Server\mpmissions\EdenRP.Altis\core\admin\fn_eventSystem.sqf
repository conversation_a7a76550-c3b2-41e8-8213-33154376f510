/*
    File: fn_eventSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages admin event system.
*/

params [["_admin", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _admin) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_activeEvents") then {
            eden_activeEvents = [];
            publicVariable "eden_activeEvents";
        };
        true
    };
    case "startEvent": {
        params ["", "", ["_eventType", "race", [""]], ["_location", [0,0,0], [[]]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _eventID = str(random 999999);
        _event = [_eventID, _eventType, _location, time, name _admin, []];
        eden_activeEvents pushBack _event;
        publicVariable "eden_activeEvents";
        
        {
            [format ["EVENT STARTED: %1 at %2! Join now!", _eventType, mapGridPosition _location]] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach allPlayers;
        
        [format ["Started %1 event (ID: %2)", _eventType, _eventID]] call EDEN_fnc_showHint;
        true
    };
    case "endEvent": {
        params ["", "", ["_eventID", "", [""]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _eventIndex = -1;
        {
            if ((_x select 0) == _eventID) then { _eventIndex = _forEachIndex; };
        } forEach eden_activeEvents;
        
        if (_eventIndex == -1) exitWith { false };
        
        _event = eden_activeEvents select _eventIndex;
        eden_activeEvents deleteAt _eventIndex;
        publicVariable "eden_activeEvents";
        
        {
            [format ["EVENT ENDED: %1", (_event select 1)]] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach allPlayers;
        
        [format ["Ended event: %1", (_event select 1)]] call EDEN_fnc_showHint;
        true
    };
    case "joinEvent": {
        params ["", "", ["_eventID", "", [""]]];
        
        _eventIndex = -1;
        {
            if ((_x select 0) == _eventID) then { _eventIndex = _forEachIndex; };
        } forEach eden_activeEvents;
        
        if (_eventIndex == -1) exitWith { false };
        
        _event = eden_activeEvents select _eventIndex;
        _participants = _event select 5;
        
        if (!(getPlayerUID _admin in _participants)) then {
            _participants pushBack getPlayerUID _admin;
            _event set [5, _participants];
            eden_activeEvents set [_eventIndex, _event];
            publicVariable "eden_activeEvents";
            
            [format ["Joined event: %1", (_event select 1)]] call EDEN_fnc_showHint;
        };
        
        true
    };
    case "rewardParticipants": {
        params ["", "", ["_eventID", "", [""]], ["_reward", 5000, [0]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _eventIndex = -1;
        {
            if ((_x select 0) == _eventID) then { _eventIndex = _forEachIndex; };
        } forEach eden_activeEvents;
        
        if (_eventIndex == -1) exitWith { false };
        
        _event = eden_activeEvents select _eventIndex;
        _participants = _event select 5;
        
        {
            _participant = objNull;
            {
                if (getPlayerUID _x == _participants select _forEachIndex) then { _participant = _x; };
            } forEach allPlayers;
            
            if (!isNull _participant) then {
                _cash = _participant getVariable ["eden_cash", 0];
                _participant setVariable ["eden_cash", (_cash + _reward), true];
                [format ["Event reward: $%1", _reward]] remoteExec ["EDEN_fnc_showHint", _participant];
            };
        } forEach _participants;
        
        [format ["Rewarded %1 participants with $%2 each", count _participants, _reward]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
