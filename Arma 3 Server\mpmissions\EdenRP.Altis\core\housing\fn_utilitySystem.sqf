/*
    File: fn_utilitySystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages property utility system (electricity, water, gas).
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_propertyId", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_utilityBills", [], true];
        _player setVariable ["eden_utilityUsage", 0, true];
        true
    };
    case "payUtilities": {
        _owned = _player getVariable ["eden_ownedProperties", []];
        if !(_propertyId in _owned) exitWith {
            ["You don't own this property!"] call EDEN_fnc_showHint;
            false
        };
        
        _billAmount = 150;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _billAmount) exitWith {
            [format ["Not enough money! Need $%1", _billAmount]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _billAmount), true];
        
        _bills = _player getVariable ["eden_utilityBills", []];
        _bill = [_propertyId, _billAmount, time, "Paid"];
        _bills pushBack _bill;
        _player setVariable ["eden_utilityBills", _bills, true];
        
        [format ["Paid utility bill: $%1", _billAmount]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "upgradeUtilities": {
        _owned = _player getVariable ["eden_ownedProperties", []];
        if !(_propertyId in _owned) exitWith {
            ["You don't own this property!"] call EDEN_fnc_showHint;
            false
        };
        
        _upgradeCost = 1000;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _upgradeCost) exitWith {
            [format ["Not enough money! Need $%1", _upgradeCost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _upgradeCost), true];
        
        ["Utilities upgraded - reduced monthly costs"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
