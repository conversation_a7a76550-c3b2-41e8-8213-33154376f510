/*
    File: fn_authorizationSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages authorization system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isServer) then {
            if (isNil "eden_permissions") then {
                eden_permissions = [
                    ["admin", ["all"]],
                    ["moderator", ["kick", "mute", "teleport", "spectate"]],
                    ["police", ["arrest", "search", "ticket", "impound"]],
                    ["ems", ["revive", "heal", "transport"]],
                    ["mechanic", ["repair", "refuel", "tow"]],
                    ["civilian", ["basic"]]
                ];
                publicVariable "eden_permissions";
            };
        };
        true
    };
    case "hasPermission": {
        params ["", "", ["_permission", "basic", [""]]];
        
        _playerUID = getPlayerUID _player;
        _playerJob = _player getVariable ["eden_job", "civilian"];
        
        // Check if player is admin
        if (_playerUID in eden_adminList) exitWith { true };
        
        // Check job-based permissions
        _hasPermission = false;
        {
            if ((_x select 0) == _playerJob) then {
                _jobPermissions = _x select 1;
                if ("all" in _jobPermissions || _permission in _jobPermissions) then {
                    _hasPermission = true;
                };
            };
        } forEach eden_permissions;
        
        _hasPermission
    };
    case "checkAccess": {
        params ["", "", ["_requiredPermission", "basic", [""]], ["_actionName", "unknown", [""]]];
        
        _hasAccess = [_player, "hasPermission", _requiredPermission] call EDEN_fnc_authorizationSystem;
        
        if (!_hasAccess) then {
            [format ["Access denied: %1 requires %2 permission", _actionName, _requiredPermission]] call EDEN_fnc_showHint;
            [format ["[AUTH] Access denied for %1: %2 (required: %3)", name _player, _actionName, _requiredPermission], "WARNING", "AUTH"] call EDEN_fnc_systemLogger;
        };
        
        _hasAccess
    };
    case "grantPermission": {
        params ["", "", ["_targetUID", "", [""]], ["_permission", "", [""]]];
        
        if (!(getPlayerUID _player in eden_adminList)) exitWith {
            ["Only admins can grant permissions"] call EDEN_fnc_showHint;
            false
        };
        
        if (!isServer) exitWith { false };
        
        // Find target player
        _targetPlayer = objNull;
        {
            if (getPlayerUID _x == _targetUID) then { _targetPlayer = _x; };
        } forEach allPlayers;
        
        if (isNull _targetPlayer) exitWith { false };
        
        _customPermissions = _targetPlayer getVariable ["eden_customPermissions", []];
        if (!(_permission in _customPermissions)) then {
            _customPermissions pushBack _permission;
            _targetPlayer setVariable ["eden_customPermissions", _customPermissions, true];
            
            [format ["Granted %1 permission to %2", _permission, name _targetPlayer]] call EDEN_fnc_showHint;
            [format ["Permission %1 granted to you by %2", _permission, name _player]] remoteExec ["EDEN_fnc_showHint", _targetPlayer];
        };
        
        true
    };
    case "revokePermission": {
        params ["", "", ["_targetUID", "", [""]], ["_permission", "", [""]]];
        
        if (!(getPlayerUID _player in eden_adminList)) exitWith {
            ["Only admins can revoke permissions"] call EDEN_fnc_showHint;
            false
        };
        
        if (!isServer) exitWith { false };
        
        // Find target player
        _targetPlayer = objNull;
        {
            if (getPlayerUID _x == _targetUID) then { _targetPlayer = _x; };
        } forEach allPlayers;
        
        if (isNull _targetPlayer) exitWith { false };
        
        _customPermissions = _targetPlayer getVariable ["eden_customPermissions", []];
        for "_i" from (count _customPermissions - 1) to 0 step -1 do {
            if ((_customPermissions select _i) == _permission) then {
                _customPermissions deleteAt _i;
            };
        };
        _targetPlayer setVariable ["eden_customPermissions", _customPermissions, true];
        
        [format ["Revoked %1 permission from %2", _permission, name _targetPlayer]] call EDEN_fnc_showHint;
        [format ["Permission %1 revoked by %2", _permission, name _player]] remoteExec ["EDEN_fnc_showHint", _targetPlayer];
        
        true
    };
    case "listPermissions": {
        _playerJob = _player getVariable ["eden_job", "civilian"];
        _customPermissions = _player getVariable ["eden_customPermissions", []];
        
        _jobPermissions = [];
        {
            if ((_x select 0) == _playerJob) then {
                _jobPermissions = _x select 1;
            };
        } forEach eden_permissions;
        
        _allPermissions = _jobPermissions + _customPermissions;
        
        _permissionText = format["=== YOUR PERMISSIONS ===\nJob: %1\n", _playerJob];
        
        {
            _permissionText = _permissionText + format["- %1\n", _x];
        } forEach _allPermissions;
        
        [_permissionText] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
