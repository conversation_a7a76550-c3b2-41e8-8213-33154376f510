/*
    File: fn_securitySystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages property security system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_propertyId", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_securitySystems", [], true];
        _player setVariable ["eden_alarmActive", false, true];
        true
    };
    case "installSecurity": {
        _owned = _player getVariable ["eden_ownedProperties", []];
        if !(_propertyId in _owned) exitWith {
            ["You don't own this property!"] call EDEN_fnc_showHint;
            false
        };
        
        _cost = 2500;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _systems = _player getVariable ["eden_securitySystems", []];
        _system = [_propertyId, "Basic", time, true];
        _systems pushBack _system;
        _player setVariable ["eden_securitySystems", _systems, true];
        
        ["Security system installed"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "activateAlarm": {
        _systems = _player getVariable ["eden_securitySystems", []];
        _hasSystem = false;
        
        {
            if ((_x select 0) == _propertyId) then { _hasSystem = true; };
        } forEach _systems;
        
        if (!_hasSystem) exitWith {
            ["No security system installed!"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_alarmActive", true, true];
        ["Security alarm activated"] call EDEN_fnc_showHint;
        true
    };
    case "triggerAlarm": {
        _alarmActive = _player getVariable ["eden_alarmActive", false];
        if (!_alarmActive) exitWith { false };
        
        ["SECURITY BREACH DETECTED!"] call EDEN_fnc_showHint;
        
        {
            if (side _x == west) then {
                ["Security alarm triggered at property"] remoteExec ["EDEN_fnc_showHint", _x];
            };
        } forEach allPlayers;
        
        true
    };
    default { false };
};
