[Default]
Version = 1
Prepared Statement Cache = true

[getPlayerData]
SQL1_1 = SELECT * FROM eden_players WHERE player_id = ?
Number Of Inputs = 1
SQL1_INPUTS = 1

[insertPlayer]
SQL1_1 = INSERT INTO eden_players (player_id, name, cash, bank) VALUES (?, ?, ?, ?)
Number Of Inputs = 4
SQL1_INPUTS = 1,2,3,4

[updatePlayerData]
SQL1_1 = UPDATE eden_players SET name = ?, cash = ?, bank = ?, experience = ?, level = ?, reputation = ?, playtime = ?, cop_level = ?, medic_level = ?, admin_level = ?, cop_licenses = ?, civ_licenses = ?, med_licenses = ?, cop_gear = ?, med_gear = ?, civ_gear = ?, virtual_inventory = ?, physical_inventory = ?, skills = ?, achievements = ?, settings = ?, arrested = ?, jail_time = ?, wanted_level = ?, bounty = ?, gang_id = ?, gang_rank = ?, phone_number = ?, contacts = ?, messages = ?, last_seen = NOW() WHERE player_id = ?
Number Of Inputs = 31
SQL1_INPUTS = 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31

[updatePlayerCash]
SQL1_1 = UPDATE eden_players SET cash = ? WHERE player_id = ?
Number Of Inputs = 2
SQL1_INPUTS = 1,2

[updatePlayerBank]
SQL1_1 = UPDATE eden_players SET bank = ? WHERE player_id = ?
Number Of Inputs = 2
SQL1_INPUTS = 1,2

[updatePlayerExperience]
SQL1_1 = UPDATE eden_players SET experience = ?, level = ? WHERE player_id = ?
Number Of Inputs = 3
SQL1_INPUTS = 1,2,3

[updatePlayerGang]
SQL1_1 = UPDATE eden_players SET gang_id = ?, gang_rank = ? WHERE player_id = ?
Number Of Inputs = 3
SQL1_INPUTS = 1,2,3

[updatePlayerLicenses]
SQL1_1 = UPDATE eden_players SET cop_licenses = ?, civ_licenses = ?, med_licenses = ? WHERE player_id = ?
Number Of Inputs = 4
SQL1_INPUTS = 1,2,3,4

[updatePlayerGear]
SQL1_1 = UPDATE eden_players SET cop_gear = ?, med_gear = ?, civ_gear = ? WHERE player_id = ?
Number Of Inputs = 4
SQL1_INPUTS = 1,2,3,4

[updatePlayerInventory]
SQL1_1 = UPDATE eden_players SET virtual_inventory = ?, physical_inventory = ? WHERE player_id = ?
Number Of Inputs = 3
SQL1_INPUTS = 1,2,3

[updatePlayerSkills]
SQL1_1 = UPDATE eden_players SET skills = ?, achievements = ? WHERE player_id = ?
Number Of Inputs = 3
SQL1_INPUTS = 1,2,3

[updatePlayerWanted]
SQL1_1 = UPDATE eden_players SET wanted_level = ?, bounty = ? WHERE player_id = ?
Number Of Inputs = 3
SQL1_INPUTS = 1,2,3

[updatePlayerArrest]
SQL1_1 = UPDATE eden_players SET arrested = ?, jail_time = ? WHERE player_id = ?
Number Of Inputs = 3
SQL1_INPUTS = 1,2,3

[updatePlayerSettings]
SQL1_1 = UPDATE eden_players SET settings = ? WHERE player_id = ?
Number Of Inputs = 2
SQL1_INPUTS = 1,2

[updatePlayerPlaytime]
SQL1_1 = UPDATE eden_players SET playtime = playtime + ?, total_logins = total_logins + 1, last_seen = NOW() WHERE player_id = ?
Number Of Inputs = 2
SQL1_INPUTS = 1,2

[getTopPlayers]
SQL1_1 = SELECT name, experience, level, reputation, playtime FROM eden_players WHERE active = 1 ORDER BY experience DESC LIMIT ?
Number Of Inputs = 1
SQL1_INPUTS = 1

[searchPlayers]
SQL1_1 = SELECT player_id, name, last_seen, active FROM eden_players WHERE name LIKE ? ORDER BY last_seen DESC LIMIT 50
Number Of Inputs = 1
SQL1_INPUTS = 1

[banPlayer]
SQL1_1 = UPDATE eden_players SET banned = 1, ban_reason = ?, ban_expires = ? WHERE player_id = ?
Number Of Inputs = 3
SQL1_INPUTS = 1,2,3

[unbanPlayer]
SQL1_1 = UPDATE eden_players SET banned = 0, ban_reason = NULL, ban_expires = NULL WHERE player_id = ?
Number Of Inputs = 1
SQL1_INPUTS = 1

[checkBan]
SQL1_1 = SELECT banned, ban_reason, ban_expires FROM eden_players WHERE player_id = ?
Number Of Inputs = 1
SQL1_INPUTS = 1

[deleteInactivePlayers]
SQL1_1 = DELETE FROM eden_players WHERE active = 0 AND last_seen < DATE_SUB(NOW(), INTERVAL ? DAY)
Number Of Inputs = 1
SQL1_INPUTS = 1
