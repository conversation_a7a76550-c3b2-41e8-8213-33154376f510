/*
    File: fn_spawnSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages admin spawn system.
*/

params [["_admin", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _admin) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_spawnableItems") then {
            eden_spawnableItems = [
                ["Vehicle", "B_Heli_Light_01_F"],
                ["Weapon", "arifle_MX_F"],
                ["Item", "FirstAidKit"],
                ["Money", "cash_stack"]
            ];
            publicVariable "eden_spawnableItems";
        };
        true
    };
    case "spawnVehicle": {
        params ["", "", ["_vehicleClass", "B_Heli_Light_01_F", [""]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _position = getPos _admin;
        _position = [(_position select 0) + 5, (_position select 1) + 5, 0];
        
        _vehicle = createVehicle [_vehicleClass, _position, [], 0, "NONE"];
        _vehicle setVariable ["eden_adminSpawned", true, true];
        
        [format ["Spawned vehicle: %1", _vehicleClass]] call EDEN_fnc_showHint;
        true
    };
    case "spawnItem": {
        params ["", "", ["_item", "FirstAidKit", [""]], ["_quantity", 1, [0]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        [_admin, "addItem", _item, _quantity] call EDEN_fnc_itemSystem;
        
        [format ["Spawned %1x %2", _quantity, _item]] call EDEN_fnc_showHint;
        true
    };
    case "spawnWeapon": {
        params ["", "", ["_weapon", "arifle_MX_F", [""]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _admin addWeapon _weapon;
        _admin addMagazines [getArray(configFile >> "CfgWeapons" >> _weapon >> "magazines") select 0, 5];
        
        [format ["Spawned weapon: %1", _weapon]] call EDEN_fnc_showHint;
        true
    };
    case "cleanupSpawned": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _cleanedCount = 0;
        {
            if (_x getVariable ["eden_adminSpawned", false]) then {
                deleteVehicle _x;
                _cleanedCount = _cleanedCount + 1;
            };
        } forEach vehicles;
        
        [format ["Cleaned up %1 admin-spawned vehicles", _cleanedCount]] call EDEN_fnc_showHint;
        true
    };
    case "spawnAtPosition": {
        params ["", "", ["_object", "B_Heli_Light_01_F", [""]], ["_position", [0,0,0], [[]]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _spawned = createVehicle [_object, _position, [], 0, "NONE"];
        _spawned setVariable ["eden_adminSpawned", true, true];
        
        [format ["Spawned %1 at position %2", _object, _position]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
