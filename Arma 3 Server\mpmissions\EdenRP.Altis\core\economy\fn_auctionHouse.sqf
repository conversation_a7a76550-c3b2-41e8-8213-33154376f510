/*
    File: fn_auctionHouse.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages auction house system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_item", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_auctionListings") then {
            eden_auctionListings = [];
            publicVariable "eden_auctionListings";
        };
        _player setVariable ["eden_auctionBids", [], true];
        true
    };
    case "listItem": {
        params ["", "", "", ["_startingBid", 100, [0]], ["_duration", 3600, [0]]];
        
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _hasItem = false;
        {
            if ((_x select 0) == _item && (_x select 1) > 0) then { _hasItem = true; };
        } forEach _virtualItems;
        
        if (!_hasItem) exitWith {
            ["You don't have this item"] call EDEN_fnc_showHint;
            false
        };
        
        _listing = [_item, getPlayerUID _player, name _player, _startingBid, _startingBid, "", time, (time + _duration), "Active"];
        eden_auctionListings pushBack _listing;
        publicVariable "eden_auctionListings";
        
        [format ["Item listed for auction - starting bid: $%1", _startingBid]] call EDEN_fnc_showHint;
        true
    };
    case "placeBid": {
        params ["", "", "", ["_listingIndex", 0, [0]], ["_bidAmount", 0, [0]]];
        
        if (_listingIndex >= count eden_auctionListings) exitWith {
            ["Invalid auction listing"] call EDEN_fnc_showHint;
            false
        };
        
        _listing = eden_auctionListings select _listingIndex;
        if ((_listing select 8) != "Active" || time >= (_listing select 7)) exitWith {
            ["Auction is not active"] call EDEN_fnc_showHint;
            false
        };
        
        _currentBid = _listing select 4;
        if (_bidAmount <= _currentBid) exitWith {
            [format ["Bid must be higher than $%1", _currentBid]] call EDEN_fnc_showHint;
            false
        };
        
        _cash = _player getVariable ["eden_cash", 0];
        if (_cash < _bidAmount) exitWith {
            [format ["Not enough money! Need $%1", _bidAmount]] call EDEN_fnc_showHint;
            false
        };
        
        _listing set [4, _bidAmount];
        _listing set [5, getPlayerUID _player];
        eden_auctionListings set [_listingIndex, _listing];
        publicVariable "eden_auctionListings";
        
        _bids = _player getVariable ["eden_auctionBids", []];
        _bids pushBack [(_listing select 0), _bidAmount, time];
        _player setVariable ["eden_auctionBids", _bids, true];
        
        [format ["Bid placed: $%1", _bidAmount]] call EDEN_fnc_showHint;
        true
    };
    case "claimItem": {
        params ["", "", "", ["_listingIndex", 0, [0]]];
        
        if (_listingIndex >= count eden_auctionListings) exitWith { false };
        
        _listing = eden_auctionListings select _listingIndex;
        if ((_listing select 5) != getPlayerUID _player || time < (_listing select 7)) exitWith {
            ["Cannot claim this item"] call EDEN_fnc_showHint;
            false
        };
        
        _item = _listing select 0;
        _finalBid = _listing select 4;
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash - _finalBid), true];
        
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _found = false;
        {
            if ((_x select 0) == _item) then {
                _x set [1, ((_x select 1) + 1)];
                _virtualItems set [_forEachIndex, _x];
                _found = true;
            };
        } forEach _virtualItems;
        
        if (!_found) then {
            _virtualItems pushBack [_item, 1];
        };
        _player setVariable ["eden_virtualItems", _virtualItems, true];
        
        _listing set [8, "Completed"];
        eden_auctionListings set [_listingIndex, _listing];
        publicVariable "eden_auctionListings";
        
        [format ["Item claimed: %1", _item]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
