/*
    EdenRP Admin Panel
    Enhanced administration tools with comprehensive logging
*/

params [
    ["_admin", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_action", "", [""]],
    ["_target", obj<PERSON>ull, [obj<PERSON><PERSON>]],
    ["_data", [], [[]]]
];

// Validate admin
if (isNull _admin || !isPlayer _admin) exitWith {
    ["Invalid admin provided to adminPanel", "ERROR", "ADMIN"] call EDEN_fnc_systemLogger;
    false
};

// Check admin permissions
private _adminLevel = _admin getVariable ["EDEN_AdminLevel", 0];
if (_adminLevel < 1) exitWith {
    ["Unauthorized access to admin panel", "WARNING", "ADMIN"] call EDEN_fnc_systemLogger;
    ["You do not have admin permissions", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
    false
};

private _result = false;
switch (toLower _action) do {
    case "kick": {
        if (_adminLevel >= 2) then {
            _result = [_admin, _target, _data] call EDEN_fnc_adminKick;
        } else {
            ["Insufficient permissions for kick", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "ban": {
        if (_adminLevel >= 3) then {
            _result = [_admin, _target, _data] call EDEN_fnc_adminBan;
        } else {
            ["Insufficient permissions for ban", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "teleport": {
        if (_adminLevel >= 2) then {
            _result = [_admin, _target, _data] call EDEN_fnc_adminTeleport;
        } else {
            ["Insufficient permissions for teleport", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "spectate": {
        if (_adminLevel >= 1) then {
            _result = [_admin, _target] call EDEN_fnc_adminSpectate;
        } else {
            ["Insufficient permissions for spectate", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "heal": {
        if (_adminLevel >= 2) then {
            _result = [_admin, _target] call EDEN_fnc_adminHeal;
        } else {
            ["Insufficient permissions for heal", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "givemoney": {
        if (_adminLevel >= 3) then {
            _result = [_admin, _target, _data] call EDEN_fnc_adminGiveMoney;
        } else {
            ["Insufficient permissions for give money", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "giveitem": {
        if (_adminLevel >= 2) then {
            _result = [_admin, _target, _data] call EDEN_fnc_adminGiveItem;
        } else {
            ["Insufficient permissions for give item", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "godmode": {
        if (_adminLevel >= 4) then {
            _result = [_admin, _target] call EDEN_fnc_adminGodMode;
        } else {
            ["Insufficient permissions for god mode", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "invisible": {
        if (_adminLevel >= 3) then {
            _result = [_admin, _target] call EDEN_fnc_adminInvisible;
        } else {
            ["Insufficient permissions for invisible", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "freeze": {
        if (_adminLevel >= 2) then {
            _result = [_admin, _target] call EDEN_fnc_adminFreeze;
        } else {
            ["Insufficient permissions for freeze", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "announce": {
        if (_adminLevel >= 3) then {
            _result = [_admin, _data] call EDEN_fnc_adminAnnounce;
        } else {
            ["Insufficient permissions for announce", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "restart": {
        if (_adminLevel >= 5) then {
            _result = [_admin, _data] call EDEN_fnc_adminRestart;
        } else {
            ["Insufficient permissions for restart", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "logs": {
        if (_adminLevel >= 2) then {
            _result = [_admin, _data] call EDEN_fnc_adminGetLogs;
        } else {
            ["Insufficient permissions for logs", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "stats": {
        if (_adminLevel >= 1) then {
            _result = [_admin] call EDEN_fnc_adminGetStats;
        } else {
            ["Insufficient permissions for stats", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "cleanup": {
        if (_adminLevel >= 3) then {
            _result = [_admin] call EDEN_fnc_adminCleanup;
        } else {
            ["Insufficient permissions for cleanup", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "economy": {
        if (_adminLevel >= 4) then {
            _result = [_admin, _data] call EDEN_fnc_adminEconomy;
        } else {
            ["Insufficient permissions for economy", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "vehicle": {
        if (_adminLevel >= 2) then {
            _result = [_admin, _data] call EDEN_fnc_adminVehicle;
        } else {
            ["Insufficient permissions for vehicle", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "weather": {
        if (_adminLevel >= 2) then {
            _result = [_admin, _data] call EDEN_fnc_adminWeather;
        } else {
            ["Insufficient permissions for weather", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    case "time": {
        if (_adminLevel >= 2) then {
            _result = [_admin, _data] call EDEN_fnc_adminTime;
        } else {
            ["Insufficient permissions for time", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
        };
    };
    default {
        [format["Unknown admin action: %1", _action], "ERROR", "ADMIN"] call EDEN_fnc_systemLogger;
    };
};

_result
