/*
    EdenRP Clamp Value Function
    Utility function to clamp numeric values within bounds
    
    This function ensures values stay within specified limits
    for security and data integrity
*/

params [
    ["_value", 0, [0]],
    ["_min", 0, [0]],
    ["_max", 100, [0]]
];

// Ensure min is not greater than max
if (_min > _max) then {
    private _temp = _min;
    _min = _max;
    _max = _temp;
};

// Clamp the value
private _result = _value;

if (_result < _min) then {
    _result = _min;
};

if (_result > _max) then {
    _result = _max;
};

_result
