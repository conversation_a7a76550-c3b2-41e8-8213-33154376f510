/*
    File: fn_upgradeSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages item upgrade and enhancement system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_item", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_itemLevels", [], true];
        _player setVariable ["eden_upgradeTokens", 0, true];
        true
    };
    case "upgradeItem": {
        params ["", "", "", ["_itemIndex", 0, [0]]];
        
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        if (_itemIndex >= count _virtualItems) exitWith {
            ["Invalid item index"] call EDEN_fnc_showHint;
            false
        };
        
        _itemLevels = _player getVariable ["eden_itemLevels", []];
        
        // Ensure levels array matches virtual items
        while {count _itemLevels < count _virtualItems} do {
            _itemLevels pushBack 0;
        };
        
        _currentLevel = _itemLevels select _itemIndex;
        if (_currentLevel >= 10) exitWith {
            ["Item is already at maximum level"] call EDEN_fnc_showHint;
            false
        };
        
        _upgradeCost = (_currentLevel + 1) * 1000;
        _tokensRequired = _currentLevel + 1;
        
        _cash = _player getVariable ["eden_cash", 0];
        _tokens = _player getVariable ["eden_upgradeTokens", 0];
        
        if (_cash < _upgradeCost) exitWith {
            [format ["Not enough money! Need $%1", _upgradeCost]] call EDEN_fnc_showHint;
            false
        };
        
        if (_tokens < _tokensRequired) exitWith {
            [format ["Not enough upgrade tokens! Need %1", _tokensRequired]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _upgradeCost), true];
        _player setVariable ["eden_upgradeTokens", (_tokens - _tokensRequired), true];
        
        _itemLevels set [_itemIndex, (_currentLevel + 1)];
        _player setVariable ["eden_itemLevels", _itemLevels, true];
        
        [format ["Item upgraded to level %1 for $%2", (_currentLevel + 1), _upgradeCost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "buyTokens": {
        params ["", "", "", ["_quantity", 1, [0]]];
        
        _tokenPrice = 500;
        _totalCost = _tokenPrice * _quantity;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _totalCost) exitWith {
            [format ["Not enough money! Need $%1", _totalCost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _totalCost), true];
        
        _tokens = _player getVariable ["eden_upgradeTokens", 0];
        _player setVariable ["eden_upgradeTokens", (_tokens + _quantity), true];
        
        [format ["Purchased %1 upgrade tokens for $%2", _quantity, _totalCost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "getUpgradeBonus": {
        params ["", "", "", ["_itemIndex", 0, [0]], ["_bonusType", "damage", [""]]];
        
        _itemLevels = _player getVariable ["eden_itemLevels", []];
        if (_itemIndex >= count _itemLevels) exitWith { 0 };
        
        _level = _itemLevels select _itemIndex;
        _bonus = _level * 0.1; // 10% per level
        
        _bonus
    };
    case "prestigeItem": {
        params ["", "", "", ["_itemIndex", 0, [0]]];
        
        _itemLevels = _player getVariable ["eden_itemLevels", []];
        if (_itemIndex >= count _itemLevels) exitWith { false };
        
        _currentLevel = _itemLevels select _itemIndex;
        if (_currentLevel < 10) exitWith {
            ["Item must be level 10 to prestige"] call EDEN_fnc_showHint;
            false
        };
        
        _itemLevels set [_itemIndex, 0];
        _player setVariable ["eden_itemLevels", _itemLevels, true];
        
        // Add prestige bonus
        _tokens = _player getVariable ["eden_upgradeTokens", 0];
        _player setVariable ["eden_upgradeTokens", (_tokens + 20), true];
        
        ["Item prestiged! Gained 20 upgrade tokens"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
