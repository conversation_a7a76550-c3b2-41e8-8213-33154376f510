/*
    EdenRP Main Initialization Script
    Enhanced Altis Life framework initialization
    
    This script handles the main initialization sequence for EdenRP
    with improved security, performance, and modularity
*/

// Initialize core variables
EDEN_Debug = false; // Set to true for development
EDEN_Version = "1.0.0";
EDEN_BuildDate = "2025-01-29";

// Performance monitoring
EDEN_InitStartTime = diag_tickTime;

// Core system initialization
if (hasInterface) then {
    // Client initialization
    [] spawn {
        // Wait for player to be ready
        waitUntil {!isNull player && player == player};
        waitUntil {!isNil "bis_fnc_init"};

        // Wait for EDEN functions to be compiled
        waitUntil {!isNil "EDEN_fnc_systemLogger"};

        // Basic client initialization
        ["EdenRP Client initializing..."] call EDEN_fnc_systemLogger;

        // Set player as initialized
        player setVariable ["EDEN_ClientReady", true];

        ["EdenRP Client initialization complete"] call EDEN_fnc_systemLogger;
    };
};

if (isServer) then {
    // Server initialization
    [] spawn {
        // Wait for EDEN functions to be compiled
        waitUntil {!isNil "EDEN_fnc_systemLogger"};

        // Initialize server systems
        [format["EdenRP Server v%1 starting initialization...", EDEN_Version]] call EDEN_fnc_systemLogger;

        // Initialize database connection
        if (!isNil "EDEN_fnc_initializeDatabase") then {
            [] call EDEN_fnc_initializeDatabase;
        };
        
        // Log server ready
        private _initTime = diag_tickTime - EDEN_InitStartTime;
        [format["EdenRP Server initialization completed in %1 seconds", _initTime]] call EDEN_fnc_systemLogger;
        
        // Set server ready flag
        EDEN_ServerReady = true;
        publicVariable "EDEN_ServerReady";
    };
};

// Global variables initialization
EDEN_PlayerCount = 0;
EDEN_PoliceCount = 0;
EDEN_MedicCount = 0;
EDEN_CivilianCount = 0;

// Economy variables
EDEN_EconomyMultiplier = 1.0;
EDEN_XPMultiplier = 1.0;

// Security variables
EDEN_AntiCheatEnabled = true;
EDEN_LoggingEnabled = true;
EDEN_ValidationEnabled = true;

// Performance variables
EDEN_MaxViewDistance = 3000;
EDEN_MaxObjectDistance = 2000;
EDEN_TerrainGrid = 25;

// Gang system variables
EDEN_MaxGangMembers = 12;
EDEN_MaxGangs = 50;
EDEN_GangTerritories = [];

// Housing system variables
EDEN_MaxHousesPerPlayer = 3;
EDEN_HouseCleanupTime = 2592000; // 30 days in seconds

// Vehicle system variables
EDEN_MaxVehiclesPerPlayer = 5;
EDEN_VehicleCleanupTime = 604800; // 7 days in seconds

// Job system variables
EDEN_JobCooldownTime = 300; // 5 minutes
EDEN_MaxJobsPerPlayer = 3;

// Communication system variables
EDEN_MaxMessageLength = 255;
EDEN_MessageCooldown = 5; // seconds

// Admin system variables
EDEN_AdminLevels = [
    "Player",      // 0
    "Supporter",   // 1
    "Moderator",   // 2
    "Admin",       // 3
    "Senior Admin",// 4
    "Developer"    // 5
];

// Initialize mission parameters
if (isServer) then {
    // Apply server parameters
    if (!isNil "paramsArray") then {
        if (count paramsArray > 0) then {
            EDEN_EconomyMultiplier = paramsArray select 0;
        };
        if (count paramsArray > 1) then {
            EDEN_XPMultiplier = paramsArray select 1;
        };
        if (count paramsArray > 2) then {
            EDEN_MaxGangMembers = paramsArray select 2;
        };
    };
    
    // Broadcast parameters to clients
    publicVariable "EDEN_EconomyMultiplier";
    publicVariable "EDEN_XPMultiplier";
    publicVariable "EDEN_MaxGangMembers";
};

// Initialize security monitoring
if (EDEN_AntiCheatEnabled) then {
    [] spawn {
        while {true} do {
            sleep 30; // Check every 30 seconds
            if (!isNil "EDEN_fnc_antiCheatMonitor") then {
                [] call EDEN_fnc_antiCheatMonitor;
            };
        };
    };
};

// Log initialization start (after functions are compiled)
[] spawn {
    waitUntil {!isNil "EDEN_fnc_systemLogger"};
    [format["EdenRP v%1 initialization started on %2", EDEN_Version, EDEN_BuildDate]] call EDEN_fnc_systemLogger;
};
