/*
    File: atm.hpp
    Author: EdenRP Development Team
    
    Description:
    ATM dialog definitions for EdenRP
*/

class EdenRP_ATM {
    idd = 3500;
    name = "EdenRP_ATM";
    movingEnable = 1;
    enableSimulation = 1;
    
    class controlsBackground {
        class Background: RscText {
            idc = -1;
            x = 0.3;
            y = 0.3;
            w = 0.4;
            h = 0.4;
            colorBackground[] = {0, 0, 0, 0.8};
        };
        
        class Title: RscText {
            idc = -1;
            text = "ATM - Bank Services";
            x = 0.3;
            y = 0.3;
            w = 0.4;
            h = 0.05;
            colorText[] = {1, 1, 1, 1};
            sizeEx = 0.04;
        };
    };
    
    class controls {
        class BalanceText: RscText {
            idc = 3501;
            text = "Balance: $0";
            x = 0.32;
            y = 0.37;
            w = 0.2;
            h = 0.05;
            colorText[] = {1, 1, 1, 1};
        };
        
        class AmountEdit: RscEdit {
            idc = 3502;
            x = 0.32;
            y = 0.45;
            w = 0.15;
            h = 0.04;
        };
        
        class WithdrawButton: RscButton {
            idc = 3503;
            text = "Withdraw";
            x = 0.32;
            y = 0.52;
            w = 0.1;
            h = 0.04;
            action = "[] call EDEN_fnc_withdrawMoney;";
        };
        
        class DepositButton: RscButton {
            idc = 3504;
            text = "Deposit";
            x = 0.44;
            y = 0.52;
            w = 0.1;
            h = 0.04;
            action = "[] call EDEN_fnc_depositMoney;";
        };
        
        class TransferButton: RscButton {
            idc = 3505;
            text = "Transfer";
            x = 0.56;
            y = 0.52;
            w = 0.1;
            h = 0.04;
            action = "[] call EDEN_fnc_transferMoney;";
        };
        
        class CloseButton: RscButton {
            idc = 3506;
            text = "Close";
            x = 0.62;
            y = 0.65;
            w = 0.06;
            h = 0.04;
            action = "closeDialog 0;";
        };
    };
};
