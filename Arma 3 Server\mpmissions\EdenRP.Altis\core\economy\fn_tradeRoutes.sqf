/*
    File: fn_tradeRoutes.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages trade routes and commerce system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_route", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_tradeRoutes") then {
            eden_tradeRoutes = [
                ["kavala_pyrgos", "Kavala", "Pyrgos", ["copper", "iron"], 1.2, 15],
                ["pyrgos_sofia", "Pyrgos", "Sofia", ["oil", "diamonds"], 1.5, 25],
                ["sofia_kavala", "Sofia", "Kavala", ["salt", "fish"], 1.1, 10]
            ];
            publicVariable "eden_tradeRoutes";
        };
        _player setVariable ["eden_activeRoute", "", true];
        _player setVariable ["eden_tradeHistory", [], true];
        true
    };
    case "startRoute": {
        _routeData = [];
        {
            if ((_x select 0) == _route) then { _routeData = _x; };
        } forEach eden_tradeRoutes;
        
        if (count _routeData == 0) exitWith {
            ["Trade route not found"] call EDEN_fnc_showHint;
            false
        };
        
        _currentRoute = _player getVariable ["eden_activeRoute", ""];
        if (_currentRoute != "") exitWith {
            ["Already on a trade route"] call EDEN_fnc_showHint;
            false
        };
        
        _requiredItems = _routeData select 3;
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _hasAllItems = true;
        
        {
            _hasItem = false;
            {
                if ((_x select 0) == _requiredItems select _forEachIndex && (_x select 1) > 0) then {
                    _hasItem = true;
                };
            } forEach _virtualItems;
            if (!_hasItem) then { _hasAllItems = false; };
        } forEach _requiredItems;
        
        if (!_hasAllItems) exitWith {
            [format ["Missing required items: %1", _requiredItems]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_activeRoute", _route, true];
        _player setVariable ["eden_routeStartTime", time, true];
        
        [format ["Started trade route: %1 to %2", (_routeData select 1), (_routeData select 2)]] call EDEN_fnc_showHint;
        true
    };
    case "completeRoute": {
        _currentRoute = _player getVariable ["eden_activeRoute", ""];
        if (_currentRoute == "") exitWith {
            ["No active trade route"] call EDEN_fnc_showHint;
            false
        };
        
        _routeData = [];
        {
            if ((_x select 0) == _currentRoute) then { _routeData = _x; };
        } forEach eden_tradeRoutes;
        
        _startTime = _player getVariable ["eden_routeStartTime", time];
        _minTime = (_routeData select 5) * 60; // Convert minutes to seconds
        
        if ((time - _startTime) < _minTime) exitWith {
            [format ["Route not complete - minimum time: %1 minutes", (_routeData select 5)]] call EDEN_fnc_showHint;
            false
        };
        
        _multiplier = _routeData select 4;
        _baseProfit = 1000;
        _profit = floor(_baseProfit * _multiplier);
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _profit), true];
        
        _player setVariable ["eden_activeRoute", "", true];
        
        _history = _player getVariable ["eden_tradeHistory", []];
        _history pushBack [_currentRoute, _profit, time];
        _player setVariable ["eden_tradeHistory", _history, true];
        
        [format ["Trade route completed! Profit: $%1", _profit]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "abandonRoute": {
        _currentRoute = _player getVariable ["eden_activeRoute", ""];
        if (_currentRoute == "") exitWith {
            ["No active trade route"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_activeRoute", "", true];
        ["Trade route abandoned"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
