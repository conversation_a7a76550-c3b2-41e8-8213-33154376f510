

[2025-07-29 06:56:23] Client version: no bootstrapper found
[2025-07-29 06:56:23] Log session started
[2025-07-29 06:56:23] [Logged Off, 0, 0] [A:1:0:0] CCMInterface::SetSteamID( [A:1:0:0] )
[2025-07-29 06:56:23] Log session started
[2025-07-29 06:56:23] [Logged Off, 4, 0] [A:1:0:0] EConnect called - scheduling connection for 50ms from now
[2025-07-29 06:56:23] Log session started
[2025-07-29 06:56:23] [Logged Off, 4, 0] [A:1:0:0] LogOn() called; not connected yet, scheduling connection. Schedule init returned 22
[2025-07-29 06:56:23] [Logged Off, 4, 0] [A:1:0:0] EConnect called - scheduling connection for 50ms from now
[2025-07-29 06:56:23] CCMInterface::YieldingConnect -- calling ISteamDirectory/GetCMListForConnect/?cellid=0&qoslevel=2
[2025-07-29 06:56:23] IPv6 HTTP connectivity test (ipv6check-http.steamserver.net / [2602:801:f002:102::a2fe:c025]:80 ([2602:801:f002:102::a2fe:c025]:80)) - SUCCESS
[2025-07-29 06:56:23] IPv6 HTTP connectivity test (ipv6check-http.steamserver.net / [2602:801:f002:102::a2fe:c025]:80 ([2602:801:f002:102::a2fe:c025]:80)) - server indicated we are using ipv6, external address = '2607:fea8:7bdd:7300:3da4:4660:29b9:5736'
[2025-07-29 06:56:23] IPv6 UDP connectivity test (ipv6check-udp.steamserver.net / [2602:801:f006:107::a2fe:c32a]:27019) - SUCCESS
[2025-07-29 06:56:24] GetCMListForConnect -- got 5 Netfilter CMs and 155 WebSocket CMs
[2025-07-29 06:56:24] GetCMListForConnect -- DC 'atl3' count: 21
[2025-07-29 06:56:24] GetCMListForConnect -- DC 'ord1' count: 23
[2025-07-29 06:56:24] GetCMListForConnect -- DC 'dfw2' count: 21
[2025-07-29 06:56:24] GetCMListForConnect -- DC 'iad1' count: 24
[2025-07-29 06:56:24] GetCMListForConnect -- DC 'sea1' count: 21
[2025-07-29 06:56:24] GetCMListForConnect -- DC 'lax1' count: 20
[2025-07-29 06:56:24] GetCMListForConnect -- DC 'lhr1' count: 23
[2025-07-29 06:56:24] GetCMListForConnect -- DC 'par1' count: 7
[2025-07-29 06:56:24] [Connecting, 4, 0] [A:1:0:0] CM Directory list says 100% of connections should be websockets, we rolled 25 - using WebSockets as default.
[2025-07-29 06:56:24] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-atl3.steamserver.net:27018) starting...
[2025-07-29 06:56:24] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-atl3.steamserver.net:443) starting...
[2025-07-29 06:56:24] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-atl3.steamserver.net:27018) starting...
[2025-07-29 06:56:24] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-ord1.steamserver.net:27020) starting...
[2025-07-29 06:56:24] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-ord1.steamserver.net:27020) results: 44.6674ms + load 14.0000
[2025-07-29 06:56:24] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() stopping early for good host (cmp2-ord1.steamserver.net:27020)
[2025-07-29 06:56:24] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-atl3.steamserver.net:27018) results: 89.9500ms + load 18.0000
[2025-07-29 06:56:24] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-atl3.steamserver.net:27018) results: 85.5873ms + load 17.0000
[2025-07-29 06:56:24] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-atl3.steamserver.net:443) results: 96.3719ms + load 17.0000
[2025-07-29 06:56:24] [Connecting, 4, 3] [A:1:0:0] Connect() starting connection (eNetQOSLevelMedium, cmp2-ord1.steamserver.net:27020, WebSocket)
[2025-07-29 06:56:24] [Connecting, 4, 3] [A:1:0:0] ConnectionCompleted() (**************:27020, WebSocket) local address (*********:50273)
[2025-07-29 06:56:24] [Connecting, 4, 3] [A:1:0:0] Client thinks it can connect via: UDP - yes, TCP - yes, WebSocket:443 - yes, WebSocket:Non443 - yes
[2025-07-29 06:56:24] [Connected, 4, 3] [A:1:0:0] Logging on [A:1:0:0]
[2025-07-29 06:56:24] [Logging On, 4, 3] [A:1:0:0] RecvMsgClientLogOnResponse() : [A:1:2666150929:46541] 'OK'
[2025-07-29 06:56:24] [Logged On, 4, 3] [A:1:2666150929:46541] RecvMsgClientLogOnResponse() : processing complete


[2025-07-29 06:56:44] Client version: no bootstrapper found
[2025-07-29 06:56:44] Log session started
[2025-07-29 06:56:44] [Logged Off, 0, 0] [A:1:0:0] CCMInterface::SetSteamID( [A:1:0:0] )
[2025-07-29 06:56:44] Log session started
[2025-07-29 06:56:44] [Logged Off, 4, 0] [A:1:0:0] EConnect called - scheduling connection for 50ms from now
[2025-07-29 06:56:44] Log session started
[2025-07-29 06:56:44] [Logged Off, 4, 0] [A:1:0:0] LogOn() called; not connected yet, scheduling connection. Schedule init returned 22
[2025-07-29 06:56:44] CCMInterface::YieldingConnect -- calling ISteamDirectory/GetCMListForConnect/?cellid=20&qoslevel=2
[2025-07-29 06:56:44] IPv6 HTTP connectivity test (ipv6check-http.steamserver.net / [2602:801:f002:102::a2fe:c025]:80 ([2602:801:f002:102::a2fe:c025]:80)) - SUCCESS
[2025-07-29 06:56:44] IPv6 HTTP connectivity test (ipv6check-http.steamserver.net / [2602:801:f002:102::a2fe:c025]:80 ([2602:801:f002:102::a2fe:c025]:80)) - server indicated we are using ipv6, external address = '2607:fea8:7bdd:7300:3da4:4660:29b9:5736'
[2025-07-29 06:56:44] IPv6 UDP connectivity test (ipv6check-udp.steamserver.net / [2602:801:f006:100::a2fe:c306]:27019) - SUCCESS
[2025-07-29 06:56:44] GetCMListForConnect -- got 8 Netfilter CMs and 152 WebSocket CMs
[2025-07-29 06:56:44] GetCMListForConnect -- DC 'ord1' count: 23
[2025-07-29 06:56:44] GetCMListForConnect -- DC 'iad1' count: 24
[2025-07-29 06:56:44] GetCMListForConnect -- DC 'atl3' count: 21
[2025-07-29 06:56:44] GetCMListForConnect -- DC 'dfw2' count: 21
[2025-07-29 06:56:44] GetCMListForConnect -- DC 'sea1' count: 21
[2025-07-29 06:56:44] GetCMListForConnect -- DC 'lax1' count: 21
[2025-07-29 06:56:44] GetCMListForConnect -- DC 'lhr1' count: 22
[2025-07-29 06:56:44] GetCMListForConnect -- DC 'par1' count: 7
[2025-07-29 06:56:44] [Connecting, 4, 0] [A:1:0:0] CM Directory list says 100% of connections should be websockets, we rolled 93 - using WebSockets as default.
[2025-07-29 06:56:44] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-ord1.steamserver.net:27020) starting...
[2025-07-29 06:56:44] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-iad1.steamserver.net:443) starting...
[2025-07-29 06:56:44] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-ord1.steamserver.net:27019) starting...
[2025-07-29 06:56:44] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-ord1.steamserver.net:443) starting...
[2025-07-29 06:56:45] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-ord1.steamserver.net:27019) results: 44.7592ms + load 13.0000
[2025-07-29 06:56:45] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() stopping early for good host (cmp2-ord1.steamserver.net:27019)
[2025-07-29 06:56:45] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-ord1.steamserver.net:443) results: 38.3012ms + load 14.0000
[2025-07-29 06:56:45] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-ord1.steamserver.net:27020) results: 40.7128ms + load 13.0000
[2025-07-29 06:56:45] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-iad1.steamserver.net:443) results: 34.0944ms + load 18.0000
[2025-07-29 06:56:45] [Connecting, 4, 3] [A:1:0:0] Connect() starting connection (eNetQOSLevelMedium, cmp1-iad1.steamserver.net:443, WebSocket)
[2025-07-29 06:56:45] [Connecting, 4, 3] [A:1:0:0] ConnectionCompleted() (**************:443, WebSocket) local address (*********:50294)
[2025-07-29 06:56:45] [Connecting, 4, 3] [A:1:0:0] Client thinks it can connect via: UDP - yes, TCP - yes, WebSocket:443 - yes, WebSocket:Non443 - yes
[2025-07-29 06:56:45] [Connected, 4, 3] [A:1:0:0] Logging on [A:1:0:0]
[2025-07-29 06:56:45] [Logging On, 4, 3] [A:1:0:0] RecvMsgClientLogOnResponse() : [A:1:2666463249:46541] 'OK'
[2025-07-29 06:56:45] [Logged On, 4, 3] [A:1:2666463249:46541] RecvMsgClientLogOnResponse() : processing complete


[2025-07-29 06:57:20] Client version: no bootstrapper found
[2025-07-29 06:57:20] Log session started
[2025-07-29 06:57:20] [Logged Off, 0, 0] [A:1:0:0] CCMInterface::SetSteamID( [A:1:0:0] )
[2025-07-29 06:57:20] Log session started
[2025-07-29 06:57:20] [Logged Off, 4, 0] [A:1:0:0] EConnect called - scheduling connection for 50ms from now
[2025-07-29 06:57:20] Log session started
[2025-07-29 06:57:20] [Logged Off, 4, 0] [A:1:0:0] LogOn() called; not connected yet, scheduling connection. Schedule init returned 22
[2025-07-29 06:57:20] CCMInterface::YieldingConnect -- calling ISteamDirectory/GetCMListForConnect/?cellid=20&qoslevel=2
[2025-07-29 06:57:20] IPv6 HTTP connectivity test (ipv6check-http.steamserver.net / [2602:801:f002:102::a2fe:c025]:80 ([2602:801:f002:102::a2fe:c025]:80)) - SUCCESS
[2025-07-29 06:57:20] IPv6 HTTP connectivity test (ipv6check-http.steamserver.net / [2602:801:f002:102::a2fe:c025]:80 ([2602:801:f002:102::a2fe:c025]:80)) - server indicated we are using ipv6, external address = '2607:fea8:7bdd:7300:3da4:4660:29b9:5736'
[2025-07-29 06:57:20] IPv6 UDP connectivity test (ipv6check-udp.steamserver.net / [2602:801:f002:101::a2fe:c004]:27019) - SUCCESS
[2025-07-29 06:57:20] GetCMListForConnect -- got 2 Netfilter CMs and 158 WebSocket CMs
[2025-07-29 06:57:20] GetCMListForConnect -- DC 'iad1' count: 23
[2025-07-29 06:57:20] GetCMListForConnect -- DC 'ord1' count: 22
[2025-07-29 06:57:20] GetCMListForConnect -- DC 'dfw2' count: 22
[2025-07-29 06:57:20] GetCMListForConnect -- DC 'sea1' count: 20
[2025-07-29 06:57:20] GetCMListForConnect -- DC 'atl3' count: 21
[2025-07-29 06:57:20] GetCMListForConnect -- DC 'lax1' count: 21
[2025-07-29 06:57:20] GetCMListForConnect -- DC 'lhr1' count: 24
[2025-07-29 06:57:20] GetCMListForConnect -- DC 'par1' count: 7
[2025-07-29 06:57:20] [Connecting, 4, 0] [A:1:0:0] CM Directory list says 100% of connections should be websockets, we rolled 25 - using WebSockets as default.
[2025-07-29 06:57:20] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-iad1.steamserver.net:443) starting...
[2025-07-29 06:57:20] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-ord1.steamserver.net:27019) starting...
[2025-07-29 06:57:20] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-ord1.steamserver.net:27019) starting...
[2025-07-29 06:57:20] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-iad1.steamserver.net:27019) starting...
[2025-07-29 06:57:20] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-ord1.steamserver.net:27019) results: 44.1730ms + load 13.0000
[2025-07-29 06:57:20] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() stopping early for good host (cmp2-ord1.steamserver.net:27019)
[2025-07-29 06:57:20] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-ord1.steamserver.net:27019) results: 40.0642ms + load 13.0000
[2025-07-29 06:57:20] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-iad1.steamserver.net:27019) results: 81.6368ms + load 19.0000
[2025-07-29 06:57:20] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-iad1.steamserver.net:443) results: 85.7729ms + load 18.0000
[2025-07-29 06:57:20] [Connecting, 4, 3] [A:1:0:0] Connect() starting connection (eNetQOSLevelMedium, cmp1-ord1.steamserver.net:27019, WebSocket)
[2025-07-29 06:57:20] [Connecting, 4, 3] [A:1:0:0] ConnectionCompleted() (***************:27019, WebSocket) local address (*********:50323)
[2025-07-29 06:57:20] [Connecting, 4, 3] [A:1:0:0] Client thinks it can connect via: UDP - yes, TCP - yes, WebSocket:443 - yes, WebSocket:Non443 - yes
[2025-07-29 06:57:20] [Connected, 4, 3] [A:1:0:0] Logging on [A:1:0:0]
[2025-07-29 06:57:20] [Logging On, 4, 3] [A:1:0:0] RecvMsgClientLogOnResponse() : [A:1:2666996753:46541] 'OK'
[2025-07-29 06:57:20] [Logged On, 4, 3] [A:1:2666996753:46541] RecvMsgClientLogOnResponse() : processing complete


[2025-07-29 07:08:12] Client version: no bootstrapper found
[2025-07-29 07:08:12] Log session started
[2025-07-29 07:08:12] [Logged Off, 0, 0] [A:1:0:0] CCMInterface::SetSteamID( [A:1:0:0] )
[2025-07-29 07:08:12] Log session started
[2025-07-29 07:08:12] [Logged Off, 4, 0] [A:1:0:0] EConnect called - scheduling connection for 50ms from now
[2025-07-29 07:08:12] Log session started
[2025-07-29 07:08:12] [Logged Off, 4, 0] [A:1:0:0] LogOn() called; not connected yet, scheduling connection. Schedule init returned 22
[2025-07-29 07:08:12] CCMInterface::YieldingConnect -- calling ISteamDirectory/GetCMListForConnect/?cellid=20&qoslevel=2
[2025-07-29 07:08:12] IPv6 HTTP connectivity test (ipv6check-http.steamserver.net / [2602:801:f002:102::a2fe:c02b]:80 ([2602:801:f002:102::a2fe:c02b]:80)) - SUCCESS
[2025-07-29 07:08:12] IPv6 HTTP connectivity test (ipv6check-http.steamserver.net / [2602:801:f002:102::a2fe:c02b]:80 ([2602:801:f002:102::a2fe:c02b]:80)) - server indicated we are using ipv6, external address = '2607:fea8:7bdd:7300:3da4:4660:29b9:5736'
[2025-07-29 07:08:12] IPv6 UDP connectivity test (ipv6check-udp.steamserver.net / [2602:801:f006:107::a2fe:c324]:27019) - SUCCESS
[2025-07-29 07:08:13] GetCMListForConnect -- got 6 Netfilter CMs and 154 WebSocket CMs
[2025-07-29 07:08:13] GetCMListForConnect -- DC 'ord1' count: 22
[2025-07-29 07:08:13] GetCMListForConnect -- DC 'iad1' count: 24
[2025-07-29 07:08:13] GetCMListForConnect -- DC 'dfw2' count: 21
[2025-07-29 07:08:13] GetCMListForConnect -- DC 'atl3' count: 21
[2025-07-29 07:08:13] GetCMListForConnect -- DC 'sea1' count: 21
[2025-07-29 07:08:13] GetCMListForConnect -- DC 'lax1' count: 22
[2025-07-29 07:08:13] GetCMListForConnect -- DC 'lhr1' count: 24
[2025-07-29 07:08:13] GetCMListForConnect -- DC 'par1' count: 5
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] CM Directory list says 100% of connections should be websockets, we rolled 36 - using WebSockets as default.
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-ord1.steamserver.net:27019) starting...
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-iad1.steamserver.net:443) starting...
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-iad1.steamserver.net:27018) starting...
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-ord1.steamserver.net:27019) starting...
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-iad1.steamserver.net:27018) results: 78.2307ms + load 19.0000
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-ord1.steamserver.net:27019) results: 90.2500ms + load 13.0000
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-ord1.steamserver.net:443) starting...
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-iad1.steamserver.net:27019) starting...
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-ord1.steamserver.net:27019) results: 84.3769ms + load 13.0000
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-iad1.steamserver.net:443) results: 75.4668ms + load 18.0000
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-atl3.steamserver.net:27018) starting...
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-dfw2.steamserver.net:443) starting...
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-ord1.steamserver.net:443) results: 57.1205ms + load 14.0000
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() stopping early for good host (cmp2-ord1.steamserver.net:443)
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-iad1.steamserver.net:27019) results: 54.4860ms + load 18.0000
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-atl3.steamserver.net:27018) results: 94.7546ms + load 19.0000
[2025-07-29 07:08:13] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-dfw2.steamserver.net:443) results: 89.7537ms + load 13.0000
[2025-07-29 07:08:13] [Connecting, 4, 3] [A:1:0:0] Connect() starting connection (eNetQOSLevelMedium, cmp2-ord1.steamserver.net:443, WebSocket)
[2025-07-29 07:08:13] [Connecting, 4, 3] [A:1:0:0] ConnectionCompleted() (**************:443, WebSocket) local address (*********:50903)
[2025-07-29 07:08:13] [Connecting, 4, 3] [A:1:0:0] Client thinks it can connect via: UDP - yes, TCP - yes, WebSocket:443 - yes, WebSocket:Non443 - yes
[2025-07-29 07:08:13] [Connected, 4, 3] [A:1:0:0] Logging on [A:1:0:0]
[2025-07-29 07:08:13] [Logging On, 4, 3] [A:1:0:0] RecvMsgClientLogOnResponse() : [A:1:3056426001:46541] 'OK'
[2025-07-29 07:08:13] [Logged On, 4, 3] [A:1:3056426001:46541] RecvMsgClientLogOnResponse() : processing complete
[2025-07-29 07:09:48] [Logged On, 4, 3] [A:1:3056426001:46541] LogOff()
[2025-07-29 07:09:48] [Logging Off, 4, 3] [A:1:3056426001:46541] AsyncDisconnect( bDontWaitOnTCPShutdown: false )
[2025-07-29 07:09:48] [Logging Off, 0, 3] [A:1:3056426001:46541] Log session ended


[2025-07-29 11:59:43] Client version: no bootstrapper found
[2025-07-29 11:59:43] Log session started
[2025-07-29 11:59:43] [Logged Off, 0, 0] [A:1:0:0] CCMInterface::SetSteamID( [A:1:0:0] )
[2025-07-29 11:59:43] Log session started
[2025-07-29 11:59:43] [Logged Off, 4, 0] [A:1:0:0] EConnect called - scheduling connection for 50ms from now
[2025-07-29 11:59:43] Log session started
[2025-07-29 11:59:43] [Logged Off, 4, 0] [A:1:0:0] LogOn() called; not connected yet, scheduling connection. Schedule init returned 22
[2025-07-29 11:59:43] CCMInterface::YieldingConnect -- calling ISteamDirectory/GetCMListForConnect/?cellid=20&qoslevel=2
[2025-07-29 11:59:43] IPv6 HTTP connectivity test (ipv6check-http.steamserver.net / [2602:801:f006:107::a2fe:c324]:80 ([2602:801:f006:107::a2fe:c324]:80)) - SUCCESS
[2025-07-29 11:59:43] IPv6 HTTP connectivity test (ipv6check-http.steamserver.net / [2602:801:f006:107::a2fe:c324]:80 ([2602:801:f006:107::a2fe:c324]:80)) - server indicated we are using ipv6, external address = '2607:fea8:7bdd:7300:3da4:4660:29b9:5736'
[2025-07-29 11:59:43] IPv6 UDP connectivity test (ipv6check-udp.steamserver.net / [2602:801:f002:102::a2fe:c02b]:27019) - SUCCESS
[2025-07-29 11:59:43] GetCMListForConnect -- got 7 Netfilter CMs and 153 WebSocket CMs
[2025-07-29 11:59:43] GetCMListForConnect -- DC 'iad1' count: 23
[2025-07-29 11:59:43] GetCMListForConnect -- DC 'ord1' count: 23
[2025-07-29 11:59:43] GetCMListForConnect -- DC 'dfw2' count: 20
[2025-07-29 11:59:43] GetCMListForConnect -- DC 'sea1' count: 20
[2025-07-29 11:59:43] GetCMListForConnect -- DC 'atl3' count: 21
[2025-07-29 11:59:43] GetCMListForConnect -- DC 'lax1' count: 20
[2025-07-29 11:59:43] GetCMListForConnect -- DC 'lhr1' count: 24
[2025-07-29 11:59:43] GetCMListForConnect -- DC 'par1' count: 9
[2025-07-29 11:59:43] [Connecting, 4, 0] [A:1:0:0] CM Directory list says 100% of connections should be websockets, we rolled 51 - using WebSockets as default.
[2025-07-29 11:59:43] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-iad1.steamserver.net:443) starting...
[2025-07-29 11:59:43] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-ord1.steamserver.net:27020) starting...
[2025-07-29 11:59:43] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-iad1.steamserver.net:27018) starting...
[2025-07-29 11:59:43] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-ord1.steamserver.net:27020) starting...
[2025-07-29 11:59:43] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-ord1.steamserver.net:27020) results: 44.4265ms + load 13.0000
[2025-07-29 11:59:43] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() stopping early for good host (cmp1-ord1.steamserver.net:27020)
[2025-07-29 11:59:43] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-ord1.steamserver.net:27020) results: 40.3914ms + load 14.0000
[2025-07-29 11:59:43] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp2-iad1.steamserver.net:443) results: 85.9480ms + load 19.0000
[2025-07-29 11:59:43] [Connecting, 4, 0] [A:1:0:0] PingWebSocketCM() (cmp1-iad1.steamserver.net:27018) results: 81.8579ms + load 19.0000
[2025-07-29 11:59:43] [Connecting, 4, 3] [A:1:0:0] Connect() starting connection (eNetQOSLevelMedium, cmp2-ord1.steamserver.net:27020, WebSocket)
[2025-07-29 11:59:43] [Connecting, 4, 3] [A:1:0:0] ConnectionCompleted() (**************:27020, WebSocket) local address (*********:58100)
[2025-07-29 11:59:43] [Connecting, 4, 3] [A:1:0:0] Client thinks it can connect via: UDP - yes, TCP - yes, WebSocket:443 - yes, WebSocket:Non443 - yes
[2025-07-29 11:59:43] [Connected, 4, 3] [A:1:0:0] Logging on [A:1:0:0]
[2025-07-29 11:59:44] [Logging On, 4, 3] [A:1:0:0] RecvMsgClientLogOnResponse() : [A:1:4042762257:46544] 'OK'
[2025-07-29 11:59:44] [Logged On, 4, 3] [A:1:4042762257:46544] RecvMsgClientLogOnResponse() : processing complete
[2025-07-29 12:01:24] [Logged On, 4, 3] [A:1:4042762257:46544] LogOff()
[2025-07-29 12:01:24] [Logging Off, 4, 3] [A:1:4042762257:46544] AsyncDisconnect( bDontWaitOnTCPShutdown: false )
[2025-07-29 12:01:24] [Logging Off, 0, 3] [A:1:4042762257:46544] Log session ended
