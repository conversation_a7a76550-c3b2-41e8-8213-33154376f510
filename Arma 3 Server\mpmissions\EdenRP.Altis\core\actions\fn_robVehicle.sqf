/*
    File: fn_robVehicle.sqf
    Author: EdenRP Development Team
    
    Description:
    Robs a vehicle (breaks into it).
    
    Parameters:
    0: OBJECT - Vehicle to rob
    1: OBJECT - Robber (optional, default: player)
    
    Returns:
    BOOLEAN - True if vehicle robbery was successful
*/

params [
    ["_vehicle", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_robber", player, [obj<PERSON><PERSON>]]
];

if (isNull _vehicle || isNull _robber) exitWith { false };

if (_robber distance _vehicle > 5) exitWith {
    ["Vehicle is too far away!"] call EDEN_fnc_showHint;
    false
};

if (!(_vehicle isKindOf "LandVehicle") && !(_vehicle isKindOf "Air") && !(_vehicle isKindOf "Ship")) exitWith {
    ["Invalid vehicle type!"] call EDEN_fnc_showHint;
    false
};

// Check if vehicle is already unlocked
if (locked _vehicle == 0) exitWith {
    ["Vehicle is already unlocked!"] call EDEN_fnc_showHint;
    false
};

// Check if robber has lockpick
_virtualItems = _robber getVariable ["eden_virtualItems", []];
_hasLockpick = false;
_lockpickIndex = -1;

{
    if ((_x select 0) == "lockpick") then {
        _hasLockpick = true;
        _lockpickIndex = _forEachIndex;
    };
} forEach _virtualItems;

if (!_hasLockpick) exitWith {
    ["You need a lockpick to rob vehicles!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_robber, "criminal_action", [_vehicle, "vehicle_robbery"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Start robbery process
[_robber, "Acts_carFixingWheel"] remoteExec ["switchMove"];
["Breaking into vehicle..."] call EDEN_fnc_showHint;

sleep 8; // Robbery time

[_robber, ""] remoteExec ["switchMove"];

// Determine success rate (70% base chance)
_playerLevel = _robber getVariable ["eden_playerLevel", 1];
_baseChance = 70;
_levelBonus = _playerLevel * 2; // +2% per level
_successChance = (_baseChance + _levelBonus) min 90; // Max 90%

_success = (random 100) < _successChance;

if (_success) then {
    // Unlock the vehicle
    _vehicle lock 0;
    
    // Search for items in vehicle
    _foundItems = [];
    _foundMoney = 0;
    
    // Random chance to find items/money
    if (random 100 < 30) then { // 30% chance to find money
        _foundMoney = 50 + random 200;
    };
    
    if (random 100 < 20) then { // 20% chance to find items
        _possibleItems = ["phone", "wallet", "sunglasses", "energy_drink"];
        _foundItem = selectRandom _possibleItems;
        _foundItems pushBack [_foundItem, 1];
    };
    
    // Give robber the loot
    if (_foundMoney > 0) then {
        _robberMoney = _robber getVariable ["eden_cash", 0];
        _robber setVariable ["eden_cash", (_robberMoney + _foundMoney), true];
    };
    
    {
        _itemName = _x select 0;
        _itemQuantity = _x select 1;
        
        _found = false;
        {
            if ((_x select 0) == _itemName) then {
                _x set [1, ((_x select 1) + _itemQuantity)];
                _found = true;
            };
        } forEach _virtualItems;
        
        if (!_found) then {
            _virtualItems pushBack [_itemName, _itemQuantity];
        };
    } forEach _foundItems;
    
    _robber setVariable ["eden_virtualItems", _virtualItems, true];
    
    // Consume lockpick (50% chance to break)
    if (random 100 < 50) then {
        _lockpickQuantity = (_virtualItems select _lockpickIndex) select 1;
        if (_lockpickQuantity <= 1) then {
            _virtualItems deleteAt _lockpickIndex;
        } else {
            (_virtualItems select _lockpickIndex) set [1, (_lockpickQuantity - 1)];
        };
        _robber setVariable ["eden_virtualItems", _virtualItems, true];
        ["Your lockpick broke!"] call EDEN_fnc_showHint;
    };
    
    // Add criminal activity
    _wantedLevel = _robber getVariable ["eden_wantedLevel", 0];
    _robber setVariable ["eden_wantedLevel", (_wantedLevel + 1), true];
    
    // Add to criminal record
    _criminalRecord = _robber getVariable ["eden_criminalRecord", []];
    _crimeRecord = [
        time,
        "System",
        "Vehicle break-in",
        typeOf _vehicle
    ];
    _criminalRecord pushBack _crimeRecord;
    _robber setVariable ["eden_criminalRecord", _criminalRecord, true];
    
    // Add bounty
    _bounty = _robber getVariable ["eden_bounty", 0];
    _robber setVariable ["eden_bounty", (_bounty + 500), true];
    
    // Alert nearby police (25% chance)
    if (random 100 < 25) then {
        {
            if (_x getVariable ["eden_isPolice", false] && _x distance _robber < 500) then {
                [
                    "Vehicle Break-in Alert",
                    format ["Vehicle break-in reported at %1", mapGridPosition _robber],
                    10,
                    "warning"
                ] remoteExec ["EDEN_fnc_showNotification", _x];
            };
        } forEach allPlayers;
    };
    
    _lootText = "";
    if (_foundMoney > 0) then { _lootText = _lootText + format ["$%1 ", _foundMoney]; };
    if (count _foundItems > 0) then { _lootText = _lootText + format ["%1 items", count _foundItems]; };
    
    if (_lootText != "") then {
        [format ["Vehicle unlocked! Found: %1", _lootText]] call EDEN_fnc_showHint;
    } else {
        ["Vehicle unlocked! No valuables found."] call EDEN_fnc_showHint;
    };
    
} else {
    ["Failed to break into vehicle!"] call EDEN_fnc_showHint;
    
    // Still consume lockpick on failure (25% chance)
    if (random 100 < 25) then {
        _lockpickQuantity = (_virtualItems select _lockpickIndex) select 1;
        if (_lockpickQuantity <= 1) then {
            _virtualItems deleteAt _lockpickIndex;
        } else {
            (_virtualItems select _lockpickIndex) set [1, (_lockpickQuantity - 1)];
        };
        _robber setVariable ["eden_virtualItems", _virtualItems, true];
        ["Your lockpick broke!"] call EDEN_fnc_showHint;
    };
};

// Log the crime attempt
[format ["[EDEN] Player %1 attempted to rob vehicle %2 - %3", name _robber, typeOf _vehicle, if (_success) then {"successful"} else {"failed"}], "WARN", "CRIME"] call EDEN_fnc_systemLogger;

[_robber] call EDEN_fnc_savePlayerData;

_success
