/*
    File: fn_hackSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Hacks into electronic systems (ATMs, security systems, etc.).
    
    Parameters:
    0: OBJECT - System to hack
    1: OBJECT - Hacker (optional, default: player)
    
    Returns:
    BOOLEAN - True if hacking was successful
*/

params [
    ["_system", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_hacker", player, [obj<PERSON><PERSON>]]
];

if (isNull _system || isNull _hacker) exitWith { false };

if (_hacker distance _system > 5) exitWith {
    ["You must be closer to the system!"] call EDEN_fnc_showHint;
    false
};

// Check if hacker has laptop
_virtualItems = _hacker getVariable ["eden_virtualItems", []];
_hasLaptop = false;

{
    if ((_x select 0) == "laptop") then {
        _hasLaptop = true;
    };
} forEach _virtualItems;

if (!_hasLaptop) exitWith {
    ["You need a laptop to hack systems!"] call EDEN_fnc_showHint;
    false
};

// Check if system was recently hacked
_lastHacked = _system getVariable ["eden_lastHacked", 0];
if ((time - _lastHacked) < 1800) exitWith { // 30 minute cooldown
    ["This system was recently hacked!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_hacker, "criminal_action", [_system, "system_hacking"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Determine system type
_systemType = typeOf _system;
_hackType = switch (true) do {
    case (_systemType in ["Land_Atm_01_F", "Land_Atm_02_F"]): { "atm" };
    case (_systemType in ["Land_TBox_F"]): { "traffic_light" };
    case (_systemType in ["Land_TTowerBig_1_F", "Land_TTowerBig_2_F"]): { "communication" };
    default { "generic" };
};

// Start hacking process
[_hacker, "Acts_carFixingWheel"] remoteExec ["switchMove"];
["Hacking system..."] call EDEN_fnc_showHint;

// Hacking takes time based on system complexity
_hackTime = switch (_hackType) do {
    case "atm": { 15 };
    case "traffic_light": { 8 };
    case "communication": { 20 };
    default { 10 };
};

sleep _hackTime;

[_hacker, ""] remoteExec ["switchMove"];

// Determine success rate based on player level and system type
_playerLevel = _hacker getVariable ["eden_playerLevel", 1];
_baseChance = switch (_hackType) do {
    case "atm": { 40 }; // ATMs are harder
    case "traffic_light": { 80 }; // Traffic lights are easier
    case "communication": { 30 }; // Communication towers are hardest
    default { 60 };
};

_levelBonus = _playerLevel * 3; // +3% per level
_successChance = (_baseChance + _levelBonus) min 85; // Max 85%

_success = (random 100) < _successChance;

if (_success) then {
    // Mark system as hacked
    _system setVariable ["eden_lastHacked", time, true];
    
    // Determine rewards based on system type
    _reward = 0;
    _specialReward = "";
    
    switch (_hackType) do {
        case "atm": {
            _reward = 1000 + random 2000; // $1000-3000
            _specialReward = "Hacked ATM for cash";
        };
        case "traffic_light": {
            _reward = 200 + random 300; // $200-500
            _specialReward = "Disrupted traffic system";
            
            // Create traffic chaos marker
            _marker = createMarker [format ["traffic_hack_%1", time], getPosATL _system];
            _marker setMarkerType "mil_warning";
            _marker setMarkerText "Traffic System Hacked";
            _marker setMarkerColor "ColorOrange";
            
            // Remove marker after 10 minutes
            [_marker] spawn {
                params ["_m"];
                sleep 600;
                deleteMarker _m;
            };
        };
        case "communication": {
            _reward = 500 + random 1000; // $500-1500
            _specialReward = "Intercepted communications";
            
            // Disrupt police communications temporarily
            {
                if (_x getVariable ["eden_isPolice", false]) then {
                    ["Communications disrupted! Radio interference detected."] remoteExec ["EDEN_fnc_showHint", _x];
                };
            } forEach allPlayers;
        };
        default {
            _reward = 300 + random 500; // $300-800
            _specialReward = "Hacked generic system";
        };
    };
    
    // Pay hacker
    _hackerMoney = _hacker getVariable ["eden_cash", 0];
    _hacker setVariable ["eden_cash", (_hackerMoney + _reward), true];
    
    // Add major criminal activity
    _wantedLevel = _hacker getVariable ["eden_wantedLevel", 0];
    _wantedIncrease = switch (_hackType) do {
        case "atm": { 3 };
        case "communication": { 4 };
        default { 2 };
    };
    _hacker setVariable ["eden_wantedLevel", (_wantedLevel + _wantedIncrease), true];
    
    // Add to criminal record
    _criminalRecord = _hacker getVariable ["eden_criminalRecord", []];
    _crimeRecord = [
        time,
        "System",
        "Computer hacking",
        _specialReward
    ];
    _criminalRecord pushBack _crimeRecord;
    _hacker setVariable ["eden_criminalRecord", _criminalRecord, true];
    
    // Add bounty
    _bounty = _hacker getVariable ["eden_bounty", 0];
    _hacker setVariable ["eden_bounty", (_bounty + (_reward * 0.5)), true];
    
    // Alert police (higher chance for serious hacks)
    _alertChance = switch (_hackType) do {
        case "atm": { 60 };
        case "communication": { 80 };
        default { 40 };
    };
    
    if (random 100 < _alertChance) then {
        {
            if (_x getVariable ["eden_isPolice", false]) then {
                [
                    "Cyber Crime Alert",
                    format ["System hacking detected at %1 - %2", mapGridPosition _hacker, _specialReward],
                    15,
                    "error"
                ] remoteExec ["EDEN_fnc_showNotification", _x];
            };
        } forEach allPlayers;
    };
    
    // Add experience
    _expGained = _reward / 10;
    _currentExp = _hacker getVariable ["eden_experience", 0];
    _hacker setVariable ["eden_experience", (_currentExp + _expGained), true];
    
    [format ["Hack successful! Gained $%1 (+%2 XP)", _reward, _expGained]] call EDEN_fnc_showHint;
    
} else {
    ["Hacking failed! System security too strong."] call EDEN_fnc_showHint;
    
    // Failed hack still triggers some police attention
    if (random 100 < 20) then {
        {
            if (_x getVariable ["eden_isPolice", false] && _x distance _hacker < 300) then {
                [
                    "Suspicious Activity",
                    format ["Suspicious electronic activity at %1", mapGridPosition _hacker],
                    8,
                    "warning"
                ] remoteExec ["EDEN_fnc_showNotification", _x];
            };
        } forEach allPlayers;
    };
};

// Log the hack attempt
[format ["[EDEN] Player %1 attempted to hack %2 - %3", name _hacker, _hackType, if (_success) then {"successful"} else {"failed"}], "CRITICAL", "CRIME"] call EDEN_fnc_systemLogger;

[_hacker] call EDEN_fnc_savePlayerData;

_success
