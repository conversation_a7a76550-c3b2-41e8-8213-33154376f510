/*
    File: fn_gatherResource.sqf
    Author: EdenRP Development Team
    
    Description:
    Allows players to gather resources from resource points.
    
    Parameters:
    0: OBJECT - Player (optional, default: player)
    1: STRING - Resource type (optional, auto-detect)
    
    Returns:
    BOOLEAN - True if resource was gathered successfully
*/

params [
    ["_player", player, [objN<PERSON>]],
    ["_resourceType", "", [""]]
];

if (isNull _player) exitWith { false };

// Auto-detect resource type based on nearby objects
if (_resourceType == "") then {
    _nearObjects = nearestObjects [_player, [], 5];
    {
        _objectType = typeOf _x;
        switch (true) do {
            case (_objectType in ["Land_Ore_01_F", "Land_Ore_02_F"]): { _resourceType = "copper"; };
            case (_objectType in ["Land_Iron_01_F", "Land_Iron_02_F"]): { _resourceType = "iron"; };
            case (_objectType in ["Land_Diamond_01_F"]): { _resourceType = "diamond"; };
            case (_objectType in ["Land_Stone_01_F", "Land_Stone_02_F"]): { _resourceType = "stone"; };
            case (_objectType in ["Land_Sand_01_F"]): { _resourceType = "sand"; };
        };
        if (_resourceType != "") exitWith {};
    } forEach _nearObjects;
};

if (_resourceType == "") exitWith {
    ["No resources found nearby!"] call EDEN_fnc_showHint;
    false
};

// Check if player has required license
_requiredLicense = switch (_resourceType) do {
    case "copper";
    case "iron";
    case "stone";
    case "sand": { "mining" };
    case "diamond": { "diamond" };
    case "oil": { "oil" };
    default { "" };
};

if (_requiredLicense != "" && !(_player getVariable [format ["eden_license_%1", _requiredLicense], false])) exitWith {
    [format ["You need a %1 license to gather this resource!", _requiredLicense]] call EDEN_fnc_showHint;
    false
};

// Check inventory space
_currentWeight = _player getVariable ["eden_currentWeight", 0];
_maxWeight = _player getVariable ["eden_maxWeight", 50];
_resourceWeight = 2; // Each resource weighs 2kg

if ((_currentWeight + _resourceWeight) > _maxWeight) exitWith {
    ["Inventory is full!"] call EDEN_fnc_showHint;
    false
};

// Gathering animation and time
[_player, "Acts_carFixingWheel"] remoteExec ["switchMove"];
["Gathering resources..."] call EDEN_fnc_showHint;

sleep 3; // Gathering time

[_player, ""] remoteExec ["switchMove"];

// Add resource to inventory
_virtualItems = _player getVariable ["eden_virtualItems", []];
_found = false;
{
    if ((_x select 0) == _resourceType) then {
        _x set [1, ((_x select 1) + 1)];
        _found = true;
    };
} forEach _virtualItems;

if (!_found) then {
    _virtualItems pushBack [_resourceType, 1];
};

_player setVariable ["eden_virtualItems", _virtualItems, true];
_player setVariable ["eden_currentWeight", (_currentWeight + _resourceWeight), true];

[format ["Gathered 1x %1", _resourceType]] call EDEN_fnc_showHint;
[_player] call EDEN_fnc_savePlayerData;

true
