/*
    File: fn_racingSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages vehicle racing system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_activeRaces") then {
            eden_activeRaces = [];
            publicVariable "eden_activeRaces";
        };
        _player setVariable ["eden_raceWins", 0, true];
        _player setVariable ["eden_racingEarnings", 0, true];
        true
    };
    case "createRace": {
        params ["", "", ["_raceName", "Street Race", [""]], ["_entryFee", 500, [0]], ["_prize", 2000, [0]]];
        
        _cash = _player getVariable ["eden_cash", 0];
        if (_cash < _entryFee) exitWith {
            [format ["Not enough money! Need $%1 entry fee", _entryFee]] call EDEN_fnc_showHint;
            false
        };
        
        _race = [_raceName, getPlayerUID _player, name _player, _entryFee, _prize, [], time, "Open"];
        eden_activeRaces pushBack _race;
        publicVariable "eden_activeRaces";
        
        [format ["Race created: %1 (Entry: $%2, Prize: $%3)", _raceName, _entryFee, _prize]] call EDEN_fnc_showHint;
        true
    };
    case "joinRace": {
        params ["", "", ["_raceIndex", 0, [0]]];
        
        if (_raceIndex >= count eden_activeRaces) exitWith {
            ["Invalid race"] call EDEN_fnc_showHint;
            false
        };
        
        _race = eden_activeRaces select _raceIndex;
        if ((_race select 7) != "Open") exitWith {
            ["Race is not open for registration"] call EDEN_fnc_showHint;
            false
        };
        
        _entryFee = _race select 3;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _entryFee) exitWith {
            [format ["Not enough money! Need $%1", _entryFee]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _entryFee), true];
        
        _participants = _race select 5;
        _participants pushBack [getPlayerUID _player, name _player];
        _race set [5, _participants];
        eden_activeRaces set [_raceIndex, _race];
        publicVariable "eden_activeRaces";
        
        [format ["Joined race: %1", (_race select 0)]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "winRace": {
        params ["", "", ["_raceIndex", 0, [0]]];
        
        if (_raceIndex >= count eden_activeRaces) exitWith { false };
        
        _race = eden_activeRaces select _raceIndex;
        _prize = _race select 4;
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _prize), true];
        
        _wins = _player getVariable ["eden_raceWins", 0];
        _player setVariable ["eden_raceWins", (_wins + 1), true];
        
        _earnings = _player getVariable ["eden_racingEarnings", 0];
        _player setVariable ["eden_racingEarnings", (_earnings + _prize), true];
        
        _race set [7, "Completed"];
        eden_activeRaces set [_raceIndex, _race];
        publicVariable "eden_activeRaces";
        
        [format ["Race won! Prize: $%1", _prize]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
