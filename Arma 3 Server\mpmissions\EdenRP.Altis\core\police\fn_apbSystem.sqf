/*
    File: fn_apbSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages APB (All Points Bulletin) system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_activeAPBs") then {
            eden_activeAPBs = [];
            publicVariable "eden_activeAPBs";
        };
        true
    };
    case "issueAPB": {
        params ["", "", "", ["_description", "", [""]], ["_priority", "Medium", [""]]];
        
        if (isNull _target) exitWith { false };
        
        _apbId = format ["APB_%1_%2", floor(random 10000), floor(time)];
        _apb = [_apbId, getPlayerUID _target, name _target, _description, _priority, name _player, time, "Active"];
        
        eden_activeAPBs pushBack _apb;
        publicVariable "eden_activeAPBs";
        
        _message = format ["APB ISSUED: %1 - %2 (%3 Priority)", name _target, _description, _priority];
        
        {
            if (playerSide == west && _x getVariable ["eden_onDuty", false]) then {
                [_message] remoteExec ["EDEN_fnc_showHint", _x];
            };
        } forEach allPlayers;
        
        [format ["APB issued for %1", name _target]] call EDEN_fnc_showHint;
        true
    };
    case "cancelAPB": {
        params ["", "", "", ["_apbId", "", [""]]];
        
        _apbIndex = -1;
        {
            if ((_x select 0) == _apbId) then { _apbIndex = _forEachIndex; };
        } forEach eden_activeAPBs;
        
        if (_apbIndex == -1) exitWith { false };
        
        _apb = eden_activeAPBs select _apbIndex;
        _apb set [7, "Cancelled"];
        eden_activeAPBs set [_apbIndex, _apb];
        publicVariable "eden_activeAPBs";
        
        [format ["APB %1 cancelled", _apbId]] call EDEN_fnc_showHint;
        true
    };
    case "viewAPBs": {
        _activeAPBs = [];
        {
            if ((_x select 7) == "Active") then {
                _activeAPBs pushBack _x;
            };
        } forEach eden_activeAPBs;
        
        if (count _activeAPBs == 0) then {
            ["No active APBs"] call EDEN_fnc_showHint;
        } else {
            _apbList = format ["Active APBs (%1):\n", count _activeAPBs];
            {
                _name = _x select 2;
                _desc = _x select 3;
                _priority = _x select 4;
                _apbList = _apbList + format ["- %1: %2 (%3)\n", _name, _desc, _priority];
            } forEach _activeAPBs;
            
            [_apbList] call EDEN_fnc_showHint;
        };
        
        true
    };
    default { false };
};
