/*
    File: fn_creditSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages credit and credit card system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_creditCards", [], true];
        _player setVariable ["eden_creditLimit", 0, true];
        _player setVariable ["eden_creditBalance", 0, true];
        true
    };
    case "applyCreditCard": {
        _creditScore = _player getVariable ["eden_creditScore", 750];
        if (_creditScore < 650) exitWith {
            ["Credit score too low for credit card"] call EDEN_fnc_showHint;
            false
        };
        
        _creditLimit = switch (true) do {
            case (_creditScore >= 800): { 10000 };
            case (_creditScore >= 750): { 7500 };
            case (_creditScore >= 700): { 5000 };
            default { 2500 };
        };
        
        _player setVariable ["eden_creditLimit", _creditLimit, true];
        _player setVariable ["eden_creditBalance", 0, true];
        
        _cards = _player getVariable ["eden_creditCards", []];
        _card = ["EDEN_CREDIT", _creditLimit, 0, time, "Active"];
        _cards pushBack _card;
        _player setVariable ["eden_creditCards", _cards, true];
        
        [format ["Credit card approved! Limit: $%1", _creditLimit]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "useCredit": {
        params ["", "", ["_amount", 100, [0]]];
        
        _creditLimit = _player getVariable ["eden_creditLimit", 0];
        _creditBalance = _player getVariable ["eden_creditBalance", 0];
        _availableCredit = _creditLimit - _creditBalance;
        
        if (_amount > _availableCredit) exitWith {
            [format ["Insufficient credit! Available: $%1", _availableCredit]] call EDEN_fnc_showHint;
            false
        };
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _amount), true];
        _player setVariable ["eden_creditBalance", (_creditBalance + _amount), true];
        
        [format ["Used $%1 credit. Balance: $%2", _amount, (_creditBalance + _amount)]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "payCredit": {
        params ["", "", ["_amount", 100, [0]]];
        
        _creditBalance = _player getVariable ["eden_creditBalance", 0];
        if (_creditBalance <= 0) exitWith {
            ["No credit balance to pay"] call EDEN_fnc_showHint;
            false
        };
        
        _cash = _player getVariable ["eden_cash", 0];
        if (_cash < _amount) exitWith {
            [format ["Not enough money! Need $%1", _amount]] call EDEN_fnc_showHint;
            false
        };
        
        _paymentAmount = _amount min _creditBalance;
        _player setVariable ["eden_cash", (_cash - _paymentAmount), true];
        _player setVariable ["eden_creditBalance", (_creditBalance - _paymentAmount), true];
        
        _creditScore = _player getVariable ["eden_creditScore", 750];
        _player setVariable ["eden_creditScore", (_creditScore + 2), true];
        
        [format ["Credit payment: $%1. Remaining balance: $%2", _paymentAmount, (_creditBalance - _paymentAmount)]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
