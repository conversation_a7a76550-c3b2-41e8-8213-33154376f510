/*
    File: fn_huntingAction.sqf
    Author: EdenRP Development Team
    
    Description:
    Performs hunting action on animals.
    
    Parameters:
    0: OBJECT - Animal to hunt
    1: OBJECT - <PERSON> (optional, default: player)
    
    Returns:
    BOOLEAN - True if hunting was successful
*/

params [
    ["_animal", obj<PERSON>ull, [obj<PERSON><PERSON>]],
    ["_hunter", player, [obj<PERSON><PERSON>]]
];

if (isNull _animal || isNull _hunter) exitWith { false };

if (_hunter distance _animal > 200) exitWith {
    ["Animal is too far away!"] call EDEN_fnc_showHint;
    false
};

// Check if player has hunting license
if (!(_hunter getVariable ["eden_license_hunting", false])) exitWith {
    ["You need a hunting license to hunt!"] call EDEN_fnc_showHint;
    false
};

// Check if player has hunting weapon
_hunterWeapons = weapons _hunter;
_hasHuntingWeapon = false;
{
    if (_x in ["srifle_DMR_06_camo_F", "srifle_LRR_camo_F", "hunting_rifle"]) then {
        _hasHuntingWeapon = true;
    };
} forEach _hunterWeapons;

if (!_hasHuntingWeapon) exitWith {
    ["You need a hunting rifle to hunt animals!"] call EDEN_fnc_showHint;
    false
};

// Check if animal is already dead
if (!alive _animal) exitWith {
    ["This animal is already dead!"] call EDEN_fnc_showHint;
    false
};

// Check inventory space
_currentWeight = _hunter getVariable ["eden_currentWeight", 0];
_maxWeight = _hunter getVariable ["eden_maxWeight", 50];
_meatWeight = 5; // Meat weighs 5kg

if ((_currentWeight + _meatWeight) > _maxWeight) exitWith {
    ["Inventory is full!"] call EDEN_fnc_showHint;
    false
};

// Determine animal type and meat yield
_animalType = typeOf _animal;
_meatType = "raw_meat";
_meatQuantity = 1;

switch (true) do {
    case (_animalType in ["Sheep_random_F", "Goat_random_F"]): {
        _meatType = "mutton";
        _meatQuantity = 2;
    };
    case (_animalType in ["Cow_01_brown_F", "Cow_01_white_F"]): {
        _meatType = "beef";
        _meatQuantity = 4;
    };
    case (_animalType in ["Pig_random_F"]): {
        _meatType = "pork";
        _meatQuantity = 3;
    };
    case (_animalType in ["Hen_random_F", "Cock_random_F"]): {
        _meatType = "chicken";
        _meatQuantity = 1;
    };
    default {
        _meatType = "wild_meat";
        _meatQuantity = 2;
    };
};

// Kill the animal
_animal setDamage 1;

// Hunting animation
[_hunter, "Acts_carFixingWheel"] remoteExec ["switchMove"];
["Processing animal..."] call EDEN_fnc_showHint;

sleep 5; // Processing time

[_hunter, ""] remoteExec ["switchMove"];

// Add meat to inventory
_virtualItems = _hunter getVariable ["eden_virtualItems", []];
_found = false;
{
    if ((_x select 0) == _meatType) then {
        _x set [1, ((_x select 1) + _meatQuantity)];
        _found = true;
    };
} forEach _virtualItems;

if (!_found) then {
    _virtualItems pushBack [_meatType, _meatQuantity];
};

_hunter setVariable ["eden_virtualItems", _virtualItems, true];
_hunter setVariable ["eden_currentWeight", (_currentWeight + (_meatWeight * _meatQuantity)), true];

// Add experience
_expGained = _meatQuantity * 20;
_currentExp = _hunter getVariable ["eden_experience", 0];
_hunter setVariable ["eden_experience", (_currentExp + _expGained), true];

// Update job stats if hunter
if ((_hunter getVariable ["eden_currentJob", ""]) == "hunter") then {
    _animalsHunted = _hunter getVariable ["eden_animalsHunted", 0];
    _hunter setVariable ["eden_animalsHunted", (_animalsHunted + 1), true];
};

// Check for level up
_newLevel = floor((_currentExp + _expGained) / 1000) + 1;
_currentLevel = _hunter getVariable ["eden_playerLevel", 1];
if (_newLevel > _currentLevel) then {
    _hunter setVariable ["eden_playerLevel", _newLevel, true];
    [format ["Level up! You are now level %1", _newLevel], 5] call EDEN_fnc_showNotification;
};

[format ["Hunted %1, gained %2x %3 (+%4 XP)", _animalType, _meatQuantity, _meatType, _expGained]] call EDEN_fnc_showHint;

// Remove animal carcass after delay
[_animal] spawn {
    params ["_carcass"];
    sleep 300; // 5 minutes
    if (!isNull _carcass) then {
        deleteVehicle _carcass;
    };
};

// Log hunting
[format ["[EDEN] Player %1 hunted %2, gained %3x %4", name _hunter, _animalType, _meatQuantity, _meatType], "DEBUG", "HUNTING"] call EDEN_fnc_systemLogger;

[_hunter] call EDEN_fnc_savePlayerData;

true
