/*
    EdenRP Server Configuration
    Enhanced server settings and parameters
*/

// Server Information
EDEN_SERVER_NAME = "EdenRP - Enhanced Altis Life Experience";
EDEN_SERVER_VERSION = "1.0.0";
EDEN_SERVER_BUILD = "********";
EDEN_SERVER_REGION = "US";
EDEN_SERVER_LANGUAGE = "EN";

// Performance Settings
EDEN_MAX_PLAYERS = 120;
EDEN_VIEW_DISTANCE = 3000;
EDEN_OBJECT_DISTANCE = 2000;
EDEN_TERRAIN_GRID = 25;
EDEN_AI_SKILL = 0.5;

// Economy Settings
EDEN_ECONOMY_MULTIPLIER = 1.0;
EDEN_XP_MULTIPLIER = 1.0;
EDEN_STARTING_CASH = 5000;
EDEN_STARTING_BANK = 15000;
EDEN_MAX_MONEY = *********;
EDEN_BANK_INTEREST_RATE = 0.02; // 2% per hour
EDEN_INFLATION_RATE = 0.001; // 0.1% per day

// Gang Settings
EDEN_MAX_GANG_MEMBERS = 12;
EDEN_MAX_GANGS = 50;
EDEN_GANG_CREATION_COST = 50000;
EDEN_GANG_BANK_LIMIT = ********;
EDEN_TERRITORY_CAPTURE_TIME = 300; // 5 minutes
EDEN_TERRITORY_PAYOUT_INTERVAL = 600; // 10 minutes

// Housing Settings
EDEN_MAX_HOUSES_PER_PLAYER = 3;
EDEN_HOUSE_CLEANUP_DAYS = 30;
EDEN_HOUSE_TAX_RATE = 0.01; // 1% per week
EDEN_HOUSE_INSURANCE_RATE = 0.005; // 0.5% per week

// Vehicle Settings
EDEN_MAX_VEHICLES_PER_PLAYER = 5;
EDEN_VEHICLE_CLEANUP_DAYS = 7;
EDEN_VEHICLE_INSURANCE_RATE = 0.02; // 2% per week
EDEN_IMPOUND_FEE_MULTIPLIER = 0.1; // 10% of vehicle value

// Job Settings
EDEN_JOB_COOLDOWN_TIME = 300; // 5 minutes
EDEN_MAX_JOBS_PER_PLAYER = 3;
EDEN_JOB_XP_MULTIPLIER = 1.0;
EDEN_JOB_PAYOUT_MULTIPLIER = 1.0;

// Police Settings
EDEN_MIN_POLICE_ONLINE = 2;
EDEN_POLICE_PAYCHECK = 1000; // Per 30 minutes
EDEN_ARREST_XP_REWARD = 100;
EDEN_TICKET_XP_REWARD = 25;
EDEN_MAX_JAIL_TIME = 120; // 2 hours
EDEN_BAIL_MULTIPLIER = 10; // 10x fine amount

// Medical Settings
EDEN_MIN_MEDICS_ONLINE = 1;
EDEN_MEDIC_PAYCHECK = 800; // Per 30 minutes
EDEN_REVIVE_XP_REWARD = 150;
EDEN_TREATMENT_XP_REWARD = 50;
EDEN_BLEEDOUT_TIME = 300; // 5 minutes
EDEN_RESPAWN_COST = 5000;

// Criminal Settings
EDEN_WANTED_DECAY_TIME = 1800; // 30 minutes
EDEN_BOUNTY_DECAY_RATE = 0.1; // 10% per hour
EDEN_ROBBERY_COOLDOWN = 3600; // 1 hour
EDEN_BANK_ROBBERY_MIN_POLICE = 3;
EDEN_FEDERAL_ROBBERY_MIN_POLICE = 5;

// Market Settings
EDEN_MARKET_UPDATE_INTERVAL = 300; // 5 minutes
EDEN_PRICE_VOLATILITY = 0.2; // 20% max change
EDEN_SUPPLY_DEMAND_FACTOR = 0.1;
EDEN_MARKET_CRASH_CHANCE = 0.001; // 0.1% per update

// Communication Settings
EDEN_MAX_MESSAGE_LENGTH = 255;
EDEN_MESSAGE_COOLDOWN = 5; // seconds
EDEN_PHONE_CALL_COST = 10; // per minute
EDEN_SMS_COST = 5; // per message
EDEN_RADIO_RANGE_MULTIPLIER = 1.0;

// Security Settings
EDEN_ANTICHEAT_ENABLED = true;
EDEN_VALIDATION_ENABLED = true;
EDEN_LOGGING_ENABLED = true;
EDEN_MAX_PING = 300;
EDEN_MAX_DESYNC = 50;
EDEN_SECURITY_CHECK_INTERVAL = 30; // seconds

// Cleanup Settings
EDEN_CLEANUP_INTERVAL = 300; // 5 minutes
EDEN_CLEANUP_DEAD_BODIES = 600; // 10 minutes
EDEN_CLEANUP_WRECKS = 1800; // 30 minutes
EDEN_CLEANUP_ITEMS = 3600; // 1 hour
EDEN_CLEANUP_MARKERS = 7200; // 2 hours

// Backup Settings
EDEN_BACKUP_INTERVAL = 3600; // 1 hour
EDEN_BACKUP_RETENTION = 168; // 7 days
EDEN_AUTO_SAVE_INTERVAL = 600; // 10 minutes
EDEN_PLAYER_SAVE_INTERVAL = 300; // 5 minutes

// Event Settings
EDEN_RANDOM_EVENTS_ENABLED = true;
EDEN_EVENT_FREQUENCY = 0.1; // 10% chance per hour
EDEN_SPECIAL_EVENTS_ENABLED = true;
EDEN_HOLIDAY_EVENTS_ENABLED = true;

// Progression Settings
EDEN_MAX_LEVEL = 100;
EDEN_XP_CURVE_MULTIPLIER = 1.5;
EDEN_SKILL_POINTS_PER_LEVEL = 2;
EDEN_MAX_SKILL_LEVEL = 100;
EDEN_ACHIEVEMENT_XP_BONUS = 500;

// Donator Settings
EDEN_DONATOR_XP_BONUS = [0, 0.1, 0.2, 0.3, 0.4, 0.5]; // Per level
EDEN_DONATOR_MONEY_BONUS = [0, 0.05, 0.1, 0.15, 0.2, 0.25]; // Per level
EDEN_DONATOR_VEHICLE_DISCOUNT = [0, 0.05, 0.1, 0.15, 0.2, 0.25]; // Per level
EDEN_DONATOR_HOUSE_DISCOUNT = [0, 0.03, 0.06, 0.09, 0.12, 0.15]; // Per level

// Weather Settings
EDEN_DYNAMIC_WEATHER = true;
EDEN_WEATHER_CHANGE_INTERVAL = 1800; // 30 minutes
EDEN_EXTREME_WEATHER_CHANCE = 0.05; // 5%
EDEN_WEATHER_AFFECTS_VISIBILITY = true;
EDEN_WEATHER_AFFECTS_DRIVING = true;

// Time Settings
EDEN_TIME_ACCELERATION = 4; // 4x real time
EDEN_DAY_DURATION = 360; // 6 hours real time
EDEN_NIGHT_DURATION = 240; // 4 hours real time
EDEN_SKIP_NIGHT = false;

// Debug Settings
EDEN_DEBUG_MODE = false;
EDEN_DEBUG_LOGGING = false;
EDEN_DEBUG_MARKERS = false;
EDEN_DEBUG_PERFORMANCE = false;
EDEN_DEBUG_DATABASE = false;

// Feature Toggles
EDEN_GANGS_ENABLED = true;
EDEN_HOUSING_ENABLED = true;
EDEN_VEHICLES_ENABLED = true;
EDEN_JOBS_ENABLED = true;
EDEN_POLICE_ENABLED = true;
EDEN_MEDICAL_ENABLED = true;
EDEN_ECONOMY_ENABLED = true;
EDEN_COMMUNICATION_ENABLED = true;
EDEN_ADMIN_ENABLED = true;
EDEN_EVENTS_ENABLED = true;
