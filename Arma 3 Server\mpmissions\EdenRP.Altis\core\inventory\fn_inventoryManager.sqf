/*
    EdenRP Inventory Manager
    Enhanced inventory management system
    
    This function handles all inventory operations with
    improved validation and security measures
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_action", "", [""]],
    ["_item", "", [""]],
    ["_quantity", 1, [0]],
    ["_data", [], [[]]]
];

// Validate parameters
if (isNull _player || !isPlayer _player) exitWith {
    ["Invalid player provided to inventoryManager", "ERROR", "INVENTORY"] call EDEN_fnc_systemLogger;
    false
};

if (_action == "") exitWith {
    ["No action specified for inventoryManager", "ERROR", "INVENTORY"] call EDEN_fnc_systemLogger;
    false
};

// Get player inventories
private _virtualInventory = _player getVariable ["EDEN_VirtualInventory", []];
private _physicalInventory = _player getVariable ["EDEN_PhysicalInventory", []];
private _maxWeight = _player getVariable ["EDEN_MaxWeight", 50];

// Calculate current weight
private _currentWeight = [_player] call EDEN_fnc_calculateWeight;

// Process action
private _result = false;
switch (toLower _action) do {
    case "add": {
        _result = [_player, _item, _quantity, _virtualInventory, _physicalInventory, _maxWeight, _currentWeight] call EDEN_fnc_addItem;
    };
    case "remove": {
        _result = [_player, _item, _quantity, _virtualInventory, _physicalInventory] call EDEN_fnc_removeItem;
    };
    case "transfer": {
        _result = [_player, _item, _quantity, _data] call EDEN_fnc_transferItem;
    };
    case "use": {
        _result = [_player, _item, _quantity] call EDEN_fnc_useItem;
    };
    case "process": {
        _result = [_player, _item, _quantity, _data] call EDEN_fnc_processItem;
    };
    case "craft": {
        _result = [_player, _item, _data] call EDEN_fnc_craftItem;
    };
    case "split": {
        _result = [_player, _item, _quantity] call EDEN_fnc_splitItem;
    };
    case "merge": {
        _result = [_player, _item] call EDEN_fnc_mergeItem;
    };
    case "check": {
        _result = [_player, _item] call EDEN_fnc_checkItem;
    };
    case "validate": {
        _result = [_player] call EDEN_fnc_validateInventory;
    };
    default {
        [format["Unknown inventory action: %1", _action], "ERROR", "INVENTORY"] call EDEN_fnc_systemLogger;
        _result = false;
    };
};

// Update player inventories if action was successful
if (_result) then {
    _player setVariable ["EDEN_VirtualInventory", _virtualInventory, false];
    _player setVariable ["EDEN_PhysicalInventory", _physicalInventory, false];
    _player setVariable ["EDEN_CurrentWeight", [_player] call EDEN_fnc_calculateWeight, false];
    
    // Sync with client
    [_virtualInventory, _physicalInventory] remoteExec ["EDEN_fnc_syncInventory", _player];
    
    // Log inventory change
    [format["Inventory %1: %2 x%3 for %4", _action, _item, _quantity, name _player], "INFO", "INVENTORY"] call EDEN_fnc_systemLogger;
};

_result
