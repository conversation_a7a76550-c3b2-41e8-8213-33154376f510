/*
    File: fn_dutyManager.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages police duty status and operations.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_policeRank", "Cadet", true];
        _player setVariable ["eden_onDuty", false, true];
        _player setVariable ["eden_dutyTime", 0, true];
        _player setVariable ["eden_arrestsMade", 0, true];
        _player setVariable ["eden_ticketsIssued", 0, true];
        true
    };
    case "goOnDuty": {
        if (_player getVariable ["eden_onDuty", false]) exitWith {
            ["You are already on duty!"] call EDEN_fnc_showHint;
            false
        };
        
        if (!(playerSide == west)) exitWith {
            ["You are not a police officer!"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_onDuty", true, true];
        _player setVariable ["eden_dutyStartTime", time, true];
        
        // Give police equipment
        removeAllWeapons _player;
        removeAllItems _player;
        removeAllAssignedItems _player;
        removeUniform _player;
        removeVest _player;
        removeBackpack _player;
        removeHeadgear _player;
        removeGoggles _player;
        
        _player forceAddUniform "U_B_GEN_Soldier_F";
        _player addVest "V_TacVest_blk_POLICE";
        _player addHeadgear "H_Cap_police";
        
        _player addWeapon "hgun_P07_F";
        _player addMagazine "16Rnd_9x21_Mag";
        _player addMagazine "16Rnd_9x21_Mag";
        
        _player addItem "ItemMap";
        _player addItem "ItemCompass";
        _player addItem "ItemWatch";
        _player addItem "ItemRadio";
        _player addItem "ItemGPS";
        
        _player assignItem "ItemMap";
        _player assignItem "ItemCompass";
        _player assignItem "ItemWatch";
        _player assignItem "ItemRadio";
        _player assignItem "ItemGPS";
        
        ["You are now on duty!"] call EDEN_fnc_showHint;
        [format ["[POLICE] %1 has gone on duty", name _player], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "goOffDuty": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You are not on duty!"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_onDuty", false, true];
        _dutyStartTime = _player getVariable ["eden_dutyStartTime", time];
        _sessionTime = time - _dutyStartTime;
        
        _totalDutyTime = _player getVariable ["eden_dutyTime", 0];
        _player setVariable ["eden_dutyTime", (_totalDutyTime + _sessionTime), true];
        
        // Remove police equipment
        removeAllWeapons _player;
        removeAllItems _player;
        removeAllAssignedItems _player;
        removeUniform _player;
        removeVest _player;
        removeBackpack _player;
        removeHeadgear _player;
        removeGoggles _player;
        
        _player forceAddUniform "U_C_Poloshirt_blue";
        
        _player addItem "ItemMap";
        _player addItem "ItemCompass";
        _player addItem "ItemWatch";
        _player addItem "ItemRadio";
        
        _player assignItem "ItemMap";
        _player assignItem "ItemCompass";
        _player assignItem "ItemWatch";
        _player assignItem "ItemRadio";
        
        ["You are now off duty!"] call EDEN_fnc_showHint;
        [format ["[POLICE] %1 has gone off duty (Session: %2 minutes)", name _player, floor(_sessionTime / 60)], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "checkDutyStatus": {
        _onDuty = _player getVariable ["eden_onDuty", false];
        _rank = _player getVariable ["eden_policeRank", "Cadet"];
        _arrests = _player getVariable ["eden_arrestsMade", 0];
        _tickets = _player getVariable ["eden_ticketsIssued", 0];
        
        _status = format ["Rank: %1\nStatus: %2\nArrests: %3\nTickets: %4", 
            _rank, 
            if (_onDuty) then {"On Duty"} else {"Off Duty"}, 
            _arrests, 
            _tickets
        ];
        
        [_status] call EDEN_fnc_showHint;
        true
    };
    case "promoteOfficer": {
        _currentRank = _player getVariable ["eden_policeRank", "Cadet"];
        _ranks = ["Cadet", "Officer", "Corporal", "Sergeant", "Lieutenant", "Captain", "Chief"];
        _currentIndex = _ranks find _currentRank;
        
        if (_currentIndex == -1 || _currentIndex >= (count _ranks - 1)) exitWith {
            ["Cannot promote further!"] call EDEN_fnc_showHint;
            false
        };
        
        _newRank = _ranks select (_currentIndex + 1);
        _player setVariable ["eden_policeRank", _newRank, true];
        
        [format ["Promoted to %1!", _newRank]] call EDEN_fnc_showHint;
        [format ["[POLICE] %1 promoted to %2", name _player, _newRank], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "demoteOfficer": {
        _currentRank = _player getVariable ["eden_policeRank", "Cadet"];
        _ranks = ["Cadet", "Officer", "Corporal", "Sergeant", "Lieutenant", "Captain", "Chief"];
        _currentIndex = _ranks find _currentRank;
        
        if (_currentIndex <= 0) exitWith {
            ["Cannot demote further!"] call EDEN_fnc_showHint;
            false
        };
        
        _newRank = _ranks select (_currentIndex - 1);
        _player setVariable ["eden_policeRank", _newRank, true];
        
        [format ["Demoted to %1!", _newRank]] call EDEN_fnc_showHint;
        [format ["[POLICE] %1 demoted to %2", name _player, _newRank], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "requestBackup": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty to request backup!"] call EDEN_fnc_showHint;
            false
        };
        
        _pos = getPos _player;
        _nearestCity = [_pos] call EDEN_fnc_getNearestCity;
        
        _backupMessage = format ["[BACKUP REQUEST] %1 requesting backup at %2", name _player, _nearestCity];
        
        {
            if (playerSide == west && _x getVariable ["eden_onDuty", false]) then {
                [_backupMessage] remoteExec ["EDEN_fnc_showHint", _x];
            };
        } forEach allPlayers;
        
        [_backupMessage, "URGENT", "POLICE"] call EDEN_fnc_systemLogger;
        ["Backup request sent!"] call EDEN_fnc_showHint;
        true
    };
    case "respondToBackup": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty to respond to backup!"] call EDEN_fnc_showHint;
            false
        };
        
        _responseMessage = format ["[BACKUP RESPONSE] %1 responding to backup call", name _player];
        
        {
            if (playerSide == west && _x getVariable ["eden_onDuty", false]) then {
                [_responseMessage] remoteExec ["EDEN_fnc_showHint", _x];
            };
        } forEach allPlayers;
        
        [_responseMessage, "INFO", "POLICE"] call EDEN_fnc_systemLogger;
        ["Backup response sent!"] call EDEN_fnc_showHint;
        true
    };
    case "getDutyStats": {
        _totalDutyTime = _player getVariable ["eden_dutyTime", 0];
        _arrests = _player getVariable ["eden_arrestsMade", 0];
        _tickets = _player getVariable ["eden_ticketsIssued", 0];
        _rank = _player getVariable ["eden_policeRank", "Cadet"];
        
        _hours = floor(_totalDutyTime / 3600);
        _minutes = floor((_totalDutyTime % 3600) / 60);
        
        _stats = format ["Police Statistics:\nRank: %1\nTotal Duty Time: %2h %3m\nArrests Made: %4\nTickets Issued: %5", 
            _rank, _hours, _minutes, _arrests, _tickets];
        
        [_stats] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
