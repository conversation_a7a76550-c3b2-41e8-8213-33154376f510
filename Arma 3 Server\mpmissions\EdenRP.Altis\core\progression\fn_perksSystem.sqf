/*
    File: fn_perksSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages player perks system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_availablePerks") then {
            eden_availablePerks = [
                ["fast_runner", "Increased movement speed", 5],
                ["lucky_finder", "Better loot chances", 10],
                ["negotiator", "Better prices", 8],
                ["tough_guy", "Reduced damage taken", 15],
                ["efficient_worker", "Faster job completion", 12]
            ];
            publicVariable "eden_availablePerks";
        };
        _player setVariable ["eden_activePerks", [], true];
        true
    };
    case "unlockPerk": {
        params ["", "", ["_perkName", "fast_runner", [""]]];
        
        _perkData = [];
        {
            if ((_x select 0) == _perkName) then { _perkData = _x; };
        } forEach eden_availablePerks;
        
        if (count _perkData == 0) exitWith {
            ["Perk not found"] call EDEN_fnc_showHint;
            false
        };
        
        _cost = _perkData select 2;
        _skillPoints = _player getVariable ["eden_skillPoints", 0];
        
        if (_skillPoints < _cost) exitWith {
            [format ["Not enough skill points! Need %1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _activePerks = _player getVariable ["eden_activePerks", []];
        if (_perkName in _activePerks) exitWith {
            ["Perk already unlocked"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_skillPoints", (_skillPoints - _cost), true];
        _activePerks pushBack _perkName;
        _player setVariable ["eden_activePerks", _activePerks, true];
        
        [format ["Unlocked perk: %1", (_perkData select 1)]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "hasPerk": {
        params ["", "", ["_perkName", "fast_runner", [""]]];
        
        _activePerks = _player getVariable ["eden_activePerks", []];
        _perkName in _activePerks
    };
    case "applyPerkEffects": {
        _activePerks = _player getVariable ["eden_activePerks", []];
        
        {
            switch (_x) do {
                case "fast_runner": {
                    _player setAnimSpeedCoef 1.2;
                };
                case "tough_guy": {
                    _player addEventHandler ["HandleDamage", {
                        params ["_unit", "_selection", "_damage"];
                        _damage * 0.8
                    }];
                };
            };
        } forEach _activePerks;
        
        true
    };
    default { false };
};
