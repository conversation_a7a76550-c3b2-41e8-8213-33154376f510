/*
    File: fn_weaponTrafficking.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages gang weapon trafficking system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_weapon", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_weaponDeals", 0, true];
        _player setVariable ["eden_weaponInventory", [], true];
        true
    };
    case "acquireWeapons": {
        _gang = _player getVariable ["eden_gang", ""];
        if (_gang == "") exitWith {
            ["You must be in a gang!"] call EDEN_fnc_showHint;
            false
        };
        
        _weapons = _player getVariable ["eden_weaponInventory", []];
        _weapons pushBack [_weapon, 1, time];
        _player setVariable ["eden_weaponInventory", _weapons, true];
        
        [format ["Acquired %1", _weapon]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "sellWeapons": {
        _weapons = _player getVariable ["eden_weaponInventory", []];
        if (count _weapons == 0) exitWith {
            ["No weapons to sell"] call EDEN_fnc_showHint;
            false
        };
        
        _price = 2000;
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _price), true];
        
        _weapons deleteAt 0;
        _player setVariable ["eden_weaponInventory", _weapons, true];
        
        _deals = _player getVariable ["eden_weaponDeals", 0];
        _player setVariable ["eden_weaponDeals", (_deals + 1), true];
        
        [format ["Sold weapon for $%1", _price]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
