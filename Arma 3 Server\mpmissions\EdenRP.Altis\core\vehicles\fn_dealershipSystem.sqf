/*
    EdenRP Vehicle Dealership System
    Enhanced vehicle purchasing and management
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_action", "", [""]],
    ["_vehicleClass", "", [""]],
    ["_data", [], [[]]]
];

// Validate parameters
if (isNull _player || !isPlayer _player) exitWith {
    ["Invalid player provided to dealershipSystem", "ERROR", "VEHICLES"] call EDEN_fnc_systemLogger;
    false
};

// Only run on server
if (hasInterface) exitWith {
    ["dealershipSystem called on client", "ERROR", "VEHICLES"] call EDEN_fnc_systemLogger;
    false
};

private _result = false;
switch (toLower _action) do {
    case "purchase": {
        _result = [_player, _vehicleClass, _data] call EDEN_fnc_purchaseVehicle;
    };
    case "sell": {
        _result = [_player, _vehicleClass, _data] call EDEN_fnc_sellVehicle;
    };
    case "customize": {
        _result = [_player, _vehicleClass, _data] call EDEN_fnc_customizeVehicle;
    };
    case "getprice": {
        _result = [_vehicleClass, _data] call EDEN_fnc_getVehiclePrice;
    };
    case "getavailable": {
        _result = [_player] call EDEN_fnc_getAvailableVehicles;
    };
    default {
        [format["Unknown dealership action: %1", _action], "ERROR", "VEHICLES"] call EDEN_fnc_systemLogger;
    };
};

_result
