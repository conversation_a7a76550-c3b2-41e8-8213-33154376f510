/*
    File: gang_menu.hpp
    Author: EdenRP Development Team
    
    Description:
    Gang menu dialog definitions for EdenRP
*/

class EdenRP_GangMenu {
    idd = 3100;
    name = "EdenRP_GangMenu";
    movingEnable = 1;
    enableSimulation = 1;
    
    class controlsBackground {
        class Background: RscText {
            idc = -1;
            x = 0.2;
            y = 0.2;
            w = 0.6;
            h = 0.6;
            colorBackground[] = {0, 0, 0, 0.8};
        };
        
        class Title: RscText {
            idc = -1;
            text = "Gang Management";
            x = 0.2;
            y = 0.2;
            w = 0.6;
            h = 0.05;
            colorText[] = {1, 1, 1, 1};
            sizeEx = 0.04;
        };
    };
    
    class controls {
        class GangList: RscListBox {
            idc = 3101;
            x = 0.25;
            y = 0.3;
            w = 0.25;
            h = 0.4;
        };
        
        class MemberList: RscListBox {
            idc = 3102;
            x = 0.55;
            y = 0.3;
            w = 0.2;
            h = 0.4;
        };
        
        class CreateGangButton: RscButton {
            idc = 3103;
            text = "Create Gang";
            x = 0.25;
            y = 0.72;
            w = 0.1;
            h = 0.04;
            action = "[] call EDEN_fnc_createGang;";
        };
        
        class JoinGangButton: RscButton {
            idc = 3104;
            text = "Join Gang";
            x = 0.37;
            y = 0.72;
            w = 0.1;
            h = 0.04;
            action = "[] call EDEN_fnc_joinGang;";
        };
        
        class LeaveGangButton: RscButton {
            idc = 3105;
            text = "Leave Gang";
            x = 0.49;
            y = 0.72;
            w = 0.1;
            h = 0.04;
            action = "[] call EDEN_fnc_leaveGang;";
        };
        
        class CloseButton: RscButton {
            idc = 3106;
            text = "Close";
            x = 0.7;
            y = 0.75;
            w = 0.08;
            h = 0.04;
            action = "closeDialog 0;";
        };
    };
};
