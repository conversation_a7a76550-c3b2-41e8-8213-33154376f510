﻿ 7:49:46 BattlEye Server: Initialized (v1.220, Arma 3 2.20.152984)
 7:49:46 Game Port: 2302, Steam Query Port: 2303
 7:49:46 Warning: Current Steam AppId: 233780 doesn't match expected value: 107410
 7:49:46 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:49:46 Host identity created.
 7:49:46 Roles assigned.
 7:49:46 Reading mission ...
 7:49:51 Script core\actions\fn_sellLicense.sqf not found
 7:49:52 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:49:52 Roles assigned.
 7:49:52 Reading mission ...
 7:49:52 Script core\actions\fn_sellLicense.sqf not found
 7:49:52 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:49:53 Roles assigned.
 7:49:53 Reading mission ...
 7:49:53 Script core\actions\fn_sellLicense.sqf not found
 7:49:53 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:49:53 Roles assigned.
 7:49:53 Reading mission ...
 7:49:54 Script core\actions\fn_sellLicense.sqf not found
 7:49:54 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:49:54 Roles assigned.
 7:49:54 Reading mission ...
 7:49:54 Script core\actions\fn_sellLicense.sqf not found
 7:49:54 Mission EdenRP.Altis read from directory.
 7:49:55 Roles assigned.
 7:49:55 Reading mission ...
 7:49:55 Script core\actions\fn_sellLicense.sqf not found
 7:49:55 Mission EdenRP.Altis read from directory.
 7:49:55 Roles assigned.
 7:49:55 Reading mission ...
 7:49:55 Script core\actions\fn_sellLicense.sqf not found
 7:49:56 Mission EdenRP.Altis read from directory.
 7:49:56 Roles assigned.
 7:49:56 Reading mission ...
 7:49:56 Script core\actions\fn_sellLicense.sqf not found
 7:49:56 Mission EdenRP.Altis read from directory.
 7:49:57 Roles assigned.
 7:49:57 Reading mission ...
 7:49:57 Script core\actions\fn_sellLicense.sqf not found
 7:49:57 Mission EdenRP.Altis read from directory.
 7:49:57 Roles assigned.
 7:49:57 Reading mission ...
 7:49:57 Script core\actions\fn_sellLicense.sqf not found
 7:49:58 Mission EdenRP.Altis read from directory.
 7:49:58 Roles assigned.
 7:49:58 Reading mission ...
 7:49:58 Script core\actions\fn_sellLicense.sqf not found
 7:49:58 Mission EdenRP.Altis read from directory.
 7:49:59 Roles assigned.
 7:49:59 Reading mission ...
 7:49:59 Script core\actions\fn_sellLicense.sqf not found
 7:49:59 Mission EdenRP.Altis read from directory.
 7:49:59 Roles assigned.
 7:49:59 Reading mission ...
 7:49:59 Script core\actions\fn_sellLicense.sqf not found
 7:50:00 Mission EdenRP.Altis read from directory.
 7:50:00 Roles assigned.
 7:50:00 Reading mission ...
 7:50:00 Script core\actions\fn_sellLicense.sqf not found
 7:50:00 Mission EdenRP.Altis read from directory.
 7:50:01 Roles assigned.
 7:50:01 Reading mission ...
 7:50:01 Script core\actions\fn_sellLicense.sqf not found
 7:50:01 Mission EdenRP.Altis read from directory.
 7:50:01 Roles assigned.
 7:50:01 Reading mission ...
 7:50:02 Script core\actions\fn_sellLicense.sqf not found
 7:50:02 Mission EdenRP.Altis read from directory.
