/*
    File: fn_buyLicense.sqf
    Author: EdenRP Development Team
    
    Description:
    Allows players to purchase licenses.
    
    Parameters:
    0: STRING - License type
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if license was purchased successfully
*/

params [
    ["_licenseType", "", [""]],
    ["_player", player, [objNull]]
];

if (_licenseType == "" || isNull _player) exitWith { false };

// License prices
_licensePrices = createHashMap;
_licensePrices set ["driver", 500];
_licensePrices set ["pilot", 2500];
_licensePrices set ["boat", 1000];
_licensePrices set ["gun", 5000];
_licensePrices set ["hunting", 1500];
_licensePrices set ["fishing", 750];
_licensePrices set ["mining", 2000];
_licensePrices set ["oil", 3000];
_licensePrices set ["diamond", 7500];
_licensePrices set ["turtle", 2500];

_price = _licensePrices getOrDefault [_licenseType, 0];
if (_price == 0) exitWith { false };

_playerMoney = _player getVariable ["eden_cash", 0];
if (_playerMoney < _price) exitWith {
    ["Insufficient funds!"] call EDEN_fnc_showHint;
    false
};

// Check if already has license
_licenseVar = format ["eden_license_%1", _licenseType];
if (_player getVariable [_licenseVar, false]) exitWith {
    ["You already have this license!"] call EDEN_fnc_showHint;
    false
};

// Purchase license
_player setVariable ["eden_cash", (_playerMoney - _price), true];
_player setVariable [_licenseVar, true, true];

[format ["License purchased: %1 ($%2)", _licenseType, _price]] call EDEN_fnc_showHint;
[_player] call EDEN_fnc_savePlayerData;

true
