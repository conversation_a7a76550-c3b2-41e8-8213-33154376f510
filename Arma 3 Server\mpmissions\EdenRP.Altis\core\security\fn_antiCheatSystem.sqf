/*
    File: fn_antiCheatSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages anti-cheat system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isServer) then {
            if (isNil "eden_antiCheatEnabled") then {
                eden_antiCheatEnabled = true;
                publicVariable "eden_antiCheatEnabled";
            };
            if (isNil "eden_suspiciousActivities") then {
                eden_suspiciousActivities = [];
                publicVariable "eden_suspiciousActivities";
            };
        };
        _player setVariable ["eden_lastPositionCheck", getPos _player, false];
        _player setVariable ["eden_speedViolations", 0, false];
        true
    };
    case "checkPlayerSpeed": {
        if (!eden_antiCheatEnabled) exitWith { true };
        
        _lastPos = _player getVariable ["eden_lastPositionCheck", getPos _player];
        _currentPos = getPos _player;
        _distance = _lastPos distance _currentPos;
        _maxSpeed = 150; // m/s maximum allowed speed
        
        if (_distance > _maxSpeed) then {
            _violations = _player getVariable ["eden_speedViolations", 0];
            _player setVariable ["eden_speedViolations", (_violations + 1), false];
            
            if (_violations >= 3) then {
                [_player, "flagSuspicious", "speed_hack"] call EDEN_fnc_antiCheatSystem;
            };
            
            [format ["[ANTICHEAT] Speed violation detected for %1", name _player], "WARNING", "ANTICHEAT"] call EDEN_fnc_systemLogger;
            false
        } else {
            _player setVariable ["eden_lastPositionCheck", _currentPos, false];
            true
        };
    };
    case "checkMoneyChanges": {
        params ["", "", ["_oldAmount", 0, [0]], ["_newAmount", 0, [0]]];
        
        if (!eden_antiCheatEnabled) exitWith { true };
        
        _difference = _newAmount - _oldAmount;
        _maxIncrease = 50000; // Maximum allowed money increase per transaction
        
        if (_difference > _maxIncrease) then {
            [_player, "flagSuspicious", "money_hack"] call EDEN_fnc_antiCheatSystem;
            [format ["[ANTICHEAT] Suspicious money increase for %1: $%2", name _player, _difference], "WARNING", "ANTICHEAT"] call EDEN_fnc_systemLogger;
            false
        } else {
            true
        };
    };
    case "flagSuspicious": {
        params ["", "", ["_reason", "unknown", [""]]];
        
        if (!isServer) exitWith { false };
        
        _suspiciousEntry = [getPlayerUID _player, name _player, _reason, time];
        eden_suspiciousActivities pushBack _suspiciousEntry;
        publicVariable "eden_suspiciousActivities";
        
        {
            if (getPlayerUID _x in eden_adminList) then {
                [format ["[ANTICHEAT] Suspicious activity: %1 - %2", name _player, _reason]] remoteExec ["EDEN_fnc_showHint", _x];
            };
        } forEach allPlayers;
        
        [format ["[ANTICHEAT] Player %1 flagged for %2", name _player, _reason], "ALERT", "ANTICHEAT"] call EDEN_fnc_systemLogger;
        true
    };
    case "validateInventory": {
        if (!eden_antiCheatEnabled) exitWith { true };
        
        _inventory = _player getVariable ["eden_virtualItems", []];
        _totalWeight = 0;
        
        {
            _itemWeight = [_x select 0] call EDEN_fnc_getItemWeight;
            _totalWeight = _totalWeight + (_itemWeight * (_x select 1));
        } forEach _inventory;
        
        _maxWeight = 100; // Maximum allowed inventory weight
        
        if (_totalWeight > _maxWeight) then {
            [_player, "flagSuspicious", "inventory_overload"] call EDEN_fnc_antiCheatSystem;
            false
        } else {
            true
        };
    };
    case "checkWeaponSpawn": {
        params ["", "", ["_weapon", "", [""]]];
        
        if (!eden_antiCheatEnabled) exitWith { true };
        
        _allowedWeapons = ["hgun_P07_F", "hgun_Rook40_F"];
        
        if (!(_weapon in _allowedWeapons)) then {
            [_player, "flagSuspicious", "illegal_weapon"] call EDEN_fnc_antiCheatSystem;
            [format ["[ANTICHEAT] Illegal weapon spawn: %1 by %2", _weapon, name _player], "WARNING", "ANTICHEAT"] call EDEN_fnc_systemLogger;
            false
        } else {
            true
        };
    };
    default { false };
};
