/*
    File: fn_drugSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages gang drug operations system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_drugType", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_drugOperations", 0, true];
        _player setVariable ["eden_drugInventory", [], true];
        true
    };
    case "produceDrugs": {
        _gang = _player getVariable ["eden_gang", ""];
        if (_gang == "") exitWith {
            ["You must be in a gang!"] call EDEN_fnc_showHint;
            false
        };
        
        _drugs = _player getVariable ["eden_drugInventory", []];
        _drugs pushBack [_drugType, 10, time];
        _player setVariable ["eden_drugInventory", _drugs, true];
        
        _ops = _player getVariable ["eden_drugOperations", 0];
        _player setVariable ["eden_drugOperations", (_ops + 1), true];
        
        [format ["Produced %1 drugs", _drugType]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "sellDrugs": {
        _drugs = _player getVariable ["eden_drugInventory", []];
        if (count _drugs == 0) exitWith {
            ["No drugs to sell"] call EDEN_fnc_showHint;
            false
        };
        
        _price = 500;
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _price), true];
        
        _drugs deleteAt 0;
        _player setVariable ["eden_drugInventory", _drugs, true];
        
        [format ["Sold drugs for $%1", _price]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
