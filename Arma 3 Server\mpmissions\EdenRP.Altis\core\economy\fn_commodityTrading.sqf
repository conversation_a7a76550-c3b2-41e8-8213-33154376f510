/*
    File: fn_commodityTrading.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages commodity trading system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_commodity", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_commodityPrices") then {
            eden_commodityPrices = [
                ["copper", 25, 100],
                ["iron", 35, 80],
                ["oil", 45, 120],
                ["diamonds", 200, 50],
                ["salt", 15, 200],
                ["fish", 20, 150]
            ];
            publicVariable "eden_commodityPrices";
        };
        _player setVariable ["eden_commodityInventory", [], true];
        true
    };
    case "buyCommodity": {
        params ["", "", "", ["_quantity", 1, [0]]];
        
        _commodityData = [];
        {
            if ((_x select 0) == _commodity) then { _commodityData = _x; };
        } forEach eden_commodityPrices;
        
        if (count _commodityData == 0) exitWith {
            ["Commodity not available"] call EDEN_fnc_showHint;
            false
        };
        
        _price = _commodityData select 1;
        _totalCost = _price * _quantity;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _totalCost) exitWith {
            [format ["Not enough money! Need $%1", _totalCost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _totalCost), true];
        
        _inventory = _player getVariable ["eden_commodityInventory", []];
        _found = false;
        {
            if ((_x select 0) == _commodity) then {
                _x set [1, ((_x select 1) + _quantity)];
                _inventory set [_forEachIndex, _x];
                _found = true;
            };
        } forEach _inventory;
        
        if (!_found) then {
            _inventory pushBack [_commodity, _quantity, _price];
        };
        _player setVariable ["eden_commodityInventory", _inventory, true];
        
        [format ["Bought %1x %2 at $%3 each", _quantity, _commodity, _price]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "sellCommodity": {
        params ["", "", "", ["_quantity", 1, [0]]];
        
        _inventory = _player getVariable ["eden_commodityInventory", []];
        _holding = [];
        _holdingIndex = -1;
        
        {
            if ((_x select 0) == _commodity) then {
                _holding = _x;
                _holdingIndex = _forEachIndex;
            };
        } forEach _inventory;
        
        if (count _holding == 0 || (_holding select 1) < _quantity) exitWith {
            ["Insufficient commodity to sell"] call EDEN_fnc_showHint;
            false
        };
        
        _commodityData = [];
        {
            if ((_x select 0) == _commodity) then { _commodityData = _x; };
        } forEach eden_commodityPrices;
        
        _currentPrice = _commodityData select 1;
        _totalValue = _currentPrice * _quantity;
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _totalValue), true];
        
        _holding set [1, ((_holding select 1) - _quantity)];
        if ((_holding select 1) <= 0) then {
            _inventory deleteAt _holdingIndex;
        } else {
            _inventory set [_holdingIndex, _holding];
        };
        _player setVariable ["eden_commodityInventory", _inventory, true];
        
        [format ["Sold %1x %2 at $%3 each", _quantity, _commodity, _currentPrice]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
