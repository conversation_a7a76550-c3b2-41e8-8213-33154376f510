/*
    File: fn_warningSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages player warning system.
*/

params [["_admin", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON><PERSON>]]];

if (isNull _admin) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_warnings") then {
            eden_warnings = [];
            publicVariable "eden_warnings";
        };
        true
    };
    case "warnPlayer": {
        params ["", "", "", ["_reason", "Rule violation", [""]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        if (isNull _target) exitWith { false };
        
        _targetUID = getPlayerUID _target;
        _warning = [_targetUID, name _target, _reason, name _admin, time];
        eden_warnings pushBack _warning;
        publicVariable "eden_warnings";
        
        // Count warnings for this player
        _warningCount = 0;
        {
            if ((_x select 0) == _targetUID) then { _warningCount = _warningCount + 1; };
        } forEach eden_warnings;
        
        [format ["WARNING #%1: %2", _warningCount, _reason]] remoteExec ["EDEN_fnc_showHint", _target];
        
        // Auto-kick after 3 warnings
        if (_warningCount >= 3) then {
            [format ["You have been kicked for accumulating %1 warnings", _warningCount]] remoteExec ["EDEN_fnc_showHint", _target];
            [_target] spawn { sleep 5; kickPlayer (_this select 0); };
        };
        
        [format ["Warned %1 (Warning #%2). Reason: %3", name _target, _warningCount, _reason]] call EDEN_fnc_showHint;
        
        [format ["[EDEN] Player %1 (%2) warned by %3. Reason: %4", name _target, _targetUID, name _admin, _reason], "ADMIN", "WARNING"] call EDEN_fnc_systemLogger;
        true
    };
    case "clearWarnings": {
        params ["", "", "", ["_playerUID", "", [""]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _clearedCount = 0;
        for "_i" from (count eden_warnings - 1) to 0 step -1 do {
            if ((eden_warnings select _i select 0) == _playerUID) then {
                eden_warnings deleteAt _i;
                _clearedCount = _clearedCount + 1;
            };
        };
        
        publicVariable "eden_warnings";
        
        [format ["Cleared %1 warnings for player %2", _clearedCount, _playerUID]] call EDEN_fnc_showHint;
        true
    };
    case "getWarnings": {
        params ["", "", "", ["_playerUID", "", [""]]];
        
        _playerWarnings = [];
        {
            if ((_x select 0) == _playerUID) then { _playerWarnings pushBack _x; };
        } forEach eden_warnings;
        
        _playerWarnings
    };
    default { false };
};
