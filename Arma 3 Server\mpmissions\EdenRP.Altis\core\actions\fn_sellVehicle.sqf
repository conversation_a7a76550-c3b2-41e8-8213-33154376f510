/*
    File: fn_sellVehicle.sqf
    Author: EdenRP Development Team
    
    Description:
    Sells a player's vehicle back to dealership.
    
    Parameters:
    0: OBJECT - Vehicle to sell
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if sale was successful
*/

params [
    ["_vehicle", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (isNull _vehicle || isNull _player) exitWith { false };

if (_player distance _vehicle > 10) exitWith {
    ["Vehicle is too far away!"] call EDEN_fnc_showHint;
    false
};

// Check ownership
_vehicleOwner = _vehicle getVariable ["eden_ownerUID", ""];
_playerUID = getPlayerUID _player;

if (_vehicleOwner != _playerUID) exitWith {
    ["You don't own this vehicle!"] call EDEN_fnc_showHint;
    false
};

// Check if near dealership
_nearDealerships = nearestObjects [_player, ["Land_CarService_F", "Land_Garage_V1_F"], 25];
if (count _nearDealerships == 0) exitWith {
    ["You must be at a vehicle dealership to sell vehicles!"] call EDEN_fnc_showHint;
    false
};

// Calculate sell price (60% of original price, modified by condition)
_originalPrice = _vehicle getVariable ["eden_purchasePrice", 0];
_vehicleClass = typeOf _vehicle;

// Default prices if no purchase price recorded
if (_originalPrice == 0) then {
    _originalPrice = switch (true) do {
        case (_vehicle isKindOf "Car"): { 15000 };
        case (_vehicle isKindOf "Truck"): { 75000 };
        case (_vehicle isKindOf "Air"): { 250000 };
        case (_vehicle isKindOf "Ship"): { 20000 };
        default { 10000 };
    };
};

_condition = 1 - (damage _vehicle);
_sellPrice = round(_originalPrice * 0.6 * _condition);

// Eject all passengers
{
    _x action ["GetOut", _vehicle];
} forEach crew _vehicle;

sleep 1;

// Delete vehicle
deleteVehicle _vehicle;

// Pay player
_playerMoney = _player getVariable ["eden_cash", 0];
_player setVariable ["eden_cash", (_playerMoney + _sellPrice), true];

[format ["Vehicle sold for $%1 (condition: %2%%)", _sellPrice, round(_condition * 100)]] call EDEN_fnc_showHint;

// Log sale
[format ["[EDEN] Player %1 sold vehicle %2 for $%3", name _player, _vehicleClass, _sellPrice], "INFO", "VEHICLE"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
