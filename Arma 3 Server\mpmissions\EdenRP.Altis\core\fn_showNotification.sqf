/*
    File: fn_showNotification.sqf
    Author: EdenRP Development Team
    
    Description:
    Shows notifications to players using the notification system.
    
    Parameters:
    0: STRING - Notification title
    1: STRING - Notification message (optional)
    2: NUMBER - Duration in seconds (optional, default: 5)
    3: STRING - Notification type (optional, default: "info")
    
    Returns:
    BOOLEAN - True if notification was shown successfully
    
    Example:
    ["Welcome!", "Welcome to EdenRP!", 5] call EDEN_fnc_showNotification;
    ["Error", "Something went wrong!", 3, "error"] call EDEN_fnc_showNotification;
*/

params [
    ["_title", "", [""]],
    ["_message", "", [""]],
    ["_duration", 5, [0]],
    ["_type", "info", [""]]
];

// Validate parameters
if (_title == "") exitWith {
    ["[EDEN] fn_showNotification: Empty notification title"] call EDEN_fnc_systemLogger;
    false
};

// Set default message if empty
if (_message == "") then {
    _message = _title;
    _title = "EdenRP";
};

// Clamp duration
_duration = _duration max 1 min 30;

// Determine notification colors based on type
_titleColor = "#FFFFFF";
_messageColor = "#CCCCCC";
_backgroundColor = "rgba(0,0,0,0.7)";

switch (toLower _type) do {
    case "success": {
        _titleColor = "#00FF00";
        _backgroundColor = "rgba(0,100,0,0.7)";
    };
    case "error": {
        _titleColor = "#FF0000";
        _backgroundColor = "rgba(100,0,0,0.7)";
    };
    case "warning": {
        _titleColor = "#FFFF00";
        _backgroundColor = "rgba(100,100,0,0.7)";
    };
    case "info": {
        _titleColor = "#00CCFF";
        _backgroundColor = "rgba(0,0,100,0.7)";
    };
};

// Create notification HTML
_notificationHTML = format [
    "<div style='position: fixed; top: 20px; right: 20px; width: 300px; background: %4; border-left: 4px solid %1; padding: 15px; border-radius: 5px; box-shadow: 0 4px 8px rgba(0,0,0,0.3); z-index: 1000;'>" +
    "<div style='color: %1; font-size: 16px; font-weight: bold; margin-bottom: 5px;'>%2</div>" +
    "<div style='color: %3; font-size: 14px; line-height: 1.4;'>%5</div>" +
    "</div>",
    _titleColor,
    _title,
    _messageColor,
    _backgroundColor,
    _message
];

// Show notification using structured text
_structuredText = parseText format [
    "<t size='1.2' color='%1' align='center'>%2</t><br/>" +
    "<t size='0.9' color='%3' align='center'>%4</t>",
    _titleColor,
    _title,
    _messageColor,
    _message
];

// Display notification
[_structuredText, 0.7, 0.1, _duration, 0.5] spawn BIS_fnc_dynamicText;

// Also show as hint for fallback
_hintText = format ["%1\n%2", _title, _message];
hintSilent _hintText;

// Auto-clear hint after duration
[{
    hintSilent "";
}, [], _duration] call CBA_fnc_waitAndExecute;

// Play notification sound based on type
switch (toLower _type) do {
    case "success": {
        playSound "FD_Finish_F";
    };
    case "error": {
        playSound "FD_CP_Not_Clear_F";
    };
    case "warning": {
        playSound "FD_CP_Clear_F";
    };
    default {
        playSound "FD_Start_F";
    };
};

// Log notification
[format ["[EDEN] Notification shown to %1: %2 - %3", name player, _title, _message], "DEBUG", "UI"] call EDEN_fnc_systemLogger;

// Return success
true
