# EdenRP Server Setup Guide for Your System

## 📁 **Your Server Directory Structure**

Based on your path: `C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server`

### **Required Directory Structure:**
```
C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\
├── arma3server_x64.exe          # Main server executable
├── @extDB3\                     # extDB3 mod folder
│   └── addons\
│       └── extDB3.pbo
├── extDB3\                      # extDB3 extension folder
│   ├── extDB3_x64.dll          # Main extension
│   ├── tbbmalloc_x64.dll       # Required library
│   ├── extdb3-conf.ini         # Database configuration
│   └── SQL_CUSTOM\             # SQL query templates
│       ├── eden_players.ini
│       ├── eden_vehicles.ini
│       ├── eden_gangs.ini
│       └── eden_houses.ini
├── mpmissions\                  # Mission files
│   └── EdenRP.Altis\           # Your EdenRP mission
├── keys\                        # BattlEye keys
├── battleye\                    # BattlEye configuration
├── server.cfg                   # Server configuration
├── basic.cfg                    # Basic server settings
└── start_server.bat            # Server startup script
```

## 🔧 **Step-by-Step Setup Instructions**

### **Step 1: Install extDB3**

1. **Download extDB3** from: https://github.com/SteezCram/extDB3/releases
2. **Extract the files** to your server directory:
   - Copy `extDB3_x64.dll` to `C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\extDB3\`
   - Copy `tbbmalloc_x64.dll` to the same folder
   - Copy the `@extDB3` folder to your server root

### **Step 2: Copy EdenRP Mission**

1. **Copy the EdenRP.Altis folder** to:
   ```
   C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mpmissions\EdenRP.Altis\
   ```

2. **Copy database configuration files** from `EdenRP.Altis\database\` to:
   ```
   C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\extDB3\
   ```

3. **Copy SQL templates** from `EdenRP.Altis\database\` to:
   ```
   C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\extDB3\SQL_CUSTOM\
   ```

### **Step 3: Install MySQL/MariaDB**

1. **Download MySQL** from: https://dev.mysql.com/downloads/mysql/
   - Or **MariaDB** from: https://mariadb.org/download/
2. **Install with these settings:**
   - Root password: `your_secure_password`
   - Port: `3306`
   - Character set: `utf8mb4`

### **Step 4: Set Up Database**

1. **Open MySQL Command Line** or **MySQL Workbench**
2. **Create the database:**
   ```sql
   CREATE DATABASE edenrp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```
3. **Create database user:**
   ```sql
   CREATE USER 'edenrp_user'@'localhost' IDENTIFIED BY 'edenrp_password';
   GRANT ALL PRIVILEGES ON edenrp.* TO 'edenrp_user'@'localhost';
   FLUSH PRIVILEGES;
   ```
4. **Import the database schema:**
   ```bash
   mysql -u edenrp_user -p edenrp < "C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\mpmissions\EdenRP.Altis\database\edenrp_schema.sql"
   ```

### **Step 5: Configure extDB3**

Edit `C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\extDB3\extdb3-conf.ini`:

```ini
[Database]
Type = MySQL
Name = Database

Host = 127.0.0.1
Port = 3306
Username = edenrp_user
Password = edenrp_password
Database = edenrp

Compress = true
Secure Auth = true

[Logging]
Version = 1
Flush = true

[Log]
Flush = true
```

### **Step 6: Create Server Configuration**

Create `C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\server.cfg`:

```
// Server Info
hostname = "EdenRP - Enhanced Altis Life";
password = "";
passwordAdmin = "admin_password_here";
serverCommandPassword = "server_password_here";

// Mission Configuration
class Missions {
    class EdenRP {
        template = "EdenRP.Altis";
        difficulty = "Custom";
    };
};

// Server Settings
maxPlayers = 120;
kickDuplicate = 1;
verifySignatures = 2;
allowedFilePatching = 1;
requiredSecureId = 2;

// Performance
maxPing = 300;
maxDesync = 150;
maxPacketLoss = 50;

// Logging
timeStampFormat = "short";
logFile = "server_console.log";

// Mods
serverMod = "@extDB3";

// Other Settings
persistent = 1;
autoInit = 1;
```

### **Step 7: Create Basic Configuration**

Create `C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\basic.cfg`:

```
MaxMsgSend = 256;
MaxSizeGuaranteed = 512;
MaxSizeNonguaranteed = 256;
MinBandwidth = 131072;
MaxBandwidth = 2097152000;
MinErrorToSend = 0.001;
MinErrorToSendNear = 0.01;
MaxCustomFileSize = 160000;
```

### **Step 8: Create Startup Script**

Create `C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\start_server.bat`:

```batch
@echo off
title EdenRP Server
echo Starting EdenRP Server...

cd /d "C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server"

arma3server_x64.exe ^
-serverMod=@extDB3 ^
-config=server.cfg ^
-cfg=basic.cfg ^
-profiles=profiles ^
-name=server ^
-world=Altis ^
-autoInit ^
-loadMissionToMemory

pause
```

## 🚀 **Step 9: Start Your Server**

1. **Make sure MySQL is running**
2. **Double-click `start_server.bat`**
3. **Check the console for any errors**
4. **Look for these success messages:**
   - `extDB3: Connected to Database`
   - `Mission EdenRP.Altis read from directory`
   - `Game started`

## 🔍 **Troubleshooting**

### **Common Issues:**

#### **extDB3 not loading:**
- Ensure `extDB3_x64.dll` and `tbbmalloc_x64.dll` are in the `extDB3` folder
- Install Visual C++ Redistributable 2019 x64

#### **Database connection failed:**
- Check MySQL is running
- Verify credentials in `extdb3-conf.ini`
- Ensure database `edenrp` exists

#### **Mission not loading:**
- Check `EdenRP.Altis` folder is in `mpmissions`
- Verify all files are present
- Check server console for specific errors

### **Log Locations:**
- Server logs: `profiles\server\server_console.log`
- extDB3 logs: `extDB3\logs\`
- Arma RPT: `%localappdata%\Arma 3\`

## 📞 **Quick Start Checklist**

- [ ] Downloaded and extracted extDB3
- [ ] Installed MySQL/MariaDB
- [ ] Created database and user
- [ ] Imported database schema
- [ ] Configured extDB3
- [ ] Copied EdenRP mission files
- [ ] Created server.cfg and basic.cfg
- [ ] Created startup script
- [ ] Started MySQL service
- [ ] Ran start_server.bat

## 🎮 **Connecting to Your Server**

1. **Start Arma 3**
2. **Go to Multiplayer > Direct Connect**
3. **Enter:** `127.0.0.1:2302`
4. **Join and create your character**

Your EdenRP server should now be running with all enhanced features!
