/*
    File: fn_organSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages organ donation and transplant system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_organ", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_organDonor", false, true];
        _player setVariable ["eden_organHealth", 100, true];
        true
    };
    case "registerDonor": {
        _player setVariable ["eden_organDonor", true, true];
        ["Registered as organ donor"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "transplant": {
        params ["", "", "", ["_recipient", objNull, [objNull]]];
        
        if (isNull _recipient) exitWith { false };
        
        _recipient setVariable ["eden_organHealth", 100, true];
        _recipient setDamage 0;
        
        ["Organ transplant successful"] call EDEN_fnc_showHint;
        ["You received an organ transplant"] remoteExec ["EDEN_fnc_showHint", _recipient];
        true
    };
    default { false };
};
