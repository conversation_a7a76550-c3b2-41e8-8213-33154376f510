/*
    File: fn_prisonSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages prison and jail system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_prisoner", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_prisonCells") then {
            eden_prisonCells = [];
            publicVariable "eden_prisonCells";
        };
        _player setVariable ["eden_jailTime", 0, true];
        _player setVariable ["eden_timesJailed", 0, true];
        true
    };
    case "jailPlayer": {
        params ["", "", "", ["_jailTime", 300, [0]]];
        
        if (isNull _prisoner) exitWith { false };
        
        _prisoner setVariable ["eden_jailed", true, true];
        _prisoner setVariable ["eden_jailStartTime", time, true];
        _prisoner setVariable ["eden_jailDuration", _jailTime, true];
        
        _times = _prisoner getVariable ["eden_timesJailed", 0];
        _prisoner setVariable ["eden_timesJailed", (_times + 1), true];
        
        _cellData = [getPlayerUID _prisoner, name _prisoner, time, _jailTime];
        eden_prisonCells pushBack _cellData;
        publicVariable "eden_prisonCells";
        
        [format ["%1 has been jailed for %2 seconds", name _prisoner, _jailTime]] call EDEN_fnc_showHint;
        [format ["You have been jailed for %1 seconds", _jailTime]] remoteExec ["EDEN_fnc_showHint", _prisoner];
        
        [_prisoner] call EDEN_fnc_savePlayerData;
        true
    };
    case "releasePlayer": {
        if (isNull _prisoner) exitWith { false };
        
        _prisoner setVariable ["eden_jailed", false, true];
        
        _uid = getPlayerUID _prisoner;
        _newCells = [];
        {
            if ((_x select 0) != _uid) then {
                _newCells pushBack _x;
            };
        } forEach eden_prisonCells;
        
        eden_prisonCells = _newCells;
        publicVariable "eden_prisonCells";
        
        [format ["%1 has been released from jail", name _prisoner]] call EDEN_fnc_showHint;
        ["You have been released from jail"] remoteExec ["EDEN_fnc_showHint", _prisoner];
        true
    };
    case "checkJailTime": {
        _jailed = _player getVariable ["eden_jailed", false];
        if (!_jailed) exitWith {
            ["You are not in jail"] call EDEN_fnc_showHint;
            false
        };
        
        _startTime = _player getVariable ["eden_jailStartTime", time];
        _duration = _player getVariable ["eden_jailDuration", 0];
        _elapsed = time - _startTime;
        _remaining = (_duration - _elapsed) max 0;
        
        if (_remaining <= 0) then {
            [_player, "releasePlayer", _player] call EDEN_fnc_prisonSystem;
        } else {
            [format ["Jail time remaining: %1 seconds", floor _remaining]] call EDEN_fnc_showHint;
        };
        
        true
    };
    case "viewPrisonCells": {
        if (count eden_prisonCells == 0) then {
            ["No prisoners in jail"] call EDEN_fnc_showHint;
        } else {
            _cellList = format ["Prison Cells (%1):\n", count eden_prisonCells];
            {
                _name = _x select 1;
                _jailTime = _x select 2;
                _duration = _x select 3;
                _cellList = _cellList + format ["- %1 (Time: %2s)\n", _name, _duration];
            } forEach eden_prisonCells;
            
            [_cellList] call EDEN_fnc_showHint;
        };
        true
    };
    default { false };
};
