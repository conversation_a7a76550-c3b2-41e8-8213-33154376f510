/*
    File: fn_robPlayer.sqf
    Author: EdenRP Development Team
    
    Description:
    Robs another player of their money and items.
    
    Parameters:
    0: OBJECT - Target player to rob
    1: OBJECT - <PERSON><PERSON> (optional, default: player)
    
    Returns:
    BOOLEAN - True if robbery was successful
*/

params [
    ["_target", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_robber", player, [obj<PERSON><PERSON>]]
];

if (isNull _target || isNull _robber) exitWith { false };

if (_target == _robber) exitWith {
    ["You cannot rob yourself!"] call EDEN_fnc_showHint;
    false
};

if (_robber distance _target > 5) exitWith {
    ["Target is too far away!"] call EDEN_fnc_showHint;
    false
};

// Check if target is restrained
if (!(_target getVariable ["eden_isRestrained", false])) exitWith {
    ["Target must be restrained before robbing!"] call EDEN_fnc_showHint;
    false
};

// Check if robber has weapon
_robberWeapons = weapons _robber;
if (count _robberWeapons == 0) exitWith {
    ["You need a weapon to rob someone!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_robber, "criminal_action", [_target, "robbery"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Start robbery process
[_robber, "Acts_carFixingWheel"] remoteExec ["switchMove"];
["Robbing player..."] call EDEN_fnc_showHint;

sleep 5; // Robbery time

[_robber, ""] remoteExec ["switchMove"];

// Calculate stolen money (50-80% of cash)
_targetCash = _target getVariable ["eden_cash", 0];
_stolenPercentage = 0.5 + (random 0.3); // 50-80%
_stolenMoney = round(_targetCash * _stolenPercentage);

// Steal money
if (_stolenMoney > 0) then {
    _target setVariable ["eden_cash", (_targetCash - _stolenMoney), true];
    _robberMoney = _robber getVariable ["eden_cash", 0];
    _robber setVariable ["eden_cash", (_robberMoney + _stolenMoney), true];
};

// Steal random items (20% chance per item)
_targetItems = _target getVariable ["eden_virtualItems", []];
_stolenItems = [];

{
    if (random 100 < 20) then { // 20% chance to steal each item
        _itemName = _x select 0;
        _itemQuantity = _x select 1;
        _stolenQuantity = ceil(_itemQuantity * 0.3); // Steal 30% of quantity
        
        if (_stolenQuantity > 0) then {
            _stolenItems pushBack [_itemName, _stolenQuantity];
            
            // Remove from target
            if (_stolenQuantity >= _itemQuantity) then {
                _targetItems deleteAt _forEachIndex;
            } else {
                _x set [1, (_itemQuantity - _stolenQuantity)];
            };
        };
    };
} forEach _targetItems;

// Add stolen items to robber
_robberItems = _robber getVariable ["eden_virtualItems", []];
{
    _itemName = _x select 0;
    _itemQuantity = _x select 1;
    
    _found = false;
    {
        if ((_x select 0) == _itemName) then {
            _x set [1, ((_x select 1) + _itemQuantity)];
            _found = true;
        };
    } forEach _robberItems;
    
    if (!_found) then {
        _robberItems pushBack [_itemName, _itemQuantity];
    };
} forEach _stolenItems;

_target setVariable ["eden_virtualItems", _targetItems, true];
_robber setVariable ["eden_virtualItems", _robberItems, true];

// Add criminal activity
_wantedLevel = _robber getVariable ["eden_wantedLevel", 0];
_robber setVariable ["eden_wantedLevel", (_wantedLevel + 3), true]; // Major crime

// Add to criminal record
_criminalRecord = _robber getVariable ["eden_criminalRecord", []];
_crimeRecord = [
    time,
    "System",
    "Armed robbery",
    name _target
];
_criminalRecord pushBack _crimeRecord;
_robber setVariable ["eden_criminalRecord", _criminalRecord, true];

// Add bounty
_bounty = _robber getVariable ["eden_bounty", 0];
_robber setVariable ["eden_bounty", (_bounty + 2500), true];

// Notifications
_stolenItemsText = if (count _stolenItems > 0) then {
    format [" and %1 items", count _stolenItems]
} else {
    ""
};

[format ["Robbery successful! Stole $%1%2 from %3", _stolenMoney, _stolenItemsText, name _target]] call EDEN_fnc_showHint;
[format ["You were robbed by %1! Lost $%2%3", name _robber, _stolenMoney, _stolenItemsText]] remoteExec ["EDEN_fnc_showHint", _target];

// Alert nearby police
{
    if (_x getVariable ["eden_isPolice", false] && _x distance _robber < 500) then {
        [
            "Armed Robbery Alert",
            format ["Armed robbery in progress at %1 - respond immediately!", mapGridPosition _robber],
            15,
            "error"
        ] remoteExec ["EDEN_fnc_showNotification", _x];
    };
} forEach allPlayers;

// Log the crime
[format ["[EDEN] Player %1 robbed %2 (stole $%3 and %4 items)", name _robber, name _target, _stolenMoney, count _stolenItems], "WARN", "CRIME"] call EDEN_fnc_systemLogger;

[_robber] call EDEN_fnc_savePlayerData;
[_target] call EDEN_fnc_savePlayerData;

true
