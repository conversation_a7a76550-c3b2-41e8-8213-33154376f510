/*
    File: fn_setupPlayerActions.sqf
    Author: EdenRP Development Team
    
    Description:
    Sets up player actions and interaction menu based on player role.
    
    Parameters:
    0: OBJECT - Player object
    
    Returns:
    BOOLEAN - True if actions were successfully set up
    
    Example:
    [player] call EDEN_fnc_setupPlayerActions;
*/

params [
    ["_player", player, [objN<PERSON>]]
];

// Validate parameters
if (isNull _player) exitWith {
    ["[EDEN] fn_setupPlayerActions: Invalid player object"] call EDEN_fnc_systemLogger;
    false
};

// Remove existing actions first
removeAllActions _player;

// Get player role
_playerRole = _player getVariable ["eden_playerRole", "civilian"];
_isPolice = _player getVariable ["eden_isPolice", false];
_isMedic = _player getVariable ["eden_isMedic", false];
_isAdmin = _player getVariable ["eden_isAdmin", false];

// Add universal actions for all players
_player addAction [
    "<t color='#00FF00'>Open Main Menu</t>",
    {
        [] call EDEN_fnc_openMainMenu;
    },
    [],
    10,
    false,
    true,
    "",
    "true",
    5
];

_player addAction [
    "<t color='#0080FF'>Open Inventory</t>",
    {
        [] call EDEN_fnc_openInventory;
    },
    [],
    9,
    false,
    true,
    "",
    "true",
    5
];

_player addAction [
    "<t color='#FFFF00'>Flip Vehicle</t>",
    {
        _vehicle = cursorTarget;
        if (!isNull _vehicle && _vehicle isKindOf "LandVehicle") then {
            _vehicle setPos [getPos _vehicle select 0, getPos _vehicle select 1, 1];
            _vehicle setVectorUp [0,0,1];
            ["Vehicle flipped successfully!"] call EDEN_fnc_showHint;
        } else {
            ["No vehicle found!"] call EDEN_fnc_showHint;
        };
    },
    [],
    8,
    false,
    true,
    "",
    "!isNull cursorTarget && cursorTarget isKindOf 'LandVehicle' && _this distance cursorTarget < 5",
    5
];

// Add civilian-specific actions
if (_playerRole == "civilian") then {
    _player addAction [
        "<t color='#FF8000'>Gather Resources</t>",
        {
            [] call EDEN_fnc_gatherResource;
        },
        [],
        7,
        false,
        true,
        "",
        "true",
        5
    ];
    
    _player addAction [
        "<t color='#8000FF'>Process Materials</t>",
        {
            [] call EDEN_fnc_processResource;
        },
        [],
        6,
        false,
        true,
        "",
        "true",
        5
    ];
};

// Add police-specific actions
if (_isPolice) then {
    _player addAction [
        "<t color='#0000FF'>Arrest Player</t>",
        {
            _target = cursorTarget;
            if (!isNull _target && _target isKindOf "Man" && alive _target) then {
                [_target] call EDEN_fnc_arrestPlayer;
            };
        },
        [],
        7,
        false,
        true,
        "",
        "!isNull cursorTarget && cursorTarget isKindOf 'Man' && alive cursorTarget && _this distance cursorTarget < 3",
        3
    ];
    
    _player addAction [
        "<t color='#4080FF'>Search Player</t>",
        {
            _target = cursorTarget;
            if (!isNull _target && _target isKindOf "Man" && alive _target) then {
                [_target] call EDEN_fnc_searchPlayer;
            };
        },
        [],
        6,
        false,
        true,
        "",
        "!isNull cursorTarget && cursorTarget isKindOf 'Man' && alive cursorTarget && _this distance cursorTarget < 3",
        3
    ];
    
    _player addAction [
        "<t color='#8080FF'>Issue Ticket</t>",
        {
            _target = cursorTarget;
            if (!isNull _target && _target isKindOf "Man" && alive _target) then {
                [_target] call EDEN_fnc_ticketPlayer;
            };
        },
        [],
        5,
        false,
        true,
        "",
        "!isNull cursorTarget && cursorTarget isKindOf 'Man' && alive cursorTarget && _this distance cursorTarget < 3",
        3
    ];
    
    _player addAction [
        "<t color='#FF4080'>Impound Vehicle</t>",
        {
            _vehicle = cursorTarget;
            if (!isNull _vehicle && _vehicle isKindOf "LandVehicle") then {
                [_vehicle] call EDEN_fnc_impoundVehicle;
            };
        },
        [],
        4,
        false,
        true,
        "",
        "!isNull cursorTarget && cursorTarget isKindOf 'LandVehicle' && _this distance cursorTarget < 5",
        5
    ];
};

// Add medical-specific actions
if (_isMedic) then {
    _player addAction [
        "<t color='#FF0000'>Revive Player</t>",
        {
            _target = cursorTarget;
            if (!isNull _target && _target isKindOf "Man" && !alive _target) then {
                [_target] call EDEN_fnc_revivePlayer;
            };
        },
        [],
        7,
        false,
        true,
        "",
        "!isNull cursorTarget && cursorTarget isKindOf 'Man' && !alive cursorTarget && _this distance cursorTarget < 3",
        3
    ];
    
    _player addAction [
        "<t color='#FF4040'>Heal Player</t>",
        {
            _target = cursorTarget;
            if (!isNull _target && _target isKindOf "Man" && alive _target) then {
                [_target] call EDEN_fnc_healPlayer;
            };
        },
        [],
        6,
        false,
        true,
        "",
        "!isNull cursorTarget && cursorTarget isKindOf 'Man' && alive cursorTarget && _this distance cursorTarget < 3",
        3
    ];
    
    _player addAction [
        "<t color='#FF8080'>Treat Injuries</t>",
        {
            _target = cursorTarget;
            if (!isNull _target && _target isKindOf "Man" && alive _target) then {
                [_target] call EDEN_fnc_treatInjury;
            };
        },
        [],
        5,
        false,
        true,
        "",
        "!isNull cursorTarget && cursorTarget isKindOf 'Man' && alive cursorTarget && _this distance cursorTarget < 3",
        3
    ];
};

// Add admin-specific actions
if (_isAdmin) then {
    _player addAction [
        "<t color='#FF00FF'>Admin Menu</t>",
        {
            [] call EDEN_fnc_openAdminMenu;
        },
        [],
        15,
        false,
        true,
        "",
        "true",
        5
    ];
    
    _player addAction [
        "<t color='#FF80FF'>Teleport to Cursor</t>",
        {
            _pos = screenToWorld [0.5, 0.5];
            _player setPos [_pos select 0, _pos select 1, 0];
            ["Teleported to cursor position!"] call EDEN_fnc_showHint;
        },
        [],
        14,
        false,
        true,
        "",
        "true",
        5
    ];
    
    _player addAction [
        "<t color='#8000FF'>Spectate Mode</t>",
        {
            [] call EDEN_fnc_toggleSpectate;
        },
        [],
        13,
        false,
        true,
        "",
        "true",
        5
    ];
};

// Add interaction actions for other players
{
    if (_x != _player && alive _x && _x isKindOf "Man") then {
        _x addAction [
            format ["<t color='#FFFFFF'>Interact with %1</t>", name _x],
            {
                params ["_target", "_caller"];
                [_target, _caller] call EDEN_fnc_playerInteraction;
            },
            [],
            1,
            false,
            true,
            "",
            format ["_this == %1 && _this distance _target < 3", _player],
            3
        ];
    };
} forEach allPlayers;

// Log successful setup
[format ["[EDEN] Player actions set up for %1 (Role: %2)", name _player, _playerRole]] call EDEN_fnc_systemLogger;

// Return success
true
