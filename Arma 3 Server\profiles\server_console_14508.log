﻿ 7:59:05 BattlEye Server: Initialized (v1.220, Arma 3 2.20.152984)
 7:59:05 Game Port: 2302, Steam Query Port: 2303
 7:59:05 Warning: Current Steam AppId: 233780 doesn't match expected value: 107410
 7:59:05 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:59:05 Host identity created.
 7:59:05 Roles assigned.
 7:59:05 Reading mission ...
 7:59:10 Script core\actions\fn_robLocation.sqf not found
 7:59:10 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:59:10 Roles assigned.
 7:59:10 Reading mission ...
 7:59:11 Script core\actions\fn_robLocation.sqf not found
 7:59:11 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:59:11 Roles assigned.
 7:59:11 Reading mission ...
 7:59:11 Script core\actions\fn_robLocation.sqf not found
 7:59:12 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:59:12 Roles assigned.
 7:59:12 Reading mission ...
 7:59:12 Script core\actions\fn_robLocation.sqf not found
 7:59:12 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:59:12 Roles assigned.
 7:59:12 Reading mission ...
 7:59:13 Script core\actions\fn_robLocation.sqf not found
 7:59:13 Mission EdenRP.<PERSON>is read from directory.
 7:59:13 Roles assigned.
 7:59:13 Reading mission ...
 7:59:13 Script core\actions\fn_robLocation.sqf not found
 7:59:14 Mission EdenRP.Altis read from directory.
 7:59:14 Roles assigned.
 7:59:14 Reading mission ...
 7:59:14 Script core\actions\fn_robLocation.sqf not found
 7:59:14 Mission EdenRP.Altis read from directory.
 7:59:14 Roles assigned.
 7:59:14 Reading mission ...
 7:59:15 Script core\actions\fn_robLocation.sqf not found
 7:59:15 Mission EdenRP.Altis read from directory.
 7:59:15 Roles assigned.
 7:59:15 Reading mission ...
 7:59:15 Script core\actions\fn_robLocation.sqf not found
 7:59:16 Mission EdenRP.Altis read from directory.
 7:59:16 Roles assigned.
 7:59:16 Reading mission ...
 7:59:16 Script core\actions\fn_robLocation.sqf not found
 7:59:16 Mission EdenRP.Altis read from directory.
 7:59:16 Roles assigned.
 7:59:16 Reading mission ...
 7:59:17 Script core\actions\fn_robLocation.sqf not found
 7:59:17 Mission EdenRP.Altis read from directory.
 7:59:17 Roles assigned.
 7:59:17 Reading mission ...
 7:59:17 Script core\actions\fn_robLocation.sqf not found
 7:59:18 Mission EdenRP.Altis read from directory.
 7:59:18 Roles assigned.
 7:59:18 Reading mission ...
 7:59:18 Script core\actions\fn_robLocation.sqf not found
 7:59:18 Mission EdenRP.Altis read from directory.
 7:59:18 Roles assigned.
 7:59:18 Reading mission ...
 7:59:19 Script core\actions\fn_robLocation.sqf not found
 7:59:19 Mission EdenRP.Altis read from directory.
 7:59:19 Roles assigned.
 7:59:19 Reading mission ...
 7:59:19 Script core\actions\fn_robLocation.sqf not found
 7:59:20 Mission EdenRP.Altis read from directory.
