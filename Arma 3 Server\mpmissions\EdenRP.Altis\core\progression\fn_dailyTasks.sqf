/*
    File: fn_dailyTasks.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages daily tasks system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_dailyTaskPool") then {
            eden_dailyTaskPool = [
                ["earn_money", "Earn $5,000", 5000, 200],
                ["complete_jobs", "Complete 5 jobs", 5, 150],
                ["drive_distance", "Drive 10km", 10000, 100],
                ["help_players", "Help 3 players", 3, 250],
                ["visit_locations", "Visit 5 different locations", 5, 180]
            ];
            publicVariable "eden_dailyTaskPool";
        };
        _player setVariable ["eden_dailyTasks", [], true];
        _player setVariable ["eden_dailyTasksCompleted", [], true];
        _player setVariable ["eden_lastTaskReset", 0, true];
        true
    };
    case "generateDailyTasks": {
        _lastReset = _player getVariable ["eden_lastTaskReset", 0];
        _timeSince = time - _lastReset;
        
        if (_timeSince < 86400) exitWith { false };
        
        _player setVariable ["eden_dailyTasksCompleted", [], true];
        _player setVariable ["eden_lastTaskReset", time, true];
        
        _selectedTasks = [];
        _availableTasks = +eden_dailyTaskPool;
        
        for "_i" from 1 to 3 do {
            if (count _availableTasks > 0) then {
                _randomIndex = floor(random(count _availableTasks));
                _task = _availableTasks select _randomIndex;
                _selectedTasks pushBack [(_task select 0), (_task select 1), 0, (_task select 2), (_task select 3)];
                _availableTasks deleteAt _randomIndex;
            };
        };
        
        _player setVariable ["eden_dailyTasks", _selectedTasks, true];
        ["New daily tasks available!"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "updateTaskProgress": {
        params ["", "", ["_taskType", "earn_money", [""]], ["_amount", 1, [0]]];
        
        _tasks = _player getVariable ["eden_dailyTasks", []];
        _updated = false;
        
        {
            if ((_x select 0) == _taskType) then {
                _currentProgress = _x select 2;
                _target = _x select 3;
                _newProgress = (_currentProgress + _amount) min _target;
                _x set [2, _newProgress];
                
                if (_newProgress >= _target && !(_taskType in (_player getVariable ["eden_dailyTasksCompleted", []]))) then {
                    _completed = _player getVariable ["eden_dailyTasksCompleted", []];
                    _completed pushBack _taskType;
                    _player setVariable ["eden_dailyTasksCompleted", _completed, true];
                    
                    _reward = _x select 4;
                    [_player, "addExperience", _reward, "Daily Task"] call EDEN_fnc_experienceSystem;
                    [format ["Daily task completed: %1 (+%2 XP)", (_x select 1), _reward]] call EDEN_fnc_showHint;
                };
                _updated = true;
            };
        } forEach _tasks;
        
        if (_updated) then {
            _player setVariable ["eden_dailyTasks", _tasks, true];
            [_player] call EDEN_fnc_savePlayerData;
        };
        
        _updated
    };
    case "showDailyTasks": {
        _tasks = _player getVariable ["eden_dailyTasks", []];
        _completed = _player getVariable ["eden_dailyTasksCompleted", []];
        
        if (count _tasks == 0) exitWith {
            ["No daily tasks available. Check back tomorrow!"] call EDEN_fnc_showHint;
            false
        };
        
        _taskText = "=== DAILY TASKS ===\n";
        
        {
            _status = if ((_x select 0) in _completed) then { "✓" } else { "○" };
            _progress = format["%1/%2", (_x select 2), (_x select 3)];
            _taskText = _taskText + format["%1 %2 [%3]\n", _status, (_x select 1), _progress];
        } forEach _tasks;
        
        [_taskText] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
