/*
    File: fn_evidenceSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages evidence collection and storage system for police.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_evidenceType", "", [""]], ["_target", obj<PERSON>ull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_evidenceLocker") then {
            eden_evidenceLocker = [];
            publicVariable "eden_evidenceLocker";
        };
        _player setVariable ["eden_evidenceCollected", 0, true];
        true
    };
    case "collectEvidence": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty to collect evidence!"] call EDEN_fnc_showHint;
            false
        };
        
        if (!(playerSide == west)) exitWith {
            ["Only police can collect evidence!"] call EDEN_fnc_showHint;
            false
        };
        
        _evidenceTypes = [
            ["fingerprints", "Fingerprints"],
            ["dna", "DNA Sample"],
            ["weapon", "Weapon Evidence"],
            ["drugs", "Drug Evidence"],
            ["documents", "Document Evidence"],
            ["digital", "Digital Evidence"],
            ["ballistics", "Ballistic Evidence"],
            ["trace", "Trace Evidence"]
        ];
        
        _evidenceData = [];
        {
            if ((_x select 0) == _evidenceType) then {
                _evidenceData = _x;
            };
        } forEach _evidenceTypes;
        
        if (count _evidenceData == 0) exitWith {
            ["Invalid evidence type!"] call EDEN_fnc_showHint;
            false
        };
        
        _evidenceId = format ["EVD_%1_%2", floor(random 10000), floor(time)];
        _collectionTime = time;
        _location = getPos _player;
        _nearestCity = [_location] call EDEN_fnc_getNearestCity;
        _description = _evidenceData select 1;
        
        _evidence = [
            _evidenceId,
            _evidenceType,
            _description,
            name _player,
            _collectionTime,
            _location,
            _nearestCity,
            "Active",
            ""
        ];
        
        eden_evidenceLocker pushBack _evidence;
        publicVariable "eden_evidenceLocker";
        
        _collected = _player getVariable ["eden_evidenceCollected", 0];
        _player setVariable ["eden_evidenceCollected", (_collected + 1), true];
        
        [format ["Evidence collected: %1 (ID: %2)", _description, _evidenceId]] call EDEN_fnc_showHint;
        [format ["[EVIDENCE] %1 collected %2 at %3", name _player, _description, _nearestCity], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "searchEvidence": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty to search evidence!"] call EDEN_fnc_showHint;
            false
        };
        
        _searchResults = [];
        {
            _evidence = _x;
            _type = _evidence select 1;
            _description = _evidence select 2;
            _status = _evidence select 7;
            
            if (_type == _evidenceType && _status == "Active") then {
                _searchResults pushBack _evidence;
            };
        } forEach eden_evidenceLocker;
        
        if (count _searchResults == 0) then {
            [format ["No %1 evidence found in locker", _evidenceType]] call EDEN_fnc_showHint;
        } else {
            _resultText = format ["Found %1 pieces of %2 evidence:\n", count _searchResults, _evidenceType];
            {
                _id = _x select 0;
                _desc = _x select 2;
                _collector = _x select 3;
                _resultText = _resultText + format ["- %1 by %2\n", _desc, _collector];
            } forEach _searchResults;
            
            [_resultText] call EDEN_fnc_showHint;
        };
        
        true
    };
    case "analyzeEvidence": {
        params ["", "", "", "", ["_evidenceId", "", [""]]];
        
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty to analyze evidence!"] call EDEN_fnc_showHint;
            false
        };
        
        _evidenceFound = false;
        _evidenceIndex = -1;
        
        {
            if ((_x select 0) == _evidenceId) then {
                _evidenceFound = true;
                _evidenceIndex = _forEachIndex;
            };
        } forEach eden_evidenceLocker;
        
        if (!_evidenceFound) exitWith {
            ["Evidence not found!"] call EDEN_fnc_showHint;
            false
        };
        
        _evidence = eden_evidenceLocker select _evidenceIndex;
        _type = _evidence select 1;
        _description = _evidence select 2;
        
        _analysisTime = 30 + (random 60); // 30-90 seconds
        [format ["Analyzing %1... Please wait %2 seconds", _description, floor _analysisTime]] call EDEN_fnc_showHint;
        
        sleep _analysisTime;
        
        _analysisResults = [];
        switch (_type) do {
            case "fingerprints": {
                _analysisResults = ["Partial match found in database", "Quality: Good", "Confidence: 85%"];
            };
            case "dna": {
                _analysisResults = ["DNA profile extracted", "Markers: 13/13", "Database match: Pending"];
            };
            case "weapon": {
                _analysisResults = ["Serial number traced", "Registered owner identified", "Ballistics match possible"];
            };
            case "drugs": {
                _analysisResults = ["Substance: Cocaine", "Purity: 78%", "Origin: South American"];
            };
            default {
                _analysisResults = ["Analysis complete", "Results inconclusive", "Further testing required"];
            };
        };
        
        _resultText = format ["Analysis Results for %1:\n", _description];
        {
            _resultText = _resultText + format ["- %1\n", _x];
        } forEach _analysisResults;
        
        _evidence set [8, _resultText]; // Store analysis results
        eden_evidenceLocker set [_evidenceIndex, _evidence];
        publicVariable "eden_evidenceLocker";
        
        [_resultText] call EDEN_fnc_showHint;
        [format ["[EVIDENCE] %1 analyzed evidence %2", name _player, _evidenceId], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
        true
    };
    case "transferEvidence": {
        params ["", "", "", "", ["_evidenceId", "", [""]], ["_targetOfficer", objNull, [objNull]]];
        
        if (isNull _targetOfficer) exitWith {
            ["No target officer specified!"] call EDEN_fnc_showHint;
            false
        };
        
        if (!(playerSide == west) || !(_targetOfficer getVariable ["eden_onDuty", false])) exitWith {
            ["Target must be an on-duty police officer!"] call EDEN_fnc_showHint;
            false
        };
        
        _evidenceFound = false;
        _evidenceIndex = -1;
        
        {
            if ((_x select 0) == _evidenceId) then {
                _evidenceFound = true;
                _evidenceIndex = _forEachIndex;
            };
        } forEach eden_evidenceLocker;
        
        if (!_evidenceFound) exitWith {
            ["Evidence not found!"] call EDEN_fnc_showHint;
            false
        };
        
        _evidence = eden_evidenceLocker select _evidenceIndex;
        _evidence set [3, name _targetOfficer]; // Change custody
        eden_evidenceLocker set [_evidenceIndex, _evidence];
        publicVariable "eden_evidenceLocker";
        
        _description = _evidence select 2;
        [format ["Evidence %1 transferred to %2", _description, name _targetOfficer]] call EDEN_fnc_showHint;
        [format ["Evidence %1 transferred from %2", _description, name _player]] remoteExec ["EDEN_fnc_showHint", _targetOfficer];
        
        [format ["[EVIDENCE] %1 transferred evidence %2 to %3", name _player, _evidenceId, name _targetOfficer], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
        true
    };
    case "disposeEvidence": {
        params ["", "", "", "", ["_evidenceId", "", [""]]];
        
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty to dispose evidence!"] call EDEN_fnc_showHint;
            false
        };
        
        _evidenceFound = false;
        _evidenceIndex = -1;
        
        {
            if ((_x select 0) == _evidenceId) then {
                _evidenceFound = true;
                _evidenceIndex = _forEachIndex;
            };
        } forEach eden_evidenceLocker;
        
        if (!_evidenceFound) exitWith {
            ["Evidence not found!"] call EDEN_fnc_showHint;
            false
        };
        
        _evidence = eden_evidenceLocker select _evidenceIndex;
        _evidence set [7, "Disposed"]; // Change status
        eden_evidenceLocker set [_evidenceIndex, _evidence];
        publicVariable "eden_evidenceLocker";
        
        _description = _evidence select 2;
        [format ["Evidence %1 has been disposed", _description]] call EDEN_fnc_showHint;
        [format ["[EVIDENCE] %1 disposed evidence %2", name _player, _evidenceId], "WARNING", "POLICE"] call EDEN_fnc_systemLogger;
        true
    };
    case "viewEvidenceLocker": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty to view evidence locker!"] call EDEN_fnc_showHint;
            false
        };
        
        _activeEvidence = [];
        {
            if ((_x select 7) == "Active") then {
                _activeEvidence pushBack _x;
            };
        } forEach eden_evidenceLocker;
        
        if (count _activeEvidence == 0) then {
            ["Evidence locker is empty"] call EDEN_fnc_showHint;
        } else {
            _lockerText = format ["Evidence Locker (%1 items):\n", count _activeEvidence];
            {
                _id = _x select 0;
                _desc = _x select 2;
                _collector = _x select 3;
                _location = _x select 6;
                _lockerText = _lockerText + format ["- %1 by %2 at %3\n", _desc, _collector, _location];
            } forEach _activeEvidence;
            
            [_lockerText] call EDEN_fnc_showHint;
        };
        
        true
    };
    default { false };
};
