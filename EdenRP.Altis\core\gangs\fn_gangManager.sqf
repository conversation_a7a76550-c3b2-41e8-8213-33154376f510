/*
    EdenRP Gang Manager
    Enhanced gang system with territories and progression
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_action", "", [""]],
    ["_data", [], [[]]]
];

// Validate parameters
if (isNull _player || !isPlayer _player) exitWith {
    ["Invalid player provided to gangMana<PERSON>", "ERROR", "GANGS"] call EDEN_fnc_systemLogger;
    false
};

private _result = false;
switch (toLower _action) do {
    case "create": {
        _result = [_player, _data] call EDEN_fnc_createGang;
    };
    case "join": {
        _result = [_player, _data] call EDEN_fnc_joinGang;
    };
    case "leave": {
        _result = [_player] call EDEN_fnc_leaveGang;
    };
    case "invite": {
        _result = [_player, _data] call EDEN_fnc_inviteToGang;
    };
    case "kick": {
        _result = [_player, _data] call EDEN_fnc_kickFromGang;
    };
    case "promote": {
        _result = [_player, _data] call EDEN_fnc_promoteGangMember;
    };
    case "demote": {
        _result = [_player, _data] call EDEN_fnc_demoteGangMember;
    };
    case "deposit": {
        _result = [_player, _data] call EDEN_fnc_gangBankDeposit;
    };
    case "withdraw": {
        _result = [_player, _data] call EDEN_fnc_gangBankWithdraw;
    };
    case "territory": {
        _result = [_player, _data] call EDEN_fnc_gangTerritoryAction;
    };
    case "alliance": {
        _result = [_player, _data] call EDEN_fnc_gangAllianceAction;
    };
    case "war": {
        _result = [_player, _data] call EDEN_fnc_gangWarAction;
    };
    case "getinfo": {
        _result = [_player] call EDEN_fnc_getGangInfo;
    };
    case "getmembers": {
        _result = [_player] call EDEN_fnc_getGangMembers;
    };
    case "getterritories": {
        _result = [_player] call EDEN_fnc_getGangTerritories;
    };
    default {
        [format["Unknown gang action: %1", _action], "ERROR", "GANGS"] call EDEN_fnc_systemLogger;
    };
};

_result
