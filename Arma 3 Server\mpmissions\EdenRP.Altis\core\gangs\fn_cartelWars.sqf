/*
    File: fn_cartelWars.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages gang cartel wars system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_targetGang", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_gangWars") then {
            eden_gangWars = [];
            publicVariable "eden_gangWars";
        };
        _player setVariable ["eden_warParticipation", 0, true];
        true
    };
    case "declareWar": {
        _gang = _player getVariable ["eden_gang", ""];
        _rank = _player getVariable ["eden_gangRank", ""];
        
        if (_gang == "" || _rank != "Leader") exitWith {
            ["Only gang leaders can declare war!"] call EDEN_fnc_showHint;
            false
        };
        
        _war = [_gang, _targetGang, time, "Active"];
        eden_gangWars pushBack _war;
        publicVariable "eden_gangWars";
        
        [format ["War declared against %1!", _targetGang]] call EDEN_fnc_showHint;
        true
    };
    case "endWar": {
        _gang = _player getVariable ["eden_gang", ""];
        _rank = _player getVariable ["eden_gangRank", ""];
        
        if (_gang == "" || _rank != "Leader") exitWith {
            ["Only gang leaders can end wars!"] call EDEN_fnc_showHint;
            false
        };
        
        {
            if (((_x select 0) == _gang || (_x select 1) == _gang) && (_x select 3) == "Active") then {
                _x set [3, "Ended"];
                eden_gangWars set [_forEachIndex, _x];
            };
        } forEach eden_gangWars;
        
        publicVariable "eden_gangWars";
        ["War ended"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
