/*
    File: fn_underCoverSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages undercover operations system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_undercover", false, true];
        _player setVariable ["eden_coverId", "", true];
        _player setVariable ["eden_underCoverOps", 0, true];
        true
    };
    case "goUndercover": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty!"] call EDEN_fnc_showHint;
            false
        };
        
        _rank = _player getVariable ["eden_policeRank", "Cadet"];
        if !(_rank in ["Sergeant", "Lieutenant", "Captain", "Chief"]) exitWith {
            ["Insufficient rank for undercover operations!"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_undercover", true, true];
        _coverId = format ["UC_%1_%2", name _player, floor(random 1000)];
        _player setVariable ["eden_coverId", _coverId, true];
        
        // Change appearance
        removeUniform _player;
        removeVest _player;
        removeHeadgear _player;
        
        _player forceAddUniform "U_C_Poloshirt_stripped";
        
        _ops = _player getVariable ["eden_underCoverOps", 0];
        _player setVariable ["eden_underCoverOps", (_ops + 1), true];
        
        [format ["Now undercover as: %1", _coverId]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "blowCover": {
        if (!(_player getVariable ["eden_undercover", false])) exitWith {
            ["You are not undercover!"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_undercover", false, true];
        _player setVariable ["eden_coverId", "", true];
        
        // Restore police uniform
        removeUniform _player;
        _player forceAddUniform "U_B_GEN_Soldier_F";
        _player addVest "V_TacVest_blk_POLICE";
        
        ["Cover blown! Returning to uniform"] call EDEN_fnc_showHint;
        true
    };
    case "surveillance": {
        params ["", "", ["_target", objNull, [objNull]]];
        
        if (!(_player getVariable ["eden_undercover", false])) exitWith {
            ["You must be undercover for surveillance!"] call EDEN_fnc_showHint;
            false
        };
        
        if (isNull _target) exitWith { false };
        
        [format ["Surveillance initiated on %1", name _target]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
