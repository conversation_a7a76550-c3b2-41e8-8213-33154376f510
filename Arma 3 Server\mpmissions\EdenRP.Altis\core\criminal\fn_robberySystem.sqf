/*
    EdenRP Robbery System
    Enhanced criminal activities with risk/reward mechanics
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_action", "", [""]],
    ["_target", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_data", [], [[]]]
];

// Validate parameters
if (isNull _player || !isPlayer _player) exitWith {
    ["Invalid player provided to robberySystem", "ERROR", "CRIMINAL"] call EDEN_fnc_systemLogger;
    false
};

private _result = false;
switch (toLower _action) do {
    case "bank": {
        _result = [_player, _data] call EDEN_fnc_robBank;
    };
    case "store": {
        _result = [_player, _data] call EDEN_fnc_robStore;
    };
    case "player": {
        _result = [_player, _target, _data] call EDEN_fnc_robPlayer;
    };
    case "vehicle": {
        _result = [_player, _target, _data] call EDEN_fnc_robVehicle;
    };
    case "federal": {
        _result = [_player, _data] call <PERSON>DEN_fnc_robFederal;
    };
    case "jewelry": {
        _result = [_player, _data] call EDEN_fnc_robJewelry;
    };
    case "gas": {
        _result = [_player, _data] call EDEN_fnc_robGasStation;
    };
    case "house": {
        _result = [_player, _data] call EDEN_fnc_robHouse;
    };
    case "getlocations": {
        _result = [] call EDEN_fnc_getRobberyLocations;
    };
    case "getstatus": {
        _result = [_data] call EDEN_fnc_getRobberyStatus;
    };
    default {
        [format["Unknown robbery action: %1", _action], "ERROR", "CRIMINAL"] call EDEN_fnc_systemLogger;
    };
};

_result
