/*
    EdenRP Gang Territory System
    Enhanced territory control with strategic gameplay
*/

params [
    ["_gang", 0, [0]],
    ["_action", "", [""]],
    ["_territory", 0, [0]],
    ["_data", [], [[]]]
];

private _result = false;
switch (toLower _action) do {
    case "capture": {
        _result = [_gang, _territory, _data] call EDEN_fnc_captureTerritory;
    };
    case "defend": {
        _result = [_gang, _territory, _data] call EDEN_fnc_defendTerritory;
    };
    case "contest": {
        _result = [_gang, _territory, _data] call EDEN_fnc_contestTerritory;
    };
    case "upgrade": {
        _result = [_gang, _territory, _data] call EDEN_fnc_upgradeTerritory;
    };
    case "collect": {
        _result = [_gang, _territory] call EDEN_fnc_collectTerritoryIncome;
    };
    case "getinfo": {
        _result = [_territory] call EDEN_fnc_getTerritoryInfo;
    };
    case "getall": {
        _result = [] call EDEN_fnc_getAllTerritories;
    };
    default {
        [format["Unknown territory action: %1", _action], "ERROR", "TERRITORY"] call EDEN_fnc_systemLogger;
    };
};

_result
