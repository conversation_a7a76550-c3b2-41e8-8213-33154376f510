/*
    File: fn_setupEventHandlers.sqf
    Author: EdenRP Development Team
    
    Description:
    Sets up essential event handlers for the EdenRP framework.
    
    Parameters:
    0: OBJECT - Player object
    
    Returns:
    BOOLEAN - True if event handlers were successfully set up
    
    Example:
    [player] call EDEN_fnc_setupEventHandlers;
*/

params [
    ["_player", player, [obj<PERSON><PERSON>]]
];

// Validate parameters
if (isNull _player) exitWith {
    ["[EDEN] fn_setupEventHandlers: Invalid player object"] call EDEN_fnc_systemLogger;
    false
};

// Player killed event handler
_player addEventHandler ["Killed", {
    params ["_unit", "_killer", "_instigator", "_useEffects"];
    
    // Log the death
    _killerName = if (!isNull _killer) then { name _killer } else { "Unknown" };
    [format ["[EDEN] Player %1 was killed by %2", name _unit, _killerName]] call EDEN_fnc_systemLogger;
    
    // Handle death based on player role
    _playerRole = _unit getVariable ["eden_playerRole", "civilian"];
    
    switch (_playerRole) do {
        case "police": {
            [_unit, _killer] call EDEN_fnc_onPoliceKilled;
        };
        case "medical": {
            [_unit, _killer] call EDEN_fnc_onMedicalKilled;
        };
        default {
            [_unit, _killer] call EDEN_fnc_onPlayerKilled;
        };
    };
    
    // Save player data
    [_unit] call EDEN_fnc_savePlayerData;
}];

// Player respawn event handler
_player addEventHandler ["Respawn", {
    params ["_unit", "_corpse"];
    
    // Log the respawn
    [format ["[EDEN] Player %1 respawned", name _unit]] call EDEN_fnc_systemLogger;
    
    // Get previous role
    _previousRole = _corpse getVariable ["eden_playerRole", "civilian"];
    
    // Reinitialize player based on previous role
    switch (_previousRole) do {
        case "police": {
            _rank = _corpse getVariable ["eden_policeRank", "Officer"];
            [_unit, _rank] call EDEN_fnc_initializePolice;
        };
        case "medical": {
            _rank = _corpse getVariable ["eden_medicalRank", "Paramedic"];
            [_unit, _rank] call EDEN_fnc_initializeMedical;
        };
        case "admin": {
            _level = _corpse getVariable ["eden_adminLevel", 1];
            [_unit, _level] call EDEN_fnc_initializeAdmin;
        };
        default {
            [_unit] call EDEN_fnc_initializeCivilian;
        };
    };
}];

// Player disconnected event handler
_player addEventHandler ["Disconnected", {
    params ["_unit", "_id", "_uid", "_name"];
    
    // Log disconnection
    [format ["[EDEN] Player %1 (UID: %2) disconnected", _name, _uid]] call EDEN_fnc_systemLogger;
    
    // Save player data before disconnect
    [_unit] call EDEN_fnc_savePlayerData;
    
    // Clean up player-specific variables
    [_unit] call EDEN_fnc_cleanupPlayer;
}];

// Player hit event handler
_player addEventHandler ["Hit", {
    params ["_unit", "_source", "_damage", "_instigator"];
    
    // Check if player is in safe zone
    if (_unit getVariable ["eden_inSafeZone", false]) then {
        _unit setDamage 0;
        if (!isNull _source && _source != _unit) then {
            ["You cannot attack players in safe zones!"] remoteExec ["EDEN_fnc_showHint", _source];
        };
    };
    
    // Handle medical system damage
    if (damage _unit > 0.8 && alive _unit) then {
        [_unit] call EDEN_fnc_handleCriticalInjury;
    };
}];

// Player inventory opened event handler
_player addEventHandler ["InventoryOpened", {
    params ["_unit", "_container"];
    
    // Check if player can access this container
    if (!([_unit, _container] call EDEN_fnc_canAccessContainer)) then {
        false
    } else {
        true
    };
}];

// Player inventory closed event handler
_player addEventHandler ["InventoryClosed", {
    params ["_unit", "_container"];
    
    // Update player weight
    [_unit] call EDEN_fnc_updatePlayerWeight;
    
    // Save inventory changes
    [_unit] call EDEN_fnc_savePlayerInventory;
}];

// Player entered vehicle event handler
_player addEventHandler ["GetInMan", {
    params ["_unit", "_role", "_vehicle", "_turret"];
    
    // Log vehicle entry
    [format ["[EDEN] Player %1 entered vehicle %2 as %3", name _unit, typeOf _vehicle, _role]] call EDEN_fnc_systemLogger;
    
    // Check if player has required license
    if (!([_unit, _vehicle] call EDEN_fnc_hasVehicleLicense)) then {
        _unit action ["GetOut", _vehicle];
        ["You don't have the required license for this vehicle!"] call EDEN_fnc_showHint;
    };
    
    // Handle police vehicle access
    if (_vehicle getVariable ["eden_isPoliceVehicle", false] && !(_unit getVariable ["eden_isPolice", false])) then {
        _unit action ["GetOut", _vehicle];
        ["This is a police vehicle!"] call EDEN_fnc_showHint;
    };
    
    // Handle medical vehicle access
    if (_vehicle getVariable ["eden_isMedicalVehicle", false] && !(_unit getVariable ["eden_isMedic", false])) then {
        _unit action ["GetOut", _vehicle];
        ["This is a medical vehicle!"] call EDEN_fnc_showHint;
    };
}];

// Player exited vehicle event handler
_player addEventHandler ["GetOutMan", {
    params ["_unit", "_role", "_vehicle", "_turret"];
    
    // Log vehicle exit
    [format ["[EDEN] Player %1 exited vehicle %2", name _unit, typeOf _vehicle]] call EDEN_fnc_systemLogger;
    
    // Auto-lock police/medical vehicles
    if (_vehicle getVariable ["eden_isPoliceVehicle", false] || _vehicle getVariable ["eden_isMedicalVehicle", false]) then {
        _vehicle lock 2;
    };
}];

// Player fired weapon event handler
_player addEventHandler ["Fired", {
    params ["_unit", "_weapon", "_muzzle", "_mode", "_ammo", "_magazine", "_projectile", "_gunner"];
    
    // Log weapon fire in populated areas
    if ([_unit] call EDEN_fnc_isInCity) then {
        [format ["[EDEN] Player %1 fired weapon %2 in city", name _unit, _weapon]] call EDEN_fnc_systemLogger;
        
        // Alert police if civilian fires weapon in city
        if (!(_unit getVariable ["eden_isPolice", false]) && !(_unit getVariable ["eden_isMedic", false])) then {
            [_unit, getPos _unit] call EDEN_fnc_alertPolice;
        };
    };
}];

// Player took damage event handler
_player addEventHandler ["HandleDamage", {
    params ["_unit", "_selection", "_damage", "_source", "_projectile", "_hitIndex", "_instigator", "_hitPoint"];
    
    // God mode for admins
    if (_unit getVariable ["eden_adminMode", false] && _unit getVariable ["eden_godMode", false]) then {
        0
    } else {
        _damage
    };
}];

// Setup key event handlers
_player addEventHandler ["KeyDown", {
    params ["_displayOrControl", "_key", "_shift", "_ctrl", "_alt"];
    
    // Handle admin hotkeys
    if (_player getVariable ["eden_isAdmin", false]) then {
        switch (_key) do {
            case 59: { // F1 - Admin Menu
                [] call EDEN_fnc_openAdminMenu;
                true
            };
            case 60: { // F2 - Teleport to cursor
                if (_player getVariable ["eden_canTeleport", false]) then {
                    [] call EDEN_fnc_teleportToCursor;
                };
                true
            };
            case 61: { // F3 - Spectate
                [] call EDEN_fnc_toggleSpectate;
                true
            };
            case 62: { // F4 - God mode
                [] call EDEN_fnc_toggleGodMode;
                true
            };
        };
    };
    
    false
}];

// Log successful setup
[format ["[EDEN] Event handlers set up for player %1", name _player]] call EDEN_fnc_systemLogger;

// Return success
true
