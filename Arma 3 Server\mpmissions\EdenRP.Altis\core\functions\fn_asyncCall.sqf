/*
    EdenRP Async Database Call
    Enhanced asynchronous database call system
    
    This function provides secure database calls with
    query tracking and error handling
*/

params [
    ["_query", "", [""]],
    ["_mode", 1, [0]]
];

// Validate parameters
if (_query == "") exitWith {
    ["Empty query provided to async<PERSON><PERSON>", "ERROR", "DATABASE"] call EDEN_fnc_systemLogger;
    -1
};

// Only run on server
if (hasInterface) exitWith {
    ["asyncCall called on client", "ERROR", "DATABASE"] call EDEN_fnc_systemLogger;
    -1
};

// Check database connection
if (!EDEN_DatabaseConnected) exitWith {
    ["Database not connected", "ERROR", "DATABASE"] call EDEN_fnc_systemLogger;
    -1
};

// Generate unique query ID
if (isNil "EDEN_QueryCounter") then {
    EDEN_QueryCounter = 0;
};

EDEN_QueryCounter = EDEN_QueryCounter + 1;
private _queryId = EDEN_QueryCounter;

// Log query (without sensitive data)
private _logQuery = _query;
if (_query find "password" > -1 || _query find "Password" > -1) then {
    _logQuery = "*** SENSITIVE DATA QUERY ***";
};

[format["Executing database query %1: %2", _queryId, _logQuery], "DEBUG", "DATABASE"] call EDEN_fnc_systemLogger;

// Execute query based on mode
private _result = -1;
switch (_mode) do {
    case 1: {
        // Async call - no return data expected
        _result = "extDB3" callExtension format["0:SQL:%1", _query];
    };
    case 2: {
        // Sync call - return data expected
        _result = "extDB3" callExtension format["1:SQL:%1", _query];
    };
    case 3: {
        // Custom protocol call
        _result = "extDB3" callExtension _query;
    };
    default {
        ["Invalid mode provided to asyncCall", "ERROR", "DATABASE"] call EDEN_fnc_systemLogger;
        _result = -1;
    };
};

// Parse result
if (_result == "") then {
    [format["Empty result from database query %1", _queryId], "WARNING", "DATABASE"] call EDEN_fnc_systemLogger;
    _result = -1;
} else {
    // Check for error codes
    if (_result select [0, 1] == "[") then {
        private _parsed = call compile _result;
        if (typeName _parsed == "ARRAY" && count _parsed > 0) then {
            private _code = _parsed select 0;
            if (_code == 0) then {
                [format["Database query %1 failed with error code 0", _queryId], "ERROR", "DATABASE"] call EDEN_fnc_systemLogger;
                _result = -1;
            } else {
                _result = _code;
            };
        };
    };
};

// Update database statistics
if (!isNil "EDEN_ServerStats") then {
    private _dbQueries = (EDEN_ServerStats select {(_x select 0) == "DatabaseQueries"}) select 0;
    if (!isNil "_dbQueries") then {
        _dbQueries set [1, (_dbQueries select 1) + 1];
    };
};

_result
