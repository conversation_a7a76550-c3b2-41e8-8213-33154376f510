/*
    EdenRP Client Initialization
    Enhanced client-side initialization system
    
    This function handles the core client initialization
    with improved security and performance monitoring
*/

// Only run on clients
if (!hasInterface) exitWith {};

// Initialize client variables
EDEN_ClientInitialized = false;
EDEN_ClientVersion = "1.0.0";
EDEN_ClientStartTime = diag_tickTime;

// Log initialization start
["Client initialization started", "INFO", "CLIENT"] call EDEN_fnc_systemLogger;

// Wait for essential systems
waitUntil {!isNull player};
waitUntil {!isNil "bis_fnc_init"};
waitUntil {!isNil "EDEN_ServerReady"};

// Initialize client security
[] call EDEN_fnc_initializeClientSecurity;

// Initialize client performance monitoring
[] call EDEN_fnc_initializeClientPerformance;

// Initialize client UI systems
[] call EDEN_fnc_initializeClientUI;

// Initialize client communication
[] call EDEN_fnc_initializeClientCommunication;

// Initialize client inventory
[] call EDEN_fnc_initializeClientInventory;

// Initialize client vehicle systems
[] call EDEN_fnc_initializeClientVehicles;

// Initialize client job systems
[] call EDEN_fnc_initializeClientJobs;

// Initialize client progression
[] call EDEN_fnc_initializeClientProgression;

// Setup client event handlers
[] call EDEN_fnc_setupClientEventHandlers;

// Setup client key bindings
[] call EDEN_fnc_setupClientKeyBindings;

// Initialize faction-specific systems
switch (playerSide) do {
    case civilian: {
        [] call EDEN_fnc_initializeCivilianClient;
    };
    case west: {
        [] call EDEN_fnc_initializePoliceClient;
    };
    case independent: {
        [] call EDEN_fnc_initializeMedicalClient;
    };
};

// Start client monitoring loops
[] spawn EDEN_fnc_clientMonitoringLoop;
[] spawn EDEN_fnc_clientPerformanceLoop;
[] spawn EDEN_fnc_clientSecurityLoop;

// Mark client as initialized
EDEN_ClientInitialized = true;

// Log initialization completion
private _initTime = diag_tickTime - EDEN_ClientStartTime;
[format["Client initialization completed in %1 seconds", _initTime], "INFO", "CLIENT"] call EDEN_fnc_systemLogger;

// Notify server of successful initialization
[getPlayerUID player, "CLIENT_READY"] remoteExec ["EDEN_fnc_clientStatusUpdate", 2];

true
