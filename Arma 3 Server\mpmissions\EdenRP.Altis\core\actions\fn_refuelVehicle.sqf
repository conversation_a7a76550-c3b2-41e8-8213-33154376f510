/*
    File: fn_refuelVehicle.sqf
    Author: EdenRP Development Team
    
    Description:
    Refuels a vehicle at gas stations.
    
    Parameters:
    0: OBJECT - Vehicle to refuel
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if refuel was successful
*/

params [
    ["_vehicle", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (isNull _vehicle || isNull _player) exitWith { false };

if (_player distance _vehicle > 10) exitWith {
    ["Vehicle is too far away!"] call EDEN_fnc_showHint;
    false
};

if (!(_vehicle isKindOf "LandVehicle") && !(_vehicle isKindOf "Air") && !(_vehicle isKindOf "Ship")) exitWith {
    ["This object cannot be refueled!"] call EDEN_fnc_showHint;
    false
};

if (!alive _vehicle) exitWith {
    ["Cannot refuel destroyed vehicles!"] call EDEN_fnc_showHint;
    false
};

// Check if near gas station
_nearGasStations = nearestObjects [_player, ["Land_fs_feed_F", "Land_FuelStation_Feed_F", "StorageBladder_01_fuel_forest_F"], 15];
if (count _nearGasStations == 0) exitWith {
    ["You must be near a gas station to refuel!"] call EDEN_fnc_showHint;
    false
};

_currentFuel = fuel _vehicle;
if (_currentFuel > 0.95) exitWith {
    ["Vehicle is already full!"] call EDEN_fnc_showHint;
    false
};

// Calculate fuel needed and cost
_fuelNeeded = 1 - _currentFuel;
_fuelCost = round(_fuelNeeded * 200); // $200 for full tank

_playerMoney = _player getVariable ["eden_cash", 0];
if (_playerMoney < _fuelCost) exitWith {
    [format ["Refuel costs $%1, but you only have $%2!", _fuelCost, _playerMoney]] call EDEN_fnc_showHint;
    false
};

// Start refueling process
[_player, "Acts_carFixingWheel"] remoteExec ["switchMove"];
[format ["Refueling vehicle... ($%1)", _fuelCost]] call EDEN_fnc_showHint;

// Refueling animation with progress
_refuelTime = _fuelNeeded * 8; // 8 seconds for full tank
_startTime = time;

while {(time - _startTime) < _refuelTime} do {
    _progress = (time - _startTime) / _refuelTime;
    _newFuel = _currentFuel + (_fuelNeeded * _progress);
    _vehicle setFuel _newFuel;
    
    _percentage = round(_progress * 100);
    [format ["Refueling... %1%%", _percentage]] call EDEN_fnc_showHint;
    
    sleep 0.5;
};

[_player, ""] remoteExec ["switchMove"];

// Complete refueling
_vehicle setFuel 1;

// Charge money
_player setVariable ["eden_cash", (_playerMoney - _fuelCost), true];

// Add small experience
_expGained = 10;
_currentExp = _player getVariable ["eden_experience", 0];
_player setVariable ["eden_experience", (_currentExp + _expGained), true];

[format ["Vehicle refueled successfully! Cost: $%1 (+%2 XP)", _fuelCost, _expGained]] call EDEN_fnc_showHint;

// Log the action
[format ["[EDEN] Player %1 refueled vehicle %2 for $%3", name _player, typeOf _vehicle, _fuelCost], "INFO", "VEHICLE"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
