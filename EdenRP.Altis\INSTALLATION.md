# EdenRP Installation Guide

## Prerequisites

Before installing EdenRP, ensure you have the following:

### Server Requirements
- **Arma 3 Dedicated Server** (Latest version)
- **Windows Server 2016+** or **Linux Ubuntu 18.04+**
- **Minimum 8GB RAM** (16GB recommended)
- **50GB+ free disk space**
- **Stable internet connection** (100Mbps+ recommended)

### Database Requirements
- **MySQL 8.0+** or **MariaDB 10.4+**
- **Database user with full privileges**
- **UTF8MB4 character set support**

### Extensions Required
- **extDB3** (Latest version)
- **Visual C++ Redistributable 2019**

## Step 1: Database Setup

### 1.1 Create Database
```sql
CREATE DATABASE edenrp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 1.2 Create Database User
```sql
CREATE USER 'edenrp_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON edenrp.* TO 'edenrp_user'@'localhost';
FLUSH PRIVILEGES;
```

### 1.3 Import Database Schema
```bash
mysql -u edenrp_user -p edenrp < database/edenrp_schema.sql
```

## Step 2: extDB3 Installation

### 2.1 Download extDB3
- Download the latest extDB3 from the official repository
- Extract to your Arma 3 server directory

### 2.2 Configure extDB3
1. Copy `database/extdb3-conf.ini` to your extDB3 folder
2. Update database credentials in the configuration file:
```ini
[Database]
Host = 127.0.0.1
Port = 3306
Username = edenrp_user
Password = your_secure_password
Database = edenrp
```

### 2.3 Copy SQL Templates
Copy all `.ini` files from `database/` to your extDB3 `SQL_CUSTOM` folder:
- `eden_players.ini`
- `eden_vehicles.ini`
- `eden_gangs.ini`
- `eden_houses.ini`

## Step 3: Mission Installation

### 3.1 Copy Mission Files
1. Copy the entire `EdenRP.Altis` folder to your server's `mpmissions` directory
2. Ensure proper file permissions (read/write for server process)

### 3.2 Configure Server.cfg
Update your `server.cfg` file:
```
class Missions {
    class EdenRP {
        template = "EdenRP.Altis";
        difficulty = "Custom";
    };
};
```

### 3.3 Update Basic.cfg
Recommended settings for `basic.cfg`:
```
MaxMsgSend = 256;
MaxSizeGuaranteed = 512;
MaxSizeNonguaranteed = 256;
MinBandwidth = 131072;
MaxBandwidth = 2097152000;
MinErrorToSend = 0.001;
MinErrorToSendNear = 0.01;
MaxCustomFileSize = 160000;
```

## Step 4: Configuration

### 4.1 Server Configuration
Edit `config/server_config.hpp` to customize:
- Server name and information
- Economy settings
- Player limits
- Feature toggles

### 4.2 Location Configuration
Modify `config/locations.hpp` if needed:
- Spawn points
- Shop locations
- Territory positions
- Safe zones

### 4.3 Admin Setup
1. Start the server once to create initial database entries
2. Add admin privileges via database:
```sql
UPDATE eden_players SET admin_level = 5 WHERE player_id = 'YOUR_STEAM_ID';
```

## Step 5: Security Setup

### 5.1 BattlEye Configuration
Ensure BattlEye is properly configured:
- Update filters for EdenRP functions
- Whitelist necessary script commands
- Configure kick/ban thresholds

### 5.2 Firewall Configuration
Open required ports:
- **2302** (Game port)
- **2303** (Steam query port)
- **2304** (Steam master port)
- **3306** (MySQL - if remote)

## Step 6: Testing

### 6.1 Initial Server Start
1. Start the Arma 3 server
2. Monitor RPT logs for errors
3. Check database connections
4. Verify extDB3 initialization

### 6.2 Client Testing
1. Connect with a test client
2. Verify character creation
3. Test basic functions:
   - Inventory system
   - Money transactions
   - Vehicle spawning
   - Job systems

### 6.3 Admin Testing
1. Test admin panel access
2. Verify logging systems
3. Test moderation tools
4. Check database logging

## Step 7: Performance Optimization

### 7.1 Database Optimization
```sql
-- Add indexes for better performance
ALTER TABLE eden_players ADD INDEX idx_last_seen (last_seen);
ALTER TABLE eden_vehicles ADD INDEX idx_last_used (last_used);
ALTER TABLE eden_logs ADD INDEX idx_timestamp (timestamp);
```

### 7.2 Server Optimization
- Adjust view distances based on player count
- Configure cleanup intervals
- Monitor memory usage
- Set appropriate restart schedules

## Step 8: Backup Setup

### 8.1 Database Backups
Create automated backup script:
```bash
#!/bin/bash
mysqldump -u edenrp_user -p edenrp > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 8.2 Mission File Backups
- Regular backups of mission files
- Version control for configuration changes
- Player data export procedures

## Troubleshooting

### Common Issues

#### Database Connection Failed
- Check MySQL service status
- Verify credentials in extdb3-conf.ini
- Ensure database exists and user has privileges
- Check firewall settings

#### extDB3 Not Loading
- Verify Visual C++ Redistributable installation
- Check file permissions
- Ensure correct extDB3 version
- Review server RPT logs

#### Players Can't Connect
- Check mission file integrity
- Verify server.cfg configuration
- Ensure proper port forwarding
- Check BattlEye configuration

#### Performance Issues
- Monitor server resources
- Adjust view distances
- Optimize database queries
- Review cleanup settings

### Log Locations
- **Server RPT**: `Arma3Server_x64.rpt`
- **extDB3 Logs**: `extDB3/logs/`
- **EdenRP Logs**: Database `eden_logs` table
- **MySQL Logs**: `/var/log/mysql/` (Linux)

## Support

For technical support:
1. Check the troubleshooting section
2. Review server and database logs
3. Verify configuration files
4. Test with minimal setup

## Security Considerations

### Database Security
- Use strong passwords
- Limit database user privileges
- Enable SSL connections
- Regular security updates

### Server Security
- Keep Arma 3 server updated
- Configure proper firewall rules
- Monitor for suspicious activity
- Regular backup verification

### Admin Security
- Limit admin privileges appropriately
- Monitor admin actions
- Regular password changes
- Two-factor authentication (if available)

## Maintenance

### Regular Tasks
- **Daily**: Monitor logs and performance
- **Weekly**: Database optimization and cleanup
- **Monthly**: Full system backup and security review
- **Quarterly**: Update dependencies and review configurations

### Update Procedures
1. Backup current installation
2. Test updates on development server
3. Schedule maintenance window
4. Apply updates with rollback plan
5. Verify functionality post-update

This completes the EdenRP installation guide. Follow each step carefully and ensure all prerequisites are met for optimal performance and security.
