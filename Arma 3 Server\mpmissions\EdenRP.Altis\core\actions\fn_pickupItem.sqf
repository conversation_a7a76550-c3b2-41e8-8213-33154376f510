/*
    File: fn_pickupItem.sqf
    Author: EdenRP Development Team
    
    Description:
    Picks up an item from the ground or environment.
    
    Parameters:
    0: OBJECT - Item to pick up
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if item was picked up successfully
*/

params [
    ["_item", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (isNull _item || isNull _player) exitWith { false };

if (_player distance _item > 5) exitWith {
    ["Item is too far away!"] call EDEN_fnc_showHint;
    false
};

// Get item information
_itemType = _item getVariable ["eden_itemType", typeOf _item];
_itemQuantity = _item getVariable ["eden_itemQuantity", 1];
_itemValue = _item getVariable ["eden_itemValue", 0];

// Check if item is pickupable
if (_item getVariable ["eden_cannotPickup", false]) exitWith {
    ["This item cannot be picked up!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_player, "item_interaction", [_item, "pickup"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Check player's carrying capacity
_virtualItems = _player getVariable ["eden_virtualItems", []];
_currentWeight = 0;
{
    _itemWeight = [_x select 0] call EDEN_fnc_getItemWeight;
    _currentWeight = _currentWeight + (_itemWeight * (_x select 1));
} forEach _virtualItems;

_itemWeight = [_itemType] call EDEN_fnc_getItemWeight;
_totalWeight = _itemWeight * _itemQuantity;
_maxWeight = _player getVariable ["eden_maxWeight", 100];

if ((_currentWeight + _totalWeight) > _maxWeight) exitWith {
    [format ["Cannot carry item! Weight limit exceeded. (%1/%2 kg)", round(_currentWeight + _totalWeight), _maxWeight]] call EDEN_fnc_showHint;
    false
};

// Check for special item restrictions
_playerJob = _player getVariable ["eden_currentJob", "unemployed"];
_restrictedItems = ["police_radio", "medical_kit", "bomb_defusal_kit"];

if (_itemType in _restrictedItems) then {
    _canPickup = switch (_itemType) do {
        case "police_radio": { _player getVariable ["eden_isPolice", false] };
        case "medical_kit": { _player getVariable ["eden_isMedic", false] || _player getVariable ["eden_isDoctor", false] };
        case "bomb_defusal_kit": { _player getVariable ["eden_isBombSquad", false] };
        default { true };
    };
    
    if (!_canPickup) exitWith {
        ["You are not authorized to pick up this item!"] call EDEN_fnc_showHint;
        false
    };
};

// Start pickup animation
[_player, "Acts_carFixingWheel"] remoteExec ["switchMove"];
["Picking up item..."] call EDEN_fnc_showHint;

sleep 2; // Pickup takes 2 seconds

[_player, ""] remoteExec ["switchMove"];

// Add item to player's inventory
_found = false;
{
    if ((_x select 0) == _itemType) then {
        _x set [1, ((_x select 1) + _itemQuantity)];
        _found = true;
    };
} forEach _virtualItems;

if (!_found) then {
    _virtualItems pushBack [_itemType, _itemQuantity];
};

_player setVariable ["eden_virtualItems", _virtualItems, true];

// Handle special item effects
switch (_itemType) do {
    case "money": {
        _playerMoney = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_playerMoney + _itemValue), true];
        [format ["Picked up $%1 in cash!", _itemValue]] call EDEN_fnc_showHint;
    };
    case "evidence": {
        _evidenceData = _item getVariable ["eden_evidenceData", []];
        if (count _evidenceData > 0) then {
            _playerEvidence = _player getVariable ["eden_collectedEvidence", []];
            _playerEvidence pushBack _evidenceData;
            _player setVariable ["eden_collectedEvidence", _playerEvidence, true];
            ["Evidence collected!"] call EDEN_fnc_showHint;
        };
    };
    case "drugs": {
        if (_player getVariable ["eden_isPolice", false]) then {
            ["Illegal drugs confiscated!"] call EDEN_fnc_showHint;
        } else {
            _wantedLevel = _player getVariable ["eden_wantedLevel", 0];
            _player setVariable ["eden_wantedLevel", (_wantedLevel + 1), true];
            ["You picked up illegal drugs! Police may be alerted."] call EDEN_fnc_showHint;
            
            // Small chance to alert nearby police
            if (random 100 < 15) then {
                {
                    if (_x getVariable ["eden_isPolice", false] && _x distance _player < 200) then {
                        [
                            "Drug Activity Alert",
                            format ["Suspicious drug activity detected near %1", mapGridPosition _player],
                            8,
                            "warning"
                        ] remoteExec ["EDEN_fnc_showNotification", _x];
                    };
                } forEach allPlayers;
            };
        };
    };
    case "weapon": {
        _weaponLicense = _player getVariable ["eden_weaponLicense", false];
        if (!_weaponLicense && !(_player getVariable ["eden_isPolice", false])) then {
            _wantedLevel = _player getVariable ["eden_wantedLevel", 0];
            _player setVariable ["eden_wantedLevel", (_wantedLevel + 2), true];
            ["Illegal weapon possession! You are now wanted by police."] call EDEN_fnc_showHint;
        };
    };
    default {
        [format ["Picked up %1x %2", _itemQuantity, _itemType]] call EDEN_fnc_showHint;
    };
};

// Add experience for finding items
_expGained = _itemQuantity * 2;
_currentExp = _player getVariable ["eden_experience", 0];
_player setVariable ["eden_experience", (_currentExp + _expGained), true];

// Update statistics
_itemsCollected = _player getVariable ["eden_itemsCollected", 0];
_player setVariable ["eden_itemsCollected", (_itemsCollected + _itemQuantity), true];

// Remove item from world
deleteVehicle _item;

// Log item pickup
[format ["[EDEN] Player %1 picked up %2x %3", name _player, _itemQuantity, _itemType], "INFO", "ITEMS"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
