@echo off
title EdenRP One-Click Setup - Do It All For Me!
color 0B
echo.
echo ===============================================
echo    EdenRP ONE-CLICK SETUP
echo    "Do It All For Me" Edition
echo ===============================================
echo.

:: Check for admin rights
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] This script requires Administrator privileges!
    echo [INFO] Right-click and select "Run as Administrator"
    echo.
    pause
    exit /b 1
)

echo [INFO] Running with Administrator privileges ✓
echo.

:: Set paths
set "SERVER_PATH=C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server"
set "SETUP_PATH=%~dp0"

echo [INFO] Server Path: %SERVER_PATH%
echo [INFO] Setup Path: %SETUP_PATH%
echo.

:: Check if server directory exists
if not exist "%SERVER_PATH%" (
    echo [ERROR] Arma 3 Server directory not found!
    echo [INFO] Expected location: %SERVER_PATH%
    echo [INFO] Please install Arma 3 Server first using SteamCMD
    echo.
    pause
    exit /b 1
)

echo [✓] Arma 3 Server directory found
echo.

echo ===============================================
echo    PHASE 1: DIRECTORY SETUP
echo ===============================================
echo.

echo [1/8] Creating directory structure...
mkdir "%SERVER_PATH%\extDB3" 2>nul
mkdir "%SERVER_PATH%\extDB3\SQL_CUSTOM" 2>nul
mkdir "%SERVER_PATH%\extDB3\logs" 2>nul
mkdir "%SERVER_PATH%\mpmissions" 2>nul
mkdir "%SERVER_PATH%\profiles" 2>nul
mkdir "%SERVER_PATH%\profiles\server" 2>nul
mkdir "%SERVER_PATH%\battleye" 2>nul
mkdir "%SERVER_PATH%\keys" 2>nul
echo [✓] Directory structure created

echo.
echo [2/8] Copying EdenRP mission files...
if exist "%SETUP_PATH%..\EdenRP.Altis" (
    echo [INFO] Copying mission files... (this may take a moment)
    xcopy "%SETUP_PATH%..\" "%SERVER_PATH%\mpmissions\EdenRP.Altis\" /E /I /Y /Q >nul 2>&1
    echo [✓] EdenRP mission files copied
) else (
    echo [WARNING] EdenRP.Altis folder not found in setup directory
    echo [INFO] Please ensure this script is in the EdenRP.Altis\setup\ folder
)

echo.
echo [3/8] Copying database configuration...
if exist "%SETUP_PATH%..\database\extdb3-conf.ini" (
    copy "%SETUP_PATH%..\database\extdb3-conf.ini" "%SERVER_PATH%\extDB3\" >nul 2>&1
    echo [✓] extDB3 configuration copied
)

if exist "%SETUP_PATH%..\database\*.ini" (
    copy "%SETUP_PATH%..\database\*.ini" "%SERVER_PATH%\extDB3\SQL_CUSTOM\" >nul 2>&1
    echo [✓] SQL templates copied
)

echo.
echo ===============================================
echo    PHASE 2: DOWNLOADING COMPONENTS
echo ===============================================
echo.

echo [4/8] Downloading extDB3...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://github.com/SteezCram/extDB3/releases/download/v1033/extDB3-v1033-Windows-Latest.zip' -OutFile '%TEMP%\extDB3.zip' -UseBasicParsing}" 2>nul
if exist "%TEMP%\extDB3.zip" (
    echo [✓] extDB3 downloaded
    
    echo [INFO] Extracting extDB3...
    powershell -Command "Expand-Archive -Path '%TEMP%\extDB3.zip' -DestinationPath '%TEMP%\extDB3' -Force" 2>nul
    
    echo [INFO] Installing extDB3 files...
    for /r "%TEMP%\extDB3" %%f in (extDB3_x64.dll) do copy "%%f" "%SERVER_PATH%\extDB3\" >nul 2>&1
    for /r "%TEMP%\extDB3" %%f in (tbbmalloc_x64.dll) do copy "%%f" "%SERVER_PATH%\extDB3\" >nul 2>&1
    for /d %%d in ("%TEMP%\extDB3\*@extDB3*") do xcopy "%%d" "%SERVER_PATH%\@extDB3\" /E /I /Y /Q >nul 2>&1
    
    echo [✓] extDB3 installed
) else (
    echo [WARNING] Failed to download extDB3 - will open browser for manual download
)

echo.
echo [5/8] Downloading Visual C++ Redistributable...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://aka.ms/vs/16/release/vc_redist.x64.exe' -OutFile '%TEMP%\vc_redist.x64.exe' -UseBasicParsing}" 2>nul
if exist "%TEMP%\vc_redist.x64.exe" (
    echo [✓] Visual C++ Redistributable downloaded
    echo [INFO] Installing Visual C++ Redistributable...
    "%TEMP%\vc_redist.x64.exe" /quiet /norestart
    echo [✓] Visual C++ Redistributable installed
) else (
    echo [WARNING] Failed to download VC++ Redistributable
)

echo.
echo ===============================================
echo    PHASE 3: CONFIGURATION FILES
echo ===============================================
echo.

echo [6/8] Creating server configuration...
(
echo // EdenRP Server Configuration - Auto Generated
echo hostname = "EdenRP - Enhanced Altis Life Experience";
echo password = "";
echo passwordAdmin = "admin123";
echo serverCommandPassword = "server123";
echo class Missions {
echo     class EdenRP {
echo         template = "EdenRP.Altis";
echo         difficulty = "Custom";
echo     };
echo };
echo maxPlayers = 120;
echo kickDuplicate = 1;
echo verifySignatures = 2;
echo allowedFilePatching = 1;
echo requiredSecureId = 2;
echo maxPing = 300;
echo maxDesync = 150;
echo maxPacketLoss = 50;
echo timeStampFormat = "short";
echo logFile = "server_console.log";
echo serverMod = "@extDB3";
echo persistent = 1;
echo autoInit = 1;
) > "%SERVER_PATH%\server.cfg"

(
echo MaxMsgSend = 256;
echo MaxSizeGuaranteed = 512;
echo MaxSizeNonguaranteed = 256;
echo MinBandwidth = 131072;
echo MaxBandwidth = 2097152000;
echo MinErrorToSend = 0.001;
echo MinErrorToSendNear = 0.01;
echo MaxCustomFileSize = 160000;
echo terrainGrid = 25;
echo viewDistance = 3000;
echo preferredObjectViewDistance = 2000;
) > "%SERVER_PATH%\basic.cfg"

echo [✓] Server configuration files created

echo.
echo [7/8] Creating startup script...
(
echo @echo off
echo title EdenRP Server - Enhanced Altis Life
echo color 0A
echo echo ===============================================
echo echo    EdenRP Server Starting...
echo echo ===============================================
echo echo.
echo cd /d "%SERVER_PATH%"
echo arma3server_x64.exe -serverMod=@extDB3 -config=server.cfg -cfg=basic.cfg -profiles=profiles -name=server -world=Altis -autoInit -loadMissionToMemory
echo pause
) > "%SERVER_PATH%\start_server.bat"

echo [✓] Startup script created

echo.
echo [8/8] Creating database setup...
(
echo -- EdenRP Database Setup - Auto Generated
echo CREATE DATABASE IF NOT EXISTS edenrp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
echo CREATE USER IF NOT EXISTS 'edenrp_user'@'localhost' IDENTIFIED BY 'edenrp_password';
echo GRANT ALL PRIVILEGES ON edenrp.* TO 'edenrp_user'@'localhost';
echo FLUSH PRIVILEGES;
echo USE edenrp;
echo SELECT 'Database setup complete! Import the schema next.' AS Status;
) > "%SERVER_PATH%\setup_database.sql"

echo [✓] Database setup script created

echo.
echo ===============================================
echo    OPENING REQUIRED DOWNLOADS
echo ===============================================
echo.

echo [INFO] Opening MySQL download page...
start "" "https://dev.mysql.com/downloads/mysql/"
timeout /t 2 >nul

if not exist "%SERVER_PATH%\extDB3\extDB3_x64.dll" (
    echo [INFO] Opening extDB3 download page...
    start "" "https://github.com/SteezCram/extDB3/releases"
    timeout /t 2 >nul
)

echo.
echo ===============================================
echo    🎉 AUTOMATED SETUP COMPLETE! 🎉
echo ===============================================
echo.
echo ✅ COMPLETED AUTOMATICALLY:
echo    • Directory structure created
echo    • EdenRP mission files copied
echo    • Configuration files generated
echo    • extDB3 downloaded and installed
echo    • Visual C++ Redistributable installed
echo    • Server startup script created
echo    • Database setup script created
echo.
echo 📋 REMAINING MANUAL STEPS:
echo    1. Install MySQL from the opened browser tab
echo    2. Run setup_database.sql in MySQL
echo    3. Import EdenRP schema: mpmissions\EdenRP.Altis\database\edenrp_schema.sql
echo    4. Double-click start_server.bat to start your server
echo.
echo 🎮 CONNECT TO YOUR SERVER:
echo    • Open Arma 3
echo    • Multiplayer ^> Direct Connect
echo    • IP: 127.0.0.1:2302
echo.
echo 🔧 ADMIN CREDENTIALS:
echo    • Admin Password: admin123
echo    • Server Password: server123
echo    • Database User: edenrp_user
echo    • Database Password: edenrp_password
echo.
echo Your EdenRP server is ready to go! 🚀
echo.

:: Cleanup
del "%TEMP%\extDB3.zip" 2>nul
del "%TEMP%\vc_redist.x64.exe" 2>nul
rmdir /s /q "%TEMP%\extDB3" 2>nul

pause
