/*
    EdenRP Remote Execution Configuration
    Enhanced security configuration for client-server communication
    
    This configuration provides strict control over remote execution
    with enhanced security measures and proper function whitelisting
*/

class CfgRemoteExec {
    // Enhanced security mode - only whitelisted functions allowed
    class Functions {
        // Server-side functions callable from clients
        mode = 1; // Whitelist mode for enhanced security
        jip = 0;  // Disable JIP by default for security
        
        // Core system functions
        class EDEN_fnc_systemLogger {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_securityValidator {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        // Player management functions
        class EDEN_fnc_playerConnect {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_playerDisconnect {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_savePlayerData {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_loadPlayerData {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        // Police functions
        class EDEN_fnc_arrestPlayer {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_ticketPlayer {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_impoundVehicle {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_updateWantedList {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_dispatchAlert {
            allowedTargets = 0; // All clients
            jip = 0;
        };
        
        // Medical functions
        class EDEN_fnc_revivePlayer {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_hospitalRespawn {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_medicalAlert {
            allowedTargets = 0; // All clients
            jip = 0;
        };
        
        // Vehicle functions
        class EDEN_fnc_purchaseVehicle {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_sellVehicle {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_storeVehicle {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_retrieveVehicle {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        // Housing functions
        class EDEN_fnc_purchaseProperty {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_sellProperty {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_accessProperty {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        // Gang functions
        class EDEN_fnc_createGang {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_joinGang {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_leaveGang {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_gangBankTransaction {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        // Economy functions
        class EDEN_fnc_buyItem {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_sellItem {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_bankTransaction {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_updateMarketPrices {
            allowedTargets = 0; // All clients
            jip = 1; // Allow JIP for market updates
        };
        
        // Job functions
        class EDEN_fnc_startJob {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_completeJob {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_processResource {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        // Admin functions
        class EDEN_fnc_adminAction {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_kickPlayer {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_banPlayer {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_teleportPlayer {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        // Communication functions
        class EDEN_fnc_sendMessage {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_broadcastMessage {
            allowedTargets = 0; // All clients
            jip = 0;
        };
        
        class EDEN_fnc_emergencyCall {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        // Notification functions
        class EDEN_fnc_showNotification {
            allowedTargets = 1; // Clients only
            jip = 0;
        };
        
        class EDEN_fnc_showHint {
            allowedTargets = 1; // Clients only
            jip = 0;
        };
        
        class EDEN_fnc_playSound {
            allowedTargets = 1; // Clients only
            jip = 0;
        };
        
        // UI functions
        class EDEN_fnc_openDialog {
            allowedTargets = 1; // Clients only
            jip = 0;
        };
        
        class EDEN_fnc_closeDialog {
            allowedTargets = 1; // Clients only
            jip = 0;
        };
        
        class EDEN_fnc_updateDialog {
            allowedTargets = 1; // Clients only
            jip = 0;
        };
        
        // Security functions
        class EDEN_fnc_validateAction {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_logSuspiciousActivity {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class EDEN_fnc_antiCheatCheck {
            allowedTargets = 2; // Server only
            jip = 0;
        };
    };
    
    // Enhanced command restrictions
    class Commands {
        mode = 1; // Whitelist mode
        jip = 0;
        
        // Essential commands only
        class hint {
            allowedTargets = 1; // Clients only
            jip = 0;
        };
        
        class hintSilent {
            allowedTargets = 1; // Clients only
            jip = 0;
        };
        
        class systemChat {
            allowedTargets = 1; // Clients only
            jip = 0;
        };
        
        class titleText {
            allowedTargets = 1; // Clients only
            jip = 0;
        };
        
        class titleFadeOut {
            allowedTargets = 1; // Clients only
            jip = 0;
        };
        
        class cutText {
            allowedTargets = 1; // Clients only
            jip = 0;
        };
        
        class playSound {
            allowedTargets = 1; // Clients only
            jip = 0;
        };
        
        class playMusic {
            allowedTargets = 1; // Clients only
            jip = 0;
        };
        
        class createMarker {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class deleteMarker {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class setMarkerPos {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class setMarkerText {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class setMarkerColor {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class setMarkerType {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class setMarkerSize {
            allowedTargets = 2; // Server only
            jip = 0;
        };
        
        class setMarkerAlpha {
            allowedTargets = 2; // Server only
            jip = 0;
        };
    };
};
