/*
    File: fn_emergencySystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages emergency medical system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON>ull]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_medicalRank", "EMT", true];
        _player setVariable ["eden_patientsHealed", 0, true];
        _player setVariable ["eden_onMedicalDuty", false, true];
        true
    };
    case "goOnDuty": {
        _player setVariable ["eden_onMedicalDuty", true, true];
        
        removeAllWeapons _player;
        removeAllItems _player;
        removeUniform _player;
        removeVest _player;
        
        _player forceAddUniform "U_C_Scientist";
        _player addItem "Medikit";
        _player addItem "FirstAidKit";
        _player addItem "FirstAidKit";
        
        ["Medical duty started!"] call EDEN_fnc_showHint;
        true
    };
    case "treatPatient": {
        if (!(_player getVariable ["eden_onMedicalDuty", false])) exitWith {
            ["You must be on medical duty!"] call EDEN_fnc_showHint;
            false
        };
        
        if (isNull _target) exitWith { false };
        
        _target setDamage 0;
        _healed = _player getVariable ["eden_patientsHealed", 0];
        _player setVariable ["eden_patientsHealed", (_healed + 1), true];
        
        [format ["Treated %1", name _target]] call EDEN_fnc_showHint;
        ["You have been treated"] remoteExec ["EDEN_fnc_showHint", _target];
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "revivePlayer": {
        if (isNull _target) exitWith { false };
        
        _target setDamage 0;
        _target setPos (getPos _target);
        
        [format ["Revived %1", name _target]] call EDEN_fnc_showHint;
        ["You have been revived"] remoteExec ["EDEN_fnc_showHint", _target];
        true
    };
    default { false };
};
