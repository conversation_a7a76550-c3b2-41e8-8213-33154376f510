/*
    File: fn_loanSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages loan and credit system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_loans", [], true];
        _player setVariable ["eden_creditScore", 750, true];
        _player setVariable ["eden_loanHistory", [], true];
        true
    };
    case "applyLoan": {
        params ["", "", ["_amount", 5000, [0]], ["_term", 12, [0]]];
        
        _creditScore = _player getVariable ["eden_creditScore", 750];
        if (_creditScore < 600) exitWith {
            ["Credit score too low for loan approval"] call EDEN_fnc_showHint;
            false
        };
        
        _maxLoan = _creditScore * 100;
        if (_amount > _maxLoan) exitWith {
            [format ["Maximum loan amount: $%1", _maxLoan]] call EDEN_fnc_showHint;
            false
        };
        
        _interestRate = switch (true) do {
            case (_creditScore >= 800): { 0.05 };
            case (_creditScore >= 700): { 0.08 };
            case (_creditScore >= 600): { 0.12 };
            default { 0.15 };
        };
        
        _monthlyPayment = floor((_amount * (1 + _interestRate)) / _term);
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _amount), true];
        
        _loans = _player getVariable ["eden_loans", []];
        _loan = [_amount, _monthlyPayment, _term, time, "Active"];
        _loans pushBack _loan;
        _player setVariable ["eden_loans", _loans, true];
        
        [format ["Loan approved: $%1 at %2%% interest, $%3/month", _amount, floor(_interestRate * 100), _monthlyPayment]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "payLoan": {
        params ["", "", ["_loanIndex", 0, [0]]];
        
        _loans = _player getVariable ["eden_loans", []];
        if (_loanIndex >= count _loans) exitWith {
            ["Invalid loan selection"] call EDEN_fnc_showHint;
            false
        };
        
        _loan = _loans select _loanIndex;
        if ((_loan select 4) != "Active") exitWith {
            ["Loan is not active"] call EDEN_fnc_showHint;
            false
        };
        
        _payment = _loan select 1;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _payment) exitWith {
            [format ["Not enough money! Need $%1", _payment]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _payment), true];
        
        _remaining = (_loan select 0) - _payment;
        _loan set [0, _remaining];
        
        if (_remaining <= 0) then {
            _loan set [4, "Paid Off"];
            _creditScore = _player getVariable ["eden_creditScore", 750];
            _player setVariable ["eden_creditScore", (_creditScore + 10), true];
            ["Loan paid off! Credit score improved"] call EDEN_fnc_showHint;
        } else {
            [format ["Loan payment made. Remaining: $%1", _remaining]] call EDEN_fnc_showHint;
        };
        
        _loans set [_loanIndex, _loan];
        _player setVariable ["eden_loans", _loans, true];
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
