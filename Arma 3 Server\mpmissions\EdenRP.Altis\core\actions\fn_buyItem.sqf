/*
    File: fn_buyItem.sqf
    Author: EdenRP Development Team
    
    Description:
    Allows players to buy items from shops.
    
    Parameters:
    0: STRING - Item class name
    1: NUMBER - Quantity (optional, default: 1)
    2: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if item was purchased successfully
*/

params [
    ["_itemClass", "", [""]],
    ["_quantity", 1, [0]],
    ["_player", player, [objNull]]
];

if (_itemClass == "" || _quantity <= 0 || isNull _player) exitWith { false };

// Item prices
_itemPrices = createHashMap;
_itemPrices set ["water_bottle", 50];
_itemPrices set ["apple", 25];
_itemPrices set ["bread", 75];
_itemPrices set ["toolkit", 500];
_itemPrices set ["first_aid_kit", 150];
_itemPrices set ["rope", 100];
_itemPrices set ["lockpick", 250];
_itemPrices set ["zipties", 75];
_itemPrices set ["phone", 1000];
_itemPrices set ["radio", 750];

_unitPrice = _itemPrices getOrDefault [_itemClass, 0];
if (_unitPrice == 0) exitWith {
    ["Item not available for purchase!"] call EDEN_fnc_showHint;
    false
};

_totalPrice = _unitPrice * _quantity;
_playerMoney = _player getVariable ["eden_cash", 0];

if (_playerMoney < _totalPrice) exitWith {
    ["Insufficient funds!"] call EDEN_fnc_showHint;
    false
};

// Check inventory space
_currentWeight = _player getVariable ["eden_currentWeight", 0];
_maxWeight = _player getVariable ["eden_maxWeight", 50];
_itemWeight = 1; // Default item weight
_totalWeight = _itemWeight * _quantity;

if ((_currentWeight + _totalWeight) > _maxWeight) exitWith {
    ["Not enough inventory space!"] call EDEN_fnc_showHint;
    false
};

// Purchase item
_player setVariable ["eden_cash", (_playerMoney - _totalPrice), true];

// Add to virtual inventory
_virtualItems = _player getVariable ["eden_virtualItems", []];
_found = false;
{
    if ((_x select 0) == _itemClass) then {
        _x set [1, ((_x select 1) + _quantity)];
        _found = true;
    };
} forEach _virtualItems;

if (!_found) then {
    _virtualItems pushBack [_itemClass, _quantity];
};

_player setVariable ["eden_virtualItems", _virtualItems, true];
_player setVariable ["eden_currentWeight", (_currentWeight + _totalWeight), true];

[format ["Purchased %1x %2 for $%3", _quantity, _itemClass, _totalPrice]] call EDEN_fnc_showHint;
[_player] call EDEN_fnc_savePlayerData;

true
