/*
    File: fn_leaderboards.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages leaderboard system.
*/

params [["_action", "init", [""]]];

switch (_action) do {
    case "init": {
        if (isServer) then {
            if (isNil "eden_leaderboards") then {
                eden_leaderboards = [
                    ["weekly_earnings", []],
                    ["monthly_arrests", []],
                    ["daily_activities", []],
                    ["seasonal_events", []]
                ];
                publicVariable "eden_leaderboards";
            };
        };
        true
    };
    case "updateWeeklyEarnings": {
        if (isServer) then {
            _weeklyData = [];
            
            {
                _earnings = _x getVariable ["eden_weeklyEarnings", 0];
                if (_earnings > 0) then {
                    _weeklyData pushBack [getPlayerUID _x, name _x, _earnings];
                };
            } forEach allPlayers;
            
            _weeklyData = [_weeklyData, [], {_x select 2}, "DESCEND"] call BIS_fnc_sortBy;
            
            {
                if ((_x select 0) == "weekly_earnings") then {
                    _x set [1, _weeklyData];
                };
            } forEach eden_leaderboards;
            
            publicVariable "eden_leaderboards";
        };
        true
    };
    case "updateMonthlyArrests": {
        if (isServer) then {
            _arrestData = [];
            
            {
                if ((_x getVariable ["eden_job", "civilian"]) == "police") then {
                    _arrests = _x getVariable ["eden_monthlyArrests", 0];
                    if (_arrests > 0) then {
                        _arrestData pushBack [getPlayerUID _x, name _x, _arrests];
                    };
                };
            } forEach allPlayers;
            
            _arrestData = [_arrestData, [], {_x select 2}, "DESCEND"] call BIS_fnc_sortBy;
            
            {
                if ((_x select 0) == "monthly_arrests") then {
                    _x set [1, _arrestData];
                };
            } forEach eden_leaderboards;
            
            publicVariable "eden_leaderboards";
        };
        true
    };
    case "getLeaderboard": {
        params ["", ["_type", "weekly_earnings", [""]]];
        
        if (isNil "eden_leaderboards") exitWith { [] };
        
        _data = [];
        {
            if ((_x select 0) == _type) then { _data = _x select 1; };
        } forEach eden_leaderboards;
        
        _data
    };
    case "showLeaderboard": {
        params ["", ["_type", "weekly_earnings", [""]], ["_player", player, [objNull]]];
        
        _data = [_type] call EDEN_fnc_leaderboards;
        
        _title = switch (_type) do {
            case "weekly_earnings": { "WEEKLY TOP EARNERS" };
            case "monthly_arrests": { "MONTHLY TOP COPS" };
            case "daily_activities": { "DAILY MOST ACTIVE" };
            case "seasonal_events": { "SEASONAL CHAMPIONS" };
            default { "LEADERBOARD" };
        };
        
        _text = format["=== %1 ===\n", _title];
        
        for "_i" from 0 to ((count _data min 10) - 1) do {
            _entry = _data select _i;
            _text = _text + format["%1. %2 - %3\n", (_i + 1), (_entry select 1), (_entry select 2)];
        };
        
        if (!isNull _player) then {
            [_text] remoteExec ["EDEN_fnc_showHint", _player];
        } else {
            [_text] call EDEN_fnc_showHint;
        };
        
        true
    };
    case "resetWeekly": {
        if (isServer) then {
            {
                _x setVariable ["eden_weeklyEarnings", 0, true];
            } forEach allPlayers;
            
            {
                if ((_x select 0) == "weekly_earnings") then {
                    _x set [1, []];
                };
            } forEach eden_leaderboards;
            
            publicVariable "eden_leaderboards";
        };
        true
    };
    default { false };
};
