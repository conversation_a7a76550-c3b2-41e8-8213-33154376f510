/*
    File: fn_ambulanceSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages ambulance operations system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_ambulanceCalls", 0, true];
        true
    };
    case "respondToCall": {
        if (!(_player getVariable ["eden_onMedicalDuty", false])) exitWith {
            ["You must be on medical duty!"] call EDEN_fnc_showHint;
            false
        };
        
        _calls = _player getVariable ["eden_ambulanceCalls", 0];
        _player setVariable ["eden_ambulanceCalls", (_calls + 1), true];
        
        ["Responding to emergency call"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "transportPatient": {
        params ["", "", ["_patient", objNull, [obj<PERSON><PERSON>]]];
        
        if (isNull _patient) exitWith { false };
        
        ["Transporting patient to hospital"] call EDEN_fnc_showHint;
        ["You are being transported to hospital"] remoteExec ["EDEN_fnc_showHint", _patient];
        true
    };
    default { false };
};
