/*
    EdenRP Banking System
    Enhanced banking with loans, investments, and interest
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_action", "", [""]],
    ["_amount", 0, [0]],
    ["_data", [], [[]]]
];

// Validate parameters
if (isNull _player || !isPlayer _player) exitWith {
    ["Invalid player provided to bankingSystem", "ERROR", "BANKING"] call EDEN_fnc_systemLogger;
    false
};

private _result = false;
switch (toLower _action) do {
    case "deposit": {
        _result = [_player, _amount] call EDEN_fnc_bankDeposit;
    };
    case "withdraw": {
        _result = [_player, _amount] call EDEN_fnc_bankWithdraw;
    };
    case "transfer": {
        _result = [_player, _amount, _data] call EDEN_fnc_bankTransfer;
    };
    case "loan": {
        _result = [_player, _amount, _data] call EDEN_fnc_bankLoan;
    };
    case "repay": {
        _result = [_player, _amount] call EDEN_fnc_bankRepayLoan;
    };
    case "invest": {
        _result = [_player, _amount, _data] call EDEN_fnc_bankInvest;
    };
    case "getbalance": {
        _result = [_player] call EDEN_fnc_getBankBalance;
    };
    case "getstatement": {
        _result = [_player] call EDEN_fnc_getBankStatement;
    };
    case "getloans": {
        _result = [_player] call EDEN_fnc_getBankLoans;
    };
    case "getinvestments": {
        _result = [_player] call EDEN_fnc_getBankInvestments;
    };
    case "getcredit": {
        _result = [_player] call EDEN_fnc_getCreditScore;
    };
    default {
        [format["Unknown banking action: %1", _action], "ERROR", "BANKING"] call EDEN_fnc_systemLogger;
    };
};

_result
