/*
    File: fn_chatSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages chat system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_chatHistory", [], true];
        _player setVariable ["eden_chatMuted", false, true];
        true
    };
    case "sendMessage": {
        params ["", "", ["_message", "", [""]], ["_channel", "global", [""]]];
        
        if (_player getVariable ["eden_chatMuted", false]) exitWith {
            ["You are muted"] call EDEN_fnc_showHint;
            false
        };
        
        if (_message == "") exitWith { false };
        
        _formattedMessage = format["[%1] %2: %3", _channel, name _player, _message];
        
        switch (_channel) do {
            case "global": {
                {
                    [_formattedMessage] remoteExec ["systemChat", _x];
                } forEach allPlayers;
            };
            case "side": {
                _job = _player getVariable ["eden_job", "civilian"];
                {
                    if ((_x getVariable ["eden_job", "civilian"]) == _job) then {
                        [_formattedMessage] remoteExec ["systemChat", _x];
                    };
                } forEach allPlayers;
            };
            case "local": {
                {
                    if (_x distance _player < 50) then {
                        [_formattedMessage] remoteExec ["systemChat", _x];
                    };
                } forEach allPlayers;
            };
        };
        
        _history = _player getVariable ["eden_chatHistory", []];
        _history pushBack [_message, _channel, time];
        _player setVariable ["eden_chatHistory", _history, true];
        
        true
    };
    case "mutePlayer": {
        params ["", "", ["_duration", 300, [0]]];
        
        _player setVariable ["eden_chatMuted", true, true];
        
        [] spawn {
            sleep _duration;
            _player setVariable ["eden_chatMuted", false, true];
            ["You have been unmuted"] call EDEN_fnc_showHint;
        };
        
        [format ["You have been muted for %1 seconds", _duration]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
