/*
    File: fn_repairVehicle.sqf
    Author: EdenRP Development Team
    
    Description:
    Repairs a damaged vehicle.
    
    Parameters:
    0: OBJECT - Vehicle to repair
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if repair was successful
*/

params [
    ["_vehicle", obj<PERSON>ull, [obj<PERSON><PERSON>]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (isNull _vehicle || isNull _player) exitWith { false };

if (_player distance _vehicle > 10) exitWith {
    ["Vehicle is too far away!"] call EDEN_fnc_showHint;
    false
};

if (!(_vehicle isKindOf "LandVehicle") && !(_vehicle isKindOf "Air") && !(_vehicle isKindOf "Ship")) exitWith {
    ["This object cannot be repaired!"] call EDEN_fnc_showHint;
    false
};

if (!alive _vehicle) exitWith {
    ["Cannot repair destroyed vehicles!"] call EDEN_fnc_showHint;
    false
};

_currentDamage = damage _vehicle;
if (_currentDamage < 0.1) exitWith {
    ["Vehicle doesn't need repairs!"] call EDEN_fnc_showHint;
    false
};

// Check if player has toolkit
_virtualItems = _player getVariable ["eden_virtualItems", []];
_hasToolkit = false;
_toolkitIndex = -1;
{
    if ((_x select 0) == "toolkit" && (_x select 1) > 0) then {
        _hasToolkit = true;
        _toolkitIndex = _forEachIndex;
    };
} forEach _virtualItems;

if (!_hasToolkit) exitWith {
    ["You need a toolkit to repair vehicles!"] call EDEN_fnc_showHint;
    false
};

// Calculate repair cost and time
_repairCost = round(_currentDamage * 1000); // $1000 per 100% damage
_repairTime = round(_currentDamage * 10); // 10 seconds per 100% damage

_playerMoney = _player getVariable ["eden_cash", 0];
if (_playerMoney < _repairCost) exitWith {
    [format ["Repair costs $%1, but you only have $%2!", _repairCost, _playerMoney]] call EDEN_fnc_showHint;
    false
};

// Start repair process
[_player, "Acts_carFixingWheel"] remoteExec ["switchMove"];
[format ["Repairing vehicle... ($%1, %2 seconds)", _repairCost, _repairTime]] call EDEN_fnc_showHint;

sleep _repairTime;

[_player, ""] remoteExec ["switchMove"];

// Repair the vehicle
_vehicle setDamage 0;
_vehicle setFuel 1; // Also refuel

// Charge money
_player setVariable ["eden_cash", (_playerMoney - _repairCost), true];

// Consume toolkit (20% chance to break)
if (random 100 < 20) then {
    _toolkitQuantity = (_virtualItems select _toolkitIndex) select 1;
    if (_toolkitQuantity <= 1) then {
        _virtualItems deleteAt _toolkitIndex;
    } else {
        (_virtualItems select _toolkitIndex) set [1, (_toolkitQuantity - 1)];
    };
    _player setVariable ["eden_virtualItems", _virtualItems, true];
    ["Your toolkit broke during the repair!"] call EDEN_fnc_showHint;
};

// Add experience
_expGained = round(_currentDamage * 50); // 50 XP per 100% damage repaired
_currentExp = _player getVariable ["eden_experience", 0];
_player setVariable ["eden_experience", (_currentExp + _expGained), true];

// Check for level up
_newLevel = floor((_currentExp + _expGained) / 1000) + 1;
_currentLevel = _player getVariable ["eden_playerLevel", 1];
if (_newLevel > _currentLevel) then {
    _player setVariable ["eden_playerLevel", _newLevel, true];
    [format ["Level up! You are now level %1", _newLevel], 5] call EDEN_fnc_showNotification;
};

[format ["Vehicle repaired successfully! Cost: $%1 (+%2 XP)", _repairCost, _expGained]] call EDEN_fnc_showHint;

// Log the action
[format ["[EDEN] Player %1 repaired vehicle %2 for $%3", name _player, typeOf _vehicle, _repairCost], "INFO", "VEHICLE"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
