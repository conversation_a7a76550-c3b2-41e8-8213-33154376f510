/*
    EdenRP Buy From Market Function
    Enhanced market purchasing with dynamic pricing
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_item", "", [""]],
    ["_quantity", 1, [0]],
    ["_data", [], [[]]]
];

// Validate parameters
if (_item == "" || _quantity <= 0) exitWith {
    ["Invalid item or quantity", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Get market location
private _location = if (count _data > 0) then {_data select 0} else {"general"};
private _marketPosition = [_player] call EDEN_fnc_getNearestMarket;

if (count _marketPosition == 0) exitWith {
    ["You are not near a market", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Get current market data
private _marketData = [_item, _location] call EDEN_fnc_getMarketData;
if (count _marketData == 0) exitWith {
    ["Item not available at this market", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

private _buyPrice = _marketData select 1;
private _stock = _marketData select 3;
private _demand = _marketData select 4;

// Check stock availability
if (_stock < _quantity) exitWith {
    [format["Only %1 units available in stock", _stock], "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Calculate dynamic price based on demand and quantity
private _priceMultiplier = 1;

// High demand increases price
if (_demand > 1.5) then {
    _priceMultiplier = _priceMultiplier * 1.2;
} else {
    if (_demand < 0.5) then {
        _priceMultiplier = _priceMultiplier * 0.8;
    };
};

// Large purchases increase price (market impact)
if (_quantity > (_stock * 0.1)) then {
    private _impactMultiplier = 1 + ((_quantity / _stock) * 0.5);
    _priceMultiplier = _priceMultiplier * _impactMultiplier;
};

// Apply player discounts
private _donatorLevel = _player getVariable ["EDEN_DonatorLevel", 0];
private _discount = _donatorLevel * 0.02; // 2% per donator level
_priceMultiplier = _priceMultiplier * (1 - _discount);

// Calculate final price
private _finalPrice = round (_buyPrice * _quantity * _priceMultiplier);

// Check player funds
private _playerCash = _player getVariable ["EDEN_Cash", 0];
private _playerBank = _player getVariable ["EDEN_Bank", 0];
private _totalFunds = _playerCash + _playerBank;

if (_totalFunds < _finalPrice) exitWith {
    [format["You need $%1 to purchase %2x %3", [_finalPrice] call EDEN_fnc_formatMoney, _quantity, _item], "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Check inventory space
if !([_player, _item, _quantity] call EDEN_fnc_canAddToInventory) exitWith {
    ["Not enough inventory space", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Process payment (prefer cash for small purchases, bank for large)
if (_finalPrice <= 1000 && _playerCash >= _finalPrice) then {
    _player setVariable ["EDEN_Cash", _playerCash - _finalPrice, false];
} else {
    if (_playerBank >= _finalPrice) then {
        _player setVariable ["EDEN_Bank", _playerBank - _finalPrice, false];
    } else {
        private _bankAmount = _playerBank;
        private _cashAmount = _finalPrice - _bankAmount;
        _player setVariable ["EDEN_Bank", 0, false];
        _player setVariable ["EDEN_Cash", _playerCash - _cashAmount, false];
    };
};

// Add items to inventory
[_player, _item, _quantity] call EDEN_fnc_addItemToInventory;

// Update market data
private _newStock = _stock - _quantity;
private _newDemand = _demand + (_quantity / 100); // Increase demand slightly
[_item, _location, "stock", _newStock] call EDEN_fnc_updateMarketData;
[_item, _location, "demand", _newDemand] call EDEN_fnc_updateMarketData;

// Update market prices based on new supply/demand
[_item, _location] call EDEN_fnc_recalculateMarketPrice;

// Log transaction
private _uid = getPlayerUID _player;
[_uid, "MARKET_PURCHASE", -_finalPrice, _totalFunds, _totalFunds - _finalPrice, format["Bought %1x %2 at %3", _quantity, _item, _location]] call EDEN_fnc_logTransaction;

// Get item display name
private _itemConfig = [_item] call EDEN_fnc_getItemConfig;
private _itemName = if (count _itemConfig > 0) then {_itemConfig select 0} else {_item};

// Notify player
[format["Purchased %1x %2 for $%3", _quantity, _itemName, [_finalPrice] call EDEN_fnc_formatMoney], "success"] remoteExec ["EDEN_fnc_showNotification", _player];

// Show price change if significant
private _priceChange = (_priceMultiplier - 1) * 100;
if (abs _priceChange > 5) then {
    private _changeText = if (_priceChange > 0) then {"increased"} else {"decreased"};
    [format["Market price %1 by %2%% due to demand", _changeText, round (abs _priceChange)], "info"] remoteExec ["EDEN_fnc_showNotification", _player];
};

// Update client money display
[_player getVariable ["EDEN_Cash", 0], _player getVariable ["EDEN_Bank", 0]] remoteExec ["EDEN_fnc_updateMoneyDisplay", _player];

// Award XP for trading
private _xpReward = round (_finalPrice / 100); // 1 XP per $100 spent
[_player, _xpReward, "TRADING"] call EDEN_fnc_awardExperience;

// Update player trading statistics
private _totalPurchases = _player getVariable ["EDEN_TotalPurchases", 0];
private _totalSpent = _player getVariable ["EDEN_TotalSpent", 0];
_player setVariable ["EDEN_TotalPurchases", _totalPurchases + _quantity, false];
_player setVariable ["EDEN_TotalSpent", _totalSpent + _finalPrice, false];

// Log market activity
[format["MARKET: %1 bought %2x %3 for $%4 at %5 (Price multiplier: %6)", 
    name _player, _quantity, _item, _finalPrice, _location, round (_priceMultiplier * 100) / 100], "INFO", "ECONOMY"] call EDEN_fnc_systemLogger;

// Trigger market events if large purchase
if (_quantity > (_stock * 0.2)) then {
    [_item, _location, "LARGE_PURCHASE", _quantity] call EDEN_fnc_triggerMarketEvent;
};

true
