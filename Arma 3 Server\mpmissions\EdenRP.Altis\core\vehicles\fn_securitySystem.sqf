/*
    File: fn_securitySystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages vehicle security system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_vehicle", objNull, [obj<PERSON>ull]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_vehicleSecurity", [], true];
        true
    };
    case "installAlarm": {
        if (isNull _vehicle) exitWith { false };
        
        _cost = 400;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _vehicle setVariable ["eden_hasAlarm", true, true];
        _vehicle setVariable ["eden_alarmOwner", getPlayerUID _player, true];
        
        _security = _player getVariable ["eden_vehicleSecurity", []];
        _security pushBack [typeOf _vehicle, "alarm", time];
        _player setVariable ["eden_vehicleSecurity", _security, true];
        
        [format ["Vehicle alarm installed for $%1", _cost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "lockVehicle": {
        if (isNull _vehicle) exitWith { false };
        
        _vehicle lock 2;
        _vehicle setVariable ["eden_lockedBy", getPlayerUID _player, true];
        
        ["Vehicle locked"] call EDEN_fnc_showHint;
        true
    };
    case "unlockVehicle": {
        if (isNull _vehicle) exitWith { false };
        
        _lockedBy = _vehicle getVariable ["eden_lockedBy", ""];
        if (_lockedBy != getPlayerUID _player) exitWith {
            ["You don't have the keys!"] call EDEN_fnc_showHint;
            false
        };
        
        _vehicle lock 0;
        _vehicle setVariable ["eden_lockedBy", "", true];
        
        ["Vehicle unlocked"] call EDEN_fnc_showHint;
        true
    };
    case "triggerAlarm": {
        if (isNull _vehicle) exitWith { false };
        
        _hasAlarm = _vehicle getVariable ["eden_hasAlarm", false];
        if (!_hasAlarm) exitWith { false };
        
        ["VEHICLE ALARM TRIGGERED!"] call EDEN_fnc_showHint;
        
        _alarmOwner = _vehicle getVariable ["eden_alarmOwner", ""];
        if (_alarmOwner != "") then {
            _owner = [_alarmOwner] call BIS_fnc_getUnitByUID;
            if (!isNull _owner) then {
                ["Your vehicle alarm has been triggered!"] remoteExec ["EDEN_fnc_showHint", _owner];
            };
        };
        
        true
    };
    default { false };
};
