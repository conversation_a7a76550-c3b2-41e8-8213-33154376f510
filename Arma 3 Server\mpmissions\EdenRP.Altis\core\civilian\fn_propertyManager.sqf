/*
    File: fn_propertyManager.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages property ownership and rental for civilians.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_propertyId", 0, [0]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_ownedProperties", [], true];
        _player setVariable ["eden_rentedProperties", [], true];
        _player setVariable ["eden_propertyIncome", 0, true];
        true
    };
    case "buyProperty": {
        _availableProperties = [
            [1, "Small Apartment", 15000, 200, "Residential"],
            [2, "Large Apartment", 25000, 350, "Residential"],
            [3, "Small House", 45000, 500, "Residential"],
            [4, "Large House", 75000, 800, "Residential"],
            [5, "Mansion", 150000, 1500, "Luxury"],
            [6, "Office Space", 60000, 600, "Commercial"],
            [7, "Warehouse", 80000, 400, "Industrial"],
            [8, "Shop Front", 50000, 700, "Retail"]
        ];
        
        _propertyData = [];
        {
            if ((_x select 0) == _propertyId) then {
                _propertyData = _x;
            };
        } forEach _availableProperties;
        
        if (count _propertyData == 0) exitWith {
            ["Property not found!"] call EDEN_fnc_showHint;
            false
        };
        
        _ownedProperties = _player getVariable ["eden_ownedProperties", []];
        _alreadyOwned = false;
        {
            if ((_x select 0) == _propertyId) then {
                _alreadyOwned = true;
            };
        } forEach _ownedProperties;
        
        if (_alreadyOwned) exitWith {
            ["You already own this property!"] call EDEN_fnc_showHint;
            false
        };
        
        _price = _propertyData select 2;
        _bankAccount = _player getVariable ["eden_bankAccount", 0];
        
        if (_bankAccount < _price) exitWith {
            [format ["Not enough money! Need $%1", _price]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_bankAccount", (_bankAccount - _price), true];
        
        _name = _propertyData select 1;
        _rentalValue = _propertyData select 3;
        _type = _propertyData select 4;
        
        _newProperty = [_propertyId, _name, _type, _rentalValue, time, false, ""]; // id, name, type, rental, lastRent, isRented, tenant
        _ownedProperties pushBack _newProperty;
        _player setVariable ["eden_ownedProperties", _ownedProperties, true];
        
        [format ["Purchased %1 for $%2!", _name, _price]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "rentProperty": {
        _availableRentals = [
            [101, "Studio Apartment", 300, "Residential"],
            [102, "1BR Apartment", 500, "Residential"],
            [103, "2BR Apartment", 750, "Residential"],
            [104, "Small Office", 600, "Commercial"],
            [105, "Storage Unit", 200, "Storage"]
        ];
        
        _propertyData = [];
        {
            if ((_x select 0) == _propertyId) then {
                _propertyData = _x;
            };
        } forEach _availableRentals;
        
        if (count _propertyData == 0) exitWith {
            ["Rental property not found!"] call EDEN_fnc_showHint;
            false
        };
        
        _rentedProperties = _player getVariable ["eden_rentedProperties", []];
        _alreadyRented = false;
        {
            if ((_x select 0) == _propertyId) then {
                _alreadyRented = true;
            };
        } forEach _rentedProperties;
        
        if (_alreadyRented) exitWith {
            ["You already rent this property!"] call EDEN_fnc_showHint;
            false
        };
        
        _monthlyRent = _propertyData select 2;
        _bankAccount = _player getVariable ["eden_bankAccount", 0];
        
        if (_bankAccount < _monthlyRent) exitWith {
            [format ["Not enough money for first month's rent! Need $%1", _monthlyRent]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_bankAccount", (_bankAccount - _monthlyRent), true];
        
        _name = _propertyData select 1;
        _type = _propertyData select 3;
        
        _newRental = [_propertyId, _name, _type, _monthlyRent, time]; // id, name, type, rent, lastPayment
        _rentedProperties pushBack _newRental;
        _player setVariable ["eden_rentedProperties", _rentedProperties, true];
        
        [format ["Rented %1 for $%2/month!", _name, _monthlyRent]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "collectRent": {
        _ownedProperties = _player getVariable ["eden_ownedProperties", []];
        _totalRent = 0;
        
        {
            _propertyData = _x;
            _rentalValue = _propertyData select 3;
            _lastRent = _propertyData select 4;
            _isRented = _propertyData select 5;
            
            if (_isRented) then {
                _daysSince = (time - _lastRent) / 86400; // Convert to days
                if (_daysSince >= 30) then { // Monthly rent
                    _rent = _rentalValue;
                    _totalRent = _totalRent + _rent;
                    _propertyData set [4, time]; // Update last rent collection
                };
            };
        } forEach _ownedProperties;
        
        if (_totalRent > 0) then {
            _bankAccount = _player getVariable ["eden_bankAccount", 0];
            _player setVariable ["eden_bankAccount", (_bankAccount + _totalRent), true];
            _player setVariable ["eden_ownedProperties", _ownedProperties, true];
            
            _currentIncome = _player getVariable ["eden_propertyIncome", 0];
            _player setVariable ["eden_propertyIncome", (_currentIncome + _totalRent), true];
            
            [format ["Collected $%1 in rent!", floor _totalRent]] call EDEN_fnc_showHint;
            [_player] call EDEN_fnc_savePlayerData;
        } else {
            ["No rent to collect."] call EDEN_fnc_showHint;
        };
        
        (_totalRent > 0)
    };
    case "payRent": {
        _rentedProperties = _player getVariable ["eden_rentedProperties", []];
        _totalRent = 0;
        _overdue = [];
        
        {
            _propertyData = _x;
            _monthlyRent = _propertyData select 3;
            _lastPayment = _propertyData select 4;
            
            _daysSince = (time - _lastPayment) / 86400;
            if (_daysSince >= 30) then {
                _totalRent = _totalRent + _monthlyRent;
                _overdue pushBack _propertyData;
            };
        } forEach _rentedProperties;
        
        if (_totalRent > 0) then {
            _bankAccount = _player getVariable ["eden_bankAccount", 0];
            if (_bankAccount >= _totalRent) then {
                _player setVariable ["eden_bankAccount", (_bankAccount - _totalRent), true];
                
                {
                    _x set [4, time]; // Update last payment
                } forEach _overdue;
                
                _player setVariable ["eden_rentedProperties", _rentedProperties, true];
                [format ["Paid $%1 in rent!", floor _totalRent]] call EDEN_fnc_showHint;
                [_player] call EDEN_fnc_savePlayerData;
            } else {
                [format ["Not enough money to pay rent! Need $%1", floor _totalRent]] call EDEN_fnc_showHint;
            };
        } else {
            ["No rent due."] call EDEN_fnc_showHint;
        };
        
        (_totalRent > 0)
    };
    case "sellProperty": {
        _ownedProperties = _player getVariable ["eden_ownedProperties", []];
        _propertyData = [];
        _index = -1;
        
        {
            if ((_x select 0) == _propertyId) then {
                _propertyData = _x;
                _index = _forEachIndex;
            };
        } forEach _ownedProperties;
        
        if (_index == -1) exitWith {
            ["You don't own this property!"] call EDEN_fnc_showHint;
            false
        };
        
        _name = _propertyData select 1;
        _salePrice = (_propertyData select 3) * 50; // 50x monthly rental value
        
        _bankAccount = _player getVariable ["eden_bankAccount", 0];
        _player setVariable ["eden_bankAccount", (_bankAccount + _salePrice), true];
        
        _ownedProperties deleteAt _index;
        _player setVariable ["eden_ownedProperties", _ownedProperties, true];
        
        [format ["Sold %1 for $%2!", _name, _salePrice]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
