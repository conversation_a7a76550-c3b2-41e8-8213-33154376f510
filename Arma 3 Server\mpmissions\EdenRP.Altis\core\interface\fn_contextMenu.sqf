/*
    File: fn_contextMenu.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages context menu system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON>ull]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_contextMenuOpen", false, true];
        true
    };
    case "showPlayerMenu": {
        if (isNull _target) exitWith { false };
        if (_player getVariable ["eden_contextMenuOpen", false]) exitWith { false };
        
        _player setVariable ["eden_contextMenuOpen", true, true];
        
        _options = [
            "View Profile",
            "Send Message",
            "Trade Items",
            "Add Friend",
            "Report Player",
            "Close"
        ];
        
        [format["Player: %1", name _target], _options] call EDEN_fnc_showDialog;
        true
    };
    case "showVehicleMenu": {
        params ["", "", "", ["_vehicle", objNull, [obj<PERSON>ull]]];
        
        if (isNull _vehicle) exitWith { false };
        
        _options = [
            "Enter Vehicle",
            "Lock/Unlock",
            "Repair",
            "Refuel",
            "Impound",
            "Close"
        ];
        
        ["Vehicle Options", _options] call EDEN_fnc_showDialog;
        true
    };
    case "showItemMenu": {
        params ["", "", "", ["_item", "", [""]]];
        
        if (_item == "") exitWith { false };
        
        _options = [
            "Use Item",
            "Drop Item",
            "Give Item",
            "Sell Item",
            "Info",
            "Close"
        ];
        
        [format["Item: %1", _item], _options] call EDEN_fnc_showDialog;
        true
    };
    case "closeMenu": {
        _player setVariable ["eden_contextMenuOpen", false, true];
        closeDialog 0;
        true
    };
    default { false };
};
