/*
    File: fn_bloodBankSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages blood bank and transfusion system.
*/

params [["_player", player, [objNull]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_bloodType", selectRandom ["A+", "A-", "B+", "B-", "AB+", "AB-", "O+", "O-"], true];
        _player setVariable ["eden_bloodLevel", 100, true];
        true
    };
    case "donateBlood": {
        _bloodLevel = _player getVariable ["eden_bloodLevel", 100];
        if (_bloodLevel < 80) exitWith {
            ["Blood level too low to donate"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_bloodLevel", (_bloodLevel - 20), true];
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + 100), true];
        
        ["Blood donated - received $100"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "transfusion": {
        params ["", "", ["_target", objNull, [objNull]]];
        
        if (isNull _target) exitWith { false };
        
        _target setVariable ["eden_bloodLevel", 100, true];
        _target setDamage ((damage _target) - 0.3);
        
        ["Blood transfusion administered"] call EDEN_fnc_showHint;
        ["You received a blood transfusion"] remoteExec ["EDEN_fnc_showHint", _target];
        true
    };
    default { false };
};
