/*
    File: fn_rewardSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages reward system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_dailyRewardClaimed", false, true];
        _player setVariable ["eden_lastRewardTime", 0, true];
        _player setVariable ["eden_rewardStreak", 0, true];
        true
    };
    case "claimDailyReward": {
        _lastClaim = _player getVariable ["eden_lastRewardTime", 0];
        _timeSince = time - _lastClaim;
        
        if (_timeSince < 86400) exitWith {
            _remaining = 86400 - _timeSince;
            [format ["Daily reward available in %1 hours", floor(_remaining / 3600)]] call EDEN_fnc_showHint;
            false
        };
        
        _streak = _player getVariable ["eden_rewardStreak", 0];
        if (_timeSince > 172800) then { _streak = 0; };
        
        _streak = _streak + 1;
        _player setVariable ["eden_rewardStreak", _streak, true];
        _player setVariable ["eden_lastRewardTime", time, true];
        
        _baseReward = 1000;
        _streakBonus = _streak * 100;
        _totalReward = _baseReward + _streakBonus;
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _totalReward), true];
        
        [format ["Daily reward claimed! $%1 (Streak: %2 days)", _totalReward, _streak]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "giveReward": {
        params ["", "", ["_type", "cash", [""]], ["_amount", 100, [0]], ["_reason", "Activity", [""]]];
        
        switch (_type) do {
            case "cash": {
                _cash = _player getVariable ["eden_cash", 0];
                _player setVariable ["eden_cash", (_cash + _amount), true];
                [format ["Reward: $%1 (%2)", _amount, _reason]] call EDEN_fnc_showHint;
            };
            case "experience": {
                [_player, "addExperience", _amount, _reason] call EDEN_fnc_experienceSystem;
            };
            case "item": {
                [_player, "addItem", _reason, _amount] call EDEN_fnc_itemSystem;
                [format ["Reward: %1x %2", _amount, _reason]] call EDEN_fnc_showHint;
            };
        };
        
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "getStreakInfo": {
        _streak = _player getVariable ["eden_rewardStreak", 0];
        _lastClaim = _player getVariable ["eden_lastRewardTime", 0];
        _nextReward = _lastClaim + 86400;
        
        [_streak, _nextReward]
    };
    default { false };
};
