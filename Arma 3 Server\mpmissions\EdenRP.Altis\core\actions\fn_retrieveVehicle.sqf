/*
    File: fn_retrieveVehicle.sqf
    Author: EdenRP Development Team
    
    Description:
    Retrieves a vehicle from the player's garage.
    
    Parameters:
    0: NUMBER - Vehicle index in garage
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if retrieval was successful
*/

params [
    ["_vehicleIndex", -1, [0]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (_vehicleIndex < 0 || isNull _player) exitWith { false };

// Check if near garage
_nearGarages = nearestObjects [_player, ["Land_CarService_F", "Land_Garage_V1_F"], 25];
if (count _nearGarages == 0) exitWith {
    ["You must be near a garage to retrieve vehicles!"] call EDEN_fnc_showHint;
    false
};

// Get player's garage
_playerGarage = _player getVariable ["eden_garage", []];
if (_vehicleIndex >= count _playerGarage) exitWith {
    ["Invalid vehicle selection!"] call EDEN_fnc_showHint;
    false
};

// Get vehicle data
_vehicleData = _playerGarage select _vehicleIndex;
_vehicleData params ["_vehicleClass", "_vehicleDamage", "_vehicleFuel", "_vehicleColor", "_vehicleMods", "_vehicleInventory"];

// Find spawn position
_spawnPos = [getPos _player, 10, 50, 5, 0, 20, 0] call BIS_fnc_findSafePos;

// Spawn vehicle
_vehicle = createVehicle [_vehicleClass, _spawnPos, [], 0, "NONE"];
_vehicle setDir (getDir _player);
_vehicle setDamage _vehicleDamage;
_vehicle setFuel _vehicleFuel;

// Set ownership
_vehicle setVariable ["eden_ownerUID", getPlayerUID _player, true];
_vehicle setVariable ["eden_ownerName", name _player, true];

// Restore vehicle properties
if (_vehicleColor != "") then {
    _vehicle setVariable ["eden_vehicleColor", _vehicleColor, true];
};

if (count _vehicleMods > 0) then {
    _vehicle setVariable ["eden_vehicleMods", _vehicleMods, true];
};

if (count _vehicleInventory > 0) then {
    _vehicle setVariable ["eden_vehicleInventory", _vehicleInventory, true];
};

// Remove from garage
_playerGarage deleteAt _vehicleIndex;
_player setVariable ["eden_garage", _playerGarage, true];

[format ["Vehicle retrieved from garage. Remaining vehicles: %1", count _playerGarage]] call EDEN_fnc_showHint;

// Log retrieval
[format ["[EDEN] Player %1 retrieved vehicle %2 from garage", name _player, _vehicleClass], "INFO", "VEHICLE"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
