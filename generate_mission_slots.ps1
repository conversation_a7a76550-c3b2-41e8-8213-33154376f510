# PowerShell script to generate mission.sqm player slots
# This will create the proper 120 player slots structure

$missionContent = @"
version=54;
class EditorData
{
	moveGridStep=1;
	angleGridStep=0.2617994;
	scaleGridStep=1;
	autoGroupingDist=10;
	toggles=1;
	class ItemIDProvider
	{
		nextID=121;
	};
	class Camera
	{
		pos[]={14441.292,20,16233.154};
		dir[]={0,0,1};
		up[]={0,1,0};
		aside[]={1,0,0};
	};
};
binarizationWanted=0;
sourceName="EdenRP";
addons[]=
{
	"A3_Characters_F",
	"a3_map_altis"
};
class AddonsMetaData
{
	class List
	{
		items=2;
		class Item0
		{
			className="A3_Characters_F";
			name="Arma 3 Alpha - Characters and Clothing";
			author="Bohemia Interactive";
			url="https://www.arma3.com";
		};
		class Item1
		{
			className="a3_map_altis";
			name="Arma 3 - Altis";
			author="Bohemia Interactive";
			url="https://www.arma3.com";
		};
	};
};
randomSeed=12345678;
class ScenarioData
{
	author="EdenRP Development Team";
	title="EdenRP Altis Life";
	subtitle="Roleplay Server";
	briefingName="EdenRP Altis Life";
	overviewText="Welcome to EdenRP - The ultimate Altis Life roleplay experience with enhanced systems for police, medical, and civilian gameplay.";
	overviewPicture="";
	loadScreen="";
	saving=0;
	class Header
	{
		gameType="RPG";
		minPlayers=1;
		maxPlayers=120;
	};
	class GroupRestrictions
	{
		west=20;
		east=15;
		civilian=85;
	};
};
class CustomAttributes
{
	class Category0
	{
		name="Multiplayer";
		class Attribute0
		{
			property="RespawnTemplates";
			expression="true";
			class Value
			{
				class data
				{
					class type
					{
						type[]=
						{
							"ARRAY"
						};
					};
					class value
					{
						items=1;
						class Item0
						{
							class data
							{
								class type
								{
									type[]=
									{
										"STRING"
									};
								};
								value="Base";
							};
						};
					};
				};
			};
		};
		class Attribute1
		{
			property="Enh_ambientAnimSpeed";
			expression="if (!is3DEN && hasInterface) then {_this spawn {sleep 1; {_x setVariable ['BIS_fnc_ambientAnim_speed', _this]} forEach allUnits;}}";
			class Value
			{
				class data
				{
					class type
					{
						type[]=
						{
							"SCALAR"
						};
					};
					value=1;
				};
			};
		};
	};
};
class Mission
{
	class Intel
	{
		timeOfChanges=1800.0002;
		startWeather=0.30000001;
		startWind=0.1;
		startWaves=0.1;
		forecastWeather=0.30000001;
		forecastWind=0.1;
		forecastWaves=0.1;
		forecastLightnings=0.1;
		year=2035;
		month=6;
		day=24;
		hour=12;
		minute=0;
		startFogDecay=0.014;
		forecastFogDecay=0.014;
	};
	class Entities
	{
		items=3;
"@

# Generate BLUFOR (Police) Group - 20 slots
$missionContent += @"
		class Item0
		{
			dataType="Group";
			side="West";
			class Entities
			{
				items=20;
"@

for ($i = 0; $i -lt 20; $i++) {
    $x = 16019 + ($i * 2)
    $missionContent += @"
				class Item$i
				{
					dataType="Object";
					class PositionInfo
					{
						position[]=[$x.154,17.93,12722.063];
						angles[]={0,0,0};
					};
					side="West";
					flags=$(if ($i -eq 0) { 7 } else { 5 });
					class Attributes
					{
						isPlayer=1;
						class Inventory
						{
						};
					};
					id=$i;
					type="B_Soldier_F";
				};
"@
}

$missionContent += @"
			};
			class Attributes
			{
			};
			id=0;
		};
"@

Write-Output "Generated BLUFOR group with 20 slots"
Write-Output $missionContent.Length
