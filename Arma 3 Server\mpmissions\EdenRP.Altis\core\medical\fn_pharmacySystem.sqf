/*
    File: fn_pharmacySystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages pharmacy and medication system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_medication", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_medications", [], true];
        _player setVariable ["eden_prescriptions", [], true];
        true
    };
    case "buyMedication": {
        _medications = [
            ["painkillers", "Painkillers", 50],
            ["antibiotics", "Antibiotics", 100],
            ["bandages", "Bandages", 25],
            ["morphine", "Morphine", 200],
            ["adrenaline", "Adrenaline", 150]
        ];
        
        _medData = [];
        {
            if ((_x select 0) == _medication) then { _medData = _x; };
        } forEach _medications;
        
        if (count _medData == 0) exitWith {
            ["Medication not available"] call EDEN_fnc_showHint;
            false
        };
        
        _cost = _medData select 2;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _meds = _player getVariable ["eden_medications", []];
        _meds pushBack _medication;
        _player setVariable ["eden_medications", _meds, true];
        
        [format ["Purchased %1 for $%2", _medData select 1, _cost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "useMedication": {
        _meds = _player getVariable ["eden_medications", []];
        if !(_medication in _meds) exitWith {
            ["You don't have this medication"] call EDEN_fnc_showHint;
            false
        };
        
        _meds = _meds - [_medication];
        _player setVariable ["eden_medications", _meds, true];
        
        switch (_medication) do {
            case "painkillers": {
                _player setDamage ((damage _player) - 0.1);
                ["Painkillers used - pain reduced"] call EDEN_fnc_showHint;
            };
            case "morphine": {
                _player setDamage ((damage _player) - 0.3);
                ["Morphine administered - major pain relief"] call EDEN_fnc_showHint;
            };
            case "adrenaline": {
                _player setVariable ["eden_adrenalineBoost", time + 300, true];
                ["Adrenaline boost active for 5 minutes"] call EDEN_fnc_showHint;
            };
        };
        
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
