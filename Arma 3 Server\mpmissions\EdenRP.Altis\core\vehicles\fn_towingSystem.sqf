/*
    File: fn_towingSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages vehicle towing system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_vehicle", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_towingJobs", [], true];
        _player setVariable ["eden_towingIncome", 0, true];
        true
    };
    case "attachVehicle": {
        params ["", "", "", ["_towTruck", objNull, [objNull]]];
        
        if (isNull _vehicle || isNull _towTruck) exitWith { false };
        
        _job = _player getVariable ["eden_job", ""];
        if (_job != "tow_driver") exitWith {
            ["You must be a tow truck driver!"] call EDEN_fnc_showHint;
            false
        };
        
        _vehicle attachTo [_towTruck, [0, -6, 0]];
        _vehicle setVariable ["eden_beingTowed", true, true];
        _towTruck setVariable ["eden_towedVehicle", _vehicle, true];
        
        ["Vehicle attached for towing"] call EDEN_fnc_showHint;
        true
    };
    case "detachVehicle": {
        if (isNull _vehicle) exitWith { false };
        
        _beingTowed = _vehicle getVariable ["eden_beingTowed", false];
        if (!_beingTowed) exitWith {
            ["Vehicle is not being towed"] call EDEN_fnc_showHint;
            false
        };
        
        detach _vehicle;
        _vehicle setVariable ["eden_beingTowed", false, true];
        
        ["Vehicle detached"] call EDEN_fnc_showHint;
        true
    };
    case "completeTowJob": {
        if (isNull _vehicle) exitWith { false };
        
        _job = _player getVariable ["eden_job", ""];
        if (_job != "tow_driver") exitWith {
            ["You must be a tow truck driver!"] call EDEN_fnc_showHint;
            false
        };
        
        _payment = 300;
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _payment), true];
        
        _jobs = _player getVariable ["eden_towingJobs", []];
        _jobs pushBack [typeOf _vehicle, _payment, time];
        _player setVariable ["eden_towingJobs", _jobs, true];
        
        _income = _player getVariable ["eden_towingIncome", 0];
        _player setVariable ["eden_towingIncome", (_income + _payment), true];
        
        detach _vehicle;
        _vehicle setVariable ["eden_beingTowed", false, true];
        
        [format ["Towing job completed - earned $%1", _payment]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
