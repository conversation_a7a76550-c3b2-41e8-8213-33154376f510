/*
    File: fn_savePlayerData.sqf
    Author: EdenRP Development Team
    
    Description:
    Saves player data to database using extDB3.
    
    Parameters:
    0: OBJECT - Player to save
    
    Returns:
    BOOLEAN - True if save was successful
*/

params [["_player", player, [objNull]]];

if (isNull _player) exitWith { false };

// Get player data
_uid = getPlayerUID _player;
_name = name _player;
_cash = _player getVariable ["eden_cash", 5000];
_bank = _player getVariable ["eden_bankAccount", 25000];
_position = getPosATL _player;
_direction = getDir _player;

// Get licenses
_licenses = [];
{
    _licenseVar = format ["eden_license_%1", _x];
    if (_player getVariable [_licenseVar, false]) then {
        _licenses pushBack _x;
    };
} forEach ["driver", "pilot", "boat", "gun", "hunting", "fishing", "mining", "oil", "diamond", "turtle"];

// Get virtual inventory
_virtualItems = _player getVariable ["eden_virtualItems", []];

// Get player stats
_playerLevel = _player getVariable ["eden_playerLevel", 1];
_experience = _player getVariable ["eden_experience", 0];
_arrests = _player getVariable ["eden_arrests", 0];
_wantedLevel = _player getVariable ["eden_wantedLevel", 0];

// Prepare SQL query
_query = format [
    "INSERT INTO players (uid, name, cash, bank, position_x, position_y, position_z, direction, licenses, inventory, level, experience, arrests, wanted_level, last_seen) VALUES ('%1', '%2', %3, %4, %5, %6, %7, %8, '%9', '%10', %11, %12, %13, %14, NOW()) ON DUPLICATE KEY UPDATE name='%2', cash=%3, bank=%4, position_x=%5, position_y=%6, position_z=%7, direction=%8, licenses='%9', inventory='%10', level=%11, experience=%12, arrests=%13, wanted_level=%14, last_seen=NOW()",
    _uid,
    _name,
    _cash,
    _bank,
    _position select 0,
    _position select 1,
    _position select 2,
    _direction,
    _licenses joinString ",",
    str _virtualItems,
    _playerLevel,
    _experience,
    _arrests,
    _wantedLevel
];

// Execute query using extDB3
_result = "extDB3" callExtension format ["1:edenrp:%1", _query];

// Log the save operation
[format ["[EDEN] Saved player data for %1 (UID: %2)", _name, _uid], "INFO", "DATABASE"] call EDEN_fnc_systemLogger;

// Return success
true
