/*
    File: fn_showroomSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages vehicle showroom and dealership system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_vehicleType", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_vehicleInventory") then {
            eden_vehicleInventory = [
                ["C_Hatchback_01_F", "Hatchback", 15000, 5],
                ["C_SUV_01_F", "SUV", 25000, 3],
                ["C_Van_01_transport_F", "Van", 35000, 2],
                ["B_Truck_01_transport_F", "Truck", 50000, 1]
            ];
            publicVariable "eden_vehicleInventory";
        };
        _player setVariable ["eden_vehiclePurchases", [], true];
        true
    };
    case "buyVehicle": {
        _vehicleData = [];
        _vehicleIndex = -1;
        
        {
            if ((_x select 0) == _vehicleType) then {
                _vehicleData = _x;
                _vehicleIndex = _forEachIndex;
            };
        } forEach eden_vehicleInventory;
        
        if (count _vehicleData == 0) exitWith {
            ["Vehicle not available"] call EDEN_fnc_showHint;
            false
        };
        
        _price = _vehicleData select 2;
        _stock = _vehicleData select 3;
        
        if (_stock <= 0) exitWith {
            ["Vehicle out of stock"] call EDEN_fnc_showHint;
            false
        };
        
        _cash = _player getVariable ["eden_cash", 0];
        if (_cash < _price) exitWith {
            [format ["Not enough money! Need $%1", _price]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _price), true];
        
        _pos = _player getPos [10, getDir _player];
        _vehicle = createVehicle [_vehicleType, _pos, [], 0, "NONE"];
        _vehicle setVariable ["eden_owner", getPlayerUID _player, true];
        
        _vehicleData set [3, (_stock - 1)];
        eden_vehicleInventory set [_vehicleIndex, _vehicleData];
        publicVariable "eden_vehicleInventory";
        
        _purchases = _player getVariable ["eden_vehiclePurchases", []];
        _purchases pushBack [_vehicleType, _price, time];
        _player setVariable ["eden_vehiclePurchases", _purchases, true];
        
        [format ["Purchased %1 for $%2", (_vehicleData select 1), _price]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "testDrive": {
        _vehicleData = [];
        {
            if ((_x select 0) == _vehicleType) then { _vehicleData = _x; };
        } forEach eden_vehicleInventory;
        
        if (count _vehicleData == 0) exitWith {
            ["Vehicle not available"] call EDEN_fnc_showHint;
            false
        };
        
        _pos = _player getPos [10, getDir _player];
        _vehicle = createVehicle [_vehicleType, _pos, [], 0, "NONE"];
        _vehicle setVariable ["eden_testDrive", true, true];
        _vehicle setVariable ["eden_testDriver", getPlayerUID _player, true];
        
        ["Test drive started - return within 5 minutes"] call EDEN_fnc_showHint;
        
        [{
            params ["_vehicle", "_player"];
            if (!isNull _vehicle) then {
                deleteVehicle _vehicle;
                ["Test drive ended"] remoteExec ["EDEN_fnc_showHint", _player];
            };
        }, [_vehicle, _player], 300] call CBA_fnc_waitAndExecute;
        
        true
    };
    case "sellVehicle": {
        params ["", "", "", ["_vehicle", objNull, [objNull]]];
        
        if (isNull _vehicle) exitWith { false };
        
        _owner = _vehicle getVariable ["eden_owner", ""];
        if (_owner != getPlayerUID _player) exitWith {
            ["You don't own this vehicle!"] call EDEN_fnc_showHint;
            false
        };
        
        _vehicleType = typeOf _vehicle;
        _sellPrice = 5000; // Base sell price
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _sellPrice), true];
        
        deleteVehicle _vehicle;
        
        [format ["Vehicle sold for $%1", _sellPrice]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
