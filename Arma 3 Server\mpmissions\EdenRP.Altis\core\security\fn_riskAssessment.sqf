/*
    File: fn_riskAssessment.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages risk assessment system.
*/

params [["_action", "init", [""]]];

switch (_action) do {
    case "init": {
        if (isServer) then {
            if (isNil "eden_riskFactors") then {
                eden_riskFactors = [
                    ["new_player", 10],
                    ["vpn_connection", 15],
                    ["multiple_accounts", 25],
                    ["previous_bans", 30],
                    ["suspicious_activity", 20],
                    ["rapid_progression", 15],
                    ["unusual_playtime", 10]
                ];
                publicVariable "eden_riskFactors";
            };
            if (isNil "eden_playerRiskScores") then {
                eden_playerRiskScores = [];
            };
        };
        true
    };
    case "assessPlayer": {
        params ["", ["_player", objNull, [objNull]]];
        
        if (!isServer || isNull _player) exitWith { 0 };
        
        _playerUID = getPlayerUID _player;
        _riskScore = 0;
        
        // Check various risk factors
        _riskScore = _riskScore + ([_player, "checkNewPlayer"] call EDEN_fnc_riskAssessment);
        _riskScore = _riskScore + ([_player, "checkPreviousBans"] call EDEN_fnc_riskAssessment);
        _riskScore = _riskScore + ([_player, "checkSuspiciousActivity"] call EDEN_fnc_riskAssessment);
        _riskScore = _riskScore + ([_player, "checkRapidProgression"] call EDEN_fnc_riskAssessment);
        _riskScore = _riskScore + ([_player, "checkUnusualPlaytime"] call EDEN_fnc_riskAssessment);
        
        // Store risk score
        _found = false;
        {
            if ((_x select 0) == _playerUID) then {
                _x set [1, _riskScore];
                _x set [2, time];
                _found = true;
            };
        } forEach eden_playerRiskScores;
        
        if (!_found) then {
            eden_playerRiskScores pushBack [_playerUID, _riskScore, time];
        };
        
        // Log high risk players
        if (_riskScore > 50) then {
            ["logSecurityEvent", _player, "high_risk_player", "HIGH", format["Risk score: %1", _riskScore]] call EDEN_fnc_auditSystem;
        };
        
        _riskScore
    };
    case "checkNewPlayer": {
        params ["", ["_player", objNull, [objNull]]];
        
        _playTime = _player getVariable ["eden_totalPlayTime", 0];
        if (_playTime < 3600) then { 10 } else { 0 } // Less than 1 hour
    };
    case "checkPreviousBans": {
        params ["", ["_player", objNull, [objNull]]];
        
        _playerUID = getPlayerUID _player;
        _banHistory = _player getVariable ["eden_banHistory", []];
        (count _banHistory) * 15 // 15 points per previous ban
    };
    case "checkSuspiciousActivity": {
        params ["", ["_player", objNull, [objNull]]];
        
        _suspicionScore = ["getSuspicionScore", getPlayerUID _player] call EDEN_fnc_behaviorAnalysis;
        if (_suspicionScore > 30) then { 20 } else { 0 }
    };
    case "checkRapidProgression": {
        params ["", ["_player", objNull, [objNull]]];
        
        _level = _player getVariable ["eden_level", 1];
        _playTime = _player getVariable ["eden_totalPlayTime", 0];
        
        if (_playTime > 0) then {
            _progressionRate = _level / (_playTime / 3600); // Levels per hour
            if (_progressionRate > 5) then { 15 } else { 0 }
        } else {
            0
        };
    };
    case "checkUnusualPlaytime": {
        params ["", ["_player", objNull, [objNull]]];
        
        _sessionTime = time - (_player getVariable ["eden_sessionStart", time]);
        if (_sessionTime > 43200) then { 10 } else { 0 } // More than 12 hours
    };
    case "getRiskLevel": {
        params ["", ["_riskScore", 0, [0]]];
        
        switch (true) do {
            case (_riskScore >= 80): { "CRITICAL" };
            case (_riskScore >= 60): { "HIGH" };
            case (_riskScore >= 40): { "MEDIUM" };
            case (_riskScore >= 20): { "LOW" };
            default { "MINIMAL" };
        }
    };
    case "getPlayerRiskScore": {
        params ["", ["_playerUID", "", [""]]];
        
        if (!isServer) exitWith { 0 };
        
        _score = 0;
        {
            if ((_x select 0) == _playerUID) then { _score = _x select 1; };
        } forEach eden_playerRiskScores;
        
        _score
    };
    case "generateRiskReport": {
        params ["", ["_admin", objNull, [objNull]]];
        
        if (isNull _admin || !(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _reportText = "=== RISK ASSESSMENT REPORT ===\n";
        
        _highRiskPlayers = [];
        {
            if ((_x select 1) > 50) then {
                _highRiskPlayers pushBack _x;
            };
        } forEach eden_playerRiskScores;
        
        _reportText = _reportText + format["High Risk Players: %1\n\n", count _highRiskPlayers];
        
        {
            _playerUID = _x select 0;
            _riskScore = _x select 1;
            _riskLevel = [_riskScore] call EDEN_fnc_riskAssessment;
            
            _playerName = "Unknown";
            {
                if (getPlayerUID _x == _playerUID) then { _playerName = name _x; };
            } forEach allPlayers;
            
            _reportText = _reportText + format["%1: %2 (%3)\n", _playerName, _riskScore, _riskLevel];
        } forEach _highRiskPlayers;
        
        [_reportText] remoteExec ["EDEN_fnc_showHint", _admin];
        true
    };
    case "applyRiskMitigation": {
        params ["", ["_player", objNull, [objNull]], ["_riskScore", 0, [0]]];
        
        if (!isServer || isNull _player) exitWith { false };
        
        _riskLevel = [_riskScore] call EDEN_fnc_riskAssessment;
        
        switch (_riskLevel) do {
            case "CRITICAL": {
                // Immediate restriction
                _player setVariable ["eden_restricted", true, true];
                ["Your account has been restricted due to high risk assessment"] remoteExec ["EDEN_fnc_showHint", _player];
                
                // Notify admins
                {
                    if (getPlayerUID _x in eden_adminList) then {
                        [format ["🚨 CRITICAL RISK: %1 has been automatically restricted", name _player]] remoteExec ["EDEN_fnc_showHint", _x];
                    };
                } forEach allPlayers;
            };
            case "HIGH": {
                // Enhanced monitoring
                _player setVariable ["eden_enhancedMonitoring", true, true];
                ["You are under enhanced monitoring due to risk factors"] remoteExec ["EDEN_fnc_showHint", _player];
            };
            case "MEDIUM": {
                // Increased validation
                _player setVariable ["eden_increasedValidation", true, true];
            };
        };
        
        true
    };
    default { 0 };
};
