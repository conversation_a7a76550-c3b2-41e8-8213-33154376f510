/*
    File: fn_rentVehicle.sqf
    Author: EdenRP Development Team
    
    Description:
    Rents a vehicle for temporary use.
    
    Parameters:
    0: STRING - Vehicle class to rent
    1: NUMBER - Rental duration in hours (optional, default: 1)
    2: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if rental was successful
*/

params [
    ["_vehicleClass", "", [""]],
    ["_duration", 1, [0]],
    ["_player", player, [objNull]]
];

if (_vehicleClass == "" || _duration <= 0 || isNull _player) exitWith { false };

// Rental prices per hour
_rentalPrices = createHashMap;
_rentalPrices set ["C_Offroad_01_F", 100];
_rentalPrices set ["C_SUV_01_F", 150];
_rentalPrices set ["C_Hatchback_01_F", 75];
_rentalPrices set ["C_Van_01_transport_F", 200];
_rentalPrices set ["B_Quadbike_01_F", 50];
_rentalPrices set ["C_Boat_Civil_01_F", 125];
_rentalPrices set ["B_Heli_Light_01_civil_F", 500];

_hourlyRate = _rentalPrices getOrDefault [_vehicleClass, 0];
if (_hourlyRate == 0) exitWith {
    ["This vehicle is not available for rent!"] call EDEN_fnc_showHint;
    false
};

_totalCost = _hourlyRate * _duration;
_playerMoney = _player getVariable ["eden_cash", 0];

if (_playerMoney < _totalCost) exitWith {
    [format ["Rental costs $%1, but you only have $%2!", _totalCost, _playerMoney]] call EDEN_fnc_showHint;
    false
};

// Check license requirements
_requiredLicense = switch (true) do {
    case (_vehicleClass isKindOf "Car"): { "driver" };
    case (_vehicleClass isKindOf "Air"): { "pilot" };
    case (_vehicleClass isKindOf "Ship"): { "boat" };
    default { "" };
};

if (_requiredLicense != "" && !(_player getVariable [format ["eden_license_%1", _requiredLicense], false])) exitWith {
    [format ["You need a %1 license to rent this vehicle!", _requiredLicense]] call EDEN_fnc_showHint;
    false
};

// Find spawn position near player
_spawnPos = [getPos _player, 10, 50, 5, 0, 20, 0] call BIS_fnc_findSafePos;

// Spawn vehicle
_vehicle = createVehicle [_vehicleClass, _spawnPos, [], 0, "NONE"];
_vehicle setDir (getDir _player);
_vehicle setFuel 1;

// Set rental properties
_vehicle setVariable ["eden_isRental", true, true];
_vehicle setVariable ["eden_renterUID", getPlayerUID _player, true];
_vehicle setVariable ["eden_renterName", name _player, true];
_vehicle setVariable ["eden_rentalExpiry", time + (_duration * 3600), true];
_vehicle setVariable ["eden_rentalCost", _totalCost, true];

// Charge player
_player setVariable ["eden_cash", (_playerMoney - _totalCost), true];

// Auto-return rental after expiry
[_vehicle, _duration] spawn {
    params ["_rentalVehicle", "_hours"];
    
    sleep (_hours * 3600); // Wait for rental period
    
    if (!isNull _rentalVehicle && _rentalVehicle getVariable ["eden_isRental", false]) then {
        // Warn players in vehicle
        {
            if (_x in _rentalVehicle) then {
                ["Rental period expired! Vehicle will be returned in 60 seconds."] remoteExec ["EDEN_fnc_showHint", _x];
            };
        } forEach allPlayers;
        
        sleep 60;
        
        // Eject passengers and delete vehicle
        {
            _x action ["GetOut", _rentalVehicle];
        } forEach crew _rentalVehicle;
        
        sleep 2;
        deleteVehicle _rentalVehicle;
    };
};

[format ["Vehicle rented for %1 hours ($%2). Expires at %3", _duration, _totalCost, [time + (_duration * 3600), "HH:MM"] call BIS_fnc_timeToString]] call EDEN_fnc_showHint;

// Log rental
[format ["[EDEN] Player %1 rented %2 for %3 hours ($%4)", name _player, _vehicleClass, _duration, _totalCost], "INFO", "VEHICLE"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
