/*
    File: fn_warrantSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages warrant system for police.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_activeWarrants") then {
            eden_activeWarrants = [];
            publicVariable "eden_activeWarrants";
        };
        _player setVariable ["eden_warrantsIssued", 0, true];
        true
    };
    case "issueWarrant": {
        params ["", "", "", ["_warrantType", "Arrest", [""]], ["_reason", "", [""]]];
        
        if (isNull _target) exitWith { false };
        
        _warrantId = format ["WAR_%1_%2", floor(random 10000), floor(time)];
        _warrant = [_warrantId, getPlayerUID _target, name _target, _warrantType, _reason, name _player, time, "Active"];
        
        eden_activeWarrants pushBack _warrant;
        publicVariable "eden_activeWarrants";
        
        _issued = _player getVariable ["eden_warrantsIssued", 0];
        _player setVariable ["eden_warrantsIssued", (_issued + 1), true];
        
        [format ["Warrant issued for %1: %2", name _target, _warrantType]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "executeWarrant": {
        params ["", "", "", ["_warrantId", "", [""]]];
        
        _warrantIndex = -1;
        {
            if ((_x select 0) == _warrantId) then { _warrantIndex = _forEachIndex; };
        } forEach eden_activeWarrants;
        
        if (_warrantIndex == -1) exitWith { false };
        
        _warrant = eden_activeWarrants select _warrantIndex;
        _warrant set [7, "Executed"];
        eden_activeWarrants set [_warrantIndex, _warrant];
        publicVariable "eden_activeWarrants";
        
        [format ["Warrant %1 executed", _warrantId]] call EDEN_fnc_showHint;
        true
    };
    case "checkWarrants": {
        if (isNull _target) exitWith { false };
        
        _targetUID = getPlayerUID _target;
        _warrants = [];
        
        {
            if ((_x select 1) == _targetUID && (_x select 7) == "Active") then {
                _warrants pushBack _x;
            };
        } forEach eden_activeWarrants;
        
        if (count _warrants == 0) then {
            [format ["%1 has no active warrants", name _target]] call EDEN_fnc_showHint;
        } else {
            _warrantList = format ["%1 has %2 active warrants:\n", name _target, count _warrants];
            {
                _type = _x select 3;
                _reason = _x select 4;
                _warrantList = _warrantList + format ["- %1: %2\n", _type, _reason];
            } forEach _warrants;
            
            [_warrantList] call EDEN_fnc_showHint;
        };
        
        true
    };
    default { false };
};
