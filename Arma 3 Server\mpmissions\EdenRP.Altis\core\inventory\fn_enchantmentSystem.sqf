/*
    File: fn_enchantmentSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages item enchantment and enhancement system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_item", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_enchantments") then {
            eden_enchantments = [
                ["efficiency", "Increases work speed by 25%", 1000],
                ["durability", "Increases item durability by 50%", 800],
                ["fortune", "Increases resource yield by 30%", 1200],
                ["lightweight", "Reduces item weight by 40%", 600],
                ["sharpness", "Increases damage by 20%", 900]
            ];
            publicVariable "eden_enchantments";
        };
        _player setVariable ["eden_itemEnchantments", [], true];
        _player setVariable ["eden_enchantingLevel", 1, true];
        true
    };
    case "enchantItem": {
        params ["", "", "", ["_itemIndex", 0, [0]], ["_enchantment", "efficiency", [""]]];
        
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        if (_itemIndex >= count _virtualItems) exitWith {
            ["Invalid item index"] call EDEN_fnc_showHint;
            false
        };
        
        _enchantmentData = [];
        {
            if ((_x select 0) == _enchantment) then { _enchantmentData = _x; };
        } forEach eden_enchantments;
        
        if (count _enchantmentData == 0) exitWith {
            ["Enchantment not found"] call EDEN_fnc_showHint;
            false
        };
        
        _cost = _enchantmentData select 2;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _level = _player getVariable ["eden_enchantingLevel", 1];
        if (_level < 2) exitWith {
            ["Enchanting level too low"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _itemEnchantments = _player getVariable ["eden_itemEnchantments", []];
        
        // Ensure enchantment array matches virtual items
        while {count _itemEnchantments < count _virtualItems} do {
            _itemEnchantments pushBack [];
        };
        
        _currentEnchantments = _itemEnchantments select _itemIndex;
        _currentEnchantments pushBack _enchantment;
        _itemEnchantments set [_itemIndex, _currentEnchantments];
        _player setVariable ["eden_itemEnchantments", _itemEnchantments, true];
        
        [format ["Enchanted item with %1", _enchantment]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "removeEnchantment": {
        params ["", "", "", ["_itemIndex", 0, [0]], ["_enchantmentIndex", 0, [0]]];
        
        _itemEnchantments = _player getVariable ["eden_itemEnchantments", []];
        if (_itemIndex >= count _itemEnchantments) exitWith {
            ["Invalid item index"] call EDEN_fnc_showHint;
            false
        };
        
        _enchantments = _itemEnchantments select _itemIndex;
        if (_enchantmentIndex >= count _enchantments) exitWith {
            ["Invalid enchantment index"] call EDEN_fnc_showHint;
            false
        };
        
        _removedEnchantment = _enchantments select _enchantmentIndex;
        _enchantments deleteAt _enchantmentIndex;
        _itemEnchantments set [_itemIndex, _enchantments];
        _player setVariable ["eden_itemEnchantments", _itemEnchantments, true];
        
        [format ["Removed %1 enchantment", _removedEnchantment]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "getEnchantmentBonus": {
        params ["", "", "", ["_itemIndex", 0, [0]], ["_bonusType", "efficiency", [""]]];
        
        _itemEnchantments = _player getVariable ["eden_itemEnchantments", []];
        if (_itemIndex >= count _itemEnchantments) exitWith { 0 };
        
        _enchantments = _itemEnchantments select _itemIndex;
        _bonus = 0;
        
        {
            switch (_x) do {
                case "efficiency": {
                    if (_bonusType == "speed") then { _bonus = _bonus + 0.25; };
                };
                case "durability": {
                    if (_bonusType == "durability") then { _bonus = _bonus + 0.5; };
                };
                case "fortune": {
                    if (_bonusType == "yield") then { _bonus = _bonus + 0.3; };
                };
                case "lightweight": {
                    if (_bonusType == "weight") then { _bonus = _bonus + 0.4; };
                };
                case "sharpness": {
                    if (_bonusType == "damage") then { _bonus = _bonus + 0.2; };
                };
            };
        } forEach _enchantments;
        
        _bonus
    };
    case "upgradeEnchanting": {
        _level = _player getVariable ["eden_enchantingLevel", 1];
        _cost = _level * 2000;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        _player setVariable ["eden_enchantingLevel", (_level + 1), true];
        
        [format ["Enchanting level upgraded to %1", (_level + 1)]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
