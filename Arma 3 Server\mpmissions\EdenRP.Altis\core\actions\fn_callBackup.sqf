/*
    File: fn_callBackup.sqf
    Author: EdenRP Development Team
    
    Description:
    Calls for police backup.
    
    Parameters:
    0: OBJECT - Officer calling backup (optional, default: player)
    1: STRING - Backup type (optional, default: "general")
    
    Returns:
    BOOLEAN - True if backup was called successfully
*/

params [
    ["_officer", player, [obj<PERSON><PERSON>]],
    ["_backupType", "general", [""]]
];

if (isNull _officer) exitWith { false };

// Check if caller is police
if (!(_officer getVariable ["eden_isPolice", false])) exitWith {
    ["Only police officers can call for backup!"] call EDEN_fnc_showHint;
    false
};

// Check cooldown
_lastBackupCall = _officer getVariable ["eden_lastBackupCall", 0];
if ((time - _lastBackupCall) < 120) exitWith { // 2 minute cooldown
    _remainingTime = 120 - (time - _lastBackupCall);
    [format ["Backup cooldown: %1 seconds remaining", round _remainingTime]] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_officer, "police_action", [_officer, "backup_request"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Set cooldown
_officer setVariable ["eden_lastBackupCall", time, true];

// Determine backup priority and message
_priority = "MEDIUM";
_backupMessage = "";
_responseRadius = 1000;

switch (_backupType) do {
    case "officer_down": {
        _priority = "CRITICAL";
        _backupMessage = "OFFICER DOWN! IMMEDIATE ASSISTANCE REQUIRED!";
        _responseRadius = 2000;
    };
    case "shots_fired": {
        _priority = "HIGH";
        _backupMessage = "SHOTS FIRED! Armed suspects, requesting immediate backup!";
        _responseRadius = 1500;
    };
    case "pursuit": {
        _priority = "HIGH";
        _backupMessage = "HIGH-SPEED PURSUIT! Requesting units for intercept!";
        _responseRadius = 1500;
    };
    case "robbery": {
        _priority = "HIGH";
        _backupMessage = "ROBBERY IN PROGRESS! Multiple units needed!";
        _responseRadius = 1200;
    };
    case "traffic_stop": {
        _priority = "LOW";
        _backupMessage = "Requesting backup for traffic stop";
        _responseRadius = 800;
    };
    case "investigation": {
        _priority = "LOW";
        _backupMessage = "Requesting additional units for investigation";
        _responseRadius = 600;
    };
    default {
        _priority = "MEDIUM";
        _backupMessage = "Requesting general backup assistance";
        _responseRadius = 1000;
    };
};

// Find available police officers
_availableOfficers = [];
{
    if (_x != _officer && _x getVariable ["eden_isPolice", false] && !(_x getVariable ["eden_isJailed", false])) then {
        _availableOfficers pushBack _x;
    };
} forEach allPlayers;

if (count _availableOfficers == 0) exitWith {
    ["No police officers available for backup!"] call EDEN_fnc_showHint;
    false
};

// Send backup request to all available officers
_officerPos = getPosATL _officer;
_gridPos = mapGridPosition _officer;

{
    _distance = _x distance _officer;
    if (_distance <= _responseRadius) then {
        [
            format ["BACKUP REQUEST - %1", _priority],
            format ["%2\nOfficer: %3\nLocation: %4\nDistance: %5m", 
                _backupMessage, 
                name _officer, 
                _gridPos, 
                round _distance
            ],
            if (_priority == "CRITICAL") then {30} else {if (_priority == "HIGH") then {20} else {15}},
            if (_priority == "CRITICAL") then {"error"} else {if (_priority == "HIGH") then {"warning"} else {"info"}}
        ] remoteExec ["EDEN_fnc_showNotification", _x];
        
        // Create waypoint marker for responding officers
        _markerName = format ["backup_%1_%2", getPlayerUID _officer, time];
        _marker = createMarkerLocal [_markerName, _officerPos];
        _marker setMarkerTypeLocal "mil_flag";
        _marker setMarkerTextLocal format ["BACKUP - %1", name _officer];
        _marker setMarkerColorLocal if (_priority == "CRITICAL") then {"ColorRed"} else {if (_priority == "HIGH") then {"ColorOrange"} else {"ColorBlue"}};
        
        // Remove marker after 10 minutes
        [_markerName] spawn {
            params ["_m"];
            sleep 600;
            deleteMarkerLocal _m;
        };
    };
} forEach _availableOfficers;

// Update officer statistics
_backupCalls = _officer getVariable ["eden_backupCalls", 0];
_officer setVariable ["eden_backupCalls", (_backupCalls + 1), true];

// Add to police activity log
_policeActivity = missionNamespace getVariable ["eden_policeActivity", []];
_activityEntry = [
    time,
    name _officer,
    "Backup Request",
    format ["%1 - %2", _backupType, _gridPos]
];
_policeActivity pushBack _activityEntry;
missionNamespace setVariable ["eden_policeActivity", _policeActivity, true];

// Confirmation message
[format ["Backup requested (%1 priority) - %2 officers notified", _priority, count _availableOfficers]] call EDEN_fnc_showHint;

// Special effects for critical backup
if (_backupType == "officer_down") then {
    // Emergency siren sound effect
    playSound3D ["A3\Sounds_F\vehicles\soft\MRAP_02\horn.wss", _officer, false, _officerPos, 5, 1, 100];
    
    // Emergency lighting effect
    [_officer] spawn {
        params ["_unit"];
        for "_i" from 1 to 10 do {
            _light = "#lightpoint" createVehicleLocal (getPosATL _unit);
            _light setLightBrightness 1;
            _light setLightColor [1, 0, 0];
            _light setLightAmbient [1, 0, 0];
            sleep 0.5;
            deleteVehicle _light;
            sleep 0.5;
        };
    };
};

// Log backup request
[format ["[EDEN] Backup requested by %1 - Type: %2, Priority: %3", name _officer, _backupType, _priority], "INFO", "POLICE"] call EDEN_fnc_systemLogger;

[_officer] call EDEN_fnc_savePlayerData;

true
