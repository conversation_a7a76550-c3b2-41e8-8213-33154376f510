/*
    File: fn_withdrawMoney.sqf
    Author: EdenRP Development Team
    
    Description:
    Withdraws money from player's bank account.
    
    Parameters:
    0: NUMBER - Amount to withdraw
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if withdrawal was successful
*/

params [
    ["_amount", 0, [0]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (_amount <= 0 || isNull _player) exitWith { false };

_bankBalance = _player getVariable ["eden_bankAccount", 0];
if (_bankBalance < _amount) exitWith {
    ["Insufficient bank funds!"] call EDEN_fnc_showHint;
    false
};

// Transfer from bank to cash
_player setVariable ["eden_bankAccount", (_bankBalance - _amount), true];
_playerCash = _player getVariable ["eden_cash", 0];
_player setVariable ["eden_cash", (_playerCash + _amount), true];

[format ["Withdrew $%1. Bank balance: $%2", _amount, (_bankBalance - _amount)]] call EDEN_fnc_showHint;
[_player] call EDEN_fnc_savePlayerData;

true
