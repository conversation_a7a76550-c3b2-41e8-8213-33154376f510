/*
    File: fn_encryptionSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages communication encryption system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_encryptionKeys", [], true];
        _player setVariable ["eden_encryptionLevel", 1, true];
        true
    };
    case "encryptMessage": {
        params ["", "", ["_message", "", [""]], ["_keyLevel", 1, [0]]];
        
        _level = _player getVariable ["eden_encryptionLevel", 1];
        if (_keyLevel > _level) exitWith {
            ["Encryption level too high"] call EDEN_fnc_showHint;
            false
        };
        
        _encryptedMsg = format["[ENC-%1] %2", _keyLevel, _message];
        [_encryptedMsg] call EDEN_fnc_showHint;
        true
    };
    case "decryptMessage": {
        params ["", "", ["_encryptedMessage", "", [""]], ["_key", "", [""]]];
        
        _keys = _player getVariable ["eden_encryptionKeys", []];
        if (!(_key in _keys)) exitWith {
            ["Invalid decryption key"] call EDEN_fnc_showHint;
            false
        };
        
        _decryptedMsg = format["[DECRYPTED] %1", _encryptedMessage];
        [_decryptedMsg] call EDEN_fnc_showHint;
        true
    };
    case "upgradeEncryption": {
        _level = _player getVariable ["eden_encryptionLevel", 1];
        _cost = _level * 3000;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        _player setVariable ["eden_encryptionLevel", (_level + 1), true];
        
        [format ["Encryption upgraded to level %1", (_level + 1)]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
