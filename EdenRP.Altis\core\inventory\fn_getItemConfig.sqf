/*
    EdenRP Get Item Configuration
    Enhanced item configuration system
*/

params [["_item", "", [""]]];

if (_item == "") exitWith {[]};

// Item configuration database
// Format: [displayName, description, weight, type, isVirtual, maxStack, value, processingData]
private _itemConfigs = [
    // Basic Items
    ["water", ["Water Bottle", "Clean drinking water", 0.5, "consumable", true, 10, 5, []]],
    ["apple", ["Apple", "Fresh red apple", 0.2, "food", true, 20, 3, []]],
    ["bread", ["Bread", "Fresh baked bread", 0.3, "food", true, 15, 8, []]],
    ["milk", ["Milk", "Fresh cow milk", 0.4, "consumable", true, 10, 6, []]],
    
    // Tools and Equipment
    ["toolkit", ["Toolkit", "Basic repair tools", 2.0, "tool", false, 1, 150, []]],
    ["cellphone", ["Cell Phone", "Modern smartphone", 0.3, "communication", false, 1, 500, []]],
    ["map", ["Map", "Detailed area map", 0.1, "navigation", false, 1, 25, []]],
    ["compass", ["Compass", "Navigation compass", 0.2, "navigation", false, 1, 50, []]],
    ["watch", ["Watch", "Digital wristwatch", 0.1, "accessory", false, 1, 100, []]],
    ["gps", ["GPS Device", "Satellite navigation", 0.4, "navigation", false, 1, 300, []]],
    
    // Medical Items
    ["medkit", ["Medical Kit", "First aid supplies", 1.5, "medical", false, 5, 200, []]],
    ["bandage", ["Bandage", "Medical bandage", 0.1, "medical", true, 50, 10, []]],
    ["morphine", ["Morphine", "Pain medication", 0.1, "medical", true, 20, 100, []]],
    ["defibrillator", ["Defibrillator", "Emergency revival device", 3.0, "medical", false, 1, 1500, []]],
    ["bloodbag", ["Blood Bag", "Medical blood supply", 0.5, "medical", true, 10, 150, []]],
    
    // Police Equipment
    ["handcuffs", ["Handcuffs", "Police restraints", 0.5, "police", false, 5, 75, []]],
    ["ticket_book", ["Ticket Book", "Traffic violation tickets", 0.2, "police", false, 1, 50, []]],
    ["radar_gun", ["Radar Gun", "Speed detection device", 1.0, "police", false, 1, 800, []]],
    ["breathalyzer", ["Breathalyzer", "Alcohol detection device", 0.8, "police", false, 1, 600, []]],
    ["evidence_bag", ["Evidence Bag", "Crime scene evidence container", 0.1, "police", true, 100, 5, []]],
    
    // Raw Materials
    ["iron_ore", ["Iron Ore", "Raw iron ore", 2.0, "material", true, 50, 15, ["iron_ingot"]]],
    ["copper_ore", ["Copper Ore", "Raw copper ore", 1.8, "material", true, 50, 12, ["copper_ingot"]]],
    ["diamond_uncut", ["Uncut Diamond", "Raw diamond", 0.1, "material", true, 20, 500, ["diamond"]]],
    ["oil_unprocessed", ["Crude Oil", "Unrefined petroleum", 1.5, "material", true, 30, 25, ["oil"]]],
    ["salt_unrefined", ["Rock Salt", "Unprocessed salt", 1.2, "material", true, 40, 8, ["salt"]]],
    
    // Processed Materials
    ["iron_ingot", ["Iron Ingot", "Processed iron", 1.5, "material", true, 30, 35, []]],
    ["copper_ingot", ["Copper Ingot", "Processed copper", 1.2, "material", true, 30, 28, []]],
    ["diamond", ["Cut Diamond", "Processed diamond", 0.05, "valuable", true, 10, 1200, []]],
    ["oil", ["Refined Oil", "Processed petroleum", 1.0, "material", true, 20, 60, []]],
    ["salt", ["Refined Salt", "Processed salt", 0.8, "material", true, 25, 18, []]],
    
    // Illegal Items
    ["marijuana", ["Marijuana", "Illegal substance", 0.3, "drug", true, 25, 150, []]],
    ["cocaine", ["Cocaine", "Illegal substance", 0.2, "drug", true, 20, 300, []]],
    ["heroin", ["Heroin", "Illegal substance", 0.1, "drug", true, 15, 500, []]],
    ["meth", ["Methamphetamine", "Illegal substance", 0.15, "drug", true, 18, 400, []]],
    
    // Weapons and Ammo
    ["pistol_9mm", ["9mm Pistol", "Semi-automatic pistol", 1.2, "weapon", false, 1, 2500, []]],
    ["rifle_556", ["5.56 Rifle", "Assault rifle", 3.5, "weapon", false, 1, 8500, []]],
    ["ammo_9mm", ["9mm Ammunition", "Pistol ammunition", 0.02, "ammo", true, 200, 5, []]],
    ["ammo_556", ["5.56 Ammunition", "Rifle ammunition", 0.03, "ammo", true, 150, 8, []]],
    
    // Fishing Items
    ["fish_tuna", ["Tuna", "Fresh caught tuna", 2.5, "food", true, 10, 45, []]],
    ["fish_salmon", ["Salmon", "Fresh caught salmon", 2.0, "food", true, 12, 35, []]],
    ["fish_mackerel", ["Mackerel", "Fresh caught mackerel", 1.5, "food", true, 15, 25, []]],
    ["turtle_raw", ["Sea Turtle", "Illegal to possess", 5.0, "illegal", true, 5, 300, ["turtle_soup"]]],
    
    // Processed Food
    ["turtle_soup", ["Turtle Soup", "Expensive delicacy", 0.8, "food", true, 8, 800, []]],
    
    // Vehicle Items
    ["lockpick", ["Lockpick", "Vehicle entry tool", 0.1, "tool", true, 10, 200, []]],
    ["spike_strip", ["Spike Strip", "Vehicle stopping device", 5.0, "police", false, 2, 500, []]],
    ["repair_kit", ["Repair Kit", "Vehicle repair supplies", 3.0, "tool", false, 3, 350, []]],
    ["fuel_can", ["Fuel Can", "Emergency fuel supply", 2.0, "tool", false, 5, 100, []]],
    
    // Crafting Materials
    ["plastic", ["Plastic", "Industrial plastic", 0.5, "material", true, 50, 12, []]],
    ["glass", ["Glass", "Refined glass", 1.0, "material", true, 30, 20, []]],
    ["rubber", ["Rubber", "Industrial rubber", 0.8, "material", true, 40, 15, []]],
    ["fabric", ["Fabric", "Textile material", 0.3, "material", true, 60, 8, []]],
    
    // Special Items
    ["gold_bar", ["Gold Bar", "Pure gold ingot", 2.0, "valuable", true, 10, 2500, []]],
    ["money_case", ["Money Case", "Briefcase full of cash", 5.0, "valuable", false, 1, 50000, []]],
    ["laptop", ["Laptop Computer", "Portable computer", 2.5, "electronics", false, 1, 1200, []]],
    ["server_data", ["Server Data", "Encrypted data drive", 0.2, "electronics", true, 5, 5000, []]]
];

// Find item configuration
private _config = [];
{
    if ((_x select 0) == _item) exitWith {
        _config = _x select 1;
    };
} forEach _itemConfigs;

_config
