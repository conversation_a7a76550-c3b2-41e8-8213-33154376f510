/*
    File: fn_recruitmentSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages gang recruitment system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_recruitmentOffers", [], true];
        _player setVariable ["eden_recruitsFound", 0, true];
        true
    };
    case "invitePlayer": {
        _gang = _player getVariable ["eden_gang", ""];
        _rank = _player getVariable ["eden_gangRank", ""];
        
        if (_gang == "" || !(_rank in ["Leader", "Captain", "Lieutenant"])) exitWith {
            ["Insufficient rank to recruit!"] call EDEN_fnc_showHint;
            false
        };
        
        if (isNull _target) exitWith { false };
        
        _targetGang = _target getVariable ["eden_gang", ""];
        if (_targetGang != "") exitWith {
            ["Target is already in a gang!"] call EDEN_fnc_showHint;
            false
        };
        
        _offers = _target getVariable ["eden_recruitmentOffers", []];
        _offers pushBack [_gang, name _player, time];
        _target setVariable ["eden_recruitmentOffers", _offers, true];
        
        [format ["Invited %1 to join %2", name _target, _gang]] call EDEN_fnc_showHint;
        [format ["You have been invited to join %1", _gang]] remoteExec ["EDEN_fnc_showHint", _target];
        true
    };
    case "acceptInvite": {
        params ["", "", "", ["_gangName", "", [""]]];
        
        _offers = _player getVariable ["eden_recruitmentOffers", []];
        _validOffer = false;
        
        {
            if ((_x select 0) == _gangName) then {
                _validOffer = true;
                _offers deleteAt _forEachIndex;
            };
        } forEach _offers;
        
        if (!_validOffer) exitWith {
            ["No valid invitation found!"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_gang", _gangName, true];
        _player setVariable ["eden_gangRank", "Member", true];
        _player setVariable ["eden_recruitmentOffers", _offers, true];
        
        [format ["Joined gang: %1", _gangName]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
