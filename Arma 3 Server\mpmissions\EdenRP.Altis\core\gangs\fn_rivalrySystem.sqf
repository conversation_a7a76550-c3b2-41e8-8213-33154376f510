/*
    File: fn_rivalrySystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages gang rivalry system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_targetGang", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_gangRivalries") then {
            eden_gangRivalries = [];
            publicVariable "eden_gangRivalries";
        };
        _player setVariable ["eden_rivalryPoints", 0, true];
        true
    };
    case "createRivalry": {
        _gang = _player getVariable ["eden_gang", ""];
        if (_gang == "") exitWith {
            ["You must be in a gang!"] call EDEN_fnc_showHint;
            false
        };
        
        _rivalry = [_gang, _targetGang, 0, time];
        eden_gangRivalries pushBack _rivalry;
        publicVariable "eden_gangRivalries";
        
        [format ["Rivalry established with %1", _targetGang]] call EDEN_fnc_showHint;
        true
    };
    case "escalateRivalry": {
        _gang = _player getVariable ["eden_gang", ""];
        
        {
            if (((_x select 0) == _gang || (_x select 1) == _gang)) then {
                _intensity = (_x select 2) + 10;
                _x set [2, _intensity];
                eden_gangRivalries set [_forEachIndex, _x];
            };
        } forEach eden_gangRivalries;
        
        publicVariable "eden_gangRivalries";
        
        _points = _player getVariable ["eden_rivalryPoints", 0];
        _player setVariable ["eden_rivalryPoints", (_points + 5), true];
        
        ["Rivalry escalated"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
