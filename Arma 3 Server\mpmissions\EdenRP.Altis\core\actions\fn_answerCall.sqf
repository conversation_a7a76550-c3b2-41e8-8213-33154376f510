/*
    File: fn_answerCall.sqf
    Author: EdenRP Development Team
    
    Description:
    Answers an incoming phone call.
    
    Parameters:
    0: OBJECT - Player answering (optional, default: player)
    
    Returns:
    BOOLEAN - True if call was answered successfully
*/

params [["_player", player, [objNull]]];

if (isNull _player) exitWith { false };

_incomingCaller = _player getVariable ["eden_incomingCall", objNull];
if (isNull _incomingCaller) exitWith {
    ["You don't have any incoming calls!"] call EDEN_fnc_showHint;
    false
};

// Check if caller is still trying to call
if (!(_incomingCaller getVariable ["eden_inCall", false])) exitWith {
    ["The caller has hung up!"] call EDEN_fnc_showHint;
    _player setVariable ["eden_incomingCall", objNull, true];
    false
};

// Answer the call
_player setVariable ["eden_inCall", true, true];
_player setVariable ["eden_callTarget", _incomingCaller, true];
_player setVariable ["eden_incomingCall", objNull, true];

_incomingCaller setVariable ["eden_callTarget", _player, true];

// Notify both parties
[format ["Call answered! You are now talking to %1", name _incomingCaller]] call EDEN_fnc_showHint;
[format ["%1 answered the call! You are now connected", name _player]] remoteExec ["EDEN_fnc_showHint", _incomingCaller];

// Set up call duration tracking
_player setVariable ["eden_callStartTime", time, true];
_incomingCaller setVariable ["eden_callStartTime", time, true];

// Log call answer
[format ["[EDEN] Phone call answered: %1 <-> %2", name _incomingCaller, name _player], "INFO", "COMMUNICATION"] call EDEN_fnc_systemLogger;

true
