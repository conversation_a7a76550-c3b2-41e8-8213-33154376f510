/*
    File: fn_rentalSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages vehicle rental system.
*/

params [["_player", player, [objN<PERSON>]], ["_action", "init", [""]], ["_vehicleType", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_rentedVehicles", [], true];
        _player setVariable ["eden_rentalHistory", [], true];
        true
    };
    case "rentVehicle": {
        params ["", "", "", ["_duration", 3600, [0]]]; // 1 hour default
        
        _rentalRates = [
            ["C_Hatchback_01_F", 50],
            ["C_SUV_01_F", 75],
            ["C_Van_01_transport_F", 100],
            ["B_Truck_01_transport_F", 150]
        ];
        
        _rate = 50;
        {
            if ((_x select 0) == _vehicleType) then { _rate = _x select 1; };
        } forEach _rentalRates;
        
        _cost = floor((_duration / 3600) * _rate);
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _pos = _player getPos [10, getDir _player];
        _vehicle = createVehicle [_vehicleType, _pos, [], 0, "NONE"];
        _vehicle setVariable ["eden_rentalVehicle", true, true];
        _vehicle setVariable ["eden_renterUID", getPlayerUID _player, true];
        _vehicle setVariable ["eden_rentalExpiry", (time + _duration), true];
        
        _rented = _player getVariable ["eden_rentedVehicles", []];
        _rental = [_vehicleType, netId _vehicle, time, (time + _duration), _cost];
        _rented pushBack _rental;
        _player setVariable ["eden_rentedVehicles", _rented, true];
        
        [format ["Vehicle rented for $%1 (%2 hours)", _cost, (_duration / 3600)]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "returnVehicle": {
        params ["", "", "", ["_vehicle", objNull, [objNull]]];
        
        if (isNull _vehicle) exitWith { false };
        
        _isRental = _vehicle getVariable ["eden_rentalVehicle", false];
        _renterUID = _vehicle getVariable ["eden_renterUID", ""];
        
        if (!_isRental || _renterUID != getPlayerUID _player) exitWith {
            ["This is not your rental vehicle!"] call EDEN_fnc_showHint;
            false
        };
        
        _rented = _player getVariable ["eden_rentedVehicles", []];
        {
            if ((_x select 1) == netId _vehicle) then {
                _rented deleteAt _forEachIndex;
            };
        } forEach _rented;
        _player setVariable ["eden_rentedVehicles", _rented, true];
        
        _history = _player getVariable ["eden_rentalHistory", []];
        _history pushBack [typeOf _vehicle, time, "Returned"];
        _player setVariable ["eden_rentalHistory", _history, true];
        
        deleteVehicle _vehicle;
        
        ["Vehicle returned successfully"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
