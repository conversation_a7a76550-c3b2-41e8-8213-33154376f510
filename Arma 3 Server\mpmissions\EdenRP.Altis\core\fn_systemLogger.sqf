/*
    EdenRP System Logger
    Enhanced logging system for EdenRP framework
    
    This function provides comprehensive logging capabilities
    with different log levels and automatic file management
*/

params [
    ["_message", "", [""]],
    ["_level", "INFO", [""]],
    ["_category", "SYSTEM", [""]]
];

// Validate parameters
if (_message == "") exitWith {
    diag_log "EDEN_LOG: [ERROR] Empty message provided to logger";
};

// Get current timestamp
private _timestamp = systemTime;
private _timeString = format [
    "%1-%2-%3 %4:%5:%6",
    _timestamp select 0,
    [_timestamp select 1, 2] call BIS_fnc_formatNumber,
    [_timestamp select 2, 2] call BIS_fnc_formatNumber,
    [_timestamp select 3, 2] call BIS_fnc_formatNumber,
    [_timestamp select 4, 2] call BIS_fnc_formatNumber,
    [_timestamp select 5, 2] call BIS_fnc_formatNumber
];

// Format log entry
private _logEntry = format [
    "[%1] [%2] [%3] %4",
    _timeString,
    toUpper _level,
    toUpper _category,
    _message
];

// Log to game log
diag_log format ["EDEN_LOG: %1", _logEntry];

// Log to RPT file with enhanced formatting
switch (toUpper _level) do {
    case "ERROR": {
        diag_log format ["========== EDEN ERROR =========="];
        diag_log format ["Time: %1", _timeString];
        diag_log format ["Category: %1", _category];
        diag_log format ["Message: %1", _message];
        diag_log format ["================================"];
    };
    case "WARNING": {
        diag_log format ["---------- EDEN WARNING ----------"];
        diag_log format ["Time: %1", _timeString];
        diag_log format ["Category: %1", _category];
        diag_log format ["Message: %1", _message];
        diag_log format ["----------------------------------"];
    };
    case "DEBUG": {
        if (EDEN_Debug) then {
            diag_log format ["DEBUG: %1", _logEntry];
        };
    };
    default {
        diag_log format ["INFO: %1", _logEntry];
    };
};

// Store in server log array if on server
if (isServer) then {
    if (isNil "EDEN_ServerLogs") then {
        EDEN_ServerLogs = [];
    };
    
    // Add to log array
    EDEN_ServerLogs pushBack [
        time,
        _timeString,
        _level,
        _category,
        _message
    ];
    
    // Maintain log size (keep last 1000 entries)
    if (count EDEN_ServerLogs > 1000) then {
        EDEN_ServerLogs = EDEN_ServerLogs select [count EDEN_ServerLogs - 1000, 1000];
    };
    
    // Update server statistics
    if (!isNil "EDEN_ServerStats") then {
        switch (toUpper _level) do {
            case "ERROR": {
                private _errorCount = (EDEN_ServerStats select {(_x select 0) == "ErrorCount"}) select 0;
                if (!isNil "_errorCount") then {
                    _errorCount set [1, (_errorCount select 1) + 1];
                };
            };
            case "WARNING": {
                private _warningCount = (EDEN_ServerStats select {(_x select 0) == "WarningCount"}) select 0;
                if (!isNil "_warningCount") then {
                    _warningCount set [1, (_warningCount select 1) + 1];
                };
            };
        };
    };
};

// Send to database if configured and on server
if (isServer && !isNil "EDEN_DatabaseConnected" && EDEN_DatabaseConnected) then {
    // Only log important messages to database to avoid spam
    if (toUpper _level in ["ERROR", "WARNING", "ADMIN", "SECURITY"]) then {
        private _query = format [
            "INSERT INTO eden_logs (timestamp, level, category, message, server_time) VALUES ('%1', '%2', '%3', '%4', %5)",
            _timeString,
            _level,
            _category,
            _message,
            time
        ];
        
        [_query, 1] call EDEN_fnc_asyncCall;
    };
};

// Send to admin clients if it's an important message
if (isServer && toUpper _level in ["ERROR", "WARNING", "ADMIN", "SECURITY"]) then {
    {
        if ((_x getVariable ["EDEN_AdminLevel", 0]) >= 2) then {
            [_logEntry, _level] remoteExec ["EDEN_fnc_adminLogNotification", _x];
        };
    } forEach allPlayers;
};

// Return the formatted log entry
_logEntry
