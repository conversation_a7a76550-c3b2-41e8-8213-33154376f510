/*
    File: fn_showHint.sqf
    Author: EdenRP Development Team
    
    Description:
    Shows a simple hint message to the player.
    
    Parameters:
    0: STRING - Hint message
    1: NUMBER - Duration in seconds (optional, default: 3)
    
    Returns:
    BOOLEAN - True if hint was shown successfully
    
    Example:
    ["Welcome to EdenRP!"] call EDEN_fnc_showHint;
    ["Action completed!", 5] call EDEN_fnc_showHint;
*/

params [
    ["_message", "", [""]],
    ["_duration", 3, [0]]
];

// Validate parameters
if (_message == "") exitWith {
    ["[EDEN] fn_showHint: Empty hint message"] call EDEN_fnc_systemLogger;
    false
};

// Clamp duration
_duration = _duration max 1 min 15;

// Show hint
hintSilent _message;

// Auto-clear hint after duration
[{
    hintSilent "";
}, [], _duration] call CBA_fnc_waitAndExecute;

// Return success
true
