/*
    File: fn_licenseManager.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages license system for civilians.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_licenseType", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_licenses", [], true];
        _player setVariable ["eden_licensePoints", 0, true];
        true
    };
    case "buyLicense": {
        _availableLicenses = [
            ["driving", "Driving License", 500, 1],
            ["fishing", "Fishing License", 200, 1],
            ["hunting", "Hunting License", 300, 1],
            ["farming", "Farming License", 250, 1],
            ["mining", "Mining License", 400, 2],
            ["trucking", "Trucking License", 750, 2],
            ["pilot", "Pilot License", 2000, 3],
            ["boat", "Boat License", 600, 1],
            ["weapon", "Weapon License", 1500, 3],
            ["business", "Business License", 1000, 2]
        ];
        
        _licenseData = [];
        {
            if ((_x select 0) == _licenseType) then {
                _licenseData = _x;
            };
        } forEach _availableLicenses;
        
        if (count _licenseData == 0) exitWith {
            ["License type not found!"] call EDEN_fnc_showHint;
            false
        };
        
        _licenses = _player getVariable ["eden_licenses", []];
        if (_licenseType in _licenses) exitWith {
            ["You already have this license!"] call EDEN_fnc_showHint;
            false
        };
        
        _name = _licenseData select 1;
        _cost = _licenseData select 2;
        _requiredLevel = _licenseData select 3;
        
        _playerLevel = _player getVariable ["eden_level", 1];
        if (_playerLevel < _requiredLevel) exitWith {
            [format ["You need to be level %1 to get this license!", _requiredLevel]] call EDEN_fnc_showHint;
            false
        };
        
        _cash = _player getVariable ["eden_cash", 0];
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        _licenses pushBack _licenseType;
        _player setVariable ["eden_licenses", _licenses, true];
        
        // Set specific license variables for easy checking
        _player setVariable [format ["eden_%1License", _licenseType], true, true];
        
        [format ["Purchased %1 for $%2!", _name, _cost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "renewLicense": {
        _licenses = _player getVariable ["eden_licenses", []];
        if !(_licenseType in _licenses) exitWith {
            ["You don't have this license!"] call EDEN_fnc_showHint;
            false
        };
        
        _renewalCost = 100; // Fixed renewal cost
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _renewalCost) exitWith {
            [format ["Not enough money for renewal! Need $%1", _renewalCost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _renewalCost), true];
        
        [format ["%1 license renewed for $%2!", _licenseType, _renewalCost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "revokeLicense": {
        _licenses = _player getVariable ["eden_licenses", []];
        if !(_licenseType in _licenses) exitWith {
            ["Player doesn't have this license!"] call EDEN_fnc_showHint;
            false
        };
        
        _licenses = _licenses - [_licenseType];
        _player setVariable ["eden_licenses", _licenses, true];
        _player setVariable [format ["eden_%1License", _licenseType], false, true];
        
        [format ["%1 license has been revoked!", _licenseType]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "checkLicense": {
        _licenses = _player getVariable ["eden_licenses", []];
        (_licenseType in _licenses)
    };
    case "addPoints": {
        params ["", "", "", ["_points", 1, [0]]];
        
        _currentPoints = _player getVariable ["eden_licensePoints", 0];
        _newPoints = _currentPoints + _points;
        _player setVariable ["eden_licensePoints", _newPoints, true];
        
        [format ["License points added: %1 (Total: %2)", _points, _newPoints]] call EDEN_fnc_showHint;
        
        if (_newPoints >= 12) then {
            [_player, "revokeLicense", "driving"] call EDEN_fnc_licenseManager;
            _player setVariable ["eden_licensePoints", 0, true];
            ["Driving license suspended due to too many points!"] call EDEN_fnc_showHint;
        };
        
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "removePoints": {
        params ["", "", "", ["_points", 1, [0]]];
        
        _currentPoints = _player getVariable ["eden_licensePoints", 0];
        _newPoints = (_currentPoints - _points) max 0;
        _player setVariable ["eden_licensePoints", _newPoints, true];
        
        [format ["License points removed: %1 (Total: %2)", _points, _newPoints]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "viewLicenses": {
        _licenses = _player getVariable ["eden_licenses", []];
        _points = _player getVariable ["eden_licensePoints", 0];
        
        if (count _licenses == 0) then {
            ["You have no licenses."] call EDEN_fnc_showHint;
        } else {
            _licenseList = "";
            {
                _licenseList = _licenseList + _x + ", ";
            } forEach _licenses;
            _licenseList = _licenseList select [0, (count _licenseList) - 2]; // Remove last comma
            
            [format ["Your licenses: %1 | Points: %2", _licenseList, _points]] call EDEN_fnc_showHint;
        };
        
        true
    };
    case "transferLicense": {
        params ["", "", "", ["_target", objNull, [objNull]]];
        
        if (isNull _target) exitWith {
            ["No target specified!"] call EDEN_fnc_showHint;
            false
        };
        
        _licenses = _player getVariable ["eden_licenses", []];
        if !(_licenseType in _licenses) exitWith {
            ["You don't have this license to transfer!"] call EDEN_fnc_showHint;
            false
        };
        
        _targetLicenses = _target getVariable ["eden_licenses", []];
        if (_licenseType in _targetLicenses) exitWith {
            ["Target already has this license!"] call EDEN_fnc_showHint;
            false
        };
        
        _transferFee = 50;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _transferFee) exitWith {
            [format ["Not enough money for transfer fee! Need $%1", _transferFee]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _transferFee), true];
        
        _licenses = _licenses - [_licenseType];
        _player setVariable ["eden_licenses", _licenses, true];
        _player setVariable [format ["eden_%1License", _licenseType], false, true];
        
        _targetLicenses pushBack _licenseType;
        _target setVariable ["eden_licenses", _targetLicenses, true];
        _target setVariable [format ["eden_%1License", _licenseType], true, true];
        
        [format ["Transferred %1 license to %2", _licenseType, name _target]] call EDEN_fnc_showHint;
        [format ["Received %1 license from %2", _licenseType, name _player]] remoteExec ["EDEN_fnc_showHint", _target];
        
        [_player] call EDEN_fnc_savePlayerData;
        [_target] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
