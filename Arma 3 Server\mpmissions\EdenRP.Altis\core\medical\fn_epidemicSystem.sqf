/*
    File: fn_epidemicSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages epidemic and disease outbreak system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_epidemicActive") then {
            eden_epidemicActive = false;
            publicVariable "eden_epidemicActive";
        };
        _player setVariable ["eden_infected", false, true];
        true
    };
    case "startEpidemic": {
        eden_epidemicActive = true;
        publicVariable "eden_epidemicActive";
        
        {
            ["EPIDEMIC ALERT: Disease outbreak detected!"] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach allPlayers;
        
        true
    };
    case "infectPlayer": {
        if (!eden_epidemicActive) exitWith { false };
        
        _player setVariable ["eden_infected", true, true];
        _player setVariable ["eden_infectionLevel", 25, true];
        
        ["You have been infected!"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "curePlayer": {
        _player setVariable ["eden_infected", false, true];
        _player setVariable ["eden_infectionLevel", 0, true];
        
        ["You have been cured"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
