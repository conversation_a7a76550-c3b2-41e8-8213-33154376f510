/*
    File: fn_auctionSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages vehicle auction system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_vehicle", objNull, [objNull]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_vehicleAuctions") then {
            eden_vehicleAuctions = [];
            publicVariable "eden_vehicleAuctions";
        };
        _player setVariable ["eden_auctionBids", [], true];
        true
    };
    case "createAuction": {
        params ["", "", "", ["_startingBid", 5000, [0]], ["_duration", 3600, [0]]];
        
        if (isNull _vehicle) exitWith { false };
        
        _auction = [typeOf _vehicle, getPlayerUID _player, name _player, _startingBid, _startingBid, "", time, (time + _duration), "Active"];
        eden_vehicleAuctions pushBack _auction;
        publicVariable "eden_vehicleAuctions";
        
        deleteVehicle _vehicle;
        
        [format ["Vehicle auction created - starting bid: $%1", _startingBid]] call EDEN_fnc_showHint;
        true
    };
    case "placeBid": {
        params ["", "", "", ["_auctionIndex", 0, [0]], ["_bidAmount", 0, [0]]];
        
        if (_auctionIndex >= count eden_vehicleAuctions) exitWith {
            ["Invalid auction"] call EDEN_fnc_showHint;
            false
        };
        
        _auction = eden_vehicleAuctions select _auctionIndex;
        if ((_auction select 8) != "Active" || time >= (_auction select 7)) exitWith {
            ["Auction is not active"] call EDEN_fnc_showHint;
            false
        };
        
        _currentBid = _auction select 4;
        if (_bidAmount <= _currentBid) exitWith {
            [format ["Bid must be higher than $%1", _currentBid]] call EDEN_fnc_showHint;
            false
        };
        
        _cash = _player getVariable ["eden_cash", 0];
        if (_cash < _bidAmount) exitWith {
            [format ["Not enough money! Need $%1", _bidAmount]] call EDEN_fnc_showHint;
            false
        };
        
        _auction set [4, _bidAmount];
        _auction set [5, getPlayerUID _player];
        eden_vehicleAuctions set [_auctionIndex, _auction];
        publicVariable "eden_vehicleAuctions";
        
        _bids = _player getVariable ["eden_auctionBids", []];
        _bids pushBack [(_auction select 0), _bidAmount, time];
        _player setVariable ["eden_auctionBids", _bids, true];
        
        [format ["Bid placed: $%1", _bidAmount]] call EDEN_fnc_showHint;
        true
    };
    case "claimVehicle": {
        params ["", "", "", ["_auctionIndex", 0, [0]]];
        
        if (_auctionIndex >= count eden_vehicleAuctions) exitWith { false };
        
        _auction = eden_vehicleAuctions select _auctionIndex;
        if ((_auction select 5) != getPlayerUID _player || time < (_auction select 7)) exitWith {
            ["Cannot claim this vehicle"] call EDEN_fnc_showHint;
            false
        };
        
        _pos = _player getPos [10, getDir _player];
        _vehicle = createVehicle [(_auction select 0), _pos, [], 0, "NONE"];
        
        _auction set [8, "Completed"];
        eden_vehicleAuctions set [_auctionIndex, _auction];
        publicVariable "eden_vehicleAuctions";
        
        ["Vehicle claimed from auction"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
