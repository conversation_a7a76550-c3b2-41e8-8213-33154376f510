/*
    File: fn_skillTrees.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages skill tree system.
*/

params [["_player", player, [objN<PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_skillTrees") then {
            eden_skillTrees = [
                ["combat", ["accuracy", "reload_speed", "weapon_handling"]],
                ["driving", ["speed", "handling", "fuel_efficiency"]],
                ["crafting", ["speed", "quality", "resource_efficiency"]],
                ["social", ["persuasion", "negotiation", "leadership"]]
            ];
            publicVariable "eden_skillTrees";
        };
        _player setVariable ["eden_skills", [], true];
        true
    };
    case "learnSkill": {
        params ["", "", ["_skillTree", "combat", [""]], ["_skill", "accuracy", [""]]];
        
        _skillPoints = _player getVariable ["eden_skillPoints", 0];
        if (_skillPoints < 1) exitWith {
            ["Not enough skill points"] call EDEN_fnc_showHint;
            false
        };
        
        _skills = _player getVariable ["eden_skills", []];
        _skillKey = format["%1_%2", _skillTree, _skill];
        _currentLevel = 0;
        
        {
            if ((_x select 0) == _skillKey) then { _currentLevel = _x select 1; };
        } forEach _skills;
        
        if (_currentLevel >= 5) exitWith {
            ["Skill already at maximum level"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_skillPoints", (_skillPoints - 1), true];
        
        _found = false;
        {
            if ((_x select 0) == _skillKey) then {
                _x set [1, (_currentLevel + 1)];
                _found = true;
            };
        } forEach _skills;
        
        if (!_found) then {
            _skills pushBack [_skillKey, 1];
        };
        
        _player setVariable ["eden_skills", _skills, true];
        
        [format ["Learned %1 - %2 (Level %3)", _skillTree, _skill, (_currentLevel + 1)]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "getSkillLevel": {
        params ["", "", ["_skillTree", "combat", [""]], ["_skill", "accuracy", [""]]];
        
        _skills = _player getVariable ["eden_skills", []];
        _skillKey = format["%1_%2", _skillTree, _skill];
        _level = 0;
        
        {
            if ((_x select 0) == _skillKey) then { _level = _x select 1; };
        } forEach _skills;
        
        _level
    };
    case "resetSkills": {
        _skills = _player getVariable ["eden_skills", []];
        _refundPoints = 0;
        
        {
            _refundPoints = _refundPoints + (_x select 1);
        } forEach _skills;
        
        _skillPoints = _player getVariable ["eden_skillPoints", 0];
        _player setVariable ["eden_skillPoints", (_skillPoints + _refundPoints), true];
        _player setVariable ["eden_skills", [], true];
        
        [format ["Skills reset! Refunded %1 skill points", _refundPoints]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
