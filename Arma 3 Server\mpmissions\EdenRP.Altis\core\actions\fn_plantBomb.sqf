/*
    File: fn_plantBomb.sqf
    Author: EdenRP Development Team
    
    Description:
    Plants an explosive device.
    
    Parameters:
    0: ARRAY - Position to plant bomb [x, y, z]
    1: OBJECT - Bomber (optional, default: player)
    
    Returns:
    BOOLEAN - True if bomb was planted successfully
*/

params [
    ["_position", [0,0,0], [[]]],
    ["_bomber", player, [obj<PERSON><PERSON>]]
];

if (count _position < 3 || isNull _bomber) exitWith { false };

// Check if bomber has explosives
_virtualItems = _bomber getVariable ["eden_virtualItems", []];
_hasExplosives = false;
_explosiveIndex = -1;

{
    if ((_x select 0) == "explosives") then {
        _hasExplosives = true;
        _explosiveIndex = _forEachIndex;
    };
} forEach _virtualItems;

if (!_hasExplosives) exitWith {
    ["You need explosives to plant a bomb!"] call EDEN_fnc_showHint;
    false
};

// Check if bomber is close enough to position
if (_bomber distance _position > 5) exitWith {
    ["You must be closer to plant the bomb!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_bomber, "criminal_action", [_position, "bomb_planting"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Check for nearby players (can't plant if enemies are too close)
_nearbyPlayers = [];
{
    if (_x != _bomber && _x distance _position < 50) then {
        _nearbyPlayers pushBack _x;
    };
} forEach allPlayers;

if (count _nearbyPlayers > 0) exitWith {
    ["Too many people nearby! Find a more secluded location."] call EDEN_fnc_showHint;
    false
};

// Start bomb planting process
[_bomber, "Acts_carFixingWheel"] remoteExec ["switchMove"];
["Planting explosive device..."] call EDEN_fnc_showHint;

sleep 12; // Planting takes 12 seconds

[_bomber, ""] remoteExec ["switchMove"];

// Consume explosives
_explosiveQuantity = (_virtualItems select _explosiveIndex) select 1;
if (_explosiveQuantity <= 1) then {
    _virtualItems deleteAt _explosiveIndex;
} else {
    (_virtualItems select _explosiveIndex) set [1, (_explosiveQuantity - 1)];
};
_bomber setVariable ["eden_virtualItems", _virtualItems, true];

// Create bomb object
_bombID = format ["BOMB_%1_%2", getPlayerUID _bomber, floor(time)];
_bomb = createVehicle ["Land_Device_assembled_F", _position, [], 0, "CAN_COLLIDE"];
_bomb setVariable ["eden_bombID", _bombID, true];
_bomb setVariable ["eden_bomber", name _bomber, true];
_bomb setVariable ["eden_plantTime", time, true];
_bomb setVariable ["eden_isArmed", true, true];

// Set bomb timer (5-10 minutes)
_timerDuration = 300 + random 300; // 5-10 minutes
_bomb setVariable ["eden_timerDuration", _timerDuration, true];
_bomb setVariable ["eden_detonationTime", (time + _timerDuration), true];

// Add to global bomb list
_activeBombs = missionNamespace getVariable ["eden_activeBombs", []];
_activeBombs pushBack [_bomb, _bombID, time + _timerDuration, name _bomber];
missionNamespace setVariable ["eden_activeBombs", _activeBombs, true];

// Create bomb marker (only visible to bomber initially)
_markerName = format ["bomb_%1", _bombID];
_marker = createMarkerLocal [_markerName, _position];
_marker setMarkerTypeLocal "mil_warning";
_marker setMarkerTextLocal format ["Bomb - %1min", round(_timerDuration/60)];
_marker setMarkerColorLocal "ColorRed";

// Add massive criminal activity
_wantedLevel = _bomber getVariable ["eden_wantedLevel", 0];
_bomber setVariable ["eden_wantedLevel", (_wantedLevel + 10), true]; // Terrorism is max crime

// Add to criminal record
_criminalRecord = _bomber getVariable ["eden_criminalRecord", []];
_crimeRecord = [
    time,
    "System",
    "Terrorism - Bomb planting",
    format ["Planted at %1", mapGridPosition _position]
];
_criminalRecord pushBack _crimeRecord;
_bomber setVariable ["eden_criminalRecord", _criminalRecord, true];

// Add huge bounty
_bounty = _bomber getVariable ["eden_bounty", 0];
_bomber setVariable ["eden_bounty", (_bounty + 10000), true];

// Schedule bomb detonation
[_bomb, _bombID, _position] spawn {
    params ["_bombObj", "_id", "_pos"];
    
    _detonationTime = _bombObj getVariable ["eden_detonationTime", time + 300];
    
    // Wait for detonation time
    waitUntil { time >= _detonationTime || !alive _bombObj || !(_bombObj getVariable ["eden_isArmed", true]) };
    
    if (alive _bombObj && (_bombObj getVariable ["eden_isArmed", true])) then {
        // Create explosion
        _explosion = createVehicle ["Bo_Mk82", _pos, [], 0, "CAN_COLLIDE"];
        
        // Damage nearby objects and players
        {
            if (_x distance _pos < 50) then {
                _damage = 1 - ((_x distance _pos) / 50); // Damage decreases with distance
                _x setDamage (damage _x + _damage);
                
                if (isPlayer _x) then {
                    ["BOMB EXPLOSION! You were caught in the blast!"] remoteExec ["EDEN_fnc_showHint", _x];
                };
            };
        } forEach (nearestObjects [_pos, ["All"], 50]);
        
        // Alert all players
        {
            [
                "TERRORIST ATTACK",
                format ["BOMB EXPLOSION at %1! Emergency services responding!", mapGridPosition _pos],
                20,
                "error"
            ] remoteExec ["EDEN_fnc_showNotification", _x];
        } forEach allPlayers;
        
        // Create explosion marker
        _explosionMarker = createMarker [format ["explosion_%1", _id], _pos];
        _explosionMarker setMarkerType "mil_destroy";
        _explosionMarker setMarkerText "BOMB EXPLOSION";
        _explosionMarker setMarkerColor "ColorRed";
        
        // Log explosion
        [format ["[EDEN] BOMB EXPLOSION at %1 - planted by %2", mapGridPosition _pos, _bombObj getVariable ["eden_bomber", "Unknown"]], "CRITICAL", "TERRORISM"] call EDEN_fnc_systemLogger;
        
        // Remove bomb object
        deleteVehicle _bombObj;
        
        // Remove from active bombs list
        _bombs = missionNamespace getVariable ["eden_activeBombs", []];
        _newBombs = [];
        {
            if ((_x select 1) != _id) then {
                _newBombs pushBack _x;
            };
        } forEach _bombs;
        missionNamespace setVariable ["eden_activeBombs", _newBombs, true];
    };
};

// Immediate police alert for bomb planting
{
    if (_x getVariable ["eden_isPolice", false]) then {
        [
            "TERRORIST THREAT",
            format ["BOMB PLANTED! Explosive device detected at %1 - EVACUATE AREA IMMEDIATELY!", mapGridPosition _position],
            25,
            "error"
        ] remoteExec ["EDEN_fnc_showNotification", _x];
    };
} forEach allPlayers;

[format ["Bomb planted! Timer set for %1 minutes. GET AWAY NOW!", round(_timerDuration/60)]] call EDEN_fnc_showHint;

// Log the bomb planting
[format ["[EDEN] TERRORIST ACTIVITY: Player %1 planted bomb at %2", name _bomber, mapGridPosition _position], "CRITICAL", "TERRORISM"] call EDEN_fnc_systemLogger;

[_bomber] call EDEN_fnc_savePlayerData;

true
