/*
    File: fn_flipVehicle.sqf
    Author: EdenRP Development Team
    
    Description:
    Flips a vehicle that is upside down or on its side.
    
    Parameters:
    0: OBJECT - Vehicle to flip
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if flip was successful
*/

params [
    ["_vehicle", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (isNull _vehicle || isNull _player) exitWith { false };

if (_player distance _vehicle > 10) exitWith {
    ["Vehicle is too far away!"] call EDEN_fnc_showHint;
    false
};

if (!(_vehicle isKindOf "LandVehicle") && !(_vehicle isKindOf "Air")) exitWith {
    ["This vehicle cannot be flipped!"] call EDEN_fnc_showHint;
    false
};

if (!alive _vehicle) exitWith {
    ["Cannot flip destroyed vehicles!"] call EDEN_fnc_showHint;
    false
};

// Check if vehicle needs flipping
_vectorUp = vectorUp _vehicle;
if ((_vectorUp select 2) > 0.5) exitWith {
    ["Vehicle doesn't need flipping!"] call EDEN_fnc_showHint;
    false
};

// Check if player has toolkit
_virtualItems = _player getVariable ["eden_virtualItems", []];
_hasToolkit = false;
{
    if ((_x select 0) == "toolkit" && (_x select 1) > 0) then {
        _hasToolkit = true;
    };
} forEach _virtualItems;

if (!_hasToolkit) exitWith {
    ["You need a toolkit to flip vehicles!"] call EDEN_fnc_showHint;
    false
};

// Flipping animation and process
[_player, "Acts_carFixingWheel"] remoteExec ["switchMove"];
["Flipping vehicle..."] call EDEN_fnc_showHint;

sleep 3;

[_player, ""] remoteExec ["switchMove"];

// Get current position and direction
_pos = getPosATL _vehicle;
_dir = getDir _vehicle;

// Flip the vehicle
_vehicle setVectorUp [0, 0, 1];
_vehicle setPosATL [_pos select 0, _pos select 1, (_pos select 2) + 1];
_vehicle setDir _dir;

// Small damage to toolkit (10% chance to break)
if (random 100 < 10) then {
    // Remove one toolkit
    {
        if ((_x select 0) == "toolkit") then {
            if ((_x select 1) <= 1) then {
                _virtualItems deleteAt _forEachIndex;
            } else {
                _x set [1, ((_x select 1) - 1)];
            };
        };
    } forEach _virtualItems;
    _player setVariable ["eden_virtualItems", _virtualItems, true];
    ["Your toolkit broke during the flip!"] call EDEN_fnc_showHint;
};

// Add experience
_expGained = 25;
_currentExp = _player getVariable ["eden_experience", 0];
_player setVariable ["eden_experience", (_currentExp + _expGained), true];

["Vehicle flipped successfully! (+25 XP)"] call EDEN_fnc_showHint;

// Log the action
[format ["[EDEN] Player %1 flipped vehicle %2", name _player, typeOf _vehicle], "INFO", "VEHICLE"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
