/*
    EdenRP Purchase Property Function
    Enhanced property purchasing with validation and features
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_propertyId", 0, [0]],
    ["_data", [], [[]]]
];

// Validate property ID
if (_propertyId <= 0) exitWith {
    ["Invalid property ID", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Get property information
private _propertyInfo = [_propertyId] call EDEN_fnc_getPropertyInfo;
if (count _propertyInfo == 0) exitWith {
    ["Property not found", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Check if property is available for purchase
private _currentOwner = _propertyInfo select 1;
if (_currentOwner != "") exitWith {
    ["This property is already owned", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Extract property data
private _propertyPosition = _propertyInfo select 0;
private _propertyPrice = _propertyInfo select 2;
private _propertyType = _propertyInfo select 7;
private _hasGarage = _propertyInfo select 6;
private _garageSize = _propertyInfo select 7;

// Check if player already owns maximum properties
private _ownedProperties = _player getVariable ["EDEN_OwnedProperties", []];
private _maxProperties = _player getVariable ["EDEN_MaxProperties", 3];
if (count _ownedProperties >= _maxProperties) exitWith {
    [format["You can only own %1 properties at a time", _maxProperties], "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Check player level requirement
private _playerLevel = _player getVariable ["EDEN_Level", 1];
private _requiredLevel = switch (_propertyType) do {
    case "apartment": {1};
    case "house": {5};
    case "mansion": {15};
    case "warehouse": {10};
    case "office": {8};
    default {1};
};

if (_playerLevel < _requiredLevel) exitWith {
    [format["You need to be level %1 to purchase this type of property", _requiredLevel], "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Apply discounts
private _finalPrice = _propertyPrice;
private _donatorLevel = _player getVariable ["EDEN_DonatorLevel", 0];
private _discount = _donatorLevel * 0.03; // 3% per donator level
_finalPrice = _finalPrice * (1 - _discount);

// Check for negotiation (if data provided)
if (count _data > 0) then {
    private _negotiatedPrice = _data select 0;
    private _maxDiscount = 0.15; // Maximum 15% discount through negotiation
    private _minPrice = _propertyPrice * (1 - _maxDiscount);
    
    if (_negotiatedPrice >= _minPrice && _negotiatedPrice < _finalPrice) then {
        _finalPrice = _negotiatedPrice;
        ["Negotiation successful!", "success"] remoteExec ["EDEN_fnc_showNotification", _player];
    };
};

_finalPrice = round _finalPrice;

// Check player funds
private _playerCash = _player getVariable ["EDEN_Cash", 0];
private _playerBank = _player getVariable ["EDEN_Bank", 0];
private _totalFunds = _playerCash + _playerBank;

if (_totalFunds < _finalPrice) exitWith {
    [format["You need $%1 to purchase this property", [_finalPrice] call EDEN_fnc_formatMoney], "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Check distance to property
if (_player distance _propertyPosition > 50) exitWith {
    ["You must be near the property to purchase it", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Deduct payment (prefer bank over cash)
if (_playerBank >= _finalPrice) then {
    _player setVariable ["EDEN_Bank", _playerBank - _finalPrice, false];
} else {
    private _bankAmount = _playerBank;
    private _cashAmount = _finalPrice - _bankAmount;
    _player setVariable ["EDEN_Bank", 0, false];
    _player setVariable ["EDEN_Cash", _playerCash - _cashAmount, false];
};

// Update property ownership in database
private _uid = getPlayerUID _player;
private _query = format [
    "EDEN_Houses:updatePropertyOwner:%1:%2:%3",
    _uid,
    _finalPrice,
    _propertyId
];

private _queryId = [_query, 1] call EDEN_fnc_asyncCall;

// Update player's owned properties
_ownedProperties pushBack [_propertyId, _propertyType, time, _finalPrice];
_player setVariable ["EDEN_OwnedProperties", _ownedProperties, false];

// Set property as locked by default
_player setVariable [format["EDEN_PropertyKey_%1", _propertyId], true, false];

// Log transaction
[_uid, "PROPERTY_PURCHASE", -_finalPrice, _totalFunds, _totalFunds - _finalPrice, format["Purchased property #%1", _propertyId]] call EDEN_fnc_logTransaction;

// Create property marker for owner
private _markerName = format ["property_%1_%2", _uid, _propertyId];
[_markerName, _propertyPosition, "House", "ColorGreen"] remoteExec ["EDEN_fnc_createPropertyMarker", _player];

// Notify player
[format["Successfully purchased property for $%1", [_finalPrice] call EDEN_fnc_formatMoney], "success"] remoteExec ["EDEN_fnc_showNotification", _player];
["You can access your property using the interaction menu", "info"] remoteExec ["EDEN_fnc_showNotification", _player];

if (_hasGarage) then {
    [format["This property includes a %1-vehicle garage", _garageSize], "info"] remoteExec ["EDEN_fnc_showNotification", _player];
};

// Update client money display
[_player getVariable ["EDEN_Cash", 0], _player getVariable ["EDEN_Bank", 0]] remoteExec ["EDEN_fnc_updateMoneyDisplay", _player];

// Award achievement for first property
if (count _ownedProperties == 1) then {
    [_player, "FIRST_PROPERTY"] call EDEN_fnc_unlockAchievement;
};

// Award XP
private _xpReward = round (_finalPrice / 1000); // 1 XP per $1000 spent
[_player, _xpReward, "PROPERTY_PURCHASE"] call EDEN_fnc_awardExperience;

// Log property purchase
[format["PROPERTY: %1 purchased property #%2 (%3) for $%4", name _player, _propertyId, _propertyType, _finalPrice], "INFO", "HOUSING"] call EDEN_fnc_systemLogger;

// Set up automatic utility payments
[_player, _propertyId] call EDEN_fnc_setupPropertyUtilities;

// Initialize property storage
[_propertyId, []] call EDEN_fnc_initializePropertyStorage;

true
