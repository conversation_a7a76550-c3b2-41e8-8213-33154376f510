/*
    File: fn_escortPlayer.sqf
    Author: EdenRP Development Team
    
    Description:
    Escorts a restrained player (makes them follow the officer).
    
    Parameters:
    0: OBJECT - Target player to escort
    1: OBJECT - Escorting officer (optional, default: player)
    
    Returns:
    BOOLEAN - True if escort was successful
    
    Example:
    [cursorTarget] call EDEN_fnc_escortPlayer;
    [_target, player] call EDEN_fnc_escortPlayer;
*/

params [
    ["_target", objNull, [obj<PERSON><PERSON>]],
    ["_officer", player, [objNull]]
];

// Validate parameters
if (isNull _target || isNull _officer) exitWith {
    ["[EDEN] fn_escortPlayer: Invalid target or officer"] call EDEN_fnc_systemLogger;
    false
};

// Check if officer is police
if (!(_officer getVariable ["eden_isPolice", false])) exitWith {
    ["Only police officers can escort players!"] call EDEN_fnc_showHint;
    false
};

// Check distance
if (_officer distance _target > 5) exitWith {
    ["Target is too far away!"] call EDEN_fnc_showHint;
    false
};

// Check if target is restrained
if (!(_target getVariable ["eden_isRestrained", false])) exitWith {
    ["Player must be restrained before escorting!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_officer, "player_action", [_target, "escort"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Get current escort status
_isEscorted = _target getVariable ["eden_isEscorted", false];
_currentEscort = _target getVariable ["eden_escortedBy", objNull];

if (_isEscorted && _currentEscort == _officer) then {
    // STOP ESCORTING
    _target setVariable ["eden_isEscorted", false, true];
    _target setVariable ["eden_escortedBy", objNull, true];
    
    // Stop the escort loop
    _target setVariable ["eden_escortActive", false, true];
    
    // Notifications
    [
        "Escort Stopped",
        format ["You are no longer escorting %1", name _target],
        3,
        "info"
    ] remoteExec ["EDEN_fnc_showNotification", _officer];
    
    [
        "Escort Ended",
        format ["Officer %1 is no longer escorting you", name _officer],
        3,
        "info"
    ] remoteExec ["EDEN_fnc_showNotification", _target];
    
    // Log the action
    [format ["[EDEN] Officer %1 stopped escorting %2", name _officer, name _target], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
    
} else if (_isEscorted && _currentEscort != _officer) then {
    // Already being escorted by someone else
    [format ["%1 is already being escorted by %2", name _target, name _currentEscort]] call EDEN_fnc_showHint;
    false
} else {
    // START ESCORTING
    _target setVariable ["eden_isEscorted", true, true];
    _target setVariable ["eden_escortedBy", _officer, true];
    _target setVariable ["eden_escortActive", true, true];
    
    // Start escort loop
    [_target, _officer] spawn {
        params ["_prisoner", "_escort"];
        
        while {
            alive _prisoner && 
            alive _escort && 
            _prisoner getVariable ["eden_escortActive", false] && 
            _prisoner getVariable ["eden_isRestrained", false]
        } do {
            // Check distance - if too far, move prisoner closer
            if (_prisoner distance _escort > 3) then {
                _direction = _escort getDir _prisoner;
                _newPos = _escort getPos [2, _direction];
                _prisoner setPos _newPos;
                
                // Show hint to prisoner
                [
                    "Stay Close",
                    "Stay close to the escorting officer",
                    2
                ] remoteExec ["EDEN_fnc_showHint", _prisoner];
            };
            
            // Check if escort is moving and make prisoner follow
            if (speed _escort > 1) then {
                _prisoner doMove (getPos _escort);
            };
            
            sleep 1;
        };
        
        // Auto-stop escort if conditions are no longer met
        if (_prisoner getVariable ["eden_escortActive", false]) then {
            _prisoner setVariable ["eden_isEscorted", false, true];
            _prisoner setVariable ["eden_escortedBy", objNull, true];
            _prisoner setVariable ["eden_escortActive", false, true];
            
            [
                "Escort Ended",
                "Escort has been automatically ended",
                3,
                "info"
            ] remoteExec ["EDEN_fnc_showNotification", _escort];
        };
    };
    
    // Notifications
    [
        "Escort Started",
        format ["You are now escorting %1", name _target],
        3,
        "success"
    ] remoteExec ["EDEN_fnc_showNotification", _officer];
    
    [
        "Being Escorted",
        format ["Officer %1 is now escorting you. Stay close!", name _officer],
        5,
        "warning"
    ] remoteExec ["EDEN_fnc_showNotification", _target];
    
    // Add escort instructions
    [
        "Escort Instructions",
        "The prisoner will automatically follow you. Keep them close and move slowly.",
        8,
        "info"
    ] remoteExec ["EDEN_fnc_showNotification", _officer];
    
    // Log the action
    [format ["[EDEN] Officer %1 started escorting %2", name _officer, name _target], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
};

// Update police statistics
if (_isEscorted && _currentEscort == _officer) then {
    _escortsEnded = _officer getVariable ["eden_escortsEnded", 0];
    _officer setVariable ["eden_escortsEnded", (_escortsEnded + 1), true];
} else if (!_isEscorted) then {
    _escortsStarted = _officer getVariable ["eden_escortsStarted", 0];
    _officer setVariable ["eden_escortsStarted", (_escortsStarted + 1), true];
};

// Save player data
[_target] call EDEN_fnc_savePlayerData;
[_officer] call EDEN_fnc_savePlayerData;

// Return success
true
