/*
    File: fn_drugTestSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages drug testing system for police.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_drugTests", 0, true];
        true
    };
    case "administerTest": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty!"] call EDEN_fnc_showHint;
            false
        };
        
        if (isNull _target) exitWith {
            ["No target specified!"] call EDEN_fnc_showHint;
            false
        };
        
        _tests = _player getVariable ["eden_drugTests", 0];
        _player setVariable ["eden_drugTests", (_tests + 1), true];
        
        ["Administering drug test..."] call EDEN_fnc_showHint;
        ["You are being given a drug test"] remoteExec ["EDEN_fnc_showHint", _target];
        
        sleep 5;
        
        _drugLevel = _target getVariable ["eden_drugLevel", 0];
        _result = random 1;
        
        if (_drugLevel > 0 || _result > 0.9) then {
            ["POSITIVE: Drugs detected in system!"] call EDEN_fnc_showHint;
            [_player, "issueTicket", _target, "Drug Possession", 2000] call EDEN_fnc_trafficSystem;
        } else {
            ["NEGATIVE: No drugs detected"] call EDEN_fnc_showHint;
        };
        
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
