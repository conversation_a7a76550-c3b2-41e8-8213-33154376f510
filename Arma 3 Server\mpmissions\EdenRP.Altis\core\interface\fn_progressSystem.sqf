/*
    File: fn_progressSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages progress bar system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_progressActive", false, true];
        true
    };
    case "startProgress": {
        params ["", "", ["_duration", 5, [0]], ["_text", "Processing...", [""]]];
        
        if (_player getVariable ["eden_progressActive", false]) exitWith { false };
        
        _player setVariable ["eden_progressActive", true, true];
        
        _startTime = time;
        _endTime = time + _duration;
        
        [] spawn {
            while {time < _endTime && (_player getVariable ["eden_progressActive", false])} do {
                _progress = ((time - _startTime) / _duration) * 100;
                _progressText = format["%1 [%2%%]", _text, floor(_progress)];
                
                [_progressText, 0.5, 0.5, 1, 0] spawn BIS_fnc_dynamicText;
                sleep 0.1;
            };
            
            _player setVariable ["eden_progressActive", false, true];
        };
        
        true
    };
    case "cancelProgress": {
        _player setVariable ["eden_progressActive", false, true];
        ["Progress cancelled"] call EDEN_fnc_showHint;
        true
    };
    case "isActive": {
        _player getVariable ["eden_progressActive", false]
    };
    default { false };
};
