/*
    EdenRP Player Data Request
    Enhanced player data loading system
    
    This function handles secure player data requests
    with validation and error handling
*/

params [
    ["_uid", "", [""]],
    ["_name", "", [""]]
];

// Validate parameters
if (_uid == "" || _name == "") exitWith {
    ["Invalid parameters provided to requestPlayerData", "ERROR", "SESSION"] call EDEN_fnc_systemLogger;
    false
};

// Only run on server
if (!hasInterface) exitWith {
    ["requestPlayerData called on client", "ERROR", "SESSION"] call EDEN_fnc_systemLogger;
    false
};

// Check if server is ready
if (!EDEN_ServerReady) exitWith {
    ["Server not ready for player data requests", "WARNING", "SESSION"] call EDEN_fnc_systemLogger;
    // Retry in 5 seconds
    [_uid, _name] spawn {
        params ["_uid", "_name"];
        sleep 5;
        [_uid, _name] call EDEN_fnc_requestPlayerData;
    };
    false
};

// Check database connection
if (!EDEN_DatabaseConnected) exitWith {
    ["Database not connected", "ERROR", "SESSION"] call EDEN_fnc_systemLogger;
    ["Database connection error. Please try again later.", "error"] remoteExec ["EDEN_fnc_showNotification", _uid];
    false
};

// Log player data request
[format["Player data requested for %1 (%2)", _name, _uid], "INFO", "SESSION"] call EDEN_fnc_systemLogger;

// Request player data from database
private _query = format ["EDEN_Players:getPlayerData:%1", _uid];
private _queryId = [_query, 2] call EDEN_fnc_asyncCall;

// Store query info for callback
if (isNil "EDEN_PendingQueries") then {
    EDEN_PendingQueries = [];
};

EDEN_PendingQueries pushBack [
    _queryId,
    "getPlayerData",
    [_uid, _name],
    time
];

[format["Player data query submitted for %1 (Query ID: %2)", _name, _queryId], "DEBUG", "SESSION"] call EDEN_fnc_systemLogger;

true
