/*
    File: fn_requestLawyer.sqf
    Author: EdenRP Development Team
    
    Description:
    Requests a lawyer for legal assistance.
    
    Parameters:
    0: OBJECT - Player requesting lawyer (optional, default: player)
    
    Returns:
    BOOLEAN - True if lawyer request was sent successfully
*/

params [["_player", player, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

// Check if player is arrested or has legal issues
_isArrested = _player getVariable ["eden_isArrested", false];
_wantedLevel = _player getVariable ["eden_wantedLevel", 0];
_unpaidTickets = 0;

_playerTickets = _player getVariable ["eden_tickets", []];
{
    if (!(_x select 6)) then { // If not paid
        _unpaidTickets = _unpaidTickets + 1;
    };
} forEach _playerTickets;

if (!_isArrested && _wantedLevel == 0 && _unpaidTickets == 0) exitWith {
    ["You don't need legal assistance right now!"] call EDEN_fnc_showHint;
    false
};

// Check if already requested lawyer recently
_lastRequest = _player getVariable ["eden_lastLawyerRequest", 0];
if ((time - _lastRequest) < 300) exitWith { // 5 minute cooldown
    ["You can only request a lawyer once every 5 minutes!"] call EDEN_fnc_showHint;
    false
};

_player setVariable ["eden_lastLawyerRequest", time, true];

// Find available lawyers (players with lawyer role)
_availableLawyers = [];
{
    if (_x getVariable ["eden_isLawyer", false] && alive _x && _x != _player) then {
        _availableLawyers pushBack _x;
    };
} forEach allPlayers;

if (count _availableLawyers == 0) exitWith {
    ["No lawyers are currently available!"] call EDEN_fnc_showHint;
    false
};

// Send request to all available lawyers
_requestMessage = format [
    "LEGAL ASSISTANCE REQUEST\nClient: %1\nLocation: %2\nCharges: %3\nWanted Level: %4\nUnpaid Tickets: %5",
    name _player,
    mapGridPosition _player,
    if (_isArrested) then {"Arrested"} else {"Free"},
    _wantedLevel,
    _unpaidTickets
];

{
    [
        "Lawyer Request",
        _requestMessage,
        15,
        "info"
    ] remoteExec ["EDEN_fnc_showNotification", _x];
} forEach _availableLawyers;

// Add to lawyer request queue
_lawyerRequests = missionNamespace getVariable ["eden_lawyerRequests", []];
_lawyerRequests pushBack [time, _player, _requestMessage];
missionNamespace setVariable ["eden_lawyerRequests", _lawyerRequests, true];

[format ["Lawyer request sent to %1 available lawyers", count _availableLawyers]] call EDEN_fnc_showHint;

// Auto-remove request after 30 minutes
[_player] spawn {
    params ["_client"];
    sleep 1800; // 30 minutes
    
    _requests = missionNamespace getVariable ["eden_lawyerRequests", []];
    _newRequests = [];
    {
        if ((_x select 1) != _client) then {
            _newRequests pushBack _x;
        };
    } forEach _requests;
    missionNamespace setVariable ["eden_lawyerRequests", _newRequests, true];
};

// Log lawyer request
[format ["[EDEN] Player %1 requested lawyer assistance", name _player], "INFO", "LEGAL"] call EDEN_fnc_systemLogger;

true
