/*
    EdenRP Show Notification
    Enhanced notification system for EdenRP
    
    This function displays notifications to players
    with different types and styling
*/

params [
    ["_message", "", [""]],
    ["_type", "info", [""]],
    ["_duration", 5, [0]]
];

// Only run on clients
if (!hasInterface) exitWith {
    ["showNotification called on server", "ERROR", "UI"] call EDEN_fnc_systemLogger;
    false
};

// Validate parameters
if (_message == "") exitWith {
    ["Empty message provided to showNotification", "ERROR", "UI"] call EDEN_fnc_systemLogger;
    false
};

// Check if notifications are enabled
private _notificationsEnabled = true;
private _settings = player getVariable ["EDEN_Settings", []];
{
    if ((_x select 0) == "Notifications_Enabled") exitWith {
        _notificationsEnabled = _x select 1;
    };
} forEach _settings;

if (!_notificationsEnabled) exitWith {
    // Player has notifications disabled
    true
};

// Clamp duration
_duration = [_duration, 1, 30] call EDEN_fnc_clampValue;

// Determine notification class based on type
private _notificationClass = "EdenRP_Default";
private _soundEffect = "";

switch (toLower _type) do {
    case "success": {
        _notificationClass = "EdenRP_Success";
        _soundEffect = "EdenRP_Success";
    };
    case "error": {
        _notificationClass = "EdenRP_Error";
        _soundEffect = "EdenRP_Error";
    };
    case "warning": {
        _notificationClass = "EdenRP_Error";
        _soundEffect = "EdenRP_Error";
    };
    case "police": {
        _notificationClass = "EdenRP_Police";
        _soundEffect = "EdenRP_Notification";
    };
    case "medical": {
        _notificationClass = "EdenRP_Medical";
        _soundEffect = "EdenRP_Notification";
    };
    default {
        _notificationClass = "EdenRP_Default";
        _soundEffect = "EdenRP_Notification";
    };
};

// Show notification
[_notificationClass, [_message]] call BIS_fnc_showNotification;

// Play sound effect if enabled
private _soundEnabled = true;
{
    if ((_x select 0) == "Sound_Enabled") exitWith {
        _soundEnabled = _x select 1;
    };
} forEach _settings;

if (_soundEnabled && _soundEffect != "") then {
    playSound _soundEffect;
};

// Also show as hint for important messages
if (toLower _type in ["error", "warning", "police", "medical"]) then {
    hint _message;
};

// Log notification for debugging
if (EDEN_Debug) then {
    [format["Notification shown: [%1] %2", _type, _message], "DEBUG", "UI"] call EDEN_fnc_systemLogger;
};

true
