=====================================================================
== C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\arma3server_x64.exe
== arma3server_x64.exe  -serverMod=@extDB3 -config=server.cfg -cfg=basic.cfg -profiles=profiles -name=server -world=Altis -autoInit -loadMissionToMemory -enableHT -noSound

Original output filename: Arma3Retail_Server_x64
Exe timestamp: 2025/06/24 13:00:10
Current time:  2025/07/29 12:10:07

Type: Public
Build: Stable
Version: 2.20.152984

Allocator: C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\Dll\tbb4malloc_bi_x64.dll [2017.0.0.0] [2017.0.0.0]
PhysMem: 32 GiB, VirtMem : 131072 GiB, AvailPhys : 20 GiB, AvailVirt : 131068 GiB, AvailPage : 21 GiB, PageSize : 4.0 KiB/2.0 MiB/HasLockMemory, CPUCount : 16
=====================================================================

12:10:07 Unable to locate a running instance of Steam. Restarting...
12:10:07 ErrorMessage: Unable to locate a running instance of Steam
12:10:07 Application terminated intentionally
ErrorMessage: Unable to locate a running instance of Steam
=======================================================
-------------------------------------------------------
Exception code: 0000DEAD  at F3607F9A
Version 2.20.152984
Fault time: 2025/07/29 12:10:09
Fault address:  F3607F9A 01:000C6F9All C:\Windows\System32\KERNELBASE.dll
file:     
world:    
Prev. code bytes: 09 3D 0D 00 48 8D 4C 24 20 48 FF 15 86 B2 1C 00
Fault code bytes: 0F 1F 44 00 00 48 8B 8C 24 C0 00 00 00 48 33 CC
=======================================================
note: Minidump has been generated into the file profiles\arma3server_x64_2025-07-29_12-10-07.mdmp
