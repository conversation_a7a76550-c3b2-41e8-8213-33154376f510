/*
    File: fn_scrapyardSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages vehicle scrapyard system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_vehicle", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_scrapHistory", [], true];
        _player setVariable ["eden_scrapParts", [], true];
        true
    };
    case "scrapVehicle": {
        if (isNull _vehicle) exitWith { false };
        
        _vehicleType = typeOf _vehicle;
        _condition = 1 - (damage _vehicle);
        
        _baseValue = 1000;
        _scrapValue = floor(_baseValue * _condition * 0.3); // 30% of base value
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _scrapValue), true];
        
        _parts = _player getVariable ["eden_scrapParts", []];
        _newParts = ["engine_parts", "metal_scrap", "electronics"];
        {
            _parts pushBack _x;
        } forEach _newParts;
        _player setVariable ["eden_scrapParts", _parts, true];
        
        _history = _player getVariable ["eden_scrapHistory", []];
        _history pushBack [_vehicleType, _scrapValue, time];
        _player setVariable ["eden_scrapHistory", _history, true];
        
        deleteVehicle _vehicle;
        
        [format ["Vehicle scrapped for $%1 + parts", _scrapValue]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "sellParts": {
        _parts = _player getVariable ["eden_scrapParts", []];
        if (count _parts == 0) exitWith {
            ["No parts to sell"] call EDEN_fnc_showHint;
            false
        };
        
        _partValue = 50;
        _totalValue = (count _parts) * _partValue;
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _totalValue), true];
        
        _player setVariable ["eden_scrapParts", [], true];
        
        [format ["Sold %1 parts for $%2", count _parts, _totalValue]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "buyParts": {
        params ["", "", "", ["_partType", "engine_parts", [""]]];
        
        _partPrices = [
            ["engine_parts", 200],
            ["transmission", 150],
            ["brakes", 100],
            ["tires", 80]
        ];
        
        _price = 100;
        {
            if ((_x select 0) == _partType) then { _price = _x select 1; };
        } forEach _partPrices;
        
        _cash = _player getVariable ["eden_cash", 0];
        if (_cash < _price) exitWith {
            [format ["Not enough money! Need $%1", _price]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _price), true];
        
        _parts = _player getVariable ["eden_scrapParts", []];
        _parts pushBack _partType;
        _player setVariable ["eden_scrapParts", _parts, true];
        
        [format ["Purchased %1 for $%2", _partType, _price]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
