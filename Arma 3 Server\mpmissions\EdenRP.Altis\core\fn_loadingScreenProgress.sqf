/*
    File: fn_loadingScreenProgress.sqf
    Author: EdenRP Development Team
    
    Description:
    Updates loading screen progress
    
    Parameters:
    0: STRING - Loading text
    1: NUMBER - Progress percentage (0-100)
*/

params ["_text", "_progress"];

if (hasInterface) then {
    EDEN_LoadingText = _text;
    EDEN_LoadingProgress = _progress;
    
    // Log progress for debugging
    if (EDEN_Debug) then {
        diag_log format["[EdenRP] Loading: %1 (%2%%)", _text, _progress];
    };
};

true
