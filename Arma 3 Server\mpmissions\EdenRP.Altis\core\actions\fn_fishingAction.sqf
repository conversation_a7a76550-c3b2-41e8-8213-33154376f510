/*
    File: fn_fishingAction.sqf
    Author: EdenRP Development Team
    
    Description:
    Performs fishing action at water locations.
    
    Parameters:
    0: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if fishing was successful
*/

params [["_player", player, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

// Check if near water
_playerPos = getPosATL _player;
if (!surfaceIsWater _playerPos && !surfaceIsWater [_playerPos select 0, _playerPos select 1, 0]) exitWith {
    ["You must be near water to fish!"] call EDEN_fnc_showHint;
    false
};

// Check if player has fishing license
if (!(_player getVariable ["eden_license_fishing", false])) exitWith {
    ["You need a fishing license to fish!"] call EDEN_fnc_showHint;
    false
};

// Check if player has fishing rod
_virtualItems = _player getVariable ["eden_virtualItems", []];
_hasFishingRod = false;
_hasBait = false;
_baitIndex = -1;

{
    if ((_x select 0) == "fishing_rod") then {
        _hasFishingRod = true;
    };
    if ((_x select 0) == "bait" && (_x select 1) > 0) then {
        _hasBait = true;
        _baitIndex = _forEachIndex;
    };
} forEach _virtualItems;

if (!_hasFishingRod) exitWith {
    ["You need a fishing rod to fish!"] call EDEN_fnc_showHint;
    false
};

if (!_hasBait) exitWith {
    ["You need bait to fish!"] call EDEN_fnc_showHint;
    false
};

// Check inventory space
_currentWeight = _player getVariable ["eden_currentWeight", 0];
_maxWeight = _player getVariable ["eden_maxWeight", 50];
_fishWeight = 2; // Each fish weighs 2kg

if ((_currentWeight + _fishWeight) > _maxWeight) exitWith {
    ["Inventory is full!"] call EDEN_fnc_showHint;
    false
};

// Start fishing
[_player, "Acts_carFixingWheel"] remoteExec ["switchMove"];
["Fishing... please wait"] call EDEN_fnc_showHint;

// Fishing takes time (10-20 seconds)
_fishingTime = 10 + random 10;
sleep _fishingTime;

[_player, ""] remoteExec ["switchMove"];

// Consume bait
_baitQuantity = (_virtualItems select _baitIndex) select 1;
if (_baitQuantity <= 1) then {
    _virtualItems deleteAt _baitIndex;
} else {
    (_virtualItems select _baitIndex) set [1, (_baitQuantity - 1)];
};

// Determine fishing success (70% chance)
_success = (random 100) < 70;

if (_success) then {
    // Determine fish type and quantity
    _fishTypes = [
        ["fish_small", 80, 1], // [type, chance, weight_multiplier]
        ["fish_medium", 15, 2],
        ["fish_large", 4, 3],
        ["fish_rare", 1, 5]
    ];
    
    _caughtFish = "fish_small"; // Default
    _fishMultiplier = 1;
    
    _roll = random 100;
    _cumulative = 0;
    {
        _cumulative = _cumulative + (_x select 1);
        if (_roll <= _cumulative) then {
            _caughtFish = _x select 0;
            _fishMultiplier = _x select 2;
            break;
        };
    } forEach _fishTypes;
    
    // Add fish to inventory
    _found = false;
    {
        if ((_x select 0) == _caughtFish) then {
            _x set [1, ((_x select 1) + 1)];
            _found = true;
        };
    } forEach _virtualItems;
    
    if (!_found) then {
        _virtualItems pushBack [_caughtFish, 1];
    };
    
    _player setVariable ["eden_virtualItems", _virtualItems, true];
    _player setVariable ["eden_currentWeight", (_currentWeight + (_fishWeight * _fishMultiplier)), true];
    
    // Add experience
    _expGained = 15 * _fishMultiplier;
    _currentExp = _player getVariable ["eden_experience", 0];
    _player setVariable ["eden_experience", (_currentExp + _expGained), true];
    
    // Update job stats if fisherman
    if ((_player getVariable ["eden_currentJob", ""]) == "fisherman") then {
        _fishCaught = _player getVariable ["eden_fishCaught", 0];
        _player setVariable ["eden_fishCaught", (_fishCaught + 1), true];
    };
    
    [format ["Caught a %1! (+%2 XP)", _caughtFish, _expGained]] call EDEN_fnc_showHint;
    
} else {
    _player setVariable ["eden_virtualItems", _virtualItems, true];
    ["No fish caught this time. Try again!"] call EDEN_fnc_showHint;
};

// Log fishing
[format ["[EDEN] Player %1 went fishing - %2", name _player, if (_success) then {"successful"} else {"unsuccessful"}], "DEBUG", "FISHING"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

_success
