/*
    File: fn_transferMoney.sqf
    Author: EdenRP Development Team
    
    Description:
    Transfers money between players.
    
    Parameters:
    0: OBJECT - Target player
    1: NUMBER - Amount to transfer
    2: OBJECT - Source player (optional, default: player)
    
    Returns:
    BOOLEAN - True if transfer was successful
*/

params [
    ["_target", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_amount", 0, [0]],
    ["_source", player, [obj<PERSON><PERSON>]]
];

if (isNull _target || isNull _source || _amount <= 0) exitWith { false };

if (_target == _source) exitWith {
    ["You cannot transfer money to yourself!"] call EDEN_fnc_showHint;
    false
};

if (!([_source, "money_transfer", [_amount]] call EDEN_fnc_securityValidator)) exitWith { false };

_sourceMoney = _source getVariable ["eden_cash", 0];
if (_sourceMoney < _amount) exitWith {
    ["Insufficient funds!"] call EDEN_fnc_showHint;
    false
};

if (_source distance _target > 5) exitWith {
    ["Target is too far away!"] call EDEN_fnc_showHint;
    false
};

// Transfer money
_source setVariable ["eden_cash", (_sourceMoney - _amount), true];
_targetMoney = _target getVariable ["eden_cash", 0];
_target setVariable ["eden_cash", (_targetMoney + _amount), true];

// Notifications
[format ["Transferred $%1 to %2", _amount, name _target]] remoteExec ["EDEN_fnc_showHint", _source];
[format ["Received $%1 from %2", _amount, name _source]] remoteExec ["EDEN_fnc_showHint", _target];

// Log transaction
[format ["[EDEN] Money transfer: %1 sent $%2 to %3", name _source, _amount, name _target], "INFO", "ECONOMY"] call EDEN_fnc_systemLogger;

[_source] call EDEN_fnc_savePlayerData;
[_target] call EDEN_fnc_savePlayerData;

true
