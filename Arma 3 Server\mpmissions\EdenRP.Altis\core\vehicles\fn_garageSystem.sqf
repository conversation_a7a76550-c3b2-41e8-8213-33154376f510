/*
    File: fn_garageSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages vehicle garage system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_vehicle", objNull, [objNull]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_ownedVehicles", [], true];
        _player setVariable ["eden_garageSlots", 3, true];
        true
    };
    case "storeVehicle": {
        if (isNull _vehicle) exitWith { false };
        
        _owned = _player getVariable ["eden_ownedVehicles", []];
        _slots = _player getVariable ["eden_garageSlots", 3];
        
        if (count _owned >= _slots) exitWith {
            ["Garage is full!"] call EDEN_fnc_showHint;
            false
        };
        
        _vehicleData = [typeOf _vehicle, getPos _vehicle, getDir _vehicle, fuel _vehicle, damage _vehicle, time];
        _owned pushBack _vehicleData;
        _player setVariable ["eden_ownedVehicles", _owned, true];
        
        deleteVehicle _vehicle;
        
        ["Vehicle stored in garage"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "retrieveVehicle": {
        params ["", "", "", ["_index", 0, [0]]];
        
        _owned = _player getVariable ["eden_ownedVehicles", []];
        if (_index >= count _owned) exitWith {
            ["Invalid vehicle selection"] call EDEN_fnc_showHint;
            false
        };
        
        _vehicleData = _owned select _index;
        _pos = _player getPos [10, getDir _player];
        
        _newVehicle = createVehicle [(_vehicleData select 0), _pos, [], 0, "NONE"];
        _newVehicle setDir (_vehicleData select 2);
        _newVehicle setFuel (_vehicleData select 3);
        _newVehicle setDamage (_vehicleData select 4);
        
        _owned deleteAt _index;
        _player setVariable ["eden_ownedVehicles", _owned, true];
        
        ["Vehicle retrieved from garage"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
