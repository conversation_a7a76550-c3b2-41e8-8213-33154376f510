/*
    EdenRP Load Player Data
    Enhanced player data loading with validation
    
    This function processes player data from database
    and applies it to the player with security checks
*/

params [
    ["_uid", "", [""]],
    ["_data", [], [[]]]
];

// Validate parameters
if (_uid == "" || count _data == 0) exitWith {
    ["Invalid parameters provided to loadPlayerData", "ERROR", "SESSION"] call EDEN_fnc_systemLogger;
    false
};

// Find player object
private _player = objNull;
{
    if (getPlayerUID _x == _uid) exitWith {
        _player = _x;
    };
} forEach allPlayers;

if (isNull _player) exitWith {
    [format["Player with UID %1 not found", _uid], "ERROR", "SESSION"] call EDEN_fnc_systemLogger;
    false
};

// Log data loading
[format["Loading data for player %1 (%2)", name _player, _uid], "INFO", "SESSION"] call EDEN_fnc_systemLogger;

// Parse database result
if (count _data == 0) then {
    // New player - create default data
    [format["Creating new player data for %1", name _player], "INFO", "SESSION"] call EDEN_fnc_systemLogger;
    [_uid, name _player] call EDEN_fnc_createNewPlayer;
} else {
    // Existing player - load data
    private _playerData = _data select 0;
    
    // Validate data integrity
    if (count _playerData < 10) exitWith {
        [format["Corrupted player data for %1", name _player], "ERROR", "SESSION"] call EDEN_fnc_systemLogger;
        ["Your player data appears to be corrupted. Please contact an administrator.", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
        false
    };
    
    // Extract data with bounds checking
    private _cash = if (count _playerData > 3) then {_playerData select 3} else {5000};
    private _bank = if (count _playerData > 4) then {_playerData select 4} else {15000};
    private _experience = if (count _playerData > 5) then {_playerData select 5} else {0};
    private _level = if (count _playerData > 6) then {_playerData select 6} else {1};
    private _reputation = if (count _playerData > 7) then {_playerData select 7} else {0};
    private _playtime = if (count _playerData > 8) then {_playerData select 8} else {0};
    private _copLevel = if (count _playerData > 12) then {_playerData select 12} else {0};
    private _medicLevel = if (count _playerData > 13) then {_playerData select 13} else {0};
    private _adminLevel = if (count _playerData > 14) then {_playerData select 14} else {0};
    private _donatorLevel = if (count _playerData > 15) then {_playerData select 15} else {0};
    
    // Validate numeric values
    _cash = [_cash, 0, *********] call EDEN_fnc_clampValue;
    _bank = [_bank, 0, *********] call EDEN_fnc_clampValue;
    _experience = [_experience, 0, *********] call EDEN_fnc_clampValue;
    _level = [_level, 1, 100] call EDEN_fnc_clampValue;
    _reputation = [_reputation, -1000, 1000] call EDEN_fnc_clampValue;
    _playtime = [_playtime, 0, 999999] call EDEN_fnc_clampValue;
    _copLevel = [_copLevel, 0, 10] call EDEN_fnc_clampValue;
    _medicLevel = [_medicLevel, 0, 7] call EDEN_fnc_clampValue;
    _adminLevel = [_adminLevel, 0, 5] call EDEN_fnc_clampValue;
    _donatorLevel = [_donatorLevel, 0, 5] call EDEN_fnc_clampValue;
    
    // Apply basic data to player
    _player setVariable ["EDEN_Cash", _cash, false];
    _player setVariable ["EDEN_Bank", _bank, false];
    _player setVariable ["EDEN_XP", _experience, false];
    _player setVariable ["EDEN_Level", _level, false];
    _player setVariable ["EDEN_Reputation", _reputation, false];
    _player setVariable ["EDEN_PlayTime", _playtime, false];
    _player setVariable ["EDEN_CopLevel", _copLevel, false];
    _player setVariable ["EDEN_MedicLevel", _medicLevel, false];
    _player setVariable ["EDEN_AdminLevel", _adminLevel, false];
    _player setVariable ["EDEN_DonatorLevel", _donatorLevel, false];
    
    // Load licenses
    private _copLicenses = if (count _playerData > 16) then {
        call compile (_playerData select 16)
    } else {[]};
    private _civLicenses = if (count _playerData > 17) then {
        call compile (_playerData select 17)
    } else {[]};
    private _medLicenses = if (count _playerData > 18) then {
        call compile (_playerData select 18)
    } else {[]};
    
    _player setVariable ["EDEN_CopLicenses", _copLicenses, false];
    _player setVariable ["EDEN_CivLicenses", _civLicenses, false];
    _player setVariable ["EDEN_MedLicenses", _medLicenses, false];
    
    // Load gear
    private _copGear = if (count _playerData > 19) then {
        call compile (_playerData select 19)
    } else {[]};
    private _medGear = if (count _playerData > 20) then {
        call compile (_playerData select 20)
    } else {[]};
    private _civGear = if (count _playerData > 21) then {
        call compile (_playerData select 21)
    } else {[]};
    
    _player setVariable ["EDEN_CopGear", _copGear, false];
    _player setVariable ["EDEN_MedGear", _medGear, false];
    _player setVariable ["EDEN_CivGear", _civGear, false];
    
    // Load inventory
    private _virtualInventory = if (count _playerData > 22) then {
        call compile (_playerData select 22)
    } else {[]};
    private _physicalInventory = if (count _playerData > 23) then {
        call compile (_playerData select 23)
    } else {[]};
    
    _player setVariable ["EDEN_VirtualInventory", _virtualInventory, false];
    _player setVariable ["EDEN_PhysicalInventory", _physicalInventory, false];
    
    // Load skills and achievements
    private _skills = if (count _playerData > 24) then {
        call compile (_playerData select 24)
    } else {[]};
    private _achievements = if (count _playerData > 25) then {
        call compile (_playerData select 25)
    } else {[]};
    
    _player setVariable ["EDEN_Skills", _skills, false];
    _player setVariable ["EDEN_Achievements", _achievements, false];
    
    // Load settings
    private _settings = if (count _playerData > 26) then {
        call compile (_playerData select 26)
    } else {[
        ["HUD_Enabled", true],
        ["Notifications_Enabled", true],
        ["Sound_Enabled", true],
        ["Music_Enabled", true]
    ]};
    
    _player setVariable ["EDEN_Settings", _settings, false];
    
    // Load criminal status
    private _arrested = if (count _playerData > 27) then {_playerData select 27} else {0};
    private _jailTime = if (count _playerData > 28) then {_playerData select 28} else {0};
    private _wantedLevel = if (count _playerData > 29) then {_playerData select 29} else {0};
    private _bounty = if (count _playerData > 30) then {_playerData select 30} else {0};
    
    _player setVariable ["EDEN_IsArrested", (_arrested == 1), true];
    _player setVariable ["EDEN_JailTime", _jailTime, false];
    _player setVariable ["EDEN_WantedLevel", _wantedLevel, true];
    _player setVariable ["EDEN_Bounty", _bounty, true];
    
    // Load gang data
    private _gangId = if (count _playerData > 31) then {_playerData select 31} else {-1};
    private _gangRank = if (count _playerData > 32) then {_playerData select 32} else {0};
    
    _player setVariable ["EDEN_GangID", _gangId, false];
    _player setVariable ["EDEN_GangRank", _gangRank, false];
    
    // Load communication data
    private _phoneNumber = if (count _playerData > 33) then {_playerData select 33} else {""};
    private _contacts = if (count _playerData > 34) then {
        call compile (_playerData select 34)
    } else {[]};
    private _messages = if (count _playerData > 35) then {
        call compile (_playerData select 35)
    } else {[]};
    
    _player setVariable ["EDEN_PhoneNumber", _phoneNumber, false];
    _player setVariable ["EDEN_Contacts", _contacts, false];
    _player setVariable ["EDEN_Messages", _messages, false];
    
    // Update login statistics
    _player setVariable ["EDEN_TotalLogins", (_player getVariable ["EDEN_TotalLogins", 0]) + 1, false];
    
    [format["Player data loaded successfully for %1", name _player], "INFO", "SESSION"] call EDEN_fnc_systemLogger;
};

// Load player vehicles
[_uid] call EDEN_fnc_loadPlayerVehicles;

// Load player houses
[_uid] call EDEN_fnc_loadPlayerHouses;

// Load gang data if applicable
private _gangId = _player getVariable ["EDEN_GangID", -1];
if (_gangId > 0) then {
    [_uid, _gangId] call EDEN_fnc_loadGangData;
};

// Apply faction-specific data
switch (playerSide) do {
    case west: {
        [_player] call EDEN_fnc_loadPoliceData;
    };
    case independent: {
        [_player] call EDEN_fnc_loadMedicalData;
    };
    case civilian: {
        [_player] call EDEN_fnc_loadCivilianData;
    };
};

// Notify client that data is loaded
["Player data loaded successfully!", "success"] remoteExec ["EDEN_fnc_showNotification", _player];
[true] remoteExec ["EDEN_fnc_dataLoadComplete", _player];

// Update player count
[] call EDEN_fnc_updatePlayerCount;

true
