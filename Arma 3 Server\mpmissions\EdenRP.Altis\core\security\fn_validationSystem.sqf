/*
    File: fn_validationSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages validation system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "validate", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "validate": {
        params ["", "", ["_actionType", "", [""]], ["_parameters", [], [[]]]];
        
        if (_actionType == "") exitWith { false };
        
        _isValid = switch (_actionType) do {
            case "money_transfer": {
                [_player, "validateMoneyTransfer", _parameters] call EDEN_fnc_validationSystem
            };
            case "item_transfer": {
                [_player, "validateItemTransfer", _parameters] call EDEN_fnc_validationSystem
            };
            case "job_action": {
                [_player, "validateJobAction", _parameters] call EDEN_fnc_validationSystem
            };
            case "vehicle_action": {
                [_player, "validateVehicleAction", _parameters] call EDEN_fnc_validationSystem
            };
            default { false };
        };
        
        _isValid
    };
    case "validateMoneyTransfer": {
        params ["", "", ["_params", [], [[]]]];
        
        if (count _params < 2) exitWith { false };
        
        _amount = _params select 0;
        _target = _params select 1;
        
        // Check if amount is valid
        if (_amount <= 0 || _amount > 1000000) exitWith { false };
        
        // Check if player has enough money
        _playerCash = _player getVariable ["eden_cash", 0];
        if (_playerCash < _amount) exitWith { false };
        
        // Check if target is valid
        if (isNull _target || _target == _player) exitWith { false };
        
        // Check distance
        if (_player distance _target > 10) exitWith { false };
        
        true
    };
    case "validateItemTransfer": {
        params ["", "", ["_params", [], [[]]]];
        
        if (count _params < 3) exitWith { false };
        
        _item = _params select 0;
        _quantity = _params select 1;
        _target = _params select 2;
        
        // Check if item exists in player inventory
        _inventory = _player getVariable ["eden_virtualItems", []];
        _hasItem = false;
        
        {
            if ((_x select 0) == _item && (_x select 1) >= _quantity) then {
                _hasItem = true;
            };
        } forEach _inventory;
        
        if (!_hasItem) exitWith { false };
        
        // Check if target is valid
        if (isNull _target || _target == _player) exitWith { false };
        
        // Check distance
        if (_player distance _target > 5) exitWith { false };
        
        true
    };
    case "validateJobAction": {
        params ["", "", ["_params", [], [[]]]];
        
        if (count _params < 1) exitWith { false };
        
        _jobAction = _params select 0;
        _playerJob = _player getVariable ["eden_job", "civilian"];
        
        _validActions = switch (_playerJob) do {
            case "police": { ["arrest", "ticket", "search", "impound"] };
            case "ems": { ["revive", "heal", "transport"] };
            case "mechanic": { ["repair", "refuel", "tow"] };
            default { ["basic"] };
        };
        
        _jobAction in _validActions
    };
    case "validateVehicleAction": {
        params ["", "", ["_params", [], [[]]]];
        
        if (count _params < 2) exitWith { false };
        
        _vehicle = _params select 0;
        _actionType = _params select 1;
        
        // Check if vehicle exists
        if (isNull _vehicle) exitWith { false };
        
        // Check distance
        if (_player distance _vehicle > 10) exitWith { false };
        
        // Check ownership for certain actions
        if (_actionType in ["lock", "unlock", "sell"]) then {
            _owner = _vehicle getVariable ["eden_owner", ""];
            if (_owner != getPlayerUID _player) exitWith { false };
        };
        
        true
    };
    case "sanitizeInput": {
        params ["", "", ["_input", "", [""]]];
        
        // Remove potentially dangerous characters
        _sanitized = _input;
        _sanitized = [_sanitized, "<", ""] call BIS_fnc_stringReplace;
        _sanitized = [_sanitized, ">", ""] call BIS_fnc_stringReplace;
        _sanitized = [_sanitized, "'", ""] call BIS_fnc_stringReplace;
        _sanitized = [_sanitized, """", ""] call BIS_fnc_stringReplace;
        _sanitized = [_sanitized, ";", ""] call BIS_fnc_stringReplace;
        
        _sanitized
    };
    default { false };
};
