@echo off
echo Testing EdenRP Server Setup...
echo.

echo 1. Checking extDB3 files...
if exist "extDB3_x64.dll" (
    echo ✓ extDB3_x64.dll found
) else (
    echo ✗ extDB3_x64.dll missing
)

if exist "tbbmalloc_x64.dll" (
    echo ✓ tbbmalloc_x64.dll found
) else (
    echo ✗ tbbmalloc_x64.dll missing
)

if exist "extdb3-conf.ini" (
    echo ✓ extdb3-conf.ini found
) else (
    echo ✗ extdb3-conf.ini missing
)

echo.
echo 2. Checking EdenRP mission files...
if exist "mpmissions\EdenRP.Altis\description.ext" (
    echo ✓ EdenRP mission found
) else (
    echo ✗ EdenRP mission missing
)

if exist "mpmissions\EdenRP.Altis\Functions.h" (
    echo ✓ Functions.h found
) else (
    echo ✗ Functions.h missing
)

if exist "mpmissions\EdenRP.Altis\core\fn_initializeLogging.sqf" (
    echo ✓ fn_initializeLogging.sqf found
) else (
    echo ✗ fn_initializeLogging.sqf missing
)

if exist "mpmissions\EdenRP.Altis\core\fn_antiCheatMonitor.sqf" (
    echo ✓ fn_antiCheatMonitor.sqf found
) else (
    echo ✗ fn_antiCheatMonitor.sqf missing
)

echo.
echo 3. Testing MySQL connection...
echo This requires HeidiSQL or MySQL client to be running
echo Please verify database connection manually

echo.
echo 4. Ready to start server!
echo Run start_server.bat to launch EdenRP
echo.
pause
