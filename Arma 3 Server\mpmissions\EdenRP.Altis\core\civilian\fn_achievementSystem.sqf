/*
    File: fn_achievementSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages achievement system for civilians.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_achievementId", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_achievements", [], true];
        _player setVariable ["eden_achievementProgress", [], true];
        _player setVariable ["eden_achievementPoints", 0, true];
        true
    };
    case "unlockAchievement": {
        _achievements = _player getVariable ["eden_achievements", []];
        
        if (_achievementId in _achievements) exitWith {
            ["Achievement already unlocked!"] call EDEN_fnc_showHint;
            false
        };
        
        _achievementData = [_player, "getAchievementData", _achievementId] call EDEN_fnc_achievementSystem;
        if (count _achievementData == 0) exitWith {
            ["Achievement not found!"] call EDEN_fnc_showHint;
            false
        };
        
        _name = _achievementData select 1;
        _points = _achievementData select 3;
        _reward = _achievementData select 4;
        
        _achievements pushBack _achievementId;
        _player setVariable ["eden_achievements", _achievements, true];
        
        _currentPoints = _player getVariable ["eden_achievementPoints", 0];
        _player setVariable ["eden_achievementPoints", (_currentPoints + _points), true];
        
        // Give reward
        switch (_reward select 0) do {
            case "money": {
                _amount = _reward select 1;
                _cash = _player getVariable ["eden_cash", 0];
                _player setVariable ["eden_cash", (_cash + _amount), true];
            };
            case "experience": {
                _amount = _reward select 1;
                _exp = _player getVariable ["eden_experience", 0];
                _player setVariable ["eden_experience", (_exp + _amount), true];
            };
            case "item": {
                _item = _reward select 1;
                _quantity = _reward select 2;
                _virtualItems = _player getVariable ["eden_virtualItems", []];
                _virtualItems pushBack [_item, _quantity];
                _player setVariable ["eden_virtualItems", _virtualItems, true];
            };
        };
        
        [format ["Achievement Unlocked: %1! (+%2 points)", _name, _points]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "checkAchievement": {
        _achievements = _player getVariable ["eden_achievements", []];
        (_achievementId in _achievements)
    };
    case "updateProgress": {
        params ["", "", "", ["_amount", 1, [0]]];
        
        _progress = _player getVariable ["eden_achievementProgress", []];
        _found = false;
        
        {
            if ((_x select 0) == _achievementId) then {
                _currentProgress = _x select 1;
                _newProgress = _currentProgress + _amount;
                _x set [1, _newProgress];
                _found = true;
                
                // Check if achievement should be unlocked
                _achievementData = [_player, "getAchievementData", _achievementId] call EDEN_fnc_achievementSystem;
                if (count _achievementData > 0) then {
                    _requirement = _achievementData select 2;
                    if (_newProgress >= _requirement) then {
                        [_player, "unlockAchievement", _achievementId] call EDEN_fnc_achievementSystem;
                    };
                };
            };
        } forEach _progress;
        
        if (!_found) then {
            _progress pushBack [_achievementId, _amount];
            _player setVariable ["eden_achievementProgress", _progress, true];
        };
        
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "getProgress": {
        _progress = _player getVariable ["eden_achievementProgress", []];
        _currentProgress = 0;
        
        {
            if ((_x select 0) == _achievementId) then {
                _currentProgress = _x select 1;
            };
        } forEach _progress;
        
        _currentProgress
    };
    case "getAchievementData": {
        _allAchievements = [
            ["first_job", "First Day at Work", 1, 10, ["money", 500]],
            ["money_maker", "Money Maker", 10000, 25, ["experience", 1000]],
            ["social_butterfly", "Social Butterfly", 50, 15, ["item", "phone", 1]],
            ["law_abiding", "Law Abiding Citizen", 30, 20, ["money", 1000]],
            ["entrepreneur", "Entrepreneur", 1, 50, ["money", 5000]],
            ["skilled_worker", "Skilled Worker", 5, 30, ["experience", 2000]],
            ["reputation_builder", "Reputation Builder", 100, 40, ["money", 2500]],
            ["vehicle_owner", "Vehicle Owner", 1, 15, ["item", "gps", 1]],
            ["property_owner", "Property Owner", 1, 35, ["money", 3000]],
            ["fisher", "Master Fisher", 100, 25, ["item", "fishing_rod_pro", 1]],
            ["farmer", "Green Thumb", 50, 20, ["item", "fertilizer", 10]],
            ["trucker", "Long Haul Driver", 25, 30, ["money", 2000]],
            ["contractor", "Reliable Contractor", 10, 25, ["experience", 1500]],
            ["business_mogul", "Business Mogul", 3, 75, ["money", 10000]],
            ["license_collector", "License Collector", 5, 35, ["money", 1500]],
            ["skill_master", "Skill Master", 50, 60, ["experience", 5000]],
            ["reputation_legend", "Reputation Legend", 500, 100, ["money", 25000]],
            ["achievement_hunter", "Achievement Hunter", 10, 50, ["item", "trophy", 1]]
        ];
        
        _data = [];
        {
            if ((_x select 0) == _achievementId) then {
                _data = _x;
            };
        } forEach _allAchievements;
        
        _data
    };
    case "viewAchievements": {
        _achievements = _player getVariable ["eden_achievements", []];
        _points = _player getVariable ["eden_achievementPoints", 0];
        
        _achievementList = format ["Achievement Points: %1\n\nUnlocked Achievements:\n", _points];
        
        if (count _achievements == 0) then {
            _achievementList = _achievementList + "None\n";
        } else {
            {
                _data = [_player, "getAchievementData", _x] call EDEN_fnc_achievementSystem;
                if (count _data > 0) then {
                    _name = _data select 1;
                    _achievementPoints = _data select 3;
                    _achievementList = _achievementList + format ["- %1 (%2 pts)\n", _name, _achievementPoints];
                };
            } forEach _achievements;
        };
        
        [_achievementList] call EDEN_fnc_showHint;
        true
    };
    case "viewProgress": {
        _progress = _player getVariable ["eden_achievementProgress", []];
        _progressList = "Achievement Progress:\n";
        
        {
            _id = _x select 0;
            _currentProgress = _x select 1;
            _data = [_player, "getAchievementData", _id] call EDEN_fnc_achievementSystem;
            
            if (count _data > 0) then {
                _name = _data select 1;
                _requirement = _data select 2;
                _percentage = ((_currentProgress / _requirement) * 100) min 100;
                
                _progressList = _progressList + format ["%1: %2/%3 (%4%%)\n", _name, _currentProgress, _requirement, floor _percentage];
            };
        } forEach _progress;
        
        [_progressList] call EDEN_fnc_showHint;
        true
    };
    case "checkJobAchievements": {
        _jobsCompleted = _player getVariable ["eden_jobsCompleted", 0];
        
        if (_jobsCompleted >= 1) then {
            [_player, "unlockAchievement", "first_job"] call EDEN_fnc_achievementSystem;
        };
        
        true
    };
    case "checkMoneyAchievements": {
        _totalEarned = _player getVariable ["eden_totalEarned", 0];
        
        if (_totalEarned >= 10000) then {
            [_player, "unlockAchievement", "money_maker"] call EDEN_fnc_achievementSystem;
        };
        
        true
    };
    case "checkSocialAchievements": {
        _playersInteracted = _player getVariable ["eden_playersInteracted", []];
        
        if (count _playersInteracted >= 50) then {
            [_player, "unlockAchievement", "social_butterfly"] call EDEN_fnc_achievementSystem;
        };
        
        true
    };
    case "checkBusinessAchievements": {
        _ownedBusinesses = _player getVariable ["eden_ownedBusinesses", []];
        
        if (count _ownedBusinesses >= 1) then {
            [_player, "unlockAchievement", "entrepreneur"] call EDEN_fnc_achievementSystem;
        };
        
        if (count _ownedBusinesses >= 3) then {
            [_player, "unlockAchievement", "business_mogul"] call EDEN_fnc_achievementSystem;
        };
        
        true
    };
    case "checkLicenseAchievements": {
        _licenses = _player getVariable ["eden_licenses", []];
        
        if (count _licenses >= 5) then {
            [_player, "unlockAchievement", "license_collector"] call EDEN_fnc_achievementSystem;
        };
        
        true
    };
    default { false };
};
