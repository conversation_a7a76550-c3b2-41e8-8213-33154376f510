/*
    File: fn_processResource.sqf
    Author: EdenRP Development Team
    
    Description:
    Processes raw resources into refined materials.
    
    Parameters:
    0: STRING - Resource type to process
    1: NUMBER - Quantity to process (optional, default: 1)
    2: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if processing was successful
*/

params [
    ["_resourceType", "", [""]],
    ["_quantity", 1, [0]],
    ["_player", player, [objNull]]
];

if (_resourceType == "" || _quantity <= 0 || isNull _player) exitWith { false };

// Processing recipes
_processingRecipes = createHashMap;
_processingRecipes set ["copper", ["copper_ingot", 2, 5]]; // [output, time_per_item, required_license_level]
_processingRecipes set ["iron", ["iron_ingot", 3, 5]];
_processingRecipes set ["diamond", ["processed_diamond", 8, 10]];
_processingRecipes set ["oil", ["refined_oil", 4, 6]];
_processingRecipes set ["stone", ["concrete", 2, 3]];
_processingRecipes set ["sand", ["glass", 2, 3]];

_recipe = _processingRecipes get _resourceType;
if (isNil "_recipe") exitWith {
    ["This resource cannot be processed!"] call EDEN_fnc_showHint;
    false
};

_recipe params ["_outputItem", "_timePerItem", "_requiredLevel"];

// Check player level
_playerLevel = _player getVariable ["eden_playerLevel", 1];
if (_playerLevel < _requiredLevel) exitWith {
    [format ["You need level %1 to process this resource!", _requiredLevel]] call EDEN_fnc_showHint;
    false
};

// Check if player has the raw resource
_virtualItems = _player getVariable ["eden_virtualItems", []];
_playerQuantity = 0;
_itemIndex = -1;

{
    if ((_x select 0) == _resourceType) then {
        _playerQuantity = _x select 1;
        _itemIndex = _forEachIndex;
    };
} forEach _virtualItems;

if (_playerQuantity < _quantity) exitWith {
    [format ["You don't have enough %1 to process! (Have: %2, Need: %3)", _resourceType, _playerQuantity, _quantity]] call EDEN_fnc_showHint;
    false
};

// Check if near processing facility
_nearProcessors = nearestObjects [_player, ["Land_Factory_Main_F", "Land_dp_smallFactory_F", "Land_Shed_Big_F"], 20];
if (count _nearProcessors == 0) exitWith {
    ["You must be near a processing facility!"] call EDEN_fnc_showHint;
    false
};

// Start processing
_totalTime = _timePerItem * _quantity;
[format ["Processing %1x %2... (%3 seconds)", _quantity, _resourceType, _totalTime]] call EDEN_fnc_showHint;

// Processing animation
[_player, "Acts_carFixingWheel"] remoteExec ["switchMove"];

// Process each item
for "_i" from 1 to _quantity do {
    sleep _timePerItem;
    [format ["Processing... %1/%2 complete", _i, _quantity]] call EDEN_fnc_showHint;
};

[_player, ""] remoteExec ["switchMove"];

// Remove raw materials
if (_playerQuantity == _quantity) then {
    _virtualItems deleteAt _itemIndex;
} else {
    (_virtualItems select _itemIndex) set [1, (_playerQuantity - _quantity)];
};

// Add processed materials
_found = false;
{
    if ((_x select 0) == _outputItem) then {
        _x set [1, ((_x select 1) + _quantity)];
        _found = true;
    };
} forEach _virtualItems;

if (!_found) then {
    _virtualItems pushBack [_outputItem, _quantity];
};

_player setVariable ["eden_virtualItems", _virtualItems, true];

// Add experience
_expGained = _quantity * 10;
_currentExp = _player getVariable ["eden_experience", 0];
_player setVariable ["eden_experience", (_currentExp + _expGained), true];

// Check for level up
_newLevel = floor((_currentExp + _expGained) / 1000) + 1;
_currentLevel = _player getVariable ["eden_playerLevel", 1];
if (_newLevel > _currentLevel) then {
    _player setVariable ["eden_playerLevel", _newLevel, true];
    [format ["Level up! You are now level %1", _newLevel], 5] call EDEN_fnc_showNotification;
};

[format ["Processed %1x %2 into %1x %3 (+%4 XP)", _quantity, _resourceType, _outputItem, _expGained]] call EDEN_fnc_showHint;

[_player] call EDEN_fnc_savePlayerData;

true
