/*
    File: fn_emergencyBroadcast.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages emergency broadcast system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_emergencyLevel") then {
            eden_emergencyLevel = 0;
            publicVariable "eden_emergencyLevel";
        };
        true
    };
    case "sendAlert": {
        params ["", "", ["_message", "", [""]], ["_alertLevel", 1, [0]]];
        
        _job = _player getVariable ["eden_job", ""];
        if (!(_job in ["police", "ems", "fire", "admin"])) exitWith {
            ["Insufficient permissions"] call EDEN_fnc_showHint;
            false
        };
        
        _alertType = switch (_alertLevel) do {
            case 1: { "ALERT" };
            case 2: { "WARNING" };
            case 3: { "EMERGENCY" };
            default { "NOTICE" };
        };
        
        {
            [format ["%1: %2", _alertType, _message]] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach allPlayers;
        
        eden_emergencyLevel = _alertLevel;
        publicVariable "eden_emergencyLevel";
        
        ["Emergency broadcast sent"] call EDEN_fnc_showHint;
        true
    };
    case "evacuation": {
        {
            ["EVACUATION ORDER: Proceed to nearest safe zone immediately!"] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach allPlayers;
        
        eden_emergencyLevel = 5;
        publicVariable "eden_emergencyLevel";
        true
    };
    default { false };
};
