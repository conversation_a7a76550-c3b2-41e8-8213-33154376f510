/*
    File: fn_revivePlayer.sqf
    Author: EdenRP Development Team
    
    Description:
    Revives an unconscious player (medical personnel only).
    
    Parameters:
    0: OBJECT - Target player to revive
    1: OBJECT - Medic (optional, default: player)
    
    Returns:
    BOOLEAN - True if revival was successful
*/

params [
    ["_target", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_medic", player, [obj<PERSON><PERSON>]]
];

if (isNull _target || isNull _medic) exitWith { false };

if (!(_medic getVariable ["eden_isMedic", false])) exitWith {
    ["Only medical personnel can revive players!"] call EDEN_fnc_showHint;
    false
};

if (_medic distance _target > 5) exitWith {
    ["Target is too far away!"] call EDEN_fnc_showHint;
    false
};

if (alive _target) exitWith {
    ["Player is not unconscious!"] call EDEN_fnc_showHint;
    false
};

// Revival animation
[_medic, "AinvPknlMstpSnonWnonDnon_medic_1"] remoteExec ["switchMove"];
["Reviving player..."] call EDEN_fnc_showHint;

sleep 8; // Revival time

[_medic, ""] remoteExec ["switchMove"];

// Revive the target
[_target, true] call ace_medical_fnc_setUnconscious; // If using ACE
_target setDamage 0.25; // Revive with some damage
_target setVariable ["eden_isDead", false, true];

// Set basic health
_target setVariable ["eden_hunger", 50, true];
_target setVariable ["eden_thirst", 50, true];

// Update medical statistics
_patientsRevived = _medic getVariable ["eden_patientsRevived", 0];
_medic setVariable ["eden_patientsRevived", (_patientsRevived + 1), true];

// Notifications
[format ["Successfully revived %1", name _target]] remoteExec ["EDEN_fnc_showHint", _medic];
[format ["You have been revived by %1", name _medic]] remoteExec ["EDEN_fnc_showHint", _target];

[format ["[EDEN] %1 revived %2", name _medic, name _target], "INFO", "MEDICAL"] call EDEN_fnc_systemLogger;

[_medic] call EDEN_fnc_savePlayerData;
[_target] call EDEN_fnc_savePlayerData;

true
