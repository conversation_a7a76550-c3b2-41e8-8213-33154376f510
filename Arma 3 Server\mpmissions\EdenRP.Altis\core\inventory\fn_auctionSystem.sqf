/*
    File: fn_auctionSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages inventory auction system for items.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_item", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_itemAuctions") then {
            eden_itemAuctions = [];
            publicVariable "eden_itemAuctions";
        };
        _player setVariable ["eden_auctionBids", [], true];
        _player setVariable ["eden_auctionWins", 0, true];
        true
    };
    case "createAuction": {
        params ["", "", "", ["_quantity", 1, [0]], ["_startingBid", 100, [0]], ["_duration", 3600, [0]]];
        
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _hasItem = false;
        _playerQuantity = 0;
        
        {
            if ((_x select 0) == _item) then {
                _hasItem = true;
                _playerQuantity = _x select 1;
            };
        } forEach _virtualItems;
        
        if (!_hasItem || _playerQuantity < _quantity) exitWith {
            ["Insufficient items to auction"] call EDEN_fnc_showHint;
            false
        };
        
        _listingFee = floor(_startingBid * 0.1); // 10% listing fee
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _listingFee) exitWith {
            [format ["Not enough money for listing fee: $%1", _listingFee]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _listingFee), true];
        
        // Remove item from player
        [_player, "removeItem", _item, _quantity] call EDEN_fnc_itemSystem;
        
        _auction = [_item, _quantity, getPlayerUID _player, name _player, _startingBid, _startingBid, "", time, (time + _duration), "Active"];
        eden_itemAuctions pushBack _auction;
        publicVariable "eden_itemAuctions";
        
        [format ["Auction created: %1x %2 starting at $%3", _quantity, _item, _startingBid]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "placeBid": {
        params ["", "", "", ["_auctionIndex", 0, [0]], ["_bidAmount", 0, [0]]];
        
        if (_auctionIndex >= count eden_itemAuctions) exitWith {
            ["Invalid auction"] call EDEN_fnc_showHint;
            false
        };
        
        _auction = eden_itemAuctions select _auctionIndex;
        if ((_auction select 9) != "Active" || time >= (_auction select 8)) exitWith {
            ["Auction is not active"] call EDEN_fnc_showHint;
            false
        };
        
        _currentBid = _auction select 5;
        if (_bidAmount <= _currentBid) exitWith {
            [format ["Bid must be higher than $%1", _currentBid]] call EDEN_fnc_showHint;
            false
        };
        
        _sellerUID = _auction select 2;
        if (_sellerUID == getPlayerUID _player) exitWith {
            ["Cannot bid on your own auction"] call EDEN_fnc_showHint;
            false
        };
        
        _cash = _player getVariable ["eden_cash", 0];
        if (_cash < _bidAmount) exitWith {
            [format ["Not enough money! Need $%1", _bidAmount]] call EDEN_fnc_showHint;
            false
        };
        
        _auction set [5, _bidAmount];
        _auction set [6, getPlayerUID _player];
        eden_itemAuctions set [_auctionIndex, _auction];
        publicVariable "eden_itemAuctions";
        
        _bids = _player getVariable ["eden_auctionBids", []];
        _bids pushBack [(_auction select 0), _bidAmount, time];
        _player setVariable ["eden_auctionBids", _bids, true];
        
        [format ["Bid placed: $%1 on %2", _bidAmount, (_auction select 0)]] call EDEN_fnc_showHint;
        true
    };
    case "claimItem": {
        params ["", "", "", ["_auctionIndex", 0, [0]]];
        
        if (_auctionIndex >= count eden_itemAuctions) exitWith { false };
        
        _auction = eden_itemAuctions select _auctionIndex;
        if ((_auction select 6) != getPlayerUID _player || time < (_auction select 8)) exitWith {
            ["Cannot claim this item"] call EDEN_fnc_showHint;
            false
        };
        
        _itemName = _auction select 0;
        _quantity = _auction select 1;
        _finalBid = _auction select 5;
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash - _finalBid), true];
        
        // Add item to winner
        [_player, "addItem", _itemName, _quantity] call EDEN_fnc_itemSystem;
        
        // Pay seller
        _sellerUID = _auction select 2;
        _seller = objNull;
        {
            if (getPlayerUID _x == _sellerUID) then { _seller = _x; };
        } forEach allPlayers;
        
        if (!isNull _seller) then {
            _commission = floor(_finalBid * 0.15); // 15% auction house commission
            _sellerEarnings = _finalBid - _commission;
            _sellerCash = _seller getVariable ["eden_cash", 0];
            _seller setVariable ["eden_cash", (_sellerCash + _sellerEarnings), true];
            
            [format ["Auction won! Earned $%1 for %2x %3", _sellerEarnings, _quantity, _itemName]] remoteExec ["EDEN_fnc_showHint", _seller];
            [_seller] call EDEN_fnc_savePlayerData;
        };
        
        _wins = _player getVariable ["eden_auctionWins", 0];
        _player setVariable ["eden_auctionWins", (_wins + 1), true];
        
        _auction set [9, "Completed"];
        eden_itemAuctions set [_auctionIndex, _auction];
        publicVariable "eden_itemAuctions";
        
        [format ["Claimed %1x %2 for $%3", _quantity, _itemName, _finalBid]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "cancelAuction": {
        params ["", "", "", ["_auctionIndex", 0, [0]]];
        
        if (_auctionIndex >= count eden_itemAuctions) exitWith { false };
        
        _auction = eden_itemAuctions select _auctionIndex;
        _sellerUID = _auction select 2;
        
        if (_sellerUID != getPlayerUID _player) exitWith {
            ["Not your auction"] call EDEN_fnc_showHint;
            false
        };
        
        if ((_auction select 6) != "") exitWith {
            ["Cannot cancel auction with bids"] call EDEN_fnc_showHint;
            false
        };
        
        _itemName = _auction select 0;
        _quantity = _auction select 1;
        
        // Return item to seller
        [_player, "addItem", _itemName, _quantity] call EDEN_fnc_itemSystem;
        
        eden_itemAuctions deleteAt _auctionIndex;
        publicVariable "eden_itemAuctions";
        
        [format ["Cancelled auction for %1x %2", _quantity, _itemName]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
