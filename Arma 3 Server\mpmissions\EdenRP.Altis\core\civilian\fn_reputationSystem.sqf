/*
    File: fn_reputationSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages reputation system for civilians.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_faction", "", [""]], ["_amount", 0, [0]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_reputation", [], true];
        
        // Initialize faction reputations
        _factions = [
            ["police", 0],
            ["medical", 0],
            ["civilian", 0],
            ["gang", 0],
            ["government", 0],
            ["business", 0],
            ["criminal", 0]
        ];
        
        _player setVariable ["eden_reputation", _factions, true];
        true
    };
    case "addReputation": {
        _reputation = _player getVariable ["eden_reputation", []];
        _found = false;
        
        {
            if ((_x select 0) == _faction) then {
                _currentRep = _x select 1;
                _newRep = (_currentRep + _amount) max -1000 min 1000; // Cap at -1000 to +1000
                _x set [1, _newRep];
                _found = true;
                
                if (_amount > 0) then {
                    [format ["Gained %1 reputation with %2", _amount, _faction]] call EDEN_fnc_showHint;
                } else {
                    [format ["Lost %1 reputation with %2", abs _amount, _faction]] call EDEN_fnc_showHint;
                };
            };
        } forEach _reputation;
        
        if (!_found) then {
            _reputation pushBack [_faction, _amount];
        };
        
        _player setVariable ["eden_reputation", _reputation, true];
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "getReputation": {
        _reputation = _player getVariable ["eden_reputation", []];
        _rep = 0;
        
        {
            if ((_x select 0) == _faction) then {
                _rep = _x select 1;
            };
        } forEach _reputation;
        
        _rep
    };
    case "getReputationLevel": {
        _rep = [_player, "getReputation", _faction] call EDEN_fnc_reputationSystem;
        
        _level = "Neutral";
        switch (true) do {
            case (_rep >= 750): { _level = "Legendary"; };
            case (_rep >= 500): { _level = "Heroic"; };
            case (_rep >= 250): { _level = "Respected"; };
            case (_rep >= 100): { _level = "Liked"; };
            case (_rep >= 50): { _level = "Friendly"; };
            case (_rep >= -49): { _level = "Neutral"; };
            case (_rep >= -99): { _level = "Disliked"; };
            case (_rep >= -249): { _level = "Hostile"; };
            case (_rep >= -499): { _level = "Hated"; };
            case (_rep >= -749): { _level = "Despised"; };
            default { _level = "Enemy"; };
        };
        
        _level
    };
    case "viewReputation": {
        _reputation = _player getVariable ["eden_reputation", []];
        _repList = "Reputation Status:\n";
        
        {
            _factionName = _x select 0;
            _rep = _x select 1;
            _level = [_player, "getReputationLevel", _factionName] call EDEN_fnc_reputationSystem;
            
            _repList = _repList + format ["%1: %2 (%3)\n", _factionName, _rep, _level];
        } forEach _reputation;
        
        [_repList] call EDEN_fnc_showHint;
        true
    };
    case "checkReputationRequirement": {
        params ["", "", "", "", ["_requiredRep", 0, [0]]];
        
        _currentRep = [_player, "getReputation", _faction] call EDEN_fnc_reputationSystem;
        (_currentRep >= _requiredRep)
    };
    case "getReputationMultiplier": {
        _rep = [_player, "getReputation", _faction] call EDEN_fnc_reputationSystem;
        
        _multiplier = 1.0;
        switch (true) do {
            case (_rep >= 750): { _multiplier = 2.0; };
            case (_rep >= 500): { _multiplier = 1.75; };
            case (_rep >= 250): { _multiplier = 1.5; };
            case (_rep >= 100): { _multiplier = 1.25; };
            case (_rep >= 50): { _multiplier = 1.1; };
            case (_rep >= -49): { _multiplier = 1.0; };
            case (_rep >= -99): { _multiplier = 0.9; };
            case (_rep >= -249): { _multiplier = 0.75; };
            case (_rep >= -499): { _multiplier = 0.5; };
            default { _multiplier = 0.25; };
        };
        
        _multiplier
    };
    case "transferReputation": {
        params ["", "", "", "", ["_target", objNull, [objNull]], ["_transferAmount", 0, [0]]];
        
        if (isNull _target) exitWith {
            ["No target specified!"] call EDEN_fnc_showHint;
            false
        };
        
        _currentRep = [_player, "getReputation", _faction] call EDEN_fnc_reputationSystem;
        if (_currentRep < _transferAmount) exitWith {
            ["Not enough reputation to transfer!"] call EDEN_fnc_showHint;
            false
        };
        
        [_player, "addReputation", _faction, -_transferAmount] call EDEN_fnc_reputationSystem;
        [_target, "addReputation", _faction, _transferAmount] call EDEN_fnc_reputationSystem;
        
        [format ["Transferred %1 %2 reputation to %3", _transferAmount, _faction, name _target]] call EDEN_fnc_showHint;
        [format ["Received %1 %2 reputation from %3", _transferAmount, _faction, name _player]] remoteExec ["EDEN_fnc_showHint", _target];
        
        true
    };
    case "decayReputation": {
        _reputation = _player getVariable ["eden_reputation", []];
        _decayAmount = 1; // Lose 1 rep per day for extreme values
        
        {
            _factionName = _x select 0;
            _rep = _x select 1;
            
            if (_rep > 500) then {
                [_player, "addReputation", _factionName, -_decayAmount] call EDEN_fnc_reputationSystem;
            } else {
                if (_rep < -500) then {
                    [_player, "addReputation", _factionName, _decayAmount] call EDEN_fnc_reputationSystem;
                };
            };
        } forEach _reputation;
        
        true
    };
    case "getReputationRewards": {
        _rep = [_player, "getReputation", _faction] call EDEN_fnc_reputationSystem;
        _rewards = [];
        
        switch (_faction) do {
            case "police": {
                if (_rep >= 100) then { _rewards pushBack "Reduced jail time"; };
                if (_rep >= 250) then { _rewards pushBack "Police equipment access"; };
                if (_rep >= 500) then { _rewards pushBack "Honorary deputy status"; };
            };
            case "medical": {
                if (_rep >= 100) then { _rewards pushBack "Free medical treatment"; };
                if (_rep >= 250) then { _rewards pushBack "Medical supply discounts"; };
                if (_rep >= 500) then { _rewards pushBack "Emergency medical priority"; };
            };
            case "business": {
                if (_rep >= 100) then { _rewards pushBack "Business loan access"; };
                if (_rep >= 250) then { _rewards pushBack "Wholesale pricing"; };
                if (_rep >= 500) then { _rewards pushBack "Exclusive business opportunities"; };
            };
            case "civilian": {
                if (_rep >= 100) then { _rewards pushBack "Community respect"; };
                if (_rep >= 250) then { _rewards pushBack "Civilian job bonuses"; };
                if (_rep >= 500) then { _rewards pushBack "Community leadership role"; };
            };
        };
        
        _rewards
    };
    case "applyReputationPenalties": {
        _rep = [_player, "getReputation", _faction] call EDEN_fnc_reputationSystem;
        _penalties = [];
        
        switch (_faction) do {
            case "police": {
                if (_rep <= -100) then { _penalties pushBack "Increased police attention"; };
                if (_rep <= -250) then { _penalties pushBack "Regular police searches"; };
                if (_rep <= -500) then { _penalties pushBack "Shoot on sight orders"; };
            };
            case "civilian": {
                if (_rep <= -100) then { _penalties pushBack "Higher prices at shops"; };
                if (_rep <= -250) then { _penalties pushBack "Refused service"; };
                if (_rep <= -500) then { _penalties pushBack "Civilian hostility"; };
            };
        };
        
        _penalties
    };
    default { false };
};
