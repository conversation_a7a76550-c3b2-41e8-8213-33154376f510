/*
    File: fn_analyticsSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages server analytics and statistics.
*/

params [["_admin", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _admin) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_analytics") then {
            eden_analytics = [
                ["totalPlayers", 0],
                ["totalPlaytime", 0],
                ["totalTransactions", 0],
                ["totalCrimes", 0],
                ["serverUptime", time]
            ];
            publicVariable "eden_analytics";
        };
        true
    };
    case "getStats": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _playerCount = count allPlayers;
        _uptime = time;
        _totalCash = 0;
        
        {
            _cash = _x getVariable ["eden_cash", 0];
            _bank = _x getVariable ["eden_bankAccount", 0];
            _totalCash = _totalCash + _cash + _bank;
        } forEach allPlayers;
        
        [format ["Server Stats - Players: %1, Uptime: %2min, Total Economy: $%3", 
            _playerCount, floor(_uptime/60), _totalCash]] call EDEN_fnc_showHint;
        true
    };
    case "trackEvent": {
        params ["", "", ["_event", "", [""]], ["_value", 1, [0]]];
        
        {
            if ((_x select 0) == _event) then {
                _x set [1, ((_x select 1) + _value)];
            };
        } forEach eden_analytics;
        
        publicVariable "eden_analytics";
        true
    };
    case "generateReport": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _report = "=== SERVER ANALYTICS REPORT ===\n";
        {
            _report = _report + format["%1: %2\n", (_x select 0), (_x select 1)];
        } forEach eden_analytics;
        
        [_report] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
