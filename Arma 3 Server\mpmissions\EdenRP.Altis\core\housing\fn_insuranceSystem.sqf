/*
    File: fn_insuranceSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages property insurance system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_propertyId", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_propertyInsurance", [], true];
        _player setVariable ["eden_insuranceClaims", [], true];
        true
    };
    case "buyInsurance": {
        _owned = _player getVariable ["eden_ownedProperties", []];
        if !(_propertyId in _owned) exitWith {
            ["You don't own this property!"] call EDEN_fnc_showHint;
            false
        };
        
        _premium = 200;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _premium) exitWith {
            [format ["Not enough money! Need $%1", _premium]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _premium), true];
        
        _insurance = _player getVariable ["eden_propertyInsurance", []];
        _policy = [_propertyId, _premium, time, (time + 2592000)]; // 30 days
        _insurance pushBack _policy;
        _player setVariable ["eden_propertyInsurance", _insurance, true];
        
        [format ["Property insurance purchased for $%1", _premium]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "fileClaim": {
        params ["", "", "", ["_damageAmount", 5000, [0]]];
        
        _insurance = _player getVariable ["eden_propertyInsurance", []];
        _hasInsurance = false;
        
        {
            if (((_x select 0) == _propertyId) && (time < (_x select 3))) then {
                _hasInsurance = true;
            };
        } forEach _insurance;
        
        if (!_hasInsurance) exitWith {
            ["No valid insurance for this property!"] call EDEN_fnc_showHint;
            false
        };
        
        _payout = floor(_damageAmount * 0.8); // 80% coverage
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _payout), true];
        
        _claims = _player getVariable ["eden_insuranceClaims", []];
        _claim = [_propertyId, _damageAmount, _payout, time];
        _claims pushBack _claim;
        _player setVariable ["eden_insuranceClaims", _claims, true];
        
        [format ["Insurance claim paid: $%1", _payout]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
