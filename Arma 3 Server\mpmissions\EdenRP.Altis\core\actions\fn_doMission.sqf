/*
    File: fn_doMission.sqf
    Author: EdenRP Development Team
    
    Description:
    Starts a job-specific mission for the player.
    
    Parameters:
    0: STRING - Mission type
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if mission was started successfully
*/

params [
    ["_missionType", "", [""]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (_missionType == "" || isNull _player) exitWith { false };

// Check if player has active mission
_activeMission = _player getVariable ["eden_activeMission", ""];
if (_activeMission != "") exitWith {
    ["You already have an active mission!"] call EDEN_fnc_showHint;
    false
};

// Mission definitions
switch (_missionType) do {
    case "delivery": {
        _destinations = [
            [3720, 13500, 0, "Kavala"],
            [14500, 16800, 0, "Athira"],
            [26000, 21000, 0, "Pyrgos"],
            [9500, 12500, 0, "Sofia"]
        ];
        
        _destination = selectRandom _destinations;
        _destination params ["_x", "_y", "_z", "_cityName"];
        
        _player setVariable ["eden_activeMission", "delivery", true];
        _player setVariable ["eden_missionDestination", [_x, _y, _z], true];
        _player setVariable ["eden_missionReward", 1500, true];
        _player setVariable ["eden_missionStartTime", time, true];
        
        // Create mission marker
        _markerName = format ["delivery_%1", getPlayerUID _player];
        _marker = createMarkerLocal [_markerName, [_x, _y, _z]];
        _marker setMarkerTypeLocal "mil_objective";
        _marker setMarkerTextLocal format ["Delivery: %1", _cityName];
        _marker setMarkerColorLocal "ColorBlue";
        
        [format ["Delivery mission started! Deliver package to %1 ($1500)", _cityName]] call EDEN_fnc_showHint;
    };
    
    case "taxi": {
        // Find random pickup location
        _pickupLocations = [
            [3720, 13500, 0, "Kavala Hospital"],
            [3650, 13200, 0, "Kavala Market"],
            [14500, 16800, 0, "Athira Square"],
            [26000, 21000, 0, "Pyrgos Center"]
        ];
        
        _pickup = selectRandom _pickupLocations;
        _pickup params ["_px", "_py", "_pz", "_pickupName"];
        
        _destinations = [
            [3720, 13500, 0, "Kavala"],
            [14500, 16800, 0, "Athira"],
            [26000, 21000, 0, "Pyrgos"]
        ];
        
        _destination = selectRandom _destinations;
        _destination params ["_dx", "_dy", "_dz", "_destName"];
        
        _player setVariable ["eden_activeMission", "taxi", true];
        _player setVariable ["eden_missionPickup", [_px, _py, _pz], true];
        _player setVariable ["eden_missionDestination", [_dx, _dy, _dz], true];
        _player setVariable ["eden_missionReward", 800, true];
        _player setVariable ["eden_missionStartTime", time, true];
        
        // Create pickup marker
        _markerName = format ["taxi_pickup_%1", getPlayerUID _player];
        _marker = createMarkerLocal [_markerName, [_px, _py, _pz]];
        _marker setMarkerTypeLocal "mil_pickup";
        _marker setMarkerTextLocal format ["Pickup: %1", _pickupName];
        _marker setMarkerColorLocal "ColorGreen";
        
        [format ["Taxi mission started! Pick up passenger at %1, deliver to %2 ($800)", _pickupName, _destName]] call EDEN_fnc_showHint;
    };
    
    case "mining": {
        _miningLocations = [
            [18500, 14500, 0, "Copper Mine"],
            [20000, 16000, 0, "Iron Mine"],
            [25000, 20000, 0, "Diamond Mine"]
        ];
        
        _location = selectRandom _miningLocations;
        _location params ["_x", "_y", "_z", "_mineName"];
        
        _player setVariable ["eden_activeMission", "mining", true];
        _player setVariable ["eden_missionLocation", [_x, _y, _z], true];
        _player setVariable ["eden_missionTarget", 10, true]; // 10 resources to mine
        _player setVariable ["eden_missionProgress", 0, true];
        _player setVariable ["eden_missionReward", 2000, true];
        _player setVariable ["eden_missionStartTime", time, true];
        
        // Create mission marker
        _markerName = format ["mining_%1", getPlayerUID _player];
        _marker = createMarkerLocal [_markerName, [_x, _y, _z]];
        _marker setMarkerTypeLocal "mil_objective";
        _marker setMarkerTextLocal format ["Mining: %1", _mineName];
        _marker setMarkerColorLocal "ColorOrange";
        
        [format ["Mining mission started! Mine 10 resources at %1 ($2000)", _mineName]] call EDEN_fnc_showHint;
    };
    
    default {
        ["Unknown mission type!"] call EDEN_fnc_showHint;
        false
    };
};

// Log mission start
[format ["[EDEN] Player %1 started mission: %2", name _player, _missionType], "INFO", "MISSIONS"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
