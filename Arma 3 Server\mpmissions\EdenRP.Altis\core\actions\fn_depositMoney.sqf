/*
    File: fn_depositMoney.sqf
    Author: EdenRP Development Team
    
    Description:
    Deposits money into player's bank account.
    
    Parameters:
    0: NUMBER - Amount to deposit
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if deposit was successful
*/

params [
    ["_amount", 0, [0]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (_amount <= 0 || isNull _player) exitWith { false };

_playerCash = _player getVariable ["eden_cash", 0];
if (_playerCash < _amount) exitWith {
    ["Insufficient cash!"] call EDEN_fnc_showHint;
    false
};

// Transfer from cash to bank
_player setVariable ["eden_cash", (_playerCash - _amount), true];
_bankBalance = _player getVariable ["eden_bankAccount", 0];
_player setVariable ["eden_bankAccount", (_bankBalance + _amount), true];

[format ["Deposited $%1. Bank balance: $%2", _amount, (_bankBalance + _amount)]] call EDEN_fnc_showHint;
[_player] call EDEN_fnc_savePlayerData;

true
