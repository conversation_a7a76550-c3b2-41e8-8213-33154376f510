@echo off
title EdenRP Server - Enhanced Altis Life
color 0A
echo ===============================================
echo    EdenRP Server Starting...
echo ===============================================
echo.
cd /d "C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server"
echo Starting server process...
echo Steam AppId: 233780 (Arma 3 Dedicated Server)
echo If the server window closes immediately, check the log files in profiles folder.
echo.

arma3server_x64.exe -serverMod=@extDB3 -config=server.cfg -cfg=basic.cfg -profiles=ServerProfile -name=ServerProfile -world=Altis -autoInit -loadMissionToMemory -enableHT -port=2302

echo.
echo Server process ended.
echo Check profiles\arma3server_x64_*.rpt for detailed logs.
pause
