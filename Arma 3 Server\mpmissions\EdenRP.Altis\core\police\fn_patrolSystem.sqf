/*
    File: fn_patrolSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages police patrol system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_patrolTime", 0, true];
        _player setVariable ["eden_patrolsCompleted", 0, true];
        _player setVariable ["eden_onPatrol", false, true];
        true
    };
    case "startPatrol": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty!"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_onPatrol", true, true];
        _player setVariable ["eden_patrolStartTime", time, true];
        
        ["Patrol started!"] call EDEN_fnc_showHint;
        true
    };
    case "endPatrol": {
        if (!(_player getVariable ["eden_onPatrol", false])) exitWith {
            ["You are not on patrol!"] call EDEN_fnc_showHint;
            false
        };
        
        _startTime = _player getVariable ["eden_patrolStartTime", time];
        _duration = time - _startTime;
        _totalTime = _player getVariable ["eden_patrolTime", 0];
        _completed = _player getVariable ["eden_patrolsCompleted", 0];
        
        _player setVariable ["eden_patrolTime", (_totalTime + _duration), true];
        _player setVariable ["eden_patrolsCompleted", (_completed + 1), true];
        _player setVariable ["eden_onPatrol", false, true];
        
        [format ["Patrol completed! Duration: %1 minutes", floor(_duration / 60)]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "checkPatrolStats": {
        _totalTime = _player getVariable ["eden_patrolTime", 0];
        _completed = _player getVariable ["eden_patrolsCompleted", 0];
        _hours = floor(_totalTime / 3600);
        _minutes = floor((_totalTime % 3600) / 60);
        
        [format ["Patrol Stats:\nCompleted: %1\nTotal Time: %2h %3m", _completed, _hours, _minutes]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
