/*
    File: fn_briefingSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Handles the briefing system for EdenRP players.
    Sets up role-specific briefings and information.
    
    Parameters:
    0: OBJECT - Player object
    1: STRING - Player role (optional)
    
    Returns:
    BOOLEAN - True if briefing was successfully created
    
    Example:
    [player] call EDEN_fnc_briefingSystem;
    [player, "police"] call EDEN_fnc_briefingSystem;
*/

params [
    ["_player", player, [obj<PERSON><PERSON>]],
    ["_role", "", [""]]
];

// Validate parameters
if (isNull _player) exitWith {
    ["[EDEN] fn_briefingSystem: Invalid player object"] call EDEN_fnc_systemLogger;
    false
};

// Get player role if not provided
if (_role == "") then {
    _role = _player getVariable ["eden_playerRole", "civilian"];
};

// Clear existing briefing entries
_player removeDiarySubject "EdenRP";
_player removeDiarySubject "Rules";
_player removeDiarySubject "Commands";

// Create main EdenRP briefing subject
_player createDiarySubject ["EdenRP", "EdenRP Information"];
_player createDiarySubject ["Rules", "Server Rules"];
_player createDiarySubject ["Commands", "Available Commands"];

// Add general server information
_player createDiaryRecord ["EdenRP", ["Welcome", "
<font color='#00FF00' size='16'>Welcome to EdenRP!</font><br/><br/>
This is an enhanced Altis Life roleplay server featuring:<br/>
• Advanced economy system<br/>
• Realistic police and medical systems<br/>
• Gang warfare and territory control<br/>
• Housing and property management<br/>
• Custom vehicles and equipment<br/><br/>
<font color='#FFFF00'>Have fun and remember to roleplay!</font>
"]];

// Add server rules
_player createDiaryRecord ["Rules", ["General Rules", "
<font color='#FF0000' size='14'>Server Rules</font><br/><br/>
1. <font color='#FFFF00'>No RDM (Random Death Match)</font><br/>
2. <font color='#FFFF00'>No VDM (Vehicle Death Match)</font><br/>
3. <font color='#FFFF00'>Stay in character at all times</font><br/>
4. <font color='#FFFF00'>Respect all players and staff</font><br/>
5. <font color='#FFFF00'>No exploiting or cheating</font><br/>
6. <font color='#FFFF00'>Follow NLR (New Life Rule)</font><br/>
7. <font color='#FFFF00'>No metagaming</font><br/>
8. <font color='#FFFF00'>Quality roleplay required</font><br/><br/>
<font color='#FF0000'>Breaking rules may result in kicks, bans, or other punishments.</font>
"]];

// Add role-specific briefings
switch (toLower _role) do {
    case "civilian": {
        _player createDiaryRecord ["EdenRP", ["Civilian Guide", "
<font color='#00FFFF' size='14'>Civilian Information</font><br/><br/>
<font color='#FFFF00'>Getting Started:</font><br/>
• Visit the job center to find work<br/>
• Gather resources and sell them for money<br/>
• Buy licenses at government buildings<br/>
• Purchase vehicles at dealerships<br/><br/>
<font color='#FFFF00'>Available Jobs:</font><br/>
• Mining (copper, iron, diamonds)<br/>
• Fishing and turtle diving<br/>
• Oil processing<br/>
• Trucking and delivery<br/>
• Farming and agriculture<br/><br/>
<font color='#FFFF00'>Tips:</font><br/>
• Always carry your licenses<br/>
• Be careful of criminals and police<br/>
• Join a gang for protection<br/>
• Invest in property and vehicles
        "]];
        
        _player createDiaryRecord ["Commands", ["Civilian Commands", "
<font color='#00FFFF'>Civilian Commands:</font><br/><br/>
• <font color='#FFFF00'>Y</font> - Open main menu<br/>
• <font color='#FFFF00'>T</font> - Open inventory<br/>
• <font color='#FFFF00'>U</font> - Interact with objects<br/>
• <font color='#FFFF00'>F</font> - Flip vehicle<br/>
• <font color='#FFFF00'>H</font> - Holster weapon<br/>
• <font color='#FFFF00'>Shift + R</font> - Surrender<br/>
• <font color='#FFFF00'>Shift + G</font> - Knock out player<br/>
• <font color='#FFFF00'>Windows Key</font> - Phone menu
        "]];
    };
    
    case "police": {
        _player createDiaryRecord ["EdenRP", ["Police Guide", "
<font color='#0000FF' size='14'>Police Information</font><br/><br/>
<font color='#FFFF00'>Your Duties:</font><br/>
• Maintain law and order<br/>
• Arrest criminals and issue tickets<br/>
• Respond to emergency calls<br/>
• Patrol assigned areas<br/>
• Investigate crimes<br/><br/>
<font color='#FFFF00'>Equipment Access:</font><br/>
• Police vehicles and helicopters<br/>
• Weapons and tactical gear<br/>
• Spike strips and barriers<br/>
• Evidence collection tools<br/><br/>
<font color='#FF0000'>Remember: Always follow proper procedures and roleplay!</font>
        "]];
        
        _player createDiaryRecord ["Commands", ["Police Commands", "
<font color='#0000FF'>Police Commands:</font><br/><br/>
• <font color='#FFFF00'>Y</font> - Police menu<br/>
• <font color='#FFFF00'>T</font> - Police inventory<br/>
• <font color='#FFFF00'>U</font> - Arrest/restrain<br/>
• <font color='#FFFF00'>Shift + R</font> - Search player<br/>
• <font color='#FFFF00'>Shift + G</font> - Seize items<br/>
• <font color='#FFFF00'>Shift + T</font> - Ticket player<br/>
• <font color='#FFFF00'>Windows Key</font> - Police radio
        "]];
    };
    
    case "medical": {
        _player createDiaryRecord ["EdenRP", ["Medical Guide", "
<font color='#FF0000' size='14'>Medical Information</font><br/><br/>
<font color='#FFFF00'>Your Duties:</font><br/>
• Respond to medical emergencies<br/>
• Revive and treat injured players<br/>
• Transport patients to hospital<br/>
• Provide medical supplies<br/>
• Conduct health checkups<br/><br/>
<font color='#FFFF00'>Equipment Access:</font><br/>
• Ambulances and medical helicopters<br/>
• Medical supplies and equipment<br/>
• Defibrillators and stretchers<br/>
• First aid kits and medications<br/><br/>
<font color='#00FF00'>Save lives and help the community!</font>
        "]];
        
        _player createDiaryRecord ["Commands", ["Medical Commands", "
<font color='#FF0000'>Medical Commands:</font><br/><br/>
• <font color='#FFFF00'>Y</font> - Medical menu<br/>
• <font color='#FFFF00'>T</font> - Medical inventory<br/>
• <font color='#FFFF00'>U</font> - Revive player<br/>
• <font color='#FFFF00'>Shift + H</font> - Heal player<br/>
• <font color='#FFFF00'>Shift + T</font> - Treat injuries<br/>
• <font color='#FFFF00'>Windows Key</font> - Medical radio
        "]];
    };
    
    default {
        // Default civilian briefing for unknown roles
        _player createDiaryRecord ["EdenRP", ["Getting Started", "
<font color='#FFFFFF' size='14'>Welcome to EdenRP!</font><br/><br/>
Visit the job center to choose your career path:<br/>
• <font color='#00FFFF'>Civilian</font> - Various legal jobs<br/>
• <font color='#0000FF'>Police</font> - Law enforcement<br/>
• <font color='#FF0000'>Medical</font> - Emergency services<br/><br/>
Use the main menu (Y key) to get started!
        "]];
    };
};

// Log successful briefing creation
[format ["[EDEN] Briefing created for player %1 with role %2", name _player, _role]] call EDEN_fnc_systemLogger;

// Return success
true
