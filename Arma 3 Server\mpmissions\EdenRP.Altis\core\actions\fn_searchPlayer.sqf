/*
    File: fn_searchPlayer.sqf
    Author: EdenRP Development Team
    
    Description:
    Searches a player's inventory for illegal items.
    
    Parameters:
    0: OBJECT - Target player to search
    1: OBJECT - Searching officer (optional, default: player)
    
    Returns:
    BOOLEAN - True if search was successful
    
    Example:
    [cursorTarget] call EDEN_fnc_searchPlayer;
    [_target, player] call EDEN_fnc_searchPlayer;
*/

params [
    ["_target", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_officer", player, [obj<PERSON><PERSON>]]
];

// Validate parameters
if (isNull _target || isNull _officer) exitWith {
    ["[EDEN] fn_searchPlayer: Invalid target or officer"] call EDEN_fnc_systemLogger;
    false
};

// Check if officer is police
if (!(_officer getVariable ["eden_isPolice", false])) exitWith {
    ["Only police officers can search players!"] call EDEN_fnc_showHint;
    false
};

// Check distance
if (_officer distance _target > 5) exitWith {
    ["Target is too far away!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_officer, "player_action", [_target, "search"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Get player's inventory
_weapons = weapons _target;
_magazines = magazines _target;
_items = items _target;
_virtualItems = _target getVariable ["eden_virtualItems", []];

// Define illegal items
_illegalWeapons = [
    "arifle_Katiba_F",
    "arifle_MX_F",
    "arifle_TRG21_F",
    "LMG_Mk200_F",
    "srifle_LRR_F",
    "launch_RPG32_F"
];

_illegalItems = [
    "cocaine",
    "heroin",
    "marijuana",
    "meth",
    "stolen_goods",
    "counterfeit_money",
    "illegal_weapons",
    "explosives"
];

// Search results
_foundIllegal = [];
_foundWeapons = [];
_foundItems = [];

// Check weapons
{
    if (_x in _illegalWeapons) then {
        _foundWeapons pushBack _x;
        _foundIllegal pushBack _x;
    };
} forEach _weapons;

// Check items
{
    if (_x in _illegalItems) then {
        _foundItems pushBack _x;
        _foundIllegal pushBack _x;
    };
} forEach _items;

// Check virtual items
{
    _itemName = _x select 0;
    _quantity = _x select 1;
    
    if (_itemName in _illegalItems) then {
        _foundItems pushBack format ["%1 x%2", _itemName, _quantity];
        _foundIllegal pushBack _itemName;
    };
} forEach _virtualItems;

// Check for licenses
_hasGunLicense = _target getVariable ["eden_license_gun", false];
if (count _weapons > 0 && !_hasGunLicense) then {
    _foundIllegal pushBack "unlicensed_weapons";
};

// Create search report
_searchReport = format [
    "=== SEARCH REPORT ===\n" +
    "Officer: %1\n" +
    "Suspect: %2\n" +
    "Time: %3\n\n" +
    "WEAPONS FOUND:\n%4\n\n" +
    "ITEMS FOUND:\n%5\n\n" +
    "ILLEGAL ITEMS:\n%6\n" +
    "==================",
    name _officer,
    name _target,
    [time, "HH:MM:SS"] call BIS_fnc_timeToString,
    if (count _weapons > 0) then {_weapons joinString ", "} else {"None"},
    if (count _items > 0) then {_items joinString ", "} else {"None"},
    if (count _foundIllegal > 0) then {_foundIllegal joinString ", "} else {"None"}
];

// Show search results to officer
[
    "Search Complete",
    if (count _foundIllegal > 0) then {
        format ["Illegal items found: %1", _foundIllegal joinString ", "]
    } else {
        "No illegal items found"
    },
    10,
    if (count _foundIllegal > 0) then {"warning"} else {"success"}
] remoteExec ["EDEN_fnc_showNotification", _officer];

// Notify target
[
    "You Are Being Searched",
    format ["Officer %1 is searching your inventory", name _officer],
    5,
    "warning"
] remoteExec ["EDEN_fnc_showNotification", _target];

// If illegal items found, update wanted level
if (count _foundIllegal > 0) then {
    _wantedLevel = _target getVariable ["eden_wantedLevel", 0];
    _target setVariable ["eden_wantedLevel", (_wantedLevel + (count _foundIllegal)), true];
    
    // Add to criminal record
    _criminalRecord = _target getVariable ["eden_criminalRecord", []];
    _searchRecord = [
        time,
        name _officer,
        "Illegal items found during search",
        _foundIllegal
    ];
    _criminalRecord pushBack _searchRecord;
    _target setVariable ["eden_criminalRecord", _criminalRecord, true];
    
    // Suggest arrest
    [
        "Illegal Items Found",
        format ["Consider arresting %1 for possession of illegal items", name _target],
        10,
        "warning"
    ] remoteExec ["EDEN_fnc_showNotification", _officer];
};

// Update police statistics
_searches = _officer getVariable ["eden_searches", 0];
_officer setVariable ["eden_searches", (_searches + 1), true];

// Log the search
[format ["[EDEN] Officer %1 searched %2. Illegal items: %3", 
    name _officer, name _target, 
    if (count _foundIllegal > 0) then {_foundIllegal joinString ", "} else {"None"}
], "INFO", "POLICE"] call EDEN_fnc_systemLogger;

// Save search report to officer's data
_searchReports = _officer getVariable ["eden_searchReports", []];
_searchReports pushBack [time, name _target, _foundIllegal];
if (count _searchReports > 50) then { _searchReports deleteAt 0; }; // Keep last 50 reports
_officer setVariable ["eden_searchReports", _searchReports, true];

// Save player data
[_target] call EDEN_fnc_savePlayerData;
[_officer] call EDEN_fnc_savePlayerData;

// Return success
true
