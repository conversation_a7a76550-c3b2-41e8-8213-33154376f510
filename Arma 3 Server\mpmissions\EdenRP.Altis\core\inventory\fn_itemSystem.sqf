/*
    File: fn_itemSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages item system and inventory operations.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_item", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_itemDatabase") then {
            eden_itemDatabase = [
                ["apple", "Food", 1, 5, 100],
                ["water", "Drink", 1, 3, 100],
                ["pickaxe", "Tool", 5, 150, 80],
                ["rope", "Material", 2, 25, 90],
                ["bandage", "Medical", 1, 50, 95]
            ];
            publicVariable "eden_itemDatabase";
        };
        _player setVariable ["eden_virtualItems", [], true];
        _player setVariable ["eden_inventoryWeight", 0, true];
        _player setVariable ["eden_maxWeight", 100, true];
        true
    };
    case "addItem": {
        params ["", "", "", ["_quantity", 1, [0]]];
        
        _itemData = [];
        {
            if ((_x select 0) == _item) then { _itemData = _x; };
        } forEach eden_itemDatabase;
        
        if (count _itemData == 0) exitWith {
            ["Item not found in database"] call EDEN_fnc_showHint;
            false
        };
        
        _itemWeight = _itemData select 2;
        _totalWeight = _itemWeight * _quantity;
        _currentWeight = _player getVariable ["eden_inventoryWeight", 0];
        _maxWeight = _player getVariable ["eden_maxWeight", 100];
        
        if ((_currentWeight + _totalWeight) > _maxWeight) exitWith {
            ["Not enough inventory space"] call EDEN_fnc_showHint;
            false
        };
        
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _found = false;
        
        {
            if ((_x select 0) == _item) then {
                _x set [1, ((_x select 1) + _quantity)];
                _virtualItems set [_forEachIndex, _x];
                _found = true;
            };
        } forEach _virtualItems;
        
        if (!_found) then {
            _virtualItems pushBack [_item, _quantity];
        };
        
        _player setVariable ["eden_virtualItems", _virtualItems, true];
        _player setVariable ["eden_inventoryWeight", (_currentWeight + _totalWeight), true];
        
        [format ["Added %1x %2", _quantity, _item]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "removeItem": {
        params ["", "", "", ["_quantity", 1, [0]]];
        
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _itemIndex = -1;
        _currentQuantity = 0;
        
        {
            if ((_x select 0) == _item) then {
                _itemIndex = _forEachIndex;
                _currentQuantity = _x select 1;
            };
        } forEach _virtualItems;
        
        if (_itemIndex == -1 || _currentQuantity < _quantity) exitWith {
            ["Insufficient items"] call EDEN_fnc_showHint;
            false
        };
        
        _itemData = [];
        {
            if ((_x select 0) == _item) then { _itemData = _x; };
        } forEach eden_itemDatabase;
        
        _itemWeight = _itemData select 2;
        _weightReduction = _itemWeight * _quantity;
        _currentWeight = _player getVariable ["eden_inventoryWeight", 0];
        
        if ((_currentQuantity - _quantity) <= 0) then {
            _virtualItems deleteAt _itemIndex;
        } else {
            (_virtualItems select _itemIndex) set [1, (_currentQuantity - _quantity)];
        };
        
        _player setVariable ["eden_virtualItems", _virtualItems, true];
        _player setVariable ["eden_inventoryWeight", (_currentWeight - _weightReduction), true];
        
        [format ["Removed %1x %2", _quantity, _item]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "useItem": {
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _hasItem = false;
        
        {
            if ((_x select 0) == _item && (_x select 1) > 0) then { _hasItem = true; };
        } forEach _virtualItems;
        
        if (!_hasItem) exitWith {
            ["You don't have this item"] call EDEN_fnc_showHint;
            false
        };
        
        switch (_item) do {
            case "apple": {
                _hunger = _player getVariable ["eden_hunger", 100];
                _player setVariable ["eden_hunger", ((_hunger + 20) min 100), true];
                ["Ate apple - hunger restored"] call EDEN_fnc_showHint;
            };
            case "water": {
                _thirst = _player getVariable ["eden_thirst", 100];
                _player setVariable ["eden_thirst", ((_thirst + 25) min 100), true];
                ["Drank water - thirst quenched"] call EDEN_fnc_showHint;
            };
            case "bandage": {
                _player setDamage 0;
                ["Used bandage - health restored"] call EDEN_fnc_showHint;
            };
        };
        
        [_player, "removeItem", _item, 1] call EDEN_fnc_itemSystem;
        true
    };
    default { false };
};
