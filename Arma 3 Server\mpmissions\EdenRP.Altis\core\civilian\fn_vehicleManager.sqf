/*
    File: fn_vehicleManager.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages vehicle ownership and operations for civilians.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_vehicle", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_ownedVehicles", [], true];
        _player setVariable ["eden_vehicleKeys", [], true];
        _player setVariable ["eden_vehicleInsurance", [], true];
        true
    };
    case "registerVehicle": {
        if (isNull _vehicle) exitWith {
            ["No vehicle specified!"] call EDEN_fnc_showHint;
            false
        };
        
        _vehicleId = _vehicle getVariable ["eden_vehicleId", ""];
        if (_vehicleId == "") then {
            _vehicleId = format ["VEH_%1_%2", floor(random 10000), floor(time)];
            _vehicle setVariable ["eden_vehicleId", _vehicleId, true];
        };
        
        _ownedVehicles = _player getVariable ["eden_ownedVehicles", []];
        _alreadyOwned = false;
        {
            if ((_x select 0) == _vehicleId) then {
                _alreadyOwned = true;
            };
        } forEach _ownedVehicles;
        
        if (_alreadyOwned) exitWith {
            ["Vehicle already registered!"] call EDEN_fnc_showHint;
            false
        };
        
        _registrationFee = 500;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _registrationFee) exitWith {
            [format ["Not enough money for registration! Need $%1", _registrationFee]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _registrationFee), true];
        
        _vehicleType = typeOf _vehicle;
        _vehicleData = [_vehicleId, _vehicleType, time, true, 100]; // id, type, regTime, valid, condition
        _ownedVehicles pushBack _vehicleData;
        _player setVariable ["eden_ownedVehicles", _ownedVehicles, true];
        
        _vehicle setVariable ["eden_owner", getPlayerUID _player, true];
        _vehicle setVariable ["eden_registered", true, true];
        
        [format ["Vehicle registered! ID: %1", _vehicleId]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "lockVehicle": {
        if (isNull _vehicle) exitWith {
            ["No vehicle specified!"] call EDEN_fnc_showHint;
            false
        };
        
        _owner = _vehicle getVariable ["eden_owner", ""];
        if (_owner != getPlayerUID _player) exitWith {
            ["You don't own this vehicle!"] call EDEN_fnc_showHint;
            false
        };
        
        _locked = _vehicle getVariable ["eden_locked", false];
        if (_locked) then {
            _vehicle setVariable ["eden_locked", false, true];
            _vehicle lock 0;
            ["Vehicle unlocked."] call EDEN_fnc_showHint;
        } else {
            _vehicle setVariable ["eden_locked", true, true];
            _vehicle lock 2;
            ["Vehicle locked."] call EDEN_fnc_showHint;
        };
        
        true
    };
    case "giveKeys": {
        if (isNull _vehicle) exitWith {
            ["No vehicle specified!"] call EDEN_fnc_showHint;
            false
        };
        
        _owner = _vehicle getVariable ["eden_owner", ""];
        if (_owner != getPlayerUID _player) exitWith {
            ["You don't own this vehicle!"] call EDEN_fnc_showHint;
            false
        };
        
        _nearPlayers = [];
        {
            if (_x != _player && (_x distance _player) <= 10) then {
                _nearPlayers pushBack _x;
            };
        } forEach allPlayers;
        
        if (count _nearPlayers == 0) exitWith {
            ["No players nearby to give keys to!"] call EDEN_fnc_showHint;
            false
        };
        
        _target = _nearPlayers select 0; // Give to closest player
        _vehicleId = _vehicle getVariable ["eden_vehicleId", ""];
        
        _targetKeys = _target getVariable ["eden_vehicleKeys", []];
        if !(_vehicleId in _targetKeys) then {
            _targetKeys pushBack _vehicleId;
            _target setVariable ["eden_vehicleKeys", _targetKeys, true];
        };
        
        [format ["Gave vehicle keys to %1", name _target]] call EDEN_fnc_showHint;
        [format ["%1 gave you vehicle keys!", name _player]] remoteExec ["EDEN_fnc_showHint", _target];
        true
    };
    case "sellVehicle": {
        if (isNull _vehicle) exitWith {
            ["No vehicle specified!"] call EDEN_fnc_showHint;
            false
        };
        
        _owner = _vehicle getVariable ["eden_owner", ""];
        if (_owner != getPlayerUID _player) exitWith {
            ["You don't own this vehicle!"] call EDEN_fnc_showHint;
            false
        };
        
        _vehicleId = _vehicle getVariable ["eden_vehicleId", ""];
        _ownedVehicles = _player getVariable ["eden_ownedVehicles", []];
        _vehicleData = [];
        _index = -1;
        
        {
            if ((_x select 0) == _vehicleId) then {
                _vehicleData = _x;
                _index = _forEachIndex;
            };
        } forEach _ownedVehicles;
        
        if (_index == -1) exitWith {
            ["Vehicle not found in registry!"] call EDEN_fnc_showHint;
            false
        };
        
        _condition = _vehicleData select 4;
        _baseValue = 10000; // Base vehicle value
        _salePrice = _baseValue * (_condition / 100);
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _salePrice), true];
        
        _ownedVehicles deleteAt _index;
        _player setVariable ["eden_ownedVehicles", _ownedVehicles, true];
        
        deleteVehicle _vehicle;
        
        [format ["Vehicle sold for $%1!", floor _salePrice]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "repairVehicle": {
        if (isNull _vehicle) exitWith {
            ["No vehicle specified!"] call EDEN_fnc_showHint;
            false
        };
        
        _damage = damage _vehicle;
        if (_damage < 0.1) exitWith {
            ["Vehicle doesn't need repairs!"] call EDEN_fnc_showHint;
            false
        };
        
        _repairCost = floor(_damage * 1000);
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _repairCost) exitWith {
            [format ["Not enough money for repairs! Need $%1", _repairCost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _repairCost), true];
        _vehicle setDamage 0;
        
        // Update vehicle condition in registry
        _vehicleId = _vehicle getVariable ["eden_vehicleId", ""];
        _ownedVehicles = _player getVariable ["eden_ownedVehicles", []];
        {
            if ((_x select 0) == _vehicleId) then {
                _x set [4, 100]; // Set condition to 100%
            };
        } forEach _ownedVehicles;
        _player setVariable ["eden_ownedVehicles", _ownedVehicles, true];
        
        [format ["Vehicle repaired for $%1!", _repairCost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "refuelVehicle": {
        if (isNull _vehicle) exitWith {
            ["No vehicle specified!"] call EDEN_fnc_showHint;
            false
        };
        
        _fuel = fuel _vehicle;
        if (_fuel > 0.9) exitWith {
            ["Vehicle fuel tank is already full!"] call EDEN_fnc_showHint;
            false
        };
        
        _fuelNeeded = 1 - _fuel;
        _fuelCost = floor(_fuelNeeded * 100); // $1 per 1% fuel
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _fuelCost) exitWith {
            [format ["Not enough money for fuel! Need $%1", _fuelCost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _fuelCost), true];
        _vehicle setFuel 1;
        
        [format ["Vehicle refueled for $%1!", _fuelCost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
