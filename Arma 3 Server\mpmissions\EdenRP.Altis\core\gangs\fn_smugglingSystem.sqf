/*
    File: fn_smugglingSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages gang smuggling operations system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_cargo", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_smugglingRuns", 0, true];
        _player setVariable ["eden_contraband", [], true];
        true
    };
    case "startSmuggling": {
        _gang = _player getVariable ["eden_gang", ""];
        if (_gang == "") exitWith {
            ["You must be in a gang!"] call EDEN_fnc_showHint;
            false
        };
        
        _contraband = _player getVariable ["eden_contraband", []];
        _contraband pushBack [_cargo, 10, time];
        _player setVariable ["eden_contraband", _contraband, true];
        
        ["Smuggling operation started"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "deliverCargo": {
        _contraband = _player getVariable ["eden_contraband", []];
        if (count _contraband == 0) exitWith {
            ["No cargo to deliver"] call EDEN_fnc_showHint;
            false
        };
        
        _profit = 1500;
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _profit), true];
        
        _contraband deleteAt 0;
        _player setVariable ["eden_contraband", _contraband, true];
        
        _runs = _player getVariable ["eden_smugglingRuns", 0];
        _player setVariable ["eden_smugglingRuns", (_runs + 1), true];
        
        [format ["Cargo delivered - earned $%1", _profit]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
