/*
    File: fn_traumaSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages trauma and injury system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_injuries", [], true];
        _player setVariable ["eden_bloodLevel", 100, true];
        true
    };
    case "addInjury": {
        params ["", "", "", ["_injuryType", "Minor", [""]]];
        
        _injuries = _player getVariable ["eden_injuries", []];
        _injuries pushBack [_injuryType, time];
        _player setVariable ["eden_injuries", _injuries, true];
        
        [format ["Injury sustained: %1", _injuryType]] call EDEN_fnc_showHint;
        true
    };
    case "treatInjury": {
        if (isNull _target) exitWith { false };
        
        _injuries = _target getVariable ["eden_injuries", []];
        if (count _injuries == 0) exitWith {
            ["No injuries to treat"] call EDEN_fnc_showHint;
            false
        };
        
        _injuries deleteAt 0;
        _target setVariable ["eden_injuries", _injuries, true];
        _target setDamage ((damage _target) - 0.2);
        
        ["Injury treated"] call EDEN_fnc_showHint;
        ["Your injury has been treated"] remoteExec ["EDEN_fnc_showHint", _target];
        true
    };
    case "stabilize": {
        if (isNull _target) exitWith { false };
        
        _target setVariable ["eden_stabilized", true, true];
        ["Patient stabilized"] call EDEN_fnc_showHint;
        ["You have been stabilized"] remoteExec ["EDEN_fnc_showHint", _target];
        true
    };
    default { false };
};
