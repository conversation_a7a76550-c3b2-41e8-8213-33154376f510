/*
    File: fn_spectateSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages admin spectate system.
*/

params [["_admin", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON><PERSON>]]];

if (isNull _admin) exitWith { false };

switch (_action) do {
    case "init": {
        _admin setVariable ["eden_spectating", false, true];
        _admin setVariable ["eden_spectateTarget", objNull, true];
        true
    };
    case "startSpectate": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        if (isNull _target) exitWith { false };
        
        _admin setVariable ["eden_spectating", true, true];
        _admin setVariable ["eden_spectateTarget", _target, true];
        
        // Make admin invisible and invulnerable
        _admin allowDamage false;
        _admin setCaptive true;
        _admin setPos (getPos _target);
        
        [format ["Now spectating %1", name _target]] call EDEN_fnc_showHint;
        true
    };
    case "stopSpectate": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _admin setVariable ["eden_spectating", false, true];
        _admin setVariable ["eden_spectateTarget", objNull, true];
        
        _admin allowDamage true;
        _admin setCaptive false;
        
        ["Stopped spectating"] call EDEN_fnc_showHint;
        true
    };
    case "spectateNext": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _currentTarget = _admin getVariable ["eden_spectateTarget", objNull];
        _playerList = allPlayers - [_admin];
        
        if (count _playerList == 0) exitWith { false };
        
        _currentIndex = _playerList find _currentTarget;
        _nextIndex = (_currentIndex + 1) mod (count _playerList);
        _nextTarget = _playerList select _nextIndex;
        
        [_admin, "startSpectate", _nextTarget] call EDEN_fnc_spectateSystem;
        true
    };
    case "spectateFreeCam": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _admin setVariable ["eden_spectating", true, true];
        _admin setVariable ["eden_spectateTarget", objNull, true];
        
        _admin allowDamage false;
        _admin setCaptive true;
        
        ["Free camera mode enabled"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
