/*
    File: fn_callPlayer.sqf
    Author: EdenRP Development Team
    
    Description:
    Makes a phone call to another player.
    
    Parameters:
    0: OBJECT - Target player to call
    1: OBJECT - Caller (optional, default: player)
    
    Returns:
    BOOLEAN - True if call was initiated successfully
*/

params [
    ["_target", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_caller", player, [obj<PERSON><PERSON>]]
];

if (isNull _target || isNull _caller) exitWith { false };

if (_caller == _target) exitWith {
    ["You cannot call yourself!"] call EDEN_fnc_showHint;
    false
};

// Check if caller has phone
_virtualItems = _caller getVariable ["eden_virtualItems", []];
_hasPhone = false;
{
    if ((_x select 0) == "phone") then {
        _hasPhone = true;
    };
} forEach _virtualItems;

if (!_hasPhone) exitWith {
    ["You need a phone to make calls!"] call EDEN_fnc_showHint;
    false
};

// Check if target has phone
_targetItems = _target getVariable ["eden_virtualItems", []];
_targetHasPhone = false;
{
    if ((_x select 0) == "phone") then {
        _targetHasPhone = true;
    };
} forEach _targetItems;

if (!_targetHasPhone) exitWith {
    [format ["%1 doesn't have a phone!", name _target]] call EDEN_fnc_showHint;
    false
};

// Check if target is already in a call
if (_target getVariable ["eden_inCall", false]) exitWith {
    [format ["%1 is busy on another call!", name _target]] call EDEN_fnc_showHint;
    false
};

// Check if caller is already in a call
if (_caller getVariable ["eden_inCall", false]) exitWith {
    ["You are already in a call!"] call EDEN_fnc_showHint;
    false
};

// Initiate call
_caller setVariable ["eden_inCall", true, true];
_caller setVariable ["eden_callTarget", _target, true];
_target setVariable ["eden_incomingCall", _caller, true];

// Notify target of incoming call
[format ["Incoming call from %1. Type 'answer' to accept or 'decline' to reject.", name _caller]] remoteExec ["EDEN_fnc_showHint", _target];
["Calling... waiting for answer"] call EDEN_fnc_showHint;

// Auto-timeout after 30 seconds
[_caller, _target] spawn {
    params ["_callerPlayer", "_targetPlayer"];
    
    sleep 30;
    
    // Check if call is still pending
    if (_callerPlayer getVariable ["eden_inCall", false] && 
        _targetPlayer getVariable ["eden_incomingCall", objNull] == _callerPlayer) then {
        
        // Timeout - end call
        _callerPlayer setVariable ["eden_inCall", false, true];
        _callerPlayer setVariable ["eden_callTarget", objNull, true];
        _targetPlayer setVariable ["eden_incomingCall", objNull, true];
        
        ["Call timed out - no answer"] remoteExec ["EDEN_fnc_showHint", _callerPlayer];
        ["Missed call timeout"] remoteExec ["EDEN_fnc_showHint", _targetPlayer];
    };
};

// Log call attempt
[format ["[EDEN] Phone call: %1 calling %2", name _caller, name _target], "INFO", "COMMUNICATION"] call EDEN_fnc_systemLogger;

true
