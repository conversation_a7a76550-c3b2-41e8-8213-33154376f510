/*
    EdenRP Mining System
    Enhanced mining with skill progression and realistic mechanics
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_action", "", [""]],
    ["_location", [], [[]]],
    ["_data", [], [[]]]
];

// Validate parameters
if (isNull _player || !isPlayer _player) exitWith {
    ["Invalid player provided to miningSystem", "ERROR", "MINING"] call EDEN_fnc_systemLogger;
    false
};

private _result = false;
switch (toLower _action) do {
    case "mine": {
        _result = [_player, _location, _data] call EDEN_fnc_performMining;
    };
    case "prospect": {
        _result = [_player, _location] call EDEN_fnc_prospectArea;
    };
    case "process": {
        _result = [_player, _data] call EDEN_fnc_processOre;
    };
    case "sell": {
        _result = [_player, _data] call EDEN_fnc_sellMinerals;
    };
    case "getlocations": {
        _result = [] call EDEN_fnc_getMiningLocations;
    };
    default {
        [format["Unknown mining action: %1", _action], "ERROR", "MINING"] call EDEN_fnc_systemLogger;
    };
};

_result
