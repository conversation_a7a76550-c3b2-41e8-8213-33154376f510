/*
    File: fn_deliverySystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages general delivery system for civilians.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON>ull]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_deliveryRating", 5.0, true];
        _player setVariable ["eden_activeDeliveries", [], true];
        _player setVariable ["eden_completedDeliveries", 0, true];
        true
    };
    case "requestDelivery": {
        _activeDeliveries = _player getVariable ["eden_activeDeliveries", []];
        if (count _activeDeliveries >= 3) exitWith {
            ["You can only have 3 active deliveries!"] call EDEN_fnc_showHint;
            false
        };
        
        _deliveryTypes = [
            ["Package", 100, 5],
            ["Documents", 150, 3],
            ["Food Order", 200, 10],
            ["Medicine", 300, 2],
            ["Electronics", 250, 8]
        ];
        
        _delivery = selectRandom _deliveryTypes;
        _type = _delivery select 0;
        _pay = _delivery select 1;
        _weight = _delivery select 2;
        
        _rating = _player getVariable ["eden_deliveryRating", 5.0];
        _adjustedPay = _pay * (_rating / 5.0);
        
        _pickupLocations = ["Market", "Hospital", "Police Station", "Bank", "Shop"];
        _deliveryLocations = ["Residential Area", "Office Building", "Restaurant", "Clinic"];
        
        _pickup = selectRandom _pickupLocations;
        _destination = selectRandom _deliveryLocations;
        
        _deliveryId = floor(random 10000);
        _timeLimit = time + (600 + (random 300)); // 10-15 minutes
        
        _deliveryData = [_deliveryId, _type, _pickup, _destination, _adjustedPay, _weight, _timeLimit, false];
        _activeDeliveries pushBack _deliveryData;
        _player setVariable ["eden_activeDeliveries", _activeDeliveries, true];
        
        [format ["New delivery: %1 from %2 to %3 for $%4", _type, _pickup, _destination, floor _adjustedPay]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "pickupDelivery": {
        params ["", "", ["_deliveryId", 0, [0]]];
        
        _activeDeliveries = _player getVariable ["eden_activeDeliveries", []];
        _found = false;
        
        {
            if ((_x select 0) == _deliveryId && !(_x select 7)) then {
                _x set [7, true]; // Mark as picked up
                _found = true;
                
                _type = _x select 1;
                _weight = _x select 5;
                
                _virtualItems = _player getVariable ["eden_virtualItems", []];
                _virtualItems pushBack [format ["delivery_%1", _deliveryId], 1];
                _player setVariable ["eden_virtualItems", _virtualItems, true];
                
                [format ["Picked up %1 (Weight: %2kg)", _type, _weight]] call EDEN_fnc_showHint;
            };
        } forEach _activeDeliveries;
        
        if (_found) then {
            _player setVariable ["eden_activeDeliveries", _activeDeliveries, true];
            [_player] call EDEN_fnc_savePlayerData;
        } else {
            ["Delivery not found or already picked up!"] call EDEN_fnc_showHint;
        };
        
        _found
    };
    case "completeDelivery": {
        params ["", "", ["_deliveryId", 0, [0]]];
        
        _activeDeliveries = _player getVariable ["eden_activeDeliveries", []];
        _found = false;
        _deliveryData = [];
        
        {
            if ((_x select 0) == _deliveryId && (_x select 7)) then {
                _deliveryData = _x;
                _found = true;
            };
        } forEach _activeDeliveries;
        
        if (!_found) exitWith {
            ["Delivery not found or not picked up!"] call EDEN_fnc_showHint;
            false
        };
        
        _timeLimit = _deliveryData select 6;
        _pay = _deliveryData select 4;
        _onTime = time <= _timeLimit;
        
        if (_onTime) then {
            _cash = _player getVariable ["eden_cash", 0];
            _player setVariable ["eden_cash", (_cash + _pay), true];
            
            _rating = _player getVariable ["eden_deliveryRating", 5.0];
            _newRating = (_rating + 0.1) min 5.0;
            _player setVariable ["eden_deliveryRating", _newRating, true];
            
            [format ["Delivery completed on time! Earned $%1", floor _pay]] call EDEN_fnc_showHint;
        } else {
            _latePay = _pay * 0.5;
            _cash = _player getVariable ["eden_cash", 0];
            _player setVariable ["eden_cash", (_cash + _latePay), true];
            
            _rating = _player getVariable ["eden_deliveryRating", 5.0];
            _newRating = (_rating - 0.2) max 1.0;
            _player setVariable ["eden_deliveryRating", _newRating, true];
            
            [format ["Delivery completed late! Earned $%1 (50%% penalty)", floor _latePay]] call EDEN_fnc_showHint;
        };
        
        // Remove delivery item
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _virtualItems = _virtualItems - [[format ["delivery_%1", _deliveryId], 1]];
        _player setVariable ["eden_virtualItems", _virtualItems, true];
        
        // Remove from active deliveries
        _activeDeliveries = _activeDeliveries - [_deliveryData];
        _player setVariable ["eden_activeDeliveries", _activeDeliveries, true];
        
        _completed = _player getVariable ["eden_completedDeliveries", 0];
        _player setVariable ["eden_completedDeliveries", (_completed + 1), true];
        
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
