/*
    File: fn_encryptionSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages encryption system.
*/

params [["_action", "init", [""]]];

switch (_action) do {
    case "init": {
        if (isNil "eden_encryptionKey") then {
            eden_encryptionKey = "EDENRP2024SECURE";
        };
        true
    };
    case "encrypt": {
        params ["", ["_data", "", [""]]];
        
        if (_data == "") exitWith { "" };
        
        _encrypted = "";
        _keyLength = count eden_encryptionKey;
        
        for "_i" from 0 to (count _data - 1) do {
            _char = _data select [_i, 1];
            _keyChar = eden_encryptionKey select [(_i mod _keyLength), 1];
            _charCode = toArray _char select 0;
            _keyCode = toArray _keyChar select 0;
            _encryptedCode = (_charCode + _keyCode) mod 256;
            _encrypted = _encrypted + toString [_encryptedCode];
        };
        
        _encrypted
    };
    case "decrypt": {
        params ["", ["_encryptedData", "", [""]]];
        
        if (_encryptedData == "") exitWith { "" };
        
        _decrypted = "";
        _keyLength = count eden_encryptionKey;
        
        for "_i" from 0 to (count _encryptedData - 1) do {
            _char = _encryptedData select [_i, 1];
            _keyChar = eden_encryptionKey select [(_i mod _keyLength), 1];
            _charCode = toArray _char select 0;
            _keyCode = toArray _keyChar select 0;
            _decryptedCode = (_charCode - _keyCode + 256) mod 256;
            _decrypted = _decrypted + toString [_decryptedCode];
        };
        
        _decrypted
    };
    case "hash": {
        params ["", ["_input", "", [""]]];
        
        if (_input == "") exitWith { "" };
        
        _hash = 0;
        for "_i" from 0 to (count _input - 1) do {
            _char = _input select [_i, 1];
            _charCode = toArray _char select 0;
            _hash = (_hash * 31 + _charCode) mod 1000000;
        };
        
        str _hash
    };
    case "generateToken": {
        params ["", ["_length", 16, [0]]];
        
        _chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        _token = "";
        
        for "_i" from 1 to _length do {
            _randomIndex = floor(random(count _chars));
            _token = _token + (_chars select [_randomIndex, 1]);
        };
        
        _token
    };
    case "verifyHash": {
        params ["", ["_input", "", [""]], ["_expectedHash", "", [""]]];
        
        _actualHash = [_input] call EDEN_fnc_encryptionSystem;
        _actualHash == _expectedHash
    };
    case "encryptPlayerData": {
        params ["", ["_player", objNull, [objNull]]];
        
        if (isNull _player) exitWith { "" };
        
        _playerData = format["%1|%2|%3|%4", 
            getPlayerUID _player,
            _player getVariable ["eden_cash", 0],
            _player getVariable ["eden_bankAccount", 0],
            _player getVariable ["eden_level", 1]
        ];
        
        ["encrypt", _playerData] call EDEN_fnc_encryptionSystem
    };
    case "decryptPlayerData": {
        params ["", ["_encryptedData", "", [""]]];
        
        _decryptedData = ["decrypt", _encryptedData] call EDEN_fnc_encryptionSystem;
        _dataParts = _decryptedData splitString "|";
        
        if (count _dataParts >= 4) then {
            [
                _dataParts select 0,
                parseNumber (_dataParts select 1),
                parseNumber (_dataParts select 2),
                parseNumber (_dataParts select 3)
            ]
        } else {
            []
        };
    };
    default { "" };
};
