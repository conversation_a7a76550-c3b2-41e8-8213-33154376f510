/*
    EdenRP Market System
    Enhanced dynamic market with supply and demand
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_action", "", [""]],
    ["_item", "", [""]],
    ["_quantity", 1, [0]],
    ["_data", [], [[]]]
];

// Validate parameters
if (isNull _player || !isPlayer _player) exitWith {
    ["Invalid player provided to marketSystem", "ERROR", "ECONOMY"] call EDEN_fnc_systemLogger;
    false
};

private _result = false;
switch (toLower _action) do {
    case "buy": {
        _result = [_player, _item, _quantity, _data] call EDEN_fnc_buyFromMarket;
    };
    case "sell": {
        _result = [_player, _item, _quantity, _data] call EDEN_fnc_sellToMarket;
    };
    case "getprices": {
        _result = [_item] call EDEN_fnc_getMarketPrices;
    };
    case "gettrends": {
        _result = [_item] call EDEN_fnc_getMarketTrends;
    };
    case "getstock": {
        _result = [_item] call EDEN_fnc_getMarketStock;
    };
    case "updateprices": {
        _result = [] call EDEN_fnc_updateMarketPrices;
    };
    case "getlocations": {
        _result = [] call EDEN_fnc_getMarketLocations;
    };
    case "getnearby": {
        _result = [_player] call EDEN_fnc_getNearbyMarkets;
    };
    default {
        [format["Unknown market action: %1", _action], "ERROR", "ECONOMY"] call EDEN_fnc_systemLogger;
    };
};

_result
