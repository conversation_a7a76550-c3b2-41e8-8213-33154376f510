/*
    File: fn_mentalHealthSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages mental health and therapy system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_mentalHealth", 100, true];
        _player setVariable ["eden_stressLevel", 0, true];
        _player setVariable ["eden_therapySessions", 0, true];
        true
    };
    case "addStress": {
        params ["", "", ["_amount", 10, [0]]];
        
        _stress = _player getVariable ["eden_stressLevel", 0];
        _stress = (_stress + _amount) min 100;
        _player setVariable ["eden_stressLevel", _stress, true];
        
        _mentalHealth = _player getVariable ["eden_mentalHealth", 100];
        _mentalHealth = (_mentalHealth - (_amount / 2)) max 0;
        _player setVariable ["eden_mentalHealth", _mentalHealth, true];
        
        if (_stress > 80) then {
            ["High stress levels detected - seek therapy"] call EDEN_fnc_showHint;
        };
        
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "therapySession": {
        _cost = 500;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _stress = _player getVariable ["eden_stressLevel", 0];
        _stress = (_stress - 20) max 0;
        _player setVariable ["eden_stressLevel", _stress, true];
        
        _mentalHealth = _player getVariable ["eden_mentalHealth", 100];
        _mentalHealth = (_mentalHealth + 15) min 100;
        _player setVariable ["eden_mentalHealth", _mentalHealth, true];
        
        _sessions = _player getVariable ["eden_therapySessions", 0];
        _player setVariable ["eden_therapySessions", (_sessions + 1), true];
        
        ["Therapy session completed - stress reduced"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "checkMentalHealth": {
        _mentalHealth = _player getVariable ["eden_mentalHealth", 100];
        _stress = _player getVariable ["eden_stressLevel", 0];
        _sessions = _player getVariable ["eden_therapySessions", 0];
        
        _status = format ["Mental Health: %1%%\nStress Level: %2%%\nTherapy Sessions: %3", 
            _mentalHealth, _stress, _sessions];
        
        [_status] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
