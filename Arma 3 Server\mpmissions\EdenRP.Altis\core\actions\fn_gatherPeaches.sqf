/*
    File: fn_gatherPeaches.sqf
    Author: EdenRP Development Team
    
    Description:
    Gathers peaches from peach trees.
    
    Parameters:
    0: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if peaches were gathered successfully
*/

params [["_player", player, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

// Check if near peach trees
_nearTrees = nearestObjects [_player, ["Land_Peach_01_F", "Land_Peach_02_F"], 5];
if (count _nearTrees == 0) exitWith {
    ["You must be near peach trees to gather peaches!"] call EDEN_fnc_showHint;
    false
};

// Check inventory space
_currentWeight = _player getVariable ["eden_currentWeight", 0];
_maxWeight = _player getVariable ["eden_maxWeight", 50];
_peachWeight = 1; // Each peach weighs 1kg

if ((_currentWeight + _peachWeight) > _maxWeight) exitWith {
    ["Inventory is full!"] call EDEN_fnc_showHint;
    false
};

// Check if tree was recently harvested
_tree = _nearTrees select 0;
_lastHarvested = _tree getVariable ["eden_lastHarvested", 0];
if ((time - _lastHarvested) < 300) exitWith { // 5 minute cooldown
    ["This tree was recently harvested. Wait a few minutes."] call EDEN_fnc_showHint;
    false
};

// Gathering animation
[_player, "Acts_carFixingWheel"] remoteExec ["switchMove"];
["Gathering peaches..."] call EDEN_fnc_showHint;

sleep 3; // Gathering time

[_player, ""] remoteExec ["switchMove"];

// Random number of peaches (1-3)
_peachesGathered = 1 + floor(random 3);

// Add peaches to inventory
_virtualItems = _player getVariable ["eden_virtualItems", []];
_found = false;
{
    if ((_x select 0) == "peach") then {
        _x set [1, ((_x select 1) + _peachesGathered)];
        _found = true;
    };
} forEach _virtualItems;

if (!_found) then {
    _virtualItems pushBack ["peach", _peachesGathered];
};

_player setVariable ["eden_virtualItems", _virtualItems, true];
_player setVariable ["eden_currentWeight", (_currentWeight + (_peachesGathered * _peachWeight)), true];

// Mark tree as harvested
_tree setVariable ["eden_lastHarvested", time, true];

// Add experience
_expGained = _peachesGathered * 5;
_currentExp = _player getVariable ["eden_experience", 0];
_player setVariable ["eden_experience", (_currentExp + _expGained), true];

[format ["Gathered %1 peaches (+%2 XP)", _peachesGathered, _expGained]] call EDEN_fnc_showHint;

// Log gathering
[format ["[EDEN] Player %1 gathered %2 peaches", name _player, _peachesGathered], "DEBUG", "GATHERING"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
