/*
    EdenRP Server Initialization
    Enhanced server-side initialization for EdenRP
    
    This script handles all server-side initialization
    including database setup, economy, and core systems
*/

// Only run on server
if (!isServer) exitWith {};

// Initialize server variables
EDEN_ServerStartTime = diag_tickTime;
EDEN_ServerReady = false;
EDEN_DatabaseConnected = false;
EDEN_EconomyInitialized = false;

// Server configuration
EDEN_MaxPlayers = 120;
EDEN_ServerName = "EdenRP - Enhanced Altis Life";
EDEN_ServerVersion = "1.0.0";
EDEN_ServerRegion = "US";

// Wait for EDEN functions to be compiled
waitUntil {!isNil "EDEN_fnc_systemLogger"};

// Initialize logging system
[] call EDEN_fnc_initializeLogging;
[format["EdenRP Server v%1 starting initialization...", EDEN_ServerVersion]] call EDEN_fnc_systemLogger;

// Initialize database connection
[] spawn {
    ["Initializing database connection..."] call EDEN_fnc_systemLogger;
    
    // Initialize extDB3
    if (!("extDB3" callExtension "9:VERSION")) then {
        ["ERROR: extDB3 extension not found!"] call EDEN_fnc_systemLogger;
    } else {
        private _version = "extDB3" callExtension "9:VERSION";
        [format["extDB3 Version: %1", _version]] call EDEN_fnc_systemLogger;
        
        // Connect to database
        private _result = "extDB3" callExtension "9:ADD_DATABASE:Database";
        if (_result == "[1]") then {
            ["Database connection established"] call EDEN_fnc_systemLogger;
            
            // Initialize SQL protocols
            "extDB3" callExtension "9:ADD_DATABASE_PROTOCOL:Database:SQL:SQL";
            "extDB3" callExtension "9:ADD_DATABASE_PROTOCOL:Database:SQL_CUSTOM:EDEN_Players:eden_players.ini";
            "extDB3" callExtension "9:ADD_DATABASE_PROTOCOL:Database:SQL_CUSTOM:EDEN_Vehicles:eden_vehicles.ini";
            "extDB3" callExtension "9:ADD_DATABASE_PROTOCOL:Database:SQL_CUSTOM:EDEN_Houses:eden_houses.ini";
            "extDB3" callExtension "9:ADD_DATABASE_PROTOCOL:Database:SQL_CUSTOM:EDEN_Gangs:eden_gangs.ini";
            
            // Lock database
            "extDB3" callExtension "9:LOCK";
            
            EDEN_DatabaseConnected = true;
            publicVariable "EDEN_DatabaseConnected";
            ["Database protocols initialized and locked"] call EDEN_fnc_systemLogger;
        } else {
            [format["ERROR: Database connection failed: %1", _result]] call EDEN_fnc_systemLogger;
        };
    };
};

// Wait for database connection
waitUntil {EDEN_DatabaseConnected};

// Initialize economy system
[] spawn {
    ["Initializing economy system..."] call EDEN_fnc_systemLogger;
    
    // Load market prices from database
    [] call EDEN_fnc_loadMarketPrices;
    
    // Initialize dynamic pricing
    [] call EDEN_fnc_initializeDynamicPricing;
    
    // Start economy monitoring
    [] call EDEN_fnc_startEconomyMonitoring;
    
    EDEN_EconomyInitialized = true;
    publicVariable "EDEN_EconomyInitialized";
    ["Economy system initialized"] call EDEN_fnc_systemLogger;
};

// Initialize gang system
[] spawn {
    ["Initializing gang system..."] call EDEN_fnc_systemLogger;
    
    // Load gangs from database
    [] call EDEN_fnc_loadGangs;
    
    // Initialize gang territories
    [] call EDEN_fnc_initializeGangTerritories;
    
    // Start gang monitoring
    [] call EDEN_fnc_startGangMonitoring;
    
    ["Gang system initialized"] call EDEN_fnc_systemLogger;
};

// Initialize housing system
[] spawn {
    ["Initializing housing system..."] call EDEN_fnc_systemLogger;
    
    // Load houses from database
    [] call EDEN_fnc_loadHouses;
    
    // Initialize property markers
    [] call EDEN_fnc_initializePropertyMarkers;
    
    // Start housing monitoring
    [] call EDEN_fnc_startHousingMonitoring;
    
    ["Housing system initialized"] call EDEN_fnc_systemLogger;
};

// Initialize vehicle system
[] spawn {
    ["Initializing vehicle system..."] call EDEN_fnc_systemLogger;
    
    // Load vehicles from database
    [] call EDEN_fnc_loadVehicles;
    
    // Initialize vehicle spawns
    [] call EDEN_fnc_initializeVehicleSpawns;
    
    // Start vehicle monitoring
    [] call EDEN_fnc_startVehicleMonitoring;
    
    ["Vehicle system initialized"] call EDEN_fnc_systemLogger;
};

// Initialize job system
[] spawn {
    ["Initializing job system..."] call EDEN_fnc_systemLogger;
    
    // Initialize job locations
    [] call EDEN_fnc_initializeJobLocations;
    
    // Initialize resource spawns
    [] call EDEN_fnc_initializeResourceSpawns;
    
    // Start job monitoring
    [] call EDEN_fnc_startJobMonitoring;
    
    ["Job system initialized"] call EDEN_fnc_systemLogger;
};

// Initialize admin system
[] spawn {
    ["Initializing admin system..."] call EDEN_fnc_systemLogger;
    
    // Load admin list from database
    [] call EDEN_fnc_loadAdminList;
    
    // Initialize admin tools
    [] call EDEN_fnc_initializeAdminTools;
    
    // Start admin monitoring
    [] call EDEN_fnc_startAdminMonitoring;
    
    ["Admin system initialized"] call EDEN_fnc_systemLogger;
};

// Initialize security system
[] spawn {
    ["Initializing security system..."] call EDEN_fnc_systemLogger;
    
    // Initialize anti-cheat
    [] call EDEN_fnc_initializeAntiCheat;
    
    // Initialize validation system
    [] call EDEN_fnc_initializeValidation;
    
    // Start security monitoring
    [] call EDEN_fnc_startSecurityMonitoring;
    
    ["Security system initialized"] call EDEN_fnc_systemLogger;
};

// Initialize cleanup system
[] spawn {
    ["Initializing cleanup system..."] call EDEN_fnc_systemLogger;
    
    // Start cleanup monitoring
    [] call EDEN_fnc_startCleanupSystem;
    
    ["Cleanup system initialized"] call EDEN_fnc_systemLogger;
};

// Initialize event system
[] spawn {
    ["Initializing event system..."] call EDEN_fnc_systemLogger;
    
    // Load scheduled events
    [] call EDEN_fnc_loadScheduledEvents;
    
    // Start event monitoring
    [] call EDEN_fnc_startEventMonitoring;
    
    ["Event system initialized"] call EDEN_fnc_systemLogger;
};

// Initialize communication system
[] spawn {
    ["Initializing communication system..."] call EDEN_fnc_systemLogger;
    
    // Initialize phone system
    [] call EDEN_fnc_initializePhoneSystem;
    
    // Initialize radio system
    [] call EDEN_fnc_initializeRadioSystem;
    
    // Start communication monitoring
    [] call EDEN_fnc_startCommunicationMonitoring;
    
    ["Communication system initialized"] call EDEN_fnc_systemLogger;
};

// Initialize performance monitoring
[] spawn {
    ["Initializing performance monitoring..."] call EDEN_fnc_systemLogger;
    
    // Start performance monitoring
    [] call EDEN_fnc_startPerformanceMonitoring;
    
    ["Performance monitoring initialized"] call EDEN_fnc_systemLogger;
};

// Player connection handlers
addMissionEventHandler ["PlayerConnected", {
    params ["_id", "_uid", "_name", "_jip", "_owner", "_idstr"];
    [_id, _uid, _name, _jip, _owner, _idstr] call EDEN_fnc_playerConnected;
}];

addMissionEventHandler ["PlayerDisconnected", {
    params ["_id", "_uid", "_name", "_jip", "_owner", "_idstr"];
    [_id, _uid, _name, _jip, _owner, _idstr] call EDEN_fnc_playerDisconnected;
}];

// Server event handlers
addMissionEventHandler ["HandleDisconnect", {
    params ["_unit", "_id", "_uid", "_name"];
    [_unit, _id, _uid, _name] call EDEN_fnc_handleDisconnect;
}];

// Initialize server statistics
EDEN_ServerStats = [
    ["StartTime", EDEN_ServerStartTime],
    ["Uptime", 0],
    ["PlayersConnected", 0],
    ["TotalConnections", 0],
    ["DatabaseQueries", 0],
    ["ErrorCount", 0],
    ["WarningCount", 0],
    ["PerformanceScore", 100]
];

// Start server statistics monitoring
[] spawn {
    while {true} do {
        sleep 60; // Update every minute
        [] call EDEN_fnc_updateServerStats;
    };
};

// Start auto-save system
[] spawn {
    while {true} do {
        sleep 600; // Save every 10 minutes
        [] call EDEN_fnc_autoSaveAll;
    };
};

// Start backup system
[] spawn {
    while {true} do {
        sleep 3600; // Backup every hour
        [] call EDEN_fnc_backupDatabase;
    };
};

// Wait for all systems to initialize
[] spawn {
    waitUntil {
        EDEN_DatabaseConnected &&
        EDEN_EconomyInitialized
    };
    
    // Mark server as ready
    EDEN_ServerReady = true;
    publicVariable "EDEN_ServerReady";
    
    private _initTime = diag_tickTime - EDEN_ServerStartTime;
    [format["EdenRP Server initialization completed in %1 seconds", _initTime]] call EDEN_fnc_systemLogger;
    [format["Server is now ready for players"]] call EDEN_fnc_systemLogger;
    
    // Broadcast server ready message
    {
        ["Server initialization complete. Welcome to EdenRP!", "success"] remoteExec ["EDEN_fnc_showNotification", _x];
    } forEach allPlayers;
};

// Initialize mission-specific objects and markers
[] spawn {
    ["Initializing mission objects..."] call EDEN_fnc_systemLogger;
    
    // Create ATM objects
    [] call EDEN_fnc_createATMs;
    
    // Create shop objects
    [] call EDEN_fnc_createShops;
    
    // Create job locations
    [] call EDEN_fnc_createJobLocations;
    
    // Create spawn points
    [] call EDEN_fnc_createSpawnPoints;
    
    // Create markers
    [] call EDEN_fnc_createMarkers;
    
    ["Mission objects initialized"] call EDEN_fnc_systemLogger;
};

// Log server initialization start
[format["EdenRP Server v%1 initialization started", EDEN_ServerVersion]] call EDEN_fnc_systemLogger;
