/*
    File: fn_rehabilitationSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages rehabilitation and recovery system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_rehabProgress", 0, true];
        _player setVariable ["eden_addictionLevel", 0, true];
        true
    };
    case "startRehab": {
        _cost = 2000;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        _player setVariable ["eden_rehabProgress", 0, true];
        _player setVariable ["eden_inRehab", true, true];
        
        ["Rehabilitation program started"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "rehabSession": {
        if (!(_player getVariable ["eden_inRehab", false])) exitWith {
            ["You are not in rehabilitation"] call EDEN_fnc_showHint;
            false
        };
        
        _progress = _player getVariable ["eden_rehabProgress", 0];
        _progress = _progress + 10;
        _player setVariable ["eden_rehabProgress", _progress, true];
        
        if (_progress >= 100) then {
            _player setVariable ["eden_inRehab", false, true];
            _player setVariable ["eden_addictionLevel", 0, true];
            ["Rehabilitation completed successfully!"] call EDEN_fnc_showHint;
        } else {
            [format ["Rehabilitation progress: %1%%", _progress]] call EDEN_fnc_showHint;
        };
        
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
