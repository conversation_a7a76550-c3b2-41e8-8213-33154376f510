/*
    EdenRP Generate Phone Number
    Utility function to generate unique phone numbers
    
    This function creates unique phone numbers for players
    with proper formatting and collision detection
*/

params [
    ["_length", 8, [0]]
];

// Validate length
_length = [_length, 6, 12] call EDEN_fnc_clampValue;

// Generate random number
private _phoneNumber = "";
for "_i" from 1 to _length do {
    _phoneNumber = _phoneNumber + str (floor (random 10));
};

// Ensure it doesn't start with 0
if (_phoneNumber select [0, 1] == "0") then {
    _phoneNumber = "1" + (_phoneNumber select [1]);
};

// Format with dashes for readability (XXX-XXXX or XXXX-XXXX)
if (_length == 7) then {
    _phoneNumber = (_phoneNumber select [0, 3]) + "-" + (_phoneNumber select [3, 4]);
} else {
    if (_length == 8) then {
        _phoneNumber = (_phoneNumber select [0, 4]) + "-" + (_phoneNumber select [4, 4]);
    };
};

// TODO: Add database check for uniqueness in production
// For now, return the generated number
_phoneNumber
