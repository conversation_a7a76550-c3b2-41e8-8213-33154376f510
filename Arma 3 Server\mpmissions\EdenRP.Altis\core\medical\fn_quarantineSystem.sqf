/*
    File: fn_quarantineSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages quarantine and isolation system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_quarantined", false, true];
        _player setVariable ["eden_infectionLevel", 0, true];
        true
    };
    case "quarantine": {
        _player setVariable ["eden_quarantined", true, true];
        _player setVariable ["eden_quarantineStart", time, true];
        
        ["You have been quarantined"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "releaseQuarantine": {
        _player setVariable ["eden_quarantined", false, true];
        _player setVariable ["eden_infectionLevel", 0, true];
        
        ["Released from quarantine"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
