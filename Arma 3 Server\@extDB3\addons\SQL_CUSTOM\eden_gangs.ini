[Default]
Version = 1
Prepared Statement Cache = true

[getAllGangs]
SQL1_1 = SELECT * FROM eden_gangs WHERE active = 1 ORDER BY experience DESC
Number Of Inputs = 0

[getGangData]
SQL1_1 = SELECT * FROM eden_gangs WHERE id = ? AND active = 1
Number Of Inputs = 1
SQL1_INPUTS = 1

[getGangByName]
SQL1_1 = SELECT * FROM eden_gangs WHERE name = ? AND active = 1
Number Of Inputs = 1
SQL1_INPUTS = 1

[insertGang]
SQL1_1 = INSERT INTO eden_gangs (name, tag, leader_id, bank, max_members, color, description, rules) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
Number Of Inputs = 8
SQL1_INPUTS = 1,2,3,4,5,6,7,8

[updateGang]
SQL1_1 = UPDATE eden_gangs SET name = ?, tag = ?, bank = ?, experience = ?, level = ?, reputation = ?, max_members = ?, territory_count = ?, kills = ?, deaths = ?, color = ?, description = ?, rules = ?, perks = ?, allies = ?, enemies = ?, last_active = NOW() WHERE id = ?
Number Of Inputs = 17
SQL1_INPUTS = 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17

[updateGangBank]
SQL1_1 = UPDATE eden_gangs SET bank = ?, last_active = NOW() WHERE id = ?
Number Of Inputs = 2
SQL1_INPUTS = 1,2

[updateGangExperience]
SQL1_1 = UPDATE eden_gangs SET experience = ?, level = ?, last_active = NOW() WHERE id = ?
Number Of Inputs = 3
SQL1_INPUTS = 1,2,3

[updateGangStats]
SQL1_1 = UPDATE eden_gangs SET kills = kills + ?, deaths = deaths + ?, last_active = NOW() WHERE id = ?
Number Of Inputs = 3
SQL1_INPUTS = 1,2,3

[deleteGang]
SQL1_1 = UPDATE eden_gangs SET active = 0 WHERE id = ?
Number Of Inputs = 1
SQL1_INPUTS = 1

[transferGangLeadership]
SQL1_1 = UPDATE eden_gangs SET leader_id = ?, last_active = NOW() WHERE id = ?
Number Of Inputs = 2
SQL1_INPUTS = 1,2

[getGangMembers]
SQL1_1 = SELECT gm.*, p.name as player_name, p.last_seen FROM eden_gang_members gm LEFT JOIN eden_players p ON gm.player_id = p.player_id WHERE gm.gang_id = ? ORDER BY gm.rank DESC, gm.joined ASC
Number Of Inputs = 1
SQL1_INPUTS = 1

[insertGangMember]
SQL1_1 = INSERT INTO eden_gang_members (gang_id, player_id, rank, permissions) VALUES (?, ?, ?, ?)
Number Of Inputs = 4
SQL1_INPUTS = 1,2,3,4

[updateGangMember]
SQL1_1 = UPDATE eden_gang_members SET rank = ?, contribution = ?, permissions = ? WHERE gang_id = ? AND player_id = ?
Number Of Inputs = 5
SQL1_INPUTS = 1,2,3,4,5

[removeGangMember]
SQL1_1 = DELETE FROM eden_gang_members WHERE gang_id = ? AND player_id = ?
Number Of Inputs = 2
SQL1_INPUTS = 1,2

[getPlayerGang]
SQL1_1 = SELECT gm.*, g.name as gang_name, g.tag, g.color FROM eden_gang_members gm LEFT JOIN eden_gangs g ON gm.gang_id = g.id WHERE gm.player_id = ? AND g.active = 1
Number Of Inputs = 1
SQL1_INPUTS = 1

[updateGangMemberContribution]
SQL1_1 = UPDATE eden_gang_members SET contribution = contribution + ? WHERE gang_id = ? AND player_id = ?
Number Of Inputs = 3
SQL1_INPUTS = 1,2,3

[getGangTerritories]
SQL1_1 = SELECT * FROM eden_gang_territories WHERE gang_id = ? AND active = 1
Number Of Inputs = 1
SQL1_INPUTS = 1

[getAllTerritories]
SQL1_1 = SELECT gt.*, g.name as gang_name, g.tag, g.color FROM eden_gang_territories gt LEFT JOIN eden_gangs g ON gt.gang_id = g.id WHERE gt.active = 1
Number Of Inputs = 0

[insertTerritory]
SQL1_1 = INSERT INTO eden_gang_territories (gang_id, name, position, radius, capture_time, payout_rate) VALUES (?, ?, ?, ?, ?, ?)
Number Of Inputs = 6
SQL1_INPUTS = 1,2,3,4,5,6

[updateTerritory]
SQL1_1 = UPDATE eden_gang_territories SET gang_id = ?, last_captured = NOW(), capture_count = capture_count + 1, contested = 0, contested_by = NULL WHERE id = ?
Number Of Inputs = 2
SQL1_INPUTS = 1,2

[contestTerritory]
SQL1_1 = UPDATE eden_gang_territories SET contested = 1, contested_by = ? WHERE id = ?
Number Of Inputs = 2
SQL1_INPUTS = 1,2

[deleteTerritory]
SQL1_1 = UPDATE eden_gang_territories SET active = 0 WHERE id = ?
Number Of Inputs = 1
SQL1_INPUTS = 1

[getGangRankings]
SQL1_1 = SELECT name, tag, experience, level, reputation, kills, deaths, territory_count, (kills - deaths) as kd_ratio FROM eden_gangs WHERE active = 1 ORDER BY experience DESC LIMIT ?
Number Of Inputs = 1
SQL1_INPUTS = 1

[searchGangs]
SQL1_1 = SELECT * FROM eden_gangs WHERE (name LIKE ? OR tag LIKE ?) AND active = 1 ORDER BY experience DESC LIMIT 20
Number Of Inputs = 2
SQL1_INPUTS = 1,2

[cleanupInactiveGangs]
SQL1_1 = UPDATE eden_gangs SET active = 0 WHERE last_active < DATE_SUB(NOW(), INTERVAL ? DAY)
Number Of Inputs = 1
SQL1_INPUTS = 1
