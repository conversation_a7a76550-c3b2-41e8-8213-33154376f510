/*
    EdenRP Get Charge Configuration
    Enhanced criminal charge system
*/

params [["_charge", "", [""]]];

if (_charge == "") exitWith {[]};

// Charge configuration database
// Format: [displayName, fine, jailTime, severity, description]
private _chargeConfigs = [
    // Traffic Violations
    ["speeding_minor", ["Minor Speeding", 150, 2, 1, "Exceeding speed limit by 10-20 km/h"]],
    ["speeding_major", ["Major Speeding", 300, 5, 2, "Exceeding speed limit by 20+ km/h"]],
    ["reckless_driving", ["Reckless Driving", 500, 8, 2, "Dangerous operation of a vehicle"]],
    ["hit_and_run", ["Hit and Run", 2000, 15, 3, "Leaving scene of an accident"]],
    ["dui", ["Driving Under Influence", 1500, 12, 3, "Operating vehicle while intoxicated"]],
    ["no_license", ["No License", 250, 3, 1, "Operating vehicle without proper license"]],
    ["expired_registration", ["Expired Registration", 100, 1, 1, "Vehicle registration expired"]],
    ["illegal_parking", ["Illegal Parking", 75, 0, 1, "Parking in restricted area"]],
    
    // Theft and Property Crimes
    ["petty_theft", ["Petty Theft", 500, 5, 2, "Theft of items under $1000"]],
    ["grand_theft", ["Grand Theft", 2500, 20, 4, "Theft of items over $1000"]],
    ["vehicle_theft", ["Vehicle Theft", 5000, 30, 4, "Unauthorized use of motor vehicle"]],
    ["breaking_entering", ["Breaking and Entering", 3000, 25, 4, "Unlawful entry into property"]],
    ["burglary", ["Burglary", 4000, 35, 5, "Breaking and entering with intent to steal"]],
    ["vandalism", ["Vandalism", 800, 8, 2, "Willful destruction of property"]],
    ["trespassing", ["Trespassing", 300, 5, 2, "Unlawful presence on private property"]],
    
    // Drug Offenses
    ["drug_possession_minor", ["Minor Drug Possession", 1000, 10, 3, "Possession of small amount of drugs"]],
    ["drug_possession_major", ["Major Drug Possession", 3000, 25, 4, "Possession of large amount of drugs"]],
    ["drug_trafficking", ["Drug Trafficking", 10000, 60, 5, "Distribution of illegal substances"]],
    ["drug_manufacturing", ["Drug Manufacturing", 15000, 90, 5, "Production of illegal substances"]],
    ["drug_paraphernalia", ["Drug Paraphernalia", 500, 5, 2, "Possession of drug-related equipment"]],
    
    // Weapons Offenses
    ["illegal_weapon", ["Illegal Weapon Possession", 2000, 15, 3, "Possession of prohibited weapon"]],
    ["concealed_weapon", ["Concealed Weapon", 1500, 12, 3, "Carrying concealed weapon without permit"]],
    ["weapon_trafficking", ["Weapon Trafficking", 8000, 45, 5, "Illegal distribution of weapons"]],
    ["brandishing", ["Brandishing Weapon", 1000, 8, 3, "Threatening with a weapon"]],
    ["discharge_weapon", ["Discharging Weapon", 2500, 20, 4, "Illegal discharge of firearm"]],
    
    // Violent Crimes
    ["assault", ["Assault", 1500, 15, 3, "Physical attack on another person"]],
    ["battery", ["Battery", 2000, 20, 4, "Unlawful physical contact"]],
    ["assault_officer", ["Assault on Officer", 5000, 40, 5, "Attack on law enforcement"]],
    ["kidnapping", ["Kidnapping", 10000, 80, 5, "Unlawful restraint and transport"]],
    ["murder", ["Murder", 25000, 120, 5, "Unlawful killing of another person"]],
    ["attempted_murder", ["Attempted Murder", 15000, 90, 5, "Attempted unlawful killing"]],
    ["domestic_violence", ["Domestic Violence", 3000, 25, 4, "Violence against family member"]],
    
    // Public Order
    ["disorderly_conduct", ["Disorderly Conduct", 200, 3, 1, "Disturbing the peace"]],
    ["public_intoxication", ["Public Intoxication", 300, 4, 2, "Being drunk in public"]],
    ["loitering", ["Loitering", 150, 2, 1, "Remaining in area without purpose"]],
    ["noise_violation", ["Noise Violation", 100, 1, 1, "Excessive noise disturbance"]],
    ["littering", ["Littering", 75, 0, 1, "Improper disposal of waste"]],
    ["jaywalking", ["Jaywalking", 50, 0, 1, "Improper street crossing"]],
    
    // Government and Official
    ["resisting_arrest", ["Resisting Arrest", 2000, 15, 3, "Interfering with lawful arrest"]],
    ["obstruction", ["Obstruction of Justice", 3000, 20, 4, "Interfering with investigation"]],
    ["contempt_court", ["Contempt of Court", 1500, 10, 3, "Disrespect to court proceedings"]],
    ["perjury", ["Perjury", 5000, 30, 4, "Lying under oath"]],
    ["bribery", ["Bribery", 8000, 45, 5, "Offering money for illegal favors"]],
    ["corruption", ["Corruption", 15000, 80, 5, "Abuse of official position"]],
    
    // Financial Crimes
    ["fraud", ["Fraud", 5000, 35, 4, "Deceptive practices for financial gain"]],
    ["embezzlement", ["Embezzlement", 8000, 50, 5, "Theft of entrusted funds"]],
    ["money_laundering", ["Money Laundering", 12000, 70, 5, "Concealing illegal money sources"]],
    ["tax_evasion", ["Tax Evasion", 6000, 40, 4, "Failure to pay required taxes"]],
    ["counterfeiting", ["Counterfeiting", 10000, 60, 5, "Creating fake currency or documents"]],
    
    // Organized Crime
    ["racketeering", ["Racketeering", 20000, 100, 5, "Organized criminal enterprise"]],
    ["extortion", ["Extortion", 8000, 50, 5, "Obtaining money through threats"]],
    ["conspiracy", ["Conspiracy", 5000, 35, 4, "Planning criminal activity"]],
    ["gang_activity", ["Gang Activity", 3000, 25, 4, "Participation in criminal organization"]],
    
    // Cyber Crimes
    ["hacking", ["Computer Hacking", 5000, 30, 4, "Unauthorized computer access"]],
    ["identity_theft", ["Identity Theft", 4000, 25, 4, "Stealing personal information"]],
    ["cyber_stalking", ["Cyber Stalking", 2000, 15, 3, "Online harassment"]],
    
    // Environmental
    ["pollution", ["Environmental Pollution", 2000, 10, 3, "Illegal contamination"]],
    ["illegal_dumping", ["Illegal Dumping", 1500, 8, 2, "Improper waste disposal"]],
    
    // Special Circumstances
    ["terrorism", ["Terrorism", 50000, 200, 5, "Acts of terror against civilians"]],
    ["treason", ["Treason", 100000, 300, 5, "Betrayal of country"]],
    ["war_crimes", ["War Crimes", 75000, 250, 5, "Violations of war conventions"]],
    
    // Regulatory Violations
    ["fishing_violation", ["Fishing Violation", 200, 2, 1, "Illegal fishing activity"]],
    ["hunting_violation", ["Hunting Violation", 300, 3, 1, "Illegal hunting activity"]],
    ["permit_violation", ["Permit Violation", 500, 5, 2, "Operating without required permit"]],
    ["zoning_violation", ["Zoning Violation", 1000, 5, 2, "Violation of area restrictions"]],
    
    // Emergency Services
    ["false_report", ["False Police Report", 1000, 8, 2, "Filing fake emergency report"]],
    ["emergency_lane", ["Emergency Lane Violation", 400, 3, 2, "Blocking emergency vehicles"]],
    ["ambulance_chasing", ["Ambulance Chasing", 800, 5, 2, "Following emergency vehicles"]]
];

// Find charge configuration
private _config = [];
{
    if ((_x select 0) == _charge) exitWith {
        _config = _x select 1;
    };
} forEach _chargeConfigs;

_config
