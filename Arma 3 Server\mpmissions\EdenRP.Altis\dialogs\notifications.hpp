/*
    File: notifications.hpp
    Author: EdenRP Development Team
    
    Description:
    Notification system dialog definitions for EdenRP
*/

class EdenRP_Notification {
    idd = 4100;
    name = "EdenRP_Notification";
    movingEnable = 0;
    enableSimulation = 0;
    duration = 5;
    fadeIn = 0.5;
    fadeOut = 0.5;
    
    class controlsBackground {
        class NotificationBackground: RscText {
            idc = -1;
            x = 0.7;
            y = 0.1;
            w = 0.28;
            h = 0.08;
            colorBackground[] = {0, 0, 0, 0.7};
        };
    };
    
    class controls {
        class NotificationTitle: RscText {
            idc = 4101;
            text = "Notification";
            x = 0.71;
            y = 0.11;
            w = 0.26;
            h = 0.03;
            colorText[] = {1, 1, 1, 1};
            sizeEx = 0.03;
            shadow = 2;
        };
        
        class NotificationText: RscText {
            idc = 4102;
            text = "";
            x = 0.71;
            y = 0.14;
            w = 0.26;
            h = 0.03;
            colorText[] = {0.9, 0.9, 0.9, 1};
            sizeEx = 0.025;
            shadow = 2;
        };
        
        class NotificationIcon: RscPicture {
            idc = 4103;
            text = "";
            x = 0.72;
            y = 0.105;
            w = 0.02;
            h = 0.02;
        };
    };
};

class EdenRP_NotificationSuccess: EdenRP_Notification {
    class controlsBackground: controlsBackground {
        class NotificationBackground: NotificationBackground {
            colorBackground[] = {0, 0.5, 0, 0.7};
        };
    };
};

class EdenRP_NotificationError: EdenRP_Notification {
    class controlsBackground: controlsBackground {
        class NotificationBackground: NotificationBackground {
            colorBackground[] = {0.5, 0, 0, 0.7};
        };
    };
};

class EdenRP_NotificationWarning: EdenRP_Notification {
    class controlsBackground: controlsBackground {
        class NotificationBackground: NotificationBackground {
            colorBackground[] = {0.5, 0.5, 0, 0.7};
        };
    };
};
