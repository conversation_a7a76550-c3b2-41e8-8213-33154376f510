/*
    File: fn_claimVehicle.sqf
    Author: EdenRP Development Team
    
    Description:
    Claims an abandoned vehicle or retrieves impounded vehicle.
    
    Parameters:
    0: OBJECT - Vehicle to claim
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if claim was successful
*/

params [
    ["_vehicle", obj<PERSON>ull, [obj<PERSON><PERSON>]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (isNull _vehicle || isNull _player) exitWith { false };

if (_player distance _vehicle > 10) exitWith {
    ["Vehicle is too far away!"] call EDEN_fnc_showHint;
    false
};

// Check if vehicle is impounded
_isImpounded = _vehicle getVariable ["eden_isImpounded", false];
_vehicleOwner = _vehicle getVariable ["eden_ownerUID", ""];
_playerUID = getPlayerUID _player;

if (_isImpounded) then {
    // Claiming impounded vehicle
    if (_vehicleOwner != _playerUID) exitWith {
        ["This is not your vehicle!"] call EDEN_fnc_showHint;
        false
    };
    
    // Calculate impound fee
    _impoundTime = time - (_vehicle getVariable ["eden_impoundTime", time]);
    _impoundFee = round((_impoundTime / 3600) * 500); // $500 per hour
    _impoundFee = _impoundFee max 1000; // Minimum $1000
    
    _playerMoney = _player getVariable ["eden_cash", 0];
    if (_playerMoney < _impoundFee) exitWith {
        [format ["Impound fee is $%1, but you only have $%2!", _impoundFee, _playerMoney]] call EDEN_fnc_showHint;
        false
    };
    
    // Pay fee and release vehicle
    _player setVariable ["eden_cash", (_playerMoney - _impoundFee), true];
    _vehicle setVariable ["eden_isImpounded", false, true];
    _vehicle setVariable ["eden_impoundedBy", "", true];
    _vehicle setVariable ["eden_impoundTime", 0, true];
    _vehicle lock 0;
    
    [format ["Vehicle claimed from impound for $%1", _impoundFee]] call EDEN_fnc_showHint;
    
} else {
    // Claiming abandoned vehicle
    if (_vehicleOwner != "") exitWith {
        ["This vehicle already has an owner!"] call EDEN_fnc_showHint;
        false
    };
    
    // Check if vehicle has been abandoned long enough (30 minutes)
    _lastUsed = _vehicle getVariable ["eden_lastUsed", time];
    if ((time - _lastUsed) < 1800) exitWith {
        ["Vehicle hasn't been abandoned long enough!"] call EDEN_fnc_showHint;
        false
    };
    
    // Calculate claim cost based on vehicle type
    _claimCost = switch (true) do {
        case (_vehicle isKindOf "Car"): { 2500 };
        case (_vehicle isKindOf "Truck"): { 5000 };
        case (_vehicle isKindOf "Helicopter"): { 25000 };
        case (_vehicle isKindOf "Plane"): { 50000 };
        default { 1000 };
    };
    
    _playerMoney = _player getVariable ["eden_cash", 0];
    if (_playerMoney < _claimCost) exitWith {
        [format ["Claiming this vehicle costs $%1, but you only have $%2!", _claimCost, _playerMoney]] call EDEN_fnc_showHint;
        false
    };
    
    // Claim vehicle
    _player setVariable ["eden_cash", (_playerMoney - _claimCost), true];
    _vehicle setVariable ["eden_ownerUID", _playerUID, true];
    _vehicle setVariable ["eden_ownerName", name _player, true];
    _vehicle lock 0;
    
    [format ["Vehicle claimed for $%1", _claimCost]] call EDEN_fnc_showHint;
};

// Log the action
[format ["[EDEN] Player %1 claimed vehicle %2", name _player, typeOf _vehicle], "INFO", "VEHICLE"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
