/*
    EdenRP Create New Player
    Enhanced new player creation system
    
    This function creates a new player record in the database
    with default values and proper initialization
*/

params [
    ["_uid", "", [""]],
    ["_name", "", [""]]
];

// Validate parameters
if (_uid == "" || _name == "") exitWith {
    ["Invalid parameters provided to createNewPlayer", "ERROR", "SESSION"] call EDEN_fnc_systemLogger;
    false
};

// Only run on server
if (hasInterface) exitWith {
    ["createNewPlayer called on client", "ERROR", "SESSION"] call EDEN_fnc_systemLogger;
    false
};

// Find player object
private _player = objNull;
{
    if (getPlayerUID _x == _uid) exitWith {
        _player = _x;
    };
} forEach allPlayers;

if (isNull _player) exitWith {
    [format["Player with UID %1 not found for new player creation", _uid], "ERROR", "SESSION"] call EDEN_fnc_systemLogger;
    false
};

// Log new player creation
[format["Creating new player record for %1 (%2)", _name, _uid], "INFO", "SESSION"] call EDEN_fnc_systemLogger;

// Set default values
private _defaultCash = 5000;
private _defaultBank = 15000;

// Apply default values to player
_player setVariable ["EDEN_Cash", _defaultCash, false];
_player setVariable ["EDEN_Bank", _defaultBank, false];
_player setVariable ["EDEN_XP", 0, false];
_player setVariable ["EDEN_Level", 1, false];
_player setVariable ["EDEN_Reputation", 0, false];
_player setVariable ["EDEN_PlayTime", 0, false];
_player setVariable ["EDEN_CopLevel", 0, false];
_player setVariable ["EDEN_MedicLevel", 0, false];
_player setVariable ["EDEN_AdminLevel", 0, false];
_player setVariable ["EDEN_DonatorLevel", 0, false];

// Set default licenses (empty)
_player setVariable ["EDEN_CopLicenses", [], false];
_player setVariable ["EDEN_CivLicenses", [], false];
_player setVariable ["EDEN_MedLicenses", [], false];

// Set default gear (empty)
_player setVariable ["EDEN_CopGear", [], false];
_player setVariable ["EDEN_MedGear", [], false];
_player setVariable ["EDEN_CivGear", [], false];

// Set default inventory (empty)
_player setVariable ["EDEN_VirtualInventory", [], false];
_player setVariable ["EDEN_PhysicalInventory", [], false];

// Set default skills and achievements (empty)
_player setVariable ["EDEN_Skills", [], false];
_player setVariable ["EDEN_Achievements", [], false];

// Set default settings
private _defaultSettings = [
    ["HUD_Enabled", true],
    ["Notifications_Enabled", true],
    ["Sound_Enabled", true],
    ["Music_Enabled", true],
    ["Chat_Enabled", true],
    ["Radio_Enabled", true],
    ["Phone_Enabled", true],
    ["GPS_Enabled", true],
    ["Compass_Enabled", true],
    ["Speedometer_Enabled", true]
];
_player setVariable ["EDEN_Settings", _defaultSettings, false];

// Set default criminal status
_player setVariable ["EDEN_IsArrested", false, true];
_player setVariable ["EDEN_JailTime", 0, false];
_player setVariable ["EDEN_WantedLevel", 0, true];
_player setVariable ["EDEN_Bounty", 0, true];

// Set default gang data
_player setVariable ["EDEN_GangID", -1, false];
_player setVariable ["EDEN_GangRank", 0, false];

// Generate phone number
private _phoneNumber = [8] call EDEN_fnc_generatePhoneNumber;
_player setVariable ["EDEN_PhoneNumber", _phoneNumber, false];

// Set default communication data
_player setVariable ["EDEN_Contacts", [], false];
_player setVariable ["EDEN_Messages", [], false];

// Set session tracking
_player setVariable ["EDEN_SessionStart", time, false];
_player setVariable ["EDEN_TotalLogins", 1, false];

// Insert into database
private _query = format [
    "EDEN_Players:insertPlayer:%1:%2:%3:%4",
    _uid,
    _name,
    _defaultCash,
    _defaultBank
];

private _queryId = [_query, 1] call EDEN_fnc_asyncCall;

// Store query info for callback
if (isNil "EDEN_PendingQueries") then {
    EDEN_PendingQueries = [];
};

EDEN_PendingQueries pushBack [
    _queryId,
    "insertPlayer",
    [_uid, _name],
    time
];

// Give new player starting items
[_player] call EDEN_fnc_giveStartingItems;

// Show welcome message
["Welcome to EdenRP! You have been given starting money and basic items.", "success"] remoteExec ["EDEN_fnc_showNotification", _player];
["Use the Y menu to access your inventory, phone, and other features.", "info"] remoteExec ["EDEN_fnc_showNotification", _player];

// Log transaction for starting money
[_uid, "STARTING_MONEY", _defaultCash + _defaultBank, 0, _defaultCash + _defaultBank, "New player starting funds"] call EDEN_fnc_logTransaction;

// Update player count
[] call EDEN_fnc_updatePlayerCount;

[format["New player record created for %1 (Query ID: %2)", _name, _queryId], "DEBUG", "SESSION"] call EDEN_fnc_systemLogger;

true
