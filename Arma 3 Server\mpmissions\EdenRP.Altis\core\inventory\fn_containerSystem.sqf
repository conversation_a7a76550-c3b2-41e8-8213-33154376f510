/*
    File: fn_containerSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages container and storage system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_container", objNull, [objNull]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_containers") then {
            eden_containers = [];
            publicVariable "eden_containers";
        };
        _player setVariable ["eden_ownedContainers", [], true];
        true
    };
    case "createContainer": {
        params ["", "", "", ["_containerType", "small_box", [""]], ["_position", [0,0,0], [[]]]];
        
        _containerSizes = [
            ["small_box", 50, 500],
            ["large_box", 100, 1000],
            ["safe", 25, 2000],
            ["warehouse", 500, 10000]
        ];
        
        _containerData = [];
        {
            if ((_x select 0) == _containerType) then { _containerData = _x; };
        } forEach _containerSizes;
        
        if (count _containerData == 0) exitWith {
            ["Invalid container type"] call EDEN_fnc_showHint;
            false
        };
        
        _capacity = _containerData select 1;
        _cost = _containerData select 2;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _containerObj = createVehicle ["Land_CargoBox_V1_F", _position, [], 0, "NONE"];
        _containerID = str(random 999999);
        
        _containerObj setVariable ["eden_containerID", _containerID, true];
        _containerObj setVariable ["eden_owner", getPlayerUID _player, true];
        _containerObj setVariable ["eden_capacity", _capacity, true];
        _containerObj setVariable ["eden_contents", [], true];
        _containerObj setVariable ["eden_locked", false, true];
        
        _newContainer = [_containerID, getPlayerUID _player, _containerType, _capacity, [], _position, time];
        eden_containers pushBack _newContainer;
        publicVariable "eden_containers";
        
        _owned = _player getVariable ["eden_ownedContainers", []];
        _owned pushBack _containerID;
        _player setVariable ["eden_ownedContainers", _owned, true];
        
        [format ["Created %1 container for $%2", _containerType, _cost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "storeItem": {
        params ["", "", "", ["_item", "", [""]], ["_quantity", 1, [0]]];
        
        if (isNull _container) exitWith {
            ["No container selected"] call EDEN_fnc_showHint;
            false
        };
        
        _owner = _container getVariable ["eden_owner", ""];
        if (_owner != getPlayerUID _player) exitWith {
            ["You don't own this container"] call EDEN_fnc_showHint;
            false
        };
        
        _locked = _container getVariable ["eden_locked", false];
        if (_locked) exitWith {
            ["Container is locked"] call EDEN_fnc_showHint;
            false
        };
        
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _hasItem = false;
        _playerQuantity = 0;
        
        {
            if ((_x select 0) == _item) then {
                _hasItem = true;
                _playerQuantity = _x select 1;
            };
        } forEach _virtualItems;
        
        if (!_hasItem || _playerQuantity < _quantity) exitWith {
            ["Insufficient items to store"] call EDEN_fnc_showHint;
            false
        };
        
        _contents = _container getVariable ["eden_contents", []];
        _capacity = _container getVariable ["eden_capacity", 50];
        
        _currentWeight = 0;
        {
            _itemWeight = 1; // Default weight
            {
                if ((_x select 0) == (_contents select _forEachIndex select 0)) then {
                    _itemWeight = _x select 2;
                };
            } forEach eden_itemDatabase;
            _currentWeight = _currentWeight + (_itemWeight * ((_contents select _forEachIndex) select 1));
        } forEach _contents;
        
        _itemWeight = 1;
        {
            if ((_x select 0) == _item) then { _itemWeight = _x select 2; };
        } forEach eden_itemDatabase;
        
        if ((_currentWeight + (_itemWeight * _quantity)) > _capacity) exitWith {
            ["Container is full"] call EDEN_fnc_showHint;
            false
        };
        
        // Remove from player
        [_player, "removeItem", _item, _quantity] call EDEN_fnc_itemSystem;
        
        // Add to container
        _found = false;
        {
            if ((_x select 0) == _item) then {
                _x set [1, ((_x select 1) + _quantity)];
                _contents set [_forEachIndex, _x];
                _found = true;
            };
        } forEach _contents;
        
        if (!_found) then {
            _contents pushBack [_item, _quantity];
        };
        
        _container setVariable ["eden_contents", _contents, true];
        
        [format ["Stored %1x %2 in container", _quantity, _item]] call EDEN_fnc_showHint;
        true
    };
    case "retrieveItem": {
        params ["", "", "", ["_item", "", [""]], ["_quantity", 1, [0]]];
        
        if (isNull _container) exitWith { false };
        
        _owner = _container getVariable ["eden_owner", ""];
        if (_owner != getPlayerUID _player) exitWith {
            ["You don't own this container"] call EDEN_fnc_showHint;
            false
        };
        
        _contents = _container getVariable ["eden_contents", []];
        _itemIndex = -1;
        _availableQuantity = 0;
        
        {
            if ((_x select 0) == _item) then {
                _itemIndex = _forEachIndex;
                _availableQuantity = _x select 1;
            };
        } forEach _contents;
        
        if (_itemIndex == -1 || _availableQuantity < _quantity) exitWith {
            ["Insufficient items in container"] call EDEN_fnc_showHint;
            false
        };
        
        // Add to player
        [_player, "addItem", _item, _quantity] call EDEN_fnc_itemSystem;
        
        // Remove from container
        if ((_availableQuantity - _quantity) <= 0) then {
            _contents deleteAt _itemIndex;
        } else {
            (_contents select _itemIndex) set [1, (_availableQuantity - _quantity)];
        };
        
        _container setVariable ["eden_contents", _contents, true];
        
        [format ["Retrieved %1x %2 from container", _quantity, _item]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
