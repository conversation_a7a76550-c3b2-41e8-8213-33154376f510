/*
    File: fn_initializeLogging.sqf
    Author: EdenRP Development Team
    
    Description:
    Initializes the logging system for EdenRP server
*/

// Initialize logging variables
EDEN_LoggingEnabled = true;
EDEN_LogLevel = 2; // 0=Error, 1=Warning, 2=Info, 3=Debug
EDEN_LogToFile = true;
EDEN_LogToConsole = true;

// Create log directory if it doesn't exist
EDEN_LogDirectory = "logs\";

// Initialize log file with timestamp
private _timestamp = format ["%1-%2-%3_%4-%5-%6", 
    date select 0, 
    date select 1, 
    date select 2, 
    date select 3, 
    date select 4, 
    floor(date select 5)
];

EDEN_LogFile = format ["edenrp_%1.log", _timestamp];

// Log initialization message
[format["Logging system initialized - File: %1", EDEN_LogFile], "INFO", "SYSTEM"] call EDEN_fnc_systemLogger;

true
