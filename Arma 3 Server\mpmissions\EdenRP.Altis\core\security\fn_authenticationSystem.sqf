/*
    File: fn_authenticationSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages authentication system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isServer) then {
            if (isNil "eden_authenticatedPlayers") then {
                eden_authenticatedPlayers = [];
                publicVariable "eden_authenticatedPlayers";
            };
            if (isNil "eden_authenticationTokens") then {
                eden_authenticationTokens = [];
            };
        };
        true
    };
    case "authenticate": {
        params ["", "", ["_password", "", [""]]];
        
        if (!isServer) exitWith { false };
        
        _playerUID = getPlayerUID _player;
        _hashedPassword = ["hash", _password] call EDEN_fnc_encryptionSystem;
        
        // Check if player is already authenticated
        if (_playerUID in eden_authenticatedPlayers) exitWith { true };
        
        // For demo purposes, accept any non-empty password
        if (_password != "") then {
            eden_authenticatedPlayers pushBack _playerUID;
            publicVariable "eden_authenticatedPlayers";
            
            _token = ["generateToken", 32] call EDEN_fnc_encryptionSystem;
            eden_authenticationTokens pushBack [_playerUID, _token, time + 3600];
            
            _player setVariable ["eden_authToken", _token, true];
            
            [format ["[AUTH] Player %1 authenticated successfully", name _player], "INFO", "AUTH"] call EDEN_fnc_systemLogger;
            true
        } else {
            [format ["[AUTH] Authentication failed for %1", name _player], "WARNING", "AUTH"] call EDEN_fnc_systemLogger;
            false
        };
    };
    case "isAuthenticated": {
        _playerUID = getPlayerUID _player;
        _playerUID in eden_authenticatedPlayers
    };
    case "validateToken": {
        params ["", "", ["_token", "", [""]]];
        
        if (!isServer) exitWith { false };
        
        _playerUID = getPlayerUID _player;
        _isValid = false;
        
        {
            if ((_x select 0) == _playerUID && (_x select 1) == _token && (_x select 2) > time) then {
                _isValid = true;
            };
        } forEach eden_authenticationTokens;
        
        _isValid
    };
    case "logout": {
        if (!isServer) exitWith { false };
        
        _playerUID = getPlayerUID _player;
        
        // Remove from authenticated players
        for "_i" from (count eden_authenticatedPlayers - 1) to 0 step -1 do {
            if ((eden_authenticatedPlayers select _i) == _playerUID) then {
                eden_authenticatedPlayers deleteAt _i;
            };
        };
        publicVariable "eden_authenticatedPlayers";
        
        // Remove authentication tokens
        for "_i" from (count eden_authenticationTokens - 1) to 0 step -1 do {
            if (((eden_authenticationTokens select _i) select 0) == _playerUID) then {
                eden_authenticationTokens deleteAt _i;
            };
        };
        
        _player setVariable ["eden_authToken", "", true];
        
        [format ["[AUTH] Player %1 logged out", name _player], "INFO", "AUTH"] call EDEN_fnc_systemLogger;
        true
    };
    case "cleanupExpiredTokens": {
        if (!isServer) exitWith { false };
        
        for "_i" from (count eden_authenticationTokens - 1) to 0 step -1 do {
            _tokenData = eden_authenticationTokens select _i;
            if ((_tokenData select 2) <= time) then {
                eden_authenticationTokens deleteAt _i;
                
                _playerUID = _tokenData select 0;
                for "_j" from (count eden_authenticatedPlayers - 1) to 0 step -1 do {
                    if ((eden_authenticatedPlayers select _j) == _playerUID) then {
                        eden_authenticatedPlayers deleteAt _j;
                    };
                };
            };
        };
        publicVariable "eden_authenticatedPlayers";
        
        true
    };
    case "requireAuth": {
        params ["", "", ["_actionName", "unknown", [""]]];
        
        _isAuth = [_player, "isAuthenticated"] call EDEN_fnc_authenticationSystem;
        
        if (!_isAuth) then {
            [format ["Authentication required for %1", _actionName]] call EDEN_fnc_showHint;
        };
        
        _isAuth
    };
    default { false };
};
