/*
    File: fn_repairSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages item repair and maintenance system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_item", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_repairSkill", 1, true];
        _player setVariable ["eden_repairXP", 0, true];
        true
    };
    case "repairItem": {
        params ["", "", "", ["_itemIndex", 0, [0]]];
        
        _durabilityArray = _player getVariable ["eden_itemDurability", []];
        if (_itemIndex >= count _durabilityArray) exitWith {
            ["Invalid item index"] call EDEN_fnc_showHint;
            false
        };
        
        _currentDurability = _durabilityArray select _itemIndex;
        if (_currentDurability >= 100) exitWith {
            ["Item is already in perfect condition"] call EDEN_fnc_showHint;
            false
        };
        
        _repairCost = floor((100 - _currentDurability) * 5);
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _repairCost) exitWith {
            [format ["Not enough money! Need $%1", _repairCost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _repairCost), true];
        
        _skill = _player getVariable ["eden_repairSkill", 1];
        _repairAmount = 20 + (_skill * 5);
        _newDurability = (_currentDurability + _repairAmount) min 100;
        
        _durabilityArray set [_itemIndex, _newDurability];
        _player setVariable ["eden_itemDurability", _durabilityArray, true];
        
        // Add XP
        _xp = _player getVariable ["eden_repairXP", 0];
        _player setVariable ["eden_repairXP", (_xp + 15), true];
        
        _requiredXP = _skill * 150;
        if (_xp >= _requiredXP) then {
            _player setVariable ["eden_repairSkill", (_skill + 1), true];
            _player setVariable ["eden_repairXP", 0, true];
            [format ["Repair skill increased to level %1", (_skill + 1)]] call EDEN_fnc_showHint;
        };
        
        [format ["Item repaired to %1%% for $%2", _newDurability, _repairCost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "massRepair": {
        _durabilityArray = _player getVariable ["eden_itemDurability", []];
        _totalCost = 0;
        _repairedCount = 0;
        
        {
            if (_x < 100) then {
                _repairCost = floor((100 - _x) * 5);
                _totalCost = _totalCost + _repairCost;
                _repairedCount = _repairedCount + 1;
            };
        } forEach _durabilityArray;
        
        if (_repairedCount == 0) exitWith {
            ["All items are in perfect condition"] call EDEN_fnc_showHint;
            false
        };
        
        _cash = _player getVariable ["eden_cash", 0];
        if (_cash < _totalCost) exitWith {
            [format ["Not enough money! Need $%1", _totalCost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _totalCost), true];
        
        // Repair all items
        for "_i" from 0 to (count _durabilityArray - 1) do {
            _durabilityArray set [_i, 100];
        };
        _player setVariable ["eden_itemDurability", _durabilityArray, true];
        
        [format ["Repaired %1 items for $%2", _repairedCount, _totalCost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
