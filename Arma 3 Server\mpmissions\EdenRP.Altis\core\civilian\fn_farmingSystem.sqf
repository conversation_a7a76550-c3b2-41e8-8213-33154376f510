/*
    File: fn_farmingSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages farming system for civilians.
*/

params [["_player", player, [objNull]], ["_action", "init", [""]], ["_crop", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_farmingLevel", 1, true];
        _player setVariable ["eden_cropsHarvested", 0, true];
        _player setVariable ["eden_ownedFields", [], true];
        true
    };
    case "plant": {
        if (!(_player getVariable ["eden_farmingLicense", false])) exitWith {
            ["You need a farming license!"] call EDEN_fnc_showHint;
            false
        };
        
        _crops = ["wheat", "corn", "potatoes", "tomatoes"];
        if !(_crop in _crops) exitWith {
            ["Invalid crop type!"] call EDEN_fnc_showHint;
            false
        };
        
        _seedCost = 50;
        _cash = _player getVariable ["eden_cash", 0];
        if (_cash < _seedCost) exitWith {
            ["Not enough money for seeds!"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _seedCost), true];
        
        _fields = _player getVariable ["eden_ownedFields", []];
        _fieldId = count _fields;
        _plantTime = time;
        _growthTime = 300 + (random 120); // 5-7 minutes
        
        _fields pushBack [_fieldId, _crop, _plantTime, _growthTime, false];
        _player setVariable ["eden_ownedFields", _fields, true];
        
        [format ["Planted %1 seeds for $%2", _crop, _seedCost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "harvest": {
        _fields = _player getVariable ["eden_ownedFields", []];
        _harvestedAny = false;
        
        {
            _fieldData = _x;
            _plantTime = _fieldData select 2;
            _growthTime = _fieldData select 3;
            _harvested = _fieldData select 4;
            
            if (!_harvested && (time >= (_plantTime + _growthTime))) then {
                _cropType = _fieldData select 1;
                _level = _player getVariable ["eden_farmingLevel", 1];
                _yield = 3 + floor(random 5) + floor(_level / 2);
                
                _virtualItems = _player getVariable ["eden_virtualItems", []];
                _found = false;
                {
                    if ((_x select 0) == _cropType) then {
                        _x set [1, ((_x select 1) + _yield)];
                        _found = true;
                    };
                } forEach _virtualItems;
                
                if (!_found) then {
                    _virtualItems pushBack [_cropType, _yield];
                };
                
                _player setVariable ["eden_virtualItems", _virtualItems, true];
                
                _fieldData set [4, true];
                _harvestedAny = true;
                
                _harvested = _player getVariable ["eden_cropsHarvested", 0];
                _player setVariable ["eden_cropsHarvested", (_harvested + _yield), true];
                
                if ((_harvested % 20) == 0) then {
                    _newLevel = _player getVariable ["eden_farmingLevel", 1];
                    _player setVariable ["eden_farmingLevel", (_newLevel + 1), true];
                    [format ["Farming level increased to %1!", _newLevel + 1]] call EDEN_fnc_showHint;
                };
                
                [format ["Harvested %1x %2!", _yield, _cropType]] call EDEN_fnc_showHint;
            };
        } forEach _fields;
        
        if (_harvestedAny) then {
            _player setVariable ["eden_ownedFields", _fields, true];
            [_player] call EDEN_fnc_savePlayerData;
        } else {
            ["No crops ready for harvest."] call EDEN_fnc_showHint;
        };
        
        _harvestedAny
    };
    default { false };
};
