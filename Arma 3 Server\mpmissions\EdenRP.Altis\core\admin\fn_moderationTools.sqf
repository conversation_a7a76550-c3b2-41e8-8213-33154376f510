/*
    File: fn_moderationTools.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages moderation tools for admins.
*/

params [["_admin", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON><PERSON>]]];

if (isNull _admin) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_adminList") then {
            eden_adminList = [];
            publicVariable "eden_adminList";
        };
        true
    };
    case "kickPlayer": {
        params ["", "", "", ["_reason", "Admin kick", [""]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith {
            ["Insufficient permissions"] call EDEN_fnc_showHint;
            false
        };
        
        if (isNull _target) exitWith { false };
        
        [format ["Player %1 kicked by %2. Reason: %3", name _target, name _admin, _reason], "ADMIN", "MODERATION"] call EDEN_fnc_systemLogger;
        
        [format ["You have been kicked. Reason: %1", _reason]] remoteExec ["EDEN_fnc_showHint", _target];
        [_target] spawn { sleep 3; kickPlayer (_this select 0); };
        
        [format ["Kicked %1", name _target]] call EDEN_fnc_showHint;
        true
    };
    case "freezePlayer": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        if (isNull _target) exitWith { false };
        
        _target enableSimulation false;
        _target setVariable ["eden_frozen", true, true];
        
        ["You have been frozen by an admin"] remoteExec ["EDEN_fnc_showHint", _target];
        [format ["Froze %1", name _target]] call EDEN_fnc_showHint;
        true
    };
    case "unfreezePlayer": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        if (isNull _target) exitWith { false };
        
        _target enableSimulation true;
        _target setVariable ["eden_frozen", false, true];
        
        ["You have been unfrozen"] remoteExec ["EDEN_fnc_showHint", _target];
        [format ["Unfroze %1", name _target]] call EDEN_fnc_showHint;
        true
    };
    case "mutePlayer": {
        params ["", "", "", ["_duration", 300, [0]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        if (isNull _target) exitWith { false };
        
        _target setVariable ["eden_muted", true, true];
        _target setVariable ["eden_muteEnd", (time + _duration), true];
        
        [format ["You have been muted for %1 seconds", _duration]] remoteExec ["EDEN_fnc_showHint", _target];
        [format ["Muted %1 for %2 seconds", name _target, _duration]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
