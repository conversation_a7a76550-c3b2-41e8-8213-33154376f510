/*
    File: fn_openInventory.sqf
    Author: EdenRP Development Team
    
    Description:
    Opens the player's virtual inventory.
    
    Parameters:
    0: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if inventory was opened successfully
*/

params [["_player", player, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

// Create inventory dialog
createDialog "EdenRP_Inventory";

// Get the dialog
_dialog = findDisplay 3100;
if (isNull _dialog) exitWith {
    ["Failed to open inventory!"] call EDEN_fnc_showHint;
    false
};

// Get player inventory data
_virtualItems = _player getVariable ["eden_virtualItems", []];
_currentWeight = _player getVariable ["eden_currentWeight", 0];
_maxWeight = _player getVariable ["eden_maxWeight", 50];

// Set weight display
_weightCtrl = _dialog displayCtrl 3101;
_weightCtrl ctrlSetText format ["Weight: %1/%2 kg", _currentWeight, _maxWeight];

// Set weight progress bar
_weightBar = _dialog displayCtrl 3102;
_weightBar progressSetPosition (_currentWeight / _maxWeight);

// Populate inventory list
_inventoryList = _dialog displayCtrl 3103;
lbClear _inventoryList;

{
    _itemName = _x select 0;
    _quantity = _x select 1;
    
    // Get item display name
    _displayName = switch (_itemName) do {
        case "water_bottle": { "Water Bottle" };
        case "apple": { "Apple" };
        case "bread": { "Bread" };
        case "toolkit": { "Toolkit" };
        case "first_aid_kit": { "First Aid Kit" };
        case "lockpick": { "Lockpick" };
        case "rope": { "Rope" };
        case "zipties": { "Zip Ties" };
        case "phone": { "Mobile Phone" };
        case "radio": { "Radio" };
        case "copper": { "Copper Ore" };
        case "iron": { "Iron Ore" };
        case "diamond": { "Diamond" };
        case "stone": { "Stone" };
        case "sand": { "Sand" };
        case "oil": { "Oil" };
        case "copper_ingot": { "Copper Ingot" };
        case "iron_ingot": { "Iron Ingot" };
        case "processed_diamond": { "Processed Diamond" };
        default { _itemName };
    };
    
    _index = _inventoryList lbAdd format ["%1 x%2", _displayName, _quantity];
    _inventoryList lbSetData [_index, _itemName];
} forEach _virtualItems;

// Add item action buttons
_useBtn = _dialog displayCtrl 3104;
_dropBtn = _dialog displayCtrl 3105;
_giveBtn = _dialog displayCtrl 3106;

// Use item button
_useBtn ctrlAddEventHandler ["ButtonClick", {
    _dialog = findDisplay 3100;
    _inventoryList = _dialog displayCtrl 3103;
    _selectedIndex = lbCurSel _inventoryList;
    
    if (_selectedIndex >= 0) then {
        _itemName = _inventoryList lbData _selectedIndex;
        [_itemName] call EDEN_fnc_useItem;
        closeDialog 0;
        [] call EDEN_fnc_openInventory; // Refresh inventory
    } else {
        ["Select an item first!"] call EDEN_fnc_showHint;
    };
}];

// Drop item button
_dropBtn ctrlAddEventHandler ["ButtonClick", {
    _dialog = findDisplay 3100;
    _inventoryList = _dialog displayCtrl 3103;
    _selectedIndex = lbCurSel _inventoryList;
    
    if (_selectedIndex >= 0) then {
        _itemName = _inventoryList lbData _selectedIndex;
        [_itemName, 1] call EDEN_fnc_dropItem;
        closeDialog 0;
        [] call EDEN_fnc_openInventory; // Refresh inventory
    } else {
        ["Select an item first!"] call EDEN_fnc_showHint;
    };
}];

// Give item button
_giveBtn ctrlAddEventHandler ["ButtonClick", {
    _dialog = findDisplay 3100;
    _inventoryList = _dialog displayCtrl 3103;
    _selectedIndex = lbCurSel _inventoryList;
    
    if (_selectedIndex >= 0) then {
        _itemName = _inventoryList lbData _selectedIndex;
        
        // Find nearest player
        _nearestPlayer = objNull;
        _nearestDistance = 999;
        {
            if (_x != player && alive _x && _x isKindOf "Man") then {
                _distance = player distance _x;
                if (_distance < _nearestDistance && _distance < 5) then {
                    _nearestPlayer = _x;
                    _nearestDistance = _distance;
                };
            };
        } forEach allPlayers;
        
        if (!isNull _nearestPlayer) then {
            [_nearestPlayer, _itemName, 1] call EDEN_fnc_giveItem;
            closeDialog 0;
            [] call EDEN_fnc_openInventory; // Refresh inventory
        } else {
            ["No player nearby to give item to!"] call EDEN_fnc_showHint;
        };
    } else {
        ["Select an item first!"] call EDEN_fnc_showHint;
    };
}];

// Close button
_closeBtn = _dialog displayCtrl 3107;
_closeBtn ctrlAddEventHandler ["ButtonClick", {
    closeDialog 0;
}];

// Log inventory access
[format ["[EDEN] Player %1 opened inventory", name _player], "DEBUG", "UI"] call EDEN_fnc_systemLogger;

true
