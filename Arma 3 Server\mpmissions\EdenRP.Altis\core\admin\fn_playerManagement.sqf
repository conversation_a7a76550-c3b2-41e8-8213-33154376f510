/*
    File: fn_playerManagement.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages player administration.
*/

params [["_admin", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON><PERSON>]]];

if (isNull _admin) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_playerDatabase") then {
            eden_playerDatabase = [];
            publicVariable "eden_playerDatabase";
        };
        true
    };
    case "resetPlayer": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        if (isNull _target) exitWith { false };
        
        _target setVariable ["eden_cash", 5000, true];
        _target setVariable ["eden_bankAccount", 25000, true];
        _target setVariable ["eden_virtualItems", [], true];
        _target setVariable ["eden_licenses", [], true];
        _target setVariable ["eden_job", "civilian", true];
        
        ["Your character has been reset by an admin"] remoteExec ["EDEN_fnc_showHint", _target];
        [format ["Reset player %1", name _target]] call EDEN_fnc_showHint;
        [_target] call EDEN_fnc_savePlayerData;
        true
    };
    case "giveMoney": {
        params ["", "", "", ["_amount", 1000, [0]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        if (isNull _target) exitWith { false };
        
        _cash = _target getVariable ["eden_cash", 0];
        _target setVariable ["eden_cash", (_cash + _amount), true];
        
        [format ["Admin gave you $%1", _amount]] remoteExec ["EDEN_fnc_showHint", _target];
        [format ["Gave $%1 to %2", _amount, name _target]] call EDEN_fnc_showHint;
        [_target] call EDEN_fnc_savePlayerData;
        true
    };
    case "setJob": {
        params ["", "", "", ["_job", "civilian", [""]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        if (isNull _target) exitWith { false };
        
        _target setVariable ["eden_job", _job, true];
        
        [format ["Your job has been set to %1 by an admin", _job]] remoteExec ["EDEN_fnc_showHint", _target];
        [format ["Set %1's job to %2", name _target, _job]] call EDEN_fnc_showHint;
        [_target] call EDEN_fnc_savePlayerData;
        true
    };
    case "teleportTo": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        if (isNull _target) exitWith { false };
        
        _admin setPos (getPos _target);
        [format ["Teleported to %1", name _target]] call EDEN_fnc_showHint;
        true
    };
    case "teleportHere": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        if (isNull _target) exitWith { false };
        
        _target setPos (getPos _admin);
        ["You have been teleported by an admin"] remoteExec ["EDEN_fnc_showHint", _target];
        [format ["Teleported %1 to you", name _target]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
