/*
    File: fn_translationSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages translation system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_languages", ["english"], true];
        _player setVariable ["eden_currentLanguage", "english", true];
        true
    };
    case "translate": {
        params ["", "", ["_text", "", [""]], ["_fromLang", "english", [""]], ["_toLang", "spanish", [""]]];
        
        _languages = _player getVariable ["eden_languages", ["english"]];
        if (!(_toLang in _languages)) exitWith {
            ["You don't know that language"] call EDEN_fnc_showHint;
            false
        };
        
        _translatedText = format["[%1] %2", _toLang, _text];
        [_translatedText] call EDEN_fnc_showHint;
        true
    };
    case "learnLanguage": {
        params ["", "", ["_language", "spanish", [""]]];
        
        _cost = 5000;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _languages = _player getVariable ["eden_languages", ["english"]];
        if (_language in _languages) exitWith {
            ["You already know this language"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        _languages pushBack _language;
        _player setVariable ["eden_languages", _languages, true];
        
        [format ["Learned %1 language for $%2", _language, _cost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
