/*
    File: fn_debugTools.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages admin debug tools.
*/

params [["_admin", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _admin) exitWith { false };

switch (_action) do {
    case "init": {
        _admin setVariable ["eden_debugMode", false, true];
        true
    };
    case "toggleDebug": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _debugMode = _admin getVariable ["eden_debugMode", false];
        _admin setVariable ["eden_debugMode", !_debugMode, true];
        
        if (!_debugMode) then {
            ["Debug mode enabled"] call EDEN_fnc_showHint;
        } else {
            ["Debug mode disabled"] call EDEN_fnc_showHint;
        };
        
        true
    };
    case "showPlayerInfo": {
        params ["", "", ["_target", obj<PERSON>ull, [objNull]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        if (isNull _target) exitWith { false };
        
        _info = format["Player: %1\nUID: %2\nCash: $%3\nBank: $%4\nJob: %5\nPosition: %6", 
            name _target,
            getPlayerUID _target,
            _target getVariable ["eden_cash", 0],
            _target getVariable ["eden_bankAccount", 0],
            _target getVariable ["eden_job", "civilian"],
            getPos _target
        ];
        
        [_info] call EDEN_fnc_showHint;
        true
    };
    case "showServerInfo": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _info = format["Server Info:\nPlayers: %1\nUptime: %2 min\nFPS: %3\nMemory: %4 MB", 
            count allPlayers,
            floor(time/60),
            floor(diag_fps),
            floor(diag_memoryUsage)
        ];
        
        [_info] call EDEN_fnc_showHint;
        true
    };
    case "executeCode": {
        params ["", "", ["_code", "", [""]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        if (_code == "") exitWith { false };
        
        _result = call compile _code;
        [format ["Code executed. Result: %1", _result]] call EDEN_fnc_showHint;
        
        [format ["[EDEN] Admin %1 executed code: %2", name _admin, _code], "ADMIN", "DEBUG"] call EDEN_fnc_systemLogger;
        true
    };
    case "fixPlayer": {
        params ["", "", ["_target", objNull, [objNull]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        if (isNull _target) exitWith { false };
        
        // Reset player state
        _target setDamage 0;
        _target setFatigue 0;
        _target setVariable ["eden_frozen", false, true];
        _target enableSimulation true;
        _target setCaptive false;
        
        ["Player state fixed"] remoteExec ["EDEN_fnc_showHint", _target];
        [format ["Fixed player %1", name _target]] call EDEN_fnc_showHint;
        true
    };
    case "dumpVariables": {
        params ["", "", ["_target", objNull, [objNull]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        if (isNull _target) exitWith { false };
        
        _variables = allVariables _target;
        _output = "Player Variables:\n";
        
        {
            _value = _target getVariable [_x, "undefined"];
            _output = _output + format["%1: %2\n", _x, _value];
        } forEach _variables;
        
        [_output] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
