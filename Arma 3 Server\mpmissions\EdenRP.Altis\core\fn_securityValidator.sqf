/*
    File: fn_securityValidator.sqf
    Author: EdenRP Development Team
    
    Description:
    Validates player actions and prevents exploits/cheating.
    
    Parameters:
    0: OBJECT - Player object
    1: STRING - Action type to validate
    2: ARRAY - Additional parameters (optional)
    
    Returns:
    BOOLEAN - True if action is valid, false if blocked
    
    Example:
    [player, "money_transfer", [5000]] call EDEN_fnc_securityValidator;
*/

params [
    ["_player", player, [objNull]],
    ["_actionType", "", [""]],
    ["_params", [], [[]]]
];

// Validate parameters
if (isNull _player || _actionType == "") exitWith {
    ["[EDEN] fn_securityValidator: Invalid parameters"] call EDEN_fnc_systemLogger;
    false
};

// Initialize security tracking if not exists
if (isNil {_player getVariable "eden_securityData"}) then {
    _player setVariable ["eden_securityData", createHashMap, true];
};

_securityData = _player getVariable "eden_securityData";

// Get current time
_currentTime = time;

// Security validation based on action type
_isValid = true;
_reason = "";

switch (toLower _actionType) do {
    case "money_transfer": {
        _params params [["_amount", 0, [0]]];
        
        // Check for negative amounts
        if (_amount <= 0) then {
            _isValid = false;
            _reason = "Invalid transfer amount";
        };
        
        // Check for excessive amounts
        if (_amount > 10000000) then {
            _isValid = false;
            _reason = "Transfer amount too large";
        };
        
        // Rate limiting - max 10 transfers per minute
        _transfers = _securityData getOrDefault ["money_transfers", []];
        _recentTransfers = _transfers select {_x > (_currentTime - 60)};
        
        if (count _recentTransfers >= 10) then {
            _isValid = false;
            _reason = "Too many money transfers";
        };
        
        // Update tracking
        if (_isValid) then {
            _transfers pushBack _currentTime;
            if (count _transfers > 20) then { _transfers deleteAt 0; };
            _securityData set ["money_transfers", _transfers];
        };
    };
    
    case "item_spawn": {
        _params params [["_itemClass", "", [""]], ["_quantity", 1, [0]]];
        
        // Only admins can spawn items
        if (!(_player getVariable ["eden_isAdmin", false])) then {
            _isValid = false;
            _reason = "Unauthorized item spawning";
        };
        
        // Check for valid item class
        if (_itemClass == "" || !isClass (configFile >> "CfgWeapons" >> _itemClass) && !isClass (configFile >> "CfgMagazines" >> _itemClass) && !isClass (configFile >> "CfgVehicles" >> _itemClass)) then {
            _isValid = false;
            _reason = "Invalid item class";
        };
        
        // Check quantity limits
        if (_quantity <= 0 || _quantity > 1000) then {
            _isValid = false;
            _reason = "Invalid item quantity";
        };
    };
    
    case "teleport": {
        _params params [["_position", [0,0,0], [[]]]];
        
        // Only admins can teleport
        if (!(_player getVariable ["eden_isAdmin", false])) then {
            _isValid = false;
            _reason = "Unauthorized teleportation";
        };
        
        // Check for valid position
        if (count _position < 3) then {
            _isValid = false;
            _reason = "Invalid teleport position";
        };
        
        // Rate limiting - max 5 teleports per minute
        _teleports = _securityData getOrDefault ["teleports", []];
        _recentTeleports = _teleports select {_x > (_currentTime - 60)};
        
        if (count _recentTeleports >= 5) then {
            _isValid = false;
            _reason = "Too many teleports";
        };
        
        // Update tracking
        if (_isValid) then {
            _teleports pushBack _currentTime;
            if (count _teleports > 10) then { _teleports deleteAt 0; };
            _securityData set ["teleports", _teleports];
        };
    };
    
    case "vehicle_spawn": {
        _params params [["_vehicleClass", "", [""]]];
        
        // Check admin permissions
        if (!(_player getVariable ["eden_canSpawnVehicles", false])) then {
            _isValid = false;
            _reason = "Unauthorized vehicle spawning";
        };
        
        // Check for valid vehicle class
        if (_vehicleClass == "" || !isClass (configFile >> "CfgVehicles" >> _vehicleClass)) then {
            _isValid = false;
            _reason = "Invalid vehicle class";
        };
        
        // Rate limiting - max 3 vehicles per minute
        _spawns = _securityData getOrDefault ["vehicle_spawns", []];
        _recentSpawns = _spawns select {_x > (_currentTime - 60)};
        
        if (count _recentSpawns >= 3) then {
            _isValid = false;
            _reason = "Too many vehicle spawns";
        };
        
        // Update tracking
        if (_isValid) then {
            _spawns pushBack _currentTime;
            if (count _spawns > 10) then { _spawns deleteAt 0; };
            _securityData set ["vehicle_spawns", _spawns];
        };
    };
    
    case "database_query": {
        _params params [["_queryType", "", [""]]];
        
        // Rate limiting for database queries
        _queries = _securityData getOrDefault ["db_queries", []];
        _recentQueries = _queries select {_x > (_currentTime - 10)};
        
        if (count _recentQueries >= 20) then {
            _isValid = false;
            _reason = "Too many database queries";
        };
        
        // Update tracking
        if (_isValid) then {
            _queries pushBack _currentTime;
            if (count _queries > 50) then { _queries deleteAt 0; };
            _securityData set ["db_queries", _queries];
        };
    };
    
    case "chat_message": {
        _params params [["_message", "", [""]]];
        
        // Check message length
        if (count _message > 500) then {
            _isValid = false;
            _reason = "Message too long";
        };
        
        // Rate limiting - max 10 messages per minute
        _messages = _securityData getOrDefault ["chat_messages", []];
        _recentMessages = _messages select {_x > (_currentTime - 60)};
        
        if (count _recentMessages >= 10) then {
            _isValid = false;
            _reason = "Chat spam detected";
        };
        
        // Update tracking
        if (_isValid) then {
            _messages pushBack _currentTime;
            if (count _messages > 20) then { _messages deleteAt 0; };
            _securityData set ["chat_messages", _messages];
        };
    };
    
    case "player_action": {
        _params params [["_target", objNull, [objNull]], ["_action", "", [""]]];
        
        // Check if target is valid
        if (isNull _target) then {
            _isValid = false;
            _reason = "Invalid target";
        };
        
        // Check distance to target
        if (!isNull _target && _player distance _target > 10) then {
            _isValid = false;
            _reason = "Target too far away";
        };
        
        // Rate limiting for player actions
        _actions = _securityData getOrDefault ["player_actions", []];
        _recentActions = _actions select {_x > (_currentTime - 30)};
        
        if (count _recentActions >= 15) then {
            _isValid = false;
            _reason = "Too many player actions";
        };
        
        // Update tracking
        if (_isValid) then {
            _actions pushBack _currentTime;
            if (count _actions > 30) then { _actions deleteAt 0; };
            _securityData set ["player_actions", _actions];
        };
    };
    
    default {
        // Unknown action type - allow but log
        [format ["[EDEN] Unknown security validation type: %1", _actionType]] call EDEN_fnc_systemLogger;
    };
};

// Update security data
_player setVariable ["eden_securityData", _securityData, true];

// Log security violations
if (!_isValid) then {
    [format ["[EDEN] Security violation by %1: %2 (%3)", name _player, _actionType, _reason]] call EDEN_fnc_systemLogger;
    
    // Increment violation counter
    _violations = _player getVariable ["eden_securityViolations", 0];
    _violations = _violations + 1;
    _player setVariable ["eden_securityViolations", _violations, true];
    
    // Auto-kick for excessive violations
    if (_violations >= 10) then {
        [format ["[EDEN] Auto-kicking %1 for excessive security violations", name _player]] call EDEN_fnc_systemLogger;
        [_player, "Excessive security violations"] call EDEN_fnc_kickPlayer;
    };
    
    // Notify player
    [format ["Action blocked: %1", _reason]] remoteExec ["EDEN_fnc_showHint", _player];
};

// Return validation result
_isValid
