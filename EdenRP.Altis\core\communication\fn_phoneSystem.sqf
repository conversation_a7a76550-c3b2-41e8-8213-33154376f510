/*
    EdenRP Phone System
    Enhanced mobile communication with modern features
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_action", "", [""]],
    ["_data", [], [[]]]
];

// Validate parameters
if (isNull _player || !isPlayer _player) exitWith {
    ["Invalid player provided to phoneSystem", "ERROR", "PHONE"] call EDEN_fnc_systemLogger;
    false
};

// Check if player has a phone
if !([_player, "cellphone"] call EDEN_fnc_hasItem) exitWith {
    ["You need a cell phone to use this feature", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

private _result = false;
switch (toLower _action) do {
    case "call": {
        _result = [_player, _data] call EDEN_fnc_phoneCall;
    };
    case "answer": {
        _result = [_player, _data] call EDEN_fnc_phoneAnswer;
    };
    case "hangup": {
        _result = [_player] call EDEN_fnc_phoneHangup;
    };
    case "sms": {
        _result = [_player, _data] call EDEN_fnc_phoneSMS;
    };
    case "contacts": {
        _result = [_player, _data] call EDEN_fnc_phoneContacts;
    };
    case "history": {
        _result = [_player] call EDEN_fnc_phoneHistory;
    };
    case "emergency": {
        _result = [_player, _data] call EDEN_fnc_phoneEmergency;
    };
    case "apps": {
        _result = [_player, _data] call EDEN_fnc_phoneApps;
    };
    case "settings": {
        _result = [_player, _data] call EDEN_fnc_phoneSettings;
    };
    default {
        [format["Unknown phone action: %1", _action], "ERROR", "PHONE"] call EDEN_fnc_systemLogger;
    };
};

_result
