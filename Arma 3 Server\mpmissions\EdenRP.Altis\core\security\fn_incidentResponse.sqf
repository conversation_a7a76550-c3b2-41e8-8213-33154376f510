/*
    File: fn_incidentResponse.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages incident response system.
*/

params [["_action", "init", [""]]];

switch (_action) do {
    case "init": {
        if (isServer) then {
            if (isNil "eden_activeIncidents") then {
                eden_activeIncidents = [];
            };
            if (isNil "eden_incidentTypes") then {
                eden_incidentTypes = [
                    ["security_breach", "HIGH", ["isolate_player", "notify_admins", "log_evidence"]],
                    ["cheating_detected", "HIGH", ["ban_player", "notify_admins", "preserve_logs"]],
                    ["griefing_report", "MEDIUM", ["warn_player", "monitor_behavior", "notify_moderators"]],
                    ["server_attack", "CRITICAL", ["enable_protection", "notify_all_admins", "emergency_backup"]],
                    ["data_corruption", "HIGH", ["restore_backup", "investigate_cause", "notify_admins"]]
                ];
                publicVariable "eden_incidentTypes";
            };
        };
        true
    };
    case "reportIncident": {
        params ["", ["_incidentType", "", [""]], ["_player", objNull, [objNull]], ["_description", "", [""]], ["_evidence", [], [[]]]];
        
        if (!isServer) exitWith { false };
        
        _incidentId = format["INC_%1_%2", floor(time), floor(random 1000)];
        _timestamp = format["%1-%2-%3 %4:%5:%6", 
            date select 0, date select 1, date select 2,
            date select 3, date select 4, floor(date select 5)
        ];
        
        _incident = [
            _incidentId,
            _incidentType,
            _timestamp,
            if (isNull _player) then { "SYSTEM" } else { getPlayerUID _player },
            if (isNull _player) then { "SYSTEM" } else { name _player },
            _description,
            _evidence,
            "OPEN",
            []
        ];
        
        eden_activeIncidents pushBack _incident;
        
        // Trigger automatic response
        ["triggerResponse", _incidentId] call EDEN_fnc_incidentResponse;
        
        // Log the incident
        ["logSecurityEvent", _player, "incident_reported", "HIGH", format["Type: %1, ID: %2", _incidentType, _incidentId]] call EDEN_fnc_auditSystem;
        
        _incidentId
    };
    case "triggerResponse": {
        params ["", ["_incidentId", "", [""]]];
        
        if (!isServer) exitWith { false };
        
        // Find incident
        _incident = [];
        _incidentIndex = -1;
        
        {
            if ((_x select 0) == _incidentId) then {
                _incident = _x;
                _incidentIndex = _forEachIndex;
            };
        } forEach eden_activeIncidents;
        
        if (count _incident == 0) exitWith { false };
        
        _incidentType = _incident select 1;
        _playerUID = _incident select 3;
        _playerName = _incident select 4;
        
        // Find response plan
        _responsePlan = [];
        {
            if ((_x select 0) == _incidentType) then { _responsePlan = _x; };
        } forEach eden_incidentTypes;
        
        if (count _responsePlan == 0) exitWith { false };
        
        _severity = _responsePlan select 1;
        _actions = _responsePlan select 2;
        
        // Execute response actions
        _responseLog = [];
        
        {
            _actionResult = [_action, _playerUID, _playerName, _incidentId] call EDEN_fnc_incidentResponse;
            _responseLog pushBack [_action, time, _actionResult];
        } forEach _actions;
        
        // Update incident with response log
        _incident set [8, _responseLog];
        eden_activeIncidents set [_incidentIndex, _incident];
        
        true
    };
    case "isolate_player": {
        params ["", ["_playerUID", "", [""]], ["_playerName", "", [""]], ["_incidentId", "", [""]]];
        
        _player = objNull;
        {
            if (getPlayerUID _x == _playerUID) then { _player = _x; };
        } forEach allPlayers;
        
        if (!isNull _player) then {
            _player setVariable ["eden_isolated", true, true];
            ["You have been isolated due to a security incident"] remoteExec ["EDEN_fnc_showHint", _player];
            format["Player %1 isolated successfully", _playerName]
        } else {
            format["Player %1 not found for isolation", _playerName]
        };
    };
    case "notify_admins": {
        params ["", ["_playerUID", "", [""]], ["_playerName", "", [""]], ["_incidentId", "", [""]]];
        
        {
            if (getPlayerUID _x in eden_adminList) then {
                [format ["🚨 SECURITY INCIDENT: %1 (ID: %2)", _playerName, _incidentId]] remoteExec ["EDEN_fnc_showHint", _x];
            };
        } forEach allPlayers;
        
        "All admins notified"
    };
    case "log_evidence": {
        params ["", ["_playerUID", "", [""]], ["_playerName", "", [""]], ["_incidentId", "", [""]]];
        
        // Collect evidence
        _evidence = [];
        
        // Player position and status
        _player = objNull;
        {
            if (getPlayerUID _x == _playerUID) then { _player = _x; };
        } forEach allPlayers;
        
        if (!isNull _player) then {
            _evidence pushBack ["position", getPos _player];
            _evidence pushBack ["money", _player getVariable ["eden_cash", 0]];
            _evidence pushBack ["level", _player getVariable ["eden_level", 1]];
        };
        
        // Recent violations
        _violations = ["getPlayerViolations", _playerUID] call EDEN_fnc_intrusionDetection;
        _evidence pushBack ["violations", _violations];
        
        format["Evidence collected: %1 items", count _evidence]
    };
    case "ban_player": {
        params ["", ["_playerUID", "", [""]], ["_playerName", "", [""]], ["_incidentId", "", [""]]];
        
        _player = objNull;
        {
            if (getPlayerUID _x == _playerUID) then { _player = _x; };
        } forEach allPlayers;
        
        if (!isNull _player) then {
            [_player, "addBan", "Automatic ban due to security incident", 86400] call EDEN_fnc_banSystem;
            format["Player %1 banned successfully", _playerName]
        } else {
            format["Player %1 not found for banning", _playerName]
        };
    };
    case "getIncidentStatus": {
        params ["", ["_incidentId", "", [""]]];
        
        if (!isServer) exitWith { [] };
        
        _incident = [];
        {
            if ((_x select 0) == _incidentId) then { _incident = _x; };
        } forEach eden_activeIncidents;
        
        _incident
    };
    case "closeIncident": {
        params ["", ["_incidentId", "", [""]], ["_resolution", "", [""]]];
        
        if (!isServer) exitWith { false };
        
        {
            if ((_x select 0) == _incidentId) then {
                _x set [7, "CLOSED"];
                _responseLog = _x select 8;
                _responseLog pushBack ["incident_closed", time, _resolution];
                _x set [8, _responseLog];
            };
        } forEach eden_activeIncidents;
        
        true
    };
    case "listActiveIncidents": {
        params ["", ["_admin", objNull, [objNull]]];
        
        if (isNull _admin || !(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _activeCount = 0;
        _reportText = "=== ACTIVE INCIDENTS ===\n";
        
        {
            if ((_x select 7) == "OPEN") then {
                _activeCount = _activeCount + 1;
                _reportText = _reportText + format["%1: %2 - %3\n", 
                    (_x select 0), (_x select 1), (_x select 4)
                ];
            };
        } forEach eden_activeIncidents;
        
        _reportText = _reportText + format["\nTotal Active: %1", _activeCount];
        
        [_reportText] remoteExec ["EDEN_fnc_showHint", _admin];
        true
    };
    default { false };
};
