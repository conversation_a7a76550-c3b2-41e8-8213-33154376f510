/*
    File: fn_requestMedical.sqf
    Author: EdenRP Development Team
    
    Description:
    Requests medical assistance.
*/

params [["_player", player, [objN<PERSON>]]];

if (isNull _player) exitWith { false };

_lastRequest = _player getVariable ["eden_lastMedicalRequest", 0];
if ((time - _lastRequest) < 300) exitWith {
    ["Medical request cooldown active!"] call EDEN_fnc_showHint;
    false
};

_player setVariable ["eden_lastMedicalRequest", time, true];

{
    if (_x getVariable ["eden_isMedic", false]) then {
        [
            "MEDICAL REQUEST",
            format ["%1 needs medical assistance at %2", name _player, mapGridPosition _player],
            15,
            "warning"
        ] remoteExec ["EDEN_fnc_showNotification", _x];
    };
} forEach allPlayers;

["Medical assistance requested!"] call EDEN_fnc_showHint;
[format ["[EDEN] Medical request by %1", name _player], "INFO", "MEDICAL"] call EDEN_fnc_systemLogger;
true
