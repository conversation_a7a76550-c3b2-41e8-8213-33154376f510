/*
    File: fn_achievementSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages achievement system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_achievements") then {
            eden_achievements = [
                ["first_job", "Get your first job", 100, false],
                ["millionaire", "Earn $1,000,000", 500, false],
                ["level_10", "Reach level 10", 200, false],
                ["social_butterfly", "Add 10 friends", 150, false],
                ["law_abiding", "Go 24 hours without crime", 300, false]
            ];
            publicVariable "eden_achievements";
        };
        _player setVariable ["eden_unlockedAchievements", [], true];
        true
    };
    case "checkAchievement": {
        params ["", "", ["_achievementID", "first_job", [""]]];
        
        _unlocked = _player getVariable ["eden_unlockedAchievements", []];
        if (_achievementID in _unlocked) exitWith { false };
        
        _canUnlock = false;
        
        switch (_achievementID) do {
            case "first_job": {
                _job = _player getVariable ["eden_job", "civilian"];
                _canUnlock = (_job != "civilian");
            };
            case "millionaire": {
                _cash = _player getVariable ["eden_cash", 0];
                _bank = _player getVariable ["eden_bankAccount", 0];
                _canUnlock = ((_cash + _bank) >= 1000000);
            };
            case "level_10": {
                _level = _player getVariable ["eden_level", 1];
                _canUnlock = (_level >= 10);
            };
            case "social_butterfly": {
                _friends = _player getVariable ["eden_friends", []];
                _canUnlock = (count _friends >= 10);
            };
            case "law_abiding": {
                _lastCrime = _player getVariable ["eden_lastCrimeTime", 0];
                _canUnlock = ((time - _lastCrime) >= 86400);
            };
        };
        
        if (_canUnlock) then {
            [_player, "unlockAchievement", _achievementID] call EDEN_fnc_achievementSystem;
        };
        
        _canUnlock
    };
    case "unlockAchievement": {
        params ["", "", ["_achievementID", "first_job", [""]]];
        
        _unlocked = _player getVariable ["eden_unlockedAchievements", []];
        if (_achievementID in _unlocked) exitWith { false };
        
        _achievementData = [];
        {
            if ((_x select 0) == _achievementID) then { _achievementData = _x; };
        } forEach eden_achievements;
        
        if (count _achievementData == 0) exitWith { false };
        
        _unlocked pushBack _achievementID;
        _player setVariable ["eden_unlockedAchievements", _unlocked, true];
        
        _reward = _achievementData select 2;
        _experience = _player getVariable ["eden_experience", 0];
        _player setVariable ["eden_experience", (_experience + _reward), true];
        
        [format ["ACHIEVEMENT UNLOCKED: %1 (+%2 XP)", (_achievementData select 1), _reward]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "getProgress": {
        _unlocked = _player getVariable ["eden_unlockedAchievements", []];
        _total = count eden_achievements;
        _completed = count _unlocked;
        
        [_completed, _total]
    };
    default { false };
};
