/*
    EdenRP Altis Life Framework
    Rebuilt from Olympus Entertainment framework with significant modifications
    Author: EdenRP Development Team
    Version: 1.0.0
    
    This framework is a complete rebuild of Altis Life systems with:
    - Enhanced gameplay mechanics
    - Improved security and performance
    - Modern SQF practices
    - Modular architecture
*/

author = "EdenRP Development Team";
onLoadName = "EdenRP - Enhanced Altis Life";
onLoadMission = "Experience the next generation of Altis Life roleplay with enhanced systems and immersive gameplay.";
overviewText = "EdenRP offers an enhanced Altis Life experience with improved mechanics, balanced economy, and engaging faction systems.";
overviewTextLocked = "EdenRP - Enhanced Altis Life Roleplay Server";
overviewPicture = "images\edenrp_logo.paa";
loadScreen = "images\edenrp_loading.paa";
OnLoadIntroTime = 0;
OnLoadMissionTime = 0;
allowFunctionsLog = 1;
allowFunctionsRecompile = 0;
briefing = 0;

// Server Configuration
joinUnassigned = 1;
respawn = "BASE";
disabledAI = 1;
disableChannels[] = {0, 1, 2};
enableDebugConsole = 1;
respawnDialog = 0;
scriptedPlayer = 1;
forceRotorLibSimulation = 0;

class Header {
    gameType = "RPG";
    minPlayers = 1;
    maxPlayers = 120; // Increased from Olympus 100
};

// Enhanced cleanup settings
respawndelay = 1;
wreckLimit = 5; // Increased from 3
wreckRemovalMinTime = 90; // Increased from 60
wreckRemovalMaxTime = 480; // Increased from 320
corpseLimit = 100; // Increased from 75
corpseRemovalMinTime = 900; // Increased from 600
corpseRemovalMaxTime = 1800; // Increased from 1200
unsafeCVL = 1;

// Enhanced HUD configuration
showHUD[] = {1, 1, 0, 1, 0, 0, 1, 1, 1};

// Include dialog system
#include "dialogs\MasterHandler.hpp"

class RscTitles {
    #include "dialogs\ui.hpp"
    #include "dialogs\progress.hpp"
    #include "dialogs\hud_nameTags.hpp"
    #include "dialogs\notifications.hpp"
};

class CfgFunctions {
    #include "Functions.h"
};

class CfgRemoteExec {
    #include "CfgRemoteExec.hpp"
};

// Enhanced sound configuration
class CfgSounds {
    sounds[] = {};
    
    class EdenRP_Notification {
        name = "EdenRP_Notification";
        sound[] = {"sounds\notification.ogg", 1, 1};
        titles[] = {};
    };
    
    class EdenRP_Success {
        name = "EdenRP_Success";
        sound[] = {"sounds\success.ogg", 1, 1};
        titles[] = {};
    };
    
    class EdenRP_Error {
        name = "EdenRP_Error";
        sound[] = {"sounds\error.ogg", 1, 1};
        titles[] = {};
    };
    
    class EdenRP_Alarm {
        name = "EdenRP_Alarm";
        sound[] = {"sounds\alarm.ogg", 1, 1};
        titles[] = {};
    };
};

// Enhanced music configuration
class CfgMusic {
    tracks[] = {};
    
    class EdenRP_Ambient {
        name = "EdenRP_Ambient";
        sound[] = {"music\ambient.ogg", 1, 1};
        duration = 180;
    };
};

// Enhanced notification system
class CfgNotifications {
    class EdenRP_Default {
        title = "EdenRP";
        iconPicture = "images\edenrp_icon.paa";
        iconText = "";
        description = "%1";
        color[] = {1, 1, 1, 1};
        duration = 5;
        priority = 0;
        difficulty[] = {};
    };
    
    class EdenRP_Success {
        title = "Success";
        iconPicture = "images\icon_success.paa";
        iconText = "";
        description = "%1";
        color[] = {0, 1, 0, 1};
        duration = 4;
        priority = 1;
        difficulty[] = {};
    };
    
    class EdenRP_Error {
        title = "Error";
        iconPicture = "images\icon_error.paa";
        iconText = "";
        description = "%1";
        color[] = {1, 0, 0, 1};
        duration = 6;
        priority = 2;
        difficulty[] = {};
    };
    
    class EdenRP_Police {
        title = "Police Dispatch";
        iconPicture = "images\icon_police.paa";
        iconText = "";
        description = "%1";
        color[] = {0, 0.5, 1, 1};
        duration = 8;
        priority = 3;
        difficulty[] = {};
    };
    
    class EdenRP_Medical {
        title = "Medical Alert";
        iconPicture = "images\icon_medical.paa";
        iconText = "";
        description = "%1";
        color[] = {1, 0.5, 0, 1};
        duration = 7;
        priority = 3;
        difficulty[] = {};
    };
};

// Enhanced debriefing
debriefing = 1;
class CfgDebriefing {
    class End1 {
        title = "Session Complete";
        subtitle = "Thank you for playing EdenRP";
        description = "Your progress has been saved. See you next time!";
        pictureBackground = "images\edenrp_outro.paa";
        picture = "images\edenrp_logo.paa";
        pictureColor[] = {1, 1, 1, 1};
    };
};

// Enhanced parameters for server configuration
class Params {
    class EdenRP_Economy_Multiplier {
        title = "Economy Multiplier";
        values[] = {0.5, 0.75, 1, 1.25, 1.5, 2};
        texts[] = {"50%", "75%", "100%", "125%", "150%", "200%"};
        default = 1;
    };
    
    class EdenRP_XP_Multiplier {
        title = "XP Gain Multiplier";
        values[] = {0.5, 0.75, 1, 1.25, 1.5, 2};
        texts[] = {"50%", "75%", "100%", "125%", "150%", "200%"};
        default = 1;
    };
    
    class EdenRP_Max_Gang_Members {
        title = "Maximum Gang Members";
        values[] = {8, 10, 12, 15, 20};
        texts[] = {"8", "10", "12", "15", "20"};
        default = 12;
    };
};

// Enhanced vehicle configuration
class CfgVehicles {
    class Car;
    class Car_F: Car {
        class HitPoints;
    };
    
    // Enhanced vehicle damage system
    class EdenRP_Enhanced_Vehicle: Car_F {
        class HitPoints: HitPoints {
            class HitEngine {
                armor = 1.2; // Slightly more durable than default
                explosionShielding = 0.8;
                name = "engine";
                passThrough = 0.3;
                radius = 0.25;
                visual = "engine";
            };
        };
    };
};
