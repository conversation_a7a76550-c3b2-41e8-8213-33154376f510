/*
    File: fn_serverManagement.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages server administration.
*/

params [["_admin", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _admin) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_serverSettings") then {
            eden_serverSettings = [
                ["maxPlayers", 100],
                ["restartTime", 14400],
                ["economyMultiplier", 1.0]
            ];
            publicVariable "eden_serverSettings";
        };
        true
    };
    case "restartServer": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        {
            ["SERVER RESTART IN 5 MINUTES - SAVE YOUR PROGRESS!"] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach allPlayers;
        
        [] spawn {
            sleep 300;
            {
                [_x] call EDEN_fnc_savePlayerData;
            } forEach allPlayers;
            
            sleep 10;
            #shutdown;
        };
        
        ["Server restart initiated"] call EDEN_fnc_showHint;
        true
    };
    case "setEconomyMultiplier": {
        params ["", "", ["_multiplier", 1.0, [0]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        {
            if ((_x select 0) == "economyMultiplier") then {
                _x set [1, _multiplier];
            };
        } forEach eden_serverSettings;
        publicVariable "eden_serverSettings";
        
        [format ["Economy multiplier set to %1", _multiplier]] call EDEN_fnc_showHint;
        true
    };
    case "clearDatabase": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        eden_playerDatabase = [];
        publicVariable "eden_playerDatabase";
        
        ["Player database cleared"] call EDEN_fnc_showHint;
        true
    };
    case "saveAll": {
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        {
            [_x] call EDEN_fnc_savePlayerData;
        } forEach allPlayers;
        
        ["All player data saved"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
