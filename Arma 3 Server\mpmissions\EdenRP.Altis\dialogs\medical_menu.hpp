/*
    File: medical_menu.hpp
    Author: EdenRP Development Team
    
    Description:
    Medical menu dialog definitions for EdenRP
*/

class EdenRP_MedicalMenu {
    idd = 3300;
    name = "EdenRP_MedicalMenu";
    movingEnable = 1;
    enableSimulation = 1;
    
    class controlsBackground {
        class Background: RscText {
            idc = -1;
            x = 0.2;
            y = 0.2;
            w = 0.6;
            h = 0.6;
            colorBackground[] = {0, 0, 0, 0.8};
        };
        
        class Title: RscText {
            idc = -1;
            text = "Medical Center";
            x = 0.2;
            y = 0.2;
            w = 0.6;
            h = 0.05;
            colorText[] = {1, 1, 1, 1};
            sizeEx = 0.04;
        };
    };
    
    class controls {
        class PatientList: RscListBox {
            idc = 3301;
            x = 0.25;
            y = 0.3;
            w = 0.25;
            h = 0.4;
        };
        
        class TreatmentList: RscListBox {
            idc = 3302;
            x = 0.55;
            y = 0.3;
            w = 0.2;
            h = 0.4;
        };
        
        class ReviveButton: RscButton {
            idc = 3303;
            text = "Revive Patient";
            x = 0.25;
            y = 0.72;
            w = 0.12;
            h = 0.04;
            action = "[] call EDEN_fnc_revivePlayer;";
        };
        
        class HealButton: RscButton {
            idc = 3304;
            text = "Heal Patient";
            x = 0.39;
            y = 0.72;
            w = 0.12;
            h = 0.04;
            action = "[] call EDEN_fnc_healPlayer;";
        };
        
        class CloseButton: RscButton {
            idc = 3305;
            text = "Close";
            x = 0.7;
            y = 0.75;
            w = 0.08;
            h = 0.04;
            action = "closeDialog 0;";
        };
    };
};
