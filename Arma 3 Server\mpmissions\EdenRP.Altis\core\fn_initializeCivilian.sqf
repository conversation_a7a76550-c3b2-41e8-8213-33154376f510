/*
    File: fn_initializeCivilian.sqf
    Author: EdenRP Development Team
    
    Description:
    Initializes a player as a civilian with proper equipment, variables, and settings.
    
    Parameters:
    0: OBJECT - Player object
    
    Returns:
    BOOLEAN - True if initialization was successful
    
    Example:
    [player] call EDEN_fnc_initializeCivilian;
*/

params [
    ["_player", player, [obj<PERSON><PERSON>]]
];

// Validate parameters
if (isNull _player) exitWith {
    ["[EDEN] fn_initializeCivilian: Invalid player object"] call EDEN_fnc_systemLogger;
    false
};

// Set player role
_player setVariable ["eden_playerRole", "civilian", true];
_player setVariable ["eden_playerFaction", "civilian", true];

// Initialize player stats
_player setVariable ["eden_cash", 5000, true]; // Starting cash
_player setVariable ["eden_bankAccount", 25000, true]; // Starting bank money
_player setVariable ["eden_playerLevel", 1, true];
_player setVariable ["eden_experience", 0, true];

// Initialize licenses (all false by default)
_player setVariable ["eden_license_driver", false, true];
_player setVariable ["eden_license_pilot", false, true];
_player setVariable ["eden_license_boat", false, true];
_player setVariable ["eden_license_gun", false, true];
_player setVariable ["eden_license_hunting", false, true];
_player setVariable ["eden_license_fishing", false, true];
_player setVariable ["eden_license_mining", false, true];
_player setVariable ["eden_license_oil", false, true];
_player setVariable ["eden_license_diamond", false, true];
_player setVariable ["eden_license_turtle", false, true];

// Initialize job variables
_player setVariable ["eden_currentJob", "unemployed", true];
_player setVariable ["eden_jobExperience", 0, true];
_player setVariable ["eden_jobLevel", 1, true];

// Initialize inventory
_player setVariable ["eden_inventory", [], true];
_player setVariable ["eden_virtualItems", [], true];
_player setVariable ["eden_maxWeight", 50, true];
_player setVariable ["eden_currentWeight", 0, true];

// Initialize gang variables
_player setVariable ["eden_gangMember", false, true];
_player setVariable ["eden_gangName", "", true];
_player setVariable ["eden_gangRank", "", true];

// Initialize property variables
_player setVariable ["eden_ownedHouses", [], true];
_player setVariable ["eden_ownedVehicles", [], true];

// Initialize criminal record
_player setVariable ["eden_criminalRecord", [], true];
_player setVariable ["eden_wantedLevel", 0, true];
_player setVariable ["eden_bounty", 0, true];

// Initialize health and status
_player setVariable ["eden_isDead", false, true];
_player setVariable ["eden_isRestrained", false, true];
_player setVariable ["eden_isEscorted", false, true];
_player setVariable ["eden_hunger", 100, true];
_player setVariable ["eden_thirst", 100, true];

// Remove all gear and give civilian starting equipment
removeAllWeapons _player;
removeAllItems _player;
removeAllAssignedItems _player;
removeUniform _player;
removeVest _player;
removeBackpack _player;
removeHeadgear _player;
removeGoggles _player;

// Give civilian clothing
_player forceAddUniform "U_C_Poloshirt_blue";
_player addHeadgear "H_Cap_blu";
_player addItem "ItemMap";
_player addItem "ItemCompass";
_player addItem "ItemWatch";
_player addItem "ItemRadio";
_player addItem "ItemGPS";

// Assign items
_player assignItem "ItemMap";
_player assignItem "ItemCompass";
_player assignItem "ItemWatch";
_player assignItem "ItemRadio";
_player assignItem "ItemGPS";

// Add starting items to virtual inventory
_player setVariable ["eden_virtualItems", [
    ["water_bottle", 2],
    ["apple", 3],
    ["toolkit", 1]
], true];

// Set civilian-specific settings
_player setCaptive false;
_player setVariable ["eden_isPolice", false, true];
_player setVariable ["eden_isMedic", false, true];
_player setVariable ["eden_isAdmin", false, true];

// Initialize civilian actions
[_player] call EDEN_fnc_setupPlayerActions;

// Setup civilian-specific event handlers
_player addEventHandler ["Killed", {
    params ["_unit", "_killer"];
    [_unit, _killer] call EDEN_fnc_onPlayerKilled;
}];

_player addEventHandler ["Respawn", {
    params ["_unit", "_corpse"];
    [_unit] call EDEN_fnc_initializeCivilian;
}];

// Setup civilian HUD
[] call EDEN_fnc_setupCivilianHUD;

// Create briefing
[_player, "civilian"] call EDEN_fnc_briefingSystem;

// Sync player data with database
[_player] call EDEN_fnc_savePlayerData;

// Welcome message
[
    "Welcome to EdenRP!",
    "You are now a civilian. Visit the job center to find work and start earning money!",
    5
] call EDEN_fnc_showNotification;

// Log successful initialization
[format ["[EDEN] Player %1 initialized as civilian", name _player]] call EDEN_fnc_systemLogger;

// Return success
true
