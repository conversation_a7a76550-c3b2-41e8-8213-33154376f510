/*
    File: fn_completeMission.sqf
    Author: EdenRP Development Team
    
    Description:
    Completes the player's active mission.
    
    Parameters:
    0: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if mission was completed successfully
*/

params [["_player", player, [objNull]]];

if (isNull _player) exitWith { false };

_activeMission = _player getVariable ["eden_activeMission", ""];
if (_activeMission == "") exitWith {
    ["You don't have an active mission!"] call EDEN_fnc_showHint;
    false
};

_missionReward = _player getVariable ["eden_missionReward", 0];
_missionStartTime = _player getVariable ["eden_missionStartTime", time];
_missionDuration = time - _missionStartTime;

// Mission-specific completion logic
_completed = false;

switch (_activeMission) do {
    case "delivery": {
        _destination = _player getVariable ["eden_missionDestination", [0,0,0]];
        if (_player distance _destination < 50) then {
            _completed = true;
            
            // Update job stats
            _deliveries = _player getVariable ["eden_truckerDeliveries", 0];
            _player setVariable ["eden_truckerDeliveries", (_deliveries + 1), true];
            
            // Remove delivery marker
            _markerName = format ["delivery_%1", getPlayerUID _player];
            deleteMarkerLocal _markerName;
        } else {
            ["You must be at the delivery destination to complete this mission!"] call EDEN_fnc_showHint;
        };
    };
    
    case "taxi": {
        _destination = _player getVariable ["eden_missionDestination", [0,0,0]];
        if (_player distance _destination < 50) then {
            _completed = true;
            
            // Update job stats
            _fares = _player getVariable ["eden_taxiFares", 0];
            _player setVariable ["eden_taxiFares", (_fares + 1), true];
            
            // Remove taxi markers
            _markerName = format ["taxi_pickup_%1", getPlayerUID _player];
            deleteMarkerLocal _markerName;
            _markerName = format ["taxi_dest_%1", getPlayerUID _player];
            deleteMarkerLocal _markerName;
        } else {
            ["You must be at the destination to complete this mission!"] call EDEN_fnc_showHint;
        };
    };
    
    case "mining": {
        _target = _player getVariable ["eden_missionTarget", 10];
        _progress = _player getVariable ["eden_missionProgress", 0];
        
        if (_progress >= _target) then {
            _completed = true;
            
            // Remove mining marker
            _markerName = format ["mining_%1", getPlayerUID _player];
            deleteMarkerLocal _markerName;
        } else {
            [format ["You need to mine %1 more resources to complete this mission!", _target - _progress]] call EDEN_fnc_showHint;
        };
    };
};

if (!_completed) exitWith { false };

// Calculate time bonus (faster completion = bonus)
_timeBonus = 0;
if (_missionDuration < 600) then { // Under 10 minutes
    _timeBonus = round(_missionReward * 0.2); // 20% bonus
} else {
    if (_missionDuration < 1200) then { // Under 20 minutes
        _timeBonus = round(_missionReward * 0.1); // 10% bonus
    };
};

_totalReward = _missionReward + _timeBonus;

// Pay the player
_playerMoney = _player getVariable ["eden_cash", 0];
_player setVariable ["eden_cash", (_playerMoney + _totalReward), true];

// Add experience
_expGained = round(_missionReward / 10);
_currentExp = _player getVariable ["eden_experience", 0];
_player setVariable ["eden_experience", (_currentExp + _expGained), true];

// Check for level up
_newLevel = floor((_currentExp + _expGained) / 1000) + 1;
_currentLevel = _player getVariable ["eden_playerLevel", 1];
if (_newLevel > _currentLevel) then {
    _player setVariable ["eden_playerLevel", _newLevel, true];
    [format ["Level up! You are now level %1", _newLevel], 5] call EDEN_fnc_showNotification;
};

// Clear mission data
_player setVariable ["eden_activeMission", "", true];
_player setVariable ["eden_missionDestination", nil, true];
_player setVariable ["eden_missionPickup", nil, true];
_player setVariable ["eden_missionLocation", nil, true];
_player setVariable ["eden_missionTarget", nil, true];
_player setVariable ["eden_missionProgress", nil, true];
_player setVariable ["eden_missionReward", nil, true];
_player setVariable ["eden_missionStartTime", nil, true];

_bonusText = if (_timeBonus > 0) then { format [" (Time bonus: $%1)", _timeBonus] } else { "" };
[format ["Mission completed! Reward: $%1%2 (+%3 XP)", _totalReward, _bonusText, _expGained]] call EDEN_fnc_showHint;

// Log mission completion
[format ["[EDEN] Player %1 completed mission: %2 (reward: $%3, duration: %.1f min)", name _player, _activeMission, _totalReward, _missionDuration/60], "INFO", "MISSIONS"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
