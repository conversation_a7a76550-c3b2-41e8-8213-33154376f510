/*
    File: fn_decorationSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages property decoration and furniture system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_item", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_furniture", [], true];
        _player setVariable ["eden_decorations", [], true];
        true
    };
    case "buyFurniture": {
        _furnitureList = [
            ["sofa", "Sofa", 500],
            ["table", "Table", 200],
            ["chair", "Chair", 100],
            ["bed", "Bed", 800],
            ["tv", "Television", 1200],
            ["fridge", "Refrigerator", 1500]
        ];
        
        _itemData = [];
        {
            if ((_x select 0) == _item) then { _itemData = _x; };
        } forEach _furnitureList;
        
        if (count _itemData == 0) exitWith {
            ["Item not available"] call EDEN_fnc_showHint;
            false
        };
        
        _cost = _itemData select 2;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _furniture = _player getVariable ["eden_furniture", []];
        _furniture pushBack _item;
        _player setVariable ["eden_furniture", _furniture, true];
        
        [format ["Purchased %1 for $%2", _itemData select 1, _cost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "placeFurniture": {
        _furniture = _player getVariable ["eden_furniture", []];
        if !(_item in _furniture) exitWith {
            ["You don't own this furniture"] call EDEN_fnc_showHint;
            false
        };
        
        _decorations = _player getVariable ["eden_decorations", []];
        _decoration = [_item, getPos _player, time];
        _decorations pushBack _decoration;
        _player setVariable ["eden_decorations", _decorations, true];
        
        [format ["Placed %1", _item]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
