/*
    File: fn_giveItem.sqf
    Author: EdenRP Development Team
    
    Description:
    Gives an item from one player to another.
    
    Parameters:
    0: OBJECT - Target player to give item to
    1: STRING - Item name to give
    2: NUMBER - Quantity to give (optional, default: 1)
    3: OBJECT - Source player (optional, default: player)
    
    Returns:
    BOOLEAN - True if item was given successfully
*/

params [
    ["_target", objNull, [objNull]],
    ["_itemName", "", [""]],
    ["_quantity", 1, [0]],
    ["_source", player, [objNull]]
];

if (isNull _target || isNull _source || _itemName == "" || _quantity <= 0) exitWith { false };

if (_target == _source) exitWith {
    ["You cannot give items to yourself!"] call EDEN_fnc_showHint;
    false
};

if (_source distance _target > 5) exitWith {
    ["Target player is too far away!"] call EDEN_fnc_showHint;
    false
};

// Get source player's virtual inventory
_sourceItems = _source getVariable ["eden_virtualItems", []];
_itemIndex = -1;
_itemQuantity = 0;

// Find the item in source inventory
{
    if ((_x select 0) == _itemName) then {
        _itemIndex = _forEachIndex;
        _itemQuantity = _x select 1;
    };
} forEach _sourceItems;

if (_itemIndex == -1 || _itemQuantity < _quantity) exitWith {
    ["You don't have enough of this item!"] call EDEN_fnc_showHint;
    false
};

// Check if target has inventory space
_targetWeight = _target getVariable ["eden_currentWeight", 0];
_targetMaxWeight = _target getVariable ["eden_maxWeight", 50];
_itemWeight = _quantity * 1; // Assume 1kg per item

if ((_targetWeight + _itemWeight) > _targetMaxWeight) exitWith {
    [format ["%1's inventory is full!", name _target]] call EDEN_fnc_showHint;
    false
};

// Remove item from source inventory
if (_itemQuantity <= _quantity) then {
    _sourceItems deleteAt _itemIndex;
} else {
    (_sourceItems select _itemIndex) set [1, (_itemQuantity - _quantity)];
};

_source setVariable ["eden_virtualItems", _sourceItems, true];

// Update source weight
_sourceWeight = _source getVariable ["eden_currentWeight", 0];
_source setVariable ["eden_currentWeight", (_sourceWeight - _itemWeight), true];

// Add item to target inventory
_targetItems = _target getVariable ["eden_virtualItems", []];
_found = false;

{
    if ((_x select 0) == _itemName) then {
        _x set [1, ((_x select 1) + _quantity)];
        _found = true;
    };
} forEach _targetItems;

if (!_found) then {
    _targetItems pushBack [_itemName, _quantity];
};

_target setVariable ["eden_virtualItems", _targetItems, true];

// Update target weight
_target setVariable ["eden_currentWeight", (_targetWeight + _itemWeight), true];

// Notifications
[format ["Gave %1 x%2 to %3", _itemName, _quantity, name _target]] remoteExec ["EDEN_fnc_showHint", _source];
[format ["Received %1 x%2 from %3", _itemName, _quantity, name _source]] remoteExec ["EDEN_fnc_showHint", _target];

// Log the transaction
[format ["[EDEN] Player %1 gave %2 x%3 to %4", name _source, _itemName, _quantity, name _target], "INFO", "INVENTORY"] call EDEN_fnc_systemLogger;

// Save both players' data
[_source] call EDEN_fnc_savePlayerData;
[_target] call EDEN_fnc_savePlayerData;

true
