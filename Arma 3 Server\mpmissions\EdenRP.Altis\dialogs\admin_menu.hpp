/*
    File: admin_menu.hpp
    Author: EdenRP Development Team
    
    Description:
    Admin menu dialog definitions for EdenRP
*/

class EdenRP_AdminMenu {
    idd = 3700;
    name = "EdenRP_AdminMenu";
    movingEnable = 1;
    enableSimulation = 1;
    
    class controlsBackground {
        class Background: RscText {
            idc = -1;
            x = 0.1;
            y = 0.1;
            w = 0.8;
            h = 0.8;
            colorBackground[] = {0, 0, 0, 0.8};
        };
        
        class Title: RscText {
            idc = -1;
            text = "Admin Control Panel";
            x = 0.1;
            y = 0.1;
            w = 0.8;
            h = 0.05;
            colorText[] = {1, 1, 1, 1};
            sizeEx = 0.04;
        };
    };
    
    class controls {
        class PlayerList: RscListBox {
            idc = 3701;
            x = 0.15;
            y = 0.2;
            w = 0.3;
            h = 0.6;
        };
        
        class ActionList: RscListBox {
            idc = 3702;
            x = 0.5;
            y = 0.2;
            w = 0.25;
            h = 0.6;
        };
        
        class KickButton: RscButton {
            idc = 3703;
            text = "Kick Player";
            x = 0.15;
            y = 0.82;
            w = 0.1;
            h = 0.04;
            action = "[] call EDEN_fnc_kickPlayer;";
        };
        
        class BanButton: RscButton {
            idc = 3704;
            text = "Ban Player";
            x = 0.27;
            y = 0.82;
            w = 0.1;
            h = 0.04;
            action = "[] call EDEN_fnc_banPlayer;";
        };
        
        class TeleportButton: RscButton {
            idc = 3705;
            text = "Teleport To";
            x = 0.39;
            y = 0.82;
            w = 0.1;
            h = 0.04;
            action = "[] call EDEN_fnc_teleportToPlayer;";
        };
        
        class SpectateButton: RscButton {
            idc = 3706;
            text = "Spectate";
            x = 0.51;
            y = 0.82;
            w = 0.1;
            h = 0.04;
            action = "[] call EDEN_fnc_spectatePlayer;";
        };
        
        class CloseButton: RscButton {
            idc = 3707;
            text = "Close";
            x = 0.8;
            y = 0.85;
            w = 0.08;
            h = 0.04;
            action = "closeDialog 0;";
        };
    };
};
