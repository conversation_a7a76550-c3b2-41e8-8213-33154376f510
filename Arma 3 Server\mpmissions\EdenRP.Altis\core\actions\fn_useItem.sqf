/*
    File: fn_useItem.sqf
    Author: EdenRP Development Team
    
    Description:
    Uses an item from the player's virtual inventory.
    
    Parameters:
    0: STRING - Item name to use
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if item was used successfully
*/

params [
    ["_itemName", "", [""]],
    ["_player", player, [objNull]]
];

if (_itemName == "" || isNull _player) exitWith { false };

// Get player's virtual inventory
_virtualItems = _player getVariable ["eden_virtualItems", []];
_itemIndex = -1;
_itemQuantity = 0;

// Find the item
{
    if ((_x select 0) == _itemName) then {
        _itemIndex = _forEachIndex;
        _itemQuantity = _x select 1;
    };
} forEach _virtualItems;

if (_itemIndex == -1 || _itemQuantity <= 0) exitWith {
    ["You don't have this item!"] call EDEN_fnc_showHint;
    false
};

// Use the item based on type
_itemUsed = false;

switch (_itemName) do {
    case "water_bottle": {
        _thirst = _player getVariable ["eden_thirst", 100];
        _newThirst = (_thirst + 25) min 100;
        _player setVariable ["eden_thirst", _newThirst, true];
        ["You drank water. Thirst restored!"] call EDEN_fnc_showHint;
        _itemUsed = true;
    };
    
    case "apple": {
        _hunger = _player getVariable ["eden_hunger", 100];
        _newHunger = (_hunger + 15) min 100;
        _player setVariable ["eden_hunger", _newHunger, true];
        ["You ate an apple. Hunger reduced!"] call EDEN_fnc_showHint;
        _itemUsed = true;
    };
    
    case "bread": {
        _hunger = _player getVariable ["eden_hunger", 100];
        _newHunger = (_hunger + 35) min 100;
        _player setVariable ["eden_hunger", _newHunger, true];
        ["You ate bread. Hunger greatly reduced!"] call EDEN_fnc_showHint;
        _itemUsed = true;
    };
    
    case "first_aid_kit": {
        if (damage _player > 0) then {
            _player setDamage ((damage _player) - 0.25);
            ["You used a first aid kit. Health restored!"] call EDEN_fnc_showHint;
            _itemUsed = true;
        } else {
            ["You are already at full health!"] call EDEN_fnc_showHint;
        };
    };
    
    case "phone": {
        [] call EDEN_fnc_openPhoneMenu;
        ["Phone activated!"] call EDEN_fnc_showHint;
        // Don't consume phone
    };
    
    case "radio": {
        [] call EDEN_fnc_openRadioMenu;
        ["Radio activated!"] call EDEN_fnc_showHint;
        // Don't consume radio
    };
    
    case "toolkit": {
        ["Toolkit equipped! You can now repair and flip vehicles."] call EDEN_fnc_showHint;
        // Don't consume toolkit on equip
    };
    
    case "lockpick": {
        ["Lockpick ready! Find a locked vehicle to use it on."] call EDEN_fnc_showHint;
        // Don't consume lockpick until used
    };
    
    case "rope": {
        ["Rope equipped! You can now restrain players."] call EDEN_fnc_showHint;
        // Don't consume rope on equip
    };
    
    case "zipties": {
        ["Zip ties equipped! You can now restrain players."] call EDEN_fnc_showHint;
        // Don't consume zip ties on equip
    };
    
    default {
        [format ["Item '%1' cannot be used directly", _itemName]] call EDEN_fnc_showHint;
    };
};

// Remove item from inventory if it was consumed
if (_itemUsed) then {
    if (_itemQuantity <= 1) then {
        _virtualItems deleteAt _itemIndex;
    } else {
        (_virtualItems select _itemIndex) set [1, (_itemQuantity - 1)];
    };
    
    _player setVariable ["eden_virtualItems", _virtualItems, true];
    
    // Update weight
    _currentWeight = _player getVariable ["eden_currentWeight", 0];
    _player setVariable ["eden_currentWeight", (_currentWeight - 1), true]; // Assume 1kg per item
    
    // Save player data
    [_player] call EDEN_fnc_savePlayerData;
    
    // Log item usage
    [format ["[EDEN] Player %1 used item %2", name _player, _itemName], "DEBUG", "INVENTORY"] call EDEN_fnc_systemLogger;
};

_itemUsed
