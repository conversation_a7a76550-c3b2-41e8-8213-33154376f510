/*
    File: fn_treatInjury.sqf
    Author: EdenRP Development Team
    
    Description:
    Treats injuries on a player (medical personnel only).
    
    Parameters:
    0: OBJECT - Patient to treat
    1: OBJECT - Medic (optional, default: player)
    
    Returns:
    BOOLEAN - True if treatment was successful
*/

params [
    ["_patient", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_medic", player, [obj<PERSON><PERSON>]]
];

if (isNull _patient || isNull _medic) exitWith { false };

if (_medic distance _patient > 5) exitWith {
    ["Patient is too far away!"] call EDEN_fnc_showHint;
    false
};

// Check if medic is authorized
if (!(_medic getVariable ["eden_isMedic", false]) && !(_medic getVariable ["eden_isDoctor", false])) exitWith {
    ["Only medical personnel can treat injuries!"] call EDEN_fnc_showHint;
    false
};

// Check if medic has medical supplies
_virtualItems = _medic getVariable ["eden_virtualItems", []];
_hasMedicalKit = false;
_medicalKitIndex = -1;

{
    if ((_x select 0) == "medical_kit") then {
        _hasMedicalKit = true;
        _medicalKitIndex = _forEachIndex;
    };
} forEach _virtualItems;

if (!_hasMedicalKit) exitWith {
    ["You need a medical kit to treat injuries!"] call EDEN_fnc_showHint;
    false
};

// Check patient's condition
_patientHealth = damage _patient;
_patientInjuries = _patient getVariable ["eden_injuries", []];

if (_patientHealth <= 0.1 && count _patientInjuries == 0) exitWith {
    ["Patient is healthy and doesn't need treatment!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_medic, "medical_action", [_patient, "injury_treatment"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Start treatment process
[_medic, "Acts_carFixingWheel"] remoteExec ["switchMove"];
[format ["Treating %1's injuries...", name _patient]] call EDEN_fnc_showHint;

// Treatment takes time based on injury severity
_treatmentTime = 5 + (_patientHealth * 10) + (count _patientInjuries * 3);
sleep _treatmentTime;

[_medic, ""] remoteExec ["switchMove"];

// Determine treatment success
_medicLevel = _medic getVariable ["eden_playerLevel", 1];
_baseChance = 90; // 90% base success rate
_levelBonus = _medicLevel * 1; // +1% per level
_injurySeverity = _patientHealth * 100;
_severityPenalty = _injurySeverity * 0.5; // Penalty for severe injuries
_successChance = (_baseChance + _levelBonus - _severityPenalty) max 60; // Minimum 60%

_success = (random 100) < _successChance;

if (_success) then {
    // Calculate healing amount
    _healingAmount = 0.3 + (random 0.4); // Heal 30-70% of damage
    _newHealth = (damage _patient) - _healingAmount;
    _patient setDamage (_newHealth max 0);
    
    // Clear some injuries
    _remainingInjuries = [];
    {
        if (random 100 > 70) then { // 30% chance to keep each injury
            _remainingInjuries pushBack _x;
        };
    } forEach _patientInjuries;
    _patient setVariable ["eden_injuries", _remainingInjuries, true];
    
    // Consume medical supplies
    _medicalKitQuantity = (_virtualItems select _medicalKitIndex) select 1;
    if (_medicalKitQuantity <= 1) then {
        _virtualItems deleteAt _medicalKitIndex;
    } else {
        (_virtualItems select _medicalKitIndex) set [1, (_medicalKitQuantity - 1)];
    };
    _medic setVariable ["eden_virtualItems", _virtualItems, true];
    
    // Update medical statistics
    _patientsHealed = _medic getVariable ["eden_patientsHealed", 0];
    _medic setVariable ["eden_patientsHealed", (_patientsHealed + 1), true];
    
    // Add experience
    _expGained = round(_healingAmount * 100); // XP based on healing amount
    _currentExp = _medic getVariable ["eden_experience", 0];
    _medic setVariable ["eden_experience", (_currentExp + _expGained), true];
    
    // Payment for medical services
    _treatmentCost = 200 + (_healingAmount * 300);
    _medicMoney = _medic getVariable ["eden_cash", 0];
    _medic setVariable ["eden_cash", (_medicMoney + _treatmentCost), true];
    
    // Patient pays (if they have money)
    _patientMoney = _patient getVariable ["eden_cash", 0];
    if (_patientMoney >= _treatmentCost) then {
        _patient setVariable ["eden_cash", (_patientMoney - _treatmentCost), true];
        [format ["Medical treatment cost: $%1", round _treatmentCost]] remoteExec ["EDEN_fnc_showHint", _patient];
    } else {
        // Free treatment if patient can't afford it
        [format ["Free medical treatment provided by %1", name _medic]] remoteExec ["EDEN_fnc_showHint", _patient];
    };
    
    // Notifications
    [format ["Successfully treated %1 (+%2 XP, +$%3)", name _patient, _expGained, round _treatmentCost]] call EDEN_fnc_showHint;
    [format ["You were treated by %1. Health restored!", name _medic]] remoteExec ["EDEN_fnc_showHint", _patient];
    
    // Special effects for patient
    ["Your injuries have been treated. You feel much better!"] remoteExec ["EDEN_fnc_showNotification", _patient];
    
} else {
    // Treatment failed
    ["Treatment failed! Patient's condition is too severe."] call EDEN_fnc_showHint;
    [format ["Treatment by %1 was unsuccessful.", name _medic]] remoteExec ["EDEN_fnc_showHint", _patient];
    
    // Still consume some medical supplies on failure
    if (random 100 < 30) then {
        _medicalKitQuantity = (_virtualItems select _medicalKitIndex) select 1;
        if (_medicalKitQuantity <= 1) then {
            _virtualItems deleteAt _medicalKitIndex;
        } else {
            (_virtualItems select _medicalKitIndex) set [1, (_medicalKitQuantity - 1)];
        };
        _medic setVariable ["eden_virtualItems", _virtualItems, true];
        ["Some medical supplies were used in the failed attempt."] call EDEN_fnc_showHint;
    };
};

// Log medical treatment
[format ["[EDEN] Medical treatment: %1 treated %2 - %3", name _medic, name _patient, if (_success) then {"successful"} else {"failed"}], "INFO", "MEDICAL"] call EDEN_fnc_systemLogger;

[_medic] call EDEN_fnc_savePlayerData;
[_patient] call EDEN_fnc_savePlayerData;

_success
