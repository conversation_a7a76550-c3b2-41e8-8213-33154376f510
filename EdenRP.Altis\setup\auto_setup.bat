@echo off
title EdenRP Automated Setup
color 0A
echo.
echo ===============================================
echo    EdenRP Automated Setup Script
echo    Enhanced Altis Life Server Setup
echo ===============================================
echo.

:: Set your server path
set "SERVER_PATH=C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server"
set "SETUP_PATH=%~dp0"

echo [INFO] Server Path: %SERVER_PATH%
echo [INFO] Setup Path: %SETUP_PATH%
echo.

:: Check if server directory exists
if not exist "%SERVER_PATH%" (
    echo [ERROR] Server directory not found: %SERVER_PATH%
    echo [INFO] Please ensure your Arma 3 Server is installed at this location
    pause
    exit /b 1
)

echo [STEP 1] Creating directory structure...
mkdir "%SERVER_PATH%\extDB3" 2>nul
mkdir "%SERVER_PATH%\extDB3\SQL_CUSTOM" 2>nul
mkdir "%SERVER_PATH%\extDB3\logs" 2>nul
mkdir "%SERVER_PATH%\mpmissions" 2>nul
mkdir "%SERVER_PATH%\profiles" 2>nul
mkdir "%SERVER_PATH%\profiles\server" 2>nul
mkdir "%SERVER_PATH%\battleye" 2>nul
mkdir "%SERVER_PATH%\keys" 2>nul
echo [OK] Directory structure created

echo.
echo [STEP 2] Copying EdenRP mission files...
if exist "%SETUP_PATH%..\EdenRP.Altis" (
    xcopy "%SETUP_PATH%..\*" "%SERVER_PATH%\mpmissions\EdenRP.Altis\" /E /I /Y >nul
    echo [OK] EdenRP mission files copied
) else (
    echo [ERROR] EdenRP.Altis folder not found in setup directory
    echo [INFO] Please ensure the EdenRP.Altis folder is in the same directory as this script
)

echo.
echo [STEP 3] Copying database configuration files...
if exist "%SETUP_PATH%..\database\extdb3-conf.ini" (
    copy "%SETUP_PATH%..\database\extdb3-conf.ini" "%SERVER_PATH%\extDB3\" >nul
    echo [OK] extDB3 configuration copied
)

if exist "%SETUP_PATH%..\database\*.ini" (
    copy "%SETUP_PATH%..\database\*.ini" "%SERVER_PATH%\extDB3\SQL_CUSTOM\" >nul
    echo [OK] SQL templates copied
)

echo.
echo [STEP 4] Creating server configuration files...
call :create_server_cfg
call :create_basic_cfg
call :create_startup_script
echo [OK] Configuration files created

echo.
echo [STEP 5] Downloading required components...
echo [INFO] Opening download pages in your browser...
echo [INFO] Please download and install the following:
echo.
echo 1. extDB3: https://github.com/SteezCram/extDB3/releases
echo 2. MySQL: https://dev.mysql.com/downloads/mysql/
echo 3. Visual C++ Redistributable: https://aka.ms/vs/16/release/vc_redist.x64.exe
echo.

:: Open download pages
start "" "https://github.com/SteezCram/extDB3/releases"
timeout /t 2 >nul
start "" "https://dev.mysql.com/downloads/mysql/"
timeout /t 2 >nul
start "" "https://aka.ms/vs/16/release/vc_redist.x64.exe"

echo.
echo [STEP 6] Creating database setup script...
call :create_database_script
echo [OK] Database setup script created

echo.
echo ===============================================
echo    SETUP COMPLETE!
echo ===============================================
echo.
echo NEXT STEPS:
echo 1. Download and install the components from the opened browser tabs
echo 2. Extract extDB3 files to: %SERVER_PATH%\extDB3\
echo 3. Run setup_database.sql in MySQL to create the database
echo 4. Edit extDB3\extdb3-conf.ini with your MySQL credentials
echo 5. Run start_server.bat to start your EdenRP server
echo.
echo All configuration files have been created for you!
echo.
pause
exit /b 0

:create_server_cfg
(
echo // EdenRP Server Configuration
echo // Generated by automated setup script
echo.
echo hostname = "EdenRP - Enhanced Altis Life Experience";
echo password = "";
echo passwordAdmin = "admin123";
echo serverCommandPassword = "server123";
echo.
echo // Mission Configuration
echo class Missions {
echo     class EdenRP {
echo         template = "EdenRP.Altis";
echo         difficulty = "Custom";
echo     };
echo };
echo.
echo // Server Settings
echo maxPlayers = 120;
echo kickDuplicate = 1;
echo verifySignatures = 2;
echo allowedFilePatching = 1;
echo requiredSecureId = 2;
echo.
echo // Performance Settings
echo maxPing = 300;
echo maxDesync = 150;
echo maxPacketLoss = 50;
echo.
echo // Logging
echo timeStampFormat = "short";
echo logFile = "server_console.log";
echo.
echo // Mods
echo serverMod = "@extDB3";
echo.
echo // Other Settings
echo persistent = 1;
echo autoInit = 1;
echo enablePlayerDiag = 1;
) > "%SERVER_PATH%\server.cfg"
goto :eof

:create_basic_cfg
(
echo // EdenRP Basic Configuration
echo // Generated by automated setup script
echo.
echo MaxMsgSend = 256;
echo MaxSizeGuaranteed = 512;
echo MaxSizeNonguaranteed = 256;
echo MinBandwidth = 131072;
echo MaxBandwidth = 2097152000;
echo MinErrorToSend = 0.001;
echo MinErrorToSendNear = 0.01;
echo MaxCustomFileSize = 160000;
echo.
echo // Performance Tweaks
echo adapter = -1;
echo 3D_Performance = 1;
echo Resolution_W = 0;
echo Resolution_H = 0;
echo Resolution_Bpp = 32;
echo.
echo // Terrain Settings
echo terrainGrid = 25;
echo viewDistance = 3000;
echo preferredObjectViewDistance = 2000;
) > "%SERVER_PATH%\basic.cfg"
goto :eof

:create_startup_script
(
echo @echo off
echo title EdenRP Server - Enhanced Altis Life
echo color 0A
echo echo ===============================================
echo echo    Starting EdenRP Server
echo echo    Enhanced Altis Life Experience
echo echo ===============================================
echo echo.
echo.
echo cd /d "%SERVER_PATH%"
echo.
echo echo [INFO] Starting server with the following configuration:
echo echo - Mission: EdenRP.Altis
echo echo - Max Players: 120
echo echo - Mods: @extDB3
echo echo - Database: MySQL with extDB3
echo echo.
echo.
echo arma3server_x64.exe ^
echo -serverMod=@extDB3 ^
echo -config=server.cfg ^
echo -cfg=basic.cfg ^
echo -profiles=profiles ^
echo -name=server ^
echo -world=Altis ^
echo -autoInit ^
echo -loadMissionToMemory ^
echo -enableHT ^
echo -hugepages
echo.
echo echo.
echo echo Server stopped. Press any key to restart or close this window.
echo pause
echo goto start
) > "%SERVER_PATH%\start_server.bat"
goto :eof

:create_database_script
(
echo -- EdenRP Database Setup Script
echo -- Generated by automated setup script
echo -- Run this in MySQL Workbench or MySQL Command Line
echo.
echo -- Create database
echo CREATE DATABASE IF NOT EXISTS edenrp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
echo.
echo -- Create user
echo CREATE USER IF NOT EXISTS 'edenrp_user'@'localhost' IDENTIFIED BY 'edenrp_password';
echo GRANT ALL PRIVILEGES ON edenrp.* TO 'edenrp_user'@'localhost';
echo FLUSH PRIVILEGES;
echo.
echo -- Use the database
echo USE edenrp;
echo.
echo -- Note: After running this script, import the full schema from:
echo -- EdenRP.Altis\database\edenrp_schema.sql
echo.
echo SELECT 'Database setup complete! Now import the full schema.' AS Status;
) > "%SERVER_PATH%\setup_database.sql"
goto :eof
