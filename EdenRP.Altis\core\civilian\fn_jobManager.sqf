/*
    EdenRP Job Manager
    Enhanced civilian job system with progression
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_action", "", [""]],
    ["_jobType", "", [""]],
    ["_data", [], [[]]]
];

// Validate parameters
if (isNull _player || !isPlayer _player) exitWith {
    ["Invalid player provided to jobManager", "ERROR", "JOBS"] call EDEN_fnc_systemLogger;
    false
};

private _result = false;
switch (toLower _action) do {
    case "start": {
        _result = [_player, _jobType, _data] call EDEN_fnc_startJob;
    };
    case "complete": {
        _result = [_player, _jobType, _data] call EDEN_fnc_completeJob;
    };
    case "cancel": {
        _result = [_player] call EDEN_fnc_cancelJob;
    };
    case "progress": {
        _result = [_player, _data] call EDEN_fnc_updateJobProgress;
    };
    case "getavailable": {
        _result = [_player] call EDEN_fnc_getAvailableJobs;
    };
    case "getstatus": {
        _result = [_player] call EDEN_fnc_getJobStatus;
    };
    case "getskills": {
        _result = [_player] call EDEN_fnc_getJobSkills;
    };
    default {
        [format["Unknown job action: %1", _action], "ERROR", "JOBS"] call EDEN_fnc_systemLogger;
    };
};

_result
