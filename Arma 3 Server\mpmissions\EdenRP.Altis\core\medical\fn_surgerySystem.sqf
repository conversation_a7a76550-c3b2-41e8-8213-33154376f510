/*
    File: fn_surgerySystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages surgical procedures system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_surgeriesPerformed", 0, true];
        _player setVariable ["eden_surgicalSkill", 1, true];
        true
    };
    case "performSurgery": {
        if (!(_player getVariable ["eden_onMedicalDuty", false])) exitWith {
            ["You must be on medical duty!"] call EDEN_fnc_showHint;
            false
        };
        
        if (isNull _target) exitWith { false };
        
        _skill = _player getVariable ["eden_surgicalSkill", 1];
        _surgeries = _player getVariable ["eden_surgeriesPerformed", 0];
        
        ["Performing surgery..."] call EDEN_fnc_showHint;
        ["Surgery in progress"] remoteExec ["EDEN_fnc_showHint", _target];
        
        sleep 10;
        
        _success = (random 100) < (50 + (_skill * 10));
        
        if (_success) then {
            _target setDamage 0;
            _target setVariable ["eden_injuries", [], true];
            _player setVariable ["eden_surgeriesPerformed", (_surgeries + 1), true];
            
            ["Surgery successful!"] call EDEN_fnc_showHint;
            ["Surgery was successful - you are fully healed"] remoteExec ["EDEN_fnc_showHint", _target];
        } else {
            ["Surgery failed - patient condition unchanged"] call EDEN_fnc_showHint;
            ["Surgery was unsuccessful"] remoteExec ["EDEN_fnc_showHint", _target];
        };
        
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "emergencySurgery": {
        if (isNull _target) exitWith { false };
        
        _target setDamage 0.1; // Stabilize critical patient
        ["Emergency surgery completed - patient stabilized"] call EDEN_fnc_showHint;
        ["Emergency surgery saved your life"] remoteExec ["EDEN_fnc_showHint", _target];
        true
    };
    default { false };
};
