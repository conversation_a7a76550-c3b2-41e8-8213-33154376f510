/*
    File: fn_auditSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages audit system.
*/

params [["_action", "init", [""]]];

switch (_action) do {
    case "init": {
        if (isServer) then {
            if (isNil "eden_auditLog") then {
                eden_auditLog = [];
            };
            if (isNil "eden_auditCategories") then {
                eden_auditCategories = [
                    "PLAYER_ACTION", "ADMIN_ACTION", "MONEY_TRANSFER", 
                    "ITEM_TRANSFER", "VEHICLE_ACTION", "JOB_ACTION",
                    "SECURITY_EVENT", "SYSTEM_EVENT"
                ];
                publicVariable "eden_auditCategories";
            };
        };
        true
    };
    case "logEvent": {
        params ["", ["_player", objNull, [objNull]], ["_category", "SYSTEM_EVENT", [""]], ["_action", "", [""]], ["_details", "", [""]]];
        
        if (!isServer) exitWith { false };
        
        _timestamp = format["%1-%2-%3 %4:%5:%6", 
            date select 0, date select 1, date select 2,
            date select 3, date select 4, floor(date select 5)
        ];
        
        _playerInfo = if (isNull _player) then { "SYSTEM" } else { format["%1 (%2)", name _player, getPlayerUID _player] };
        
        _auditEntry = [_timestamp, _playerInfo, _category, _action, _details];
        eden_auditLog pushBack _auditEntry;
        
        // Keep only last 1000 entries
        if (count eden_auditLog > 1000) then {
            eden_auditLog deleteAt 0;
        };
        
        [format ["[AUDIT] %1 | %2 | %3 | %4 | %5", _timestamp, _playerInfo, _category, _action, _details], "INFO", "AUDIT"] call EDEN_fnc_systemLogger;
        true
    };
    case "logPlayerAction": {
        params ["", ["_player", objNull, [objNull]], ["_action", "", [""]], ["_target", "", [""]], ["_amount", "", [""]]];
        
        _details = format["Action: %1, Target: %2, Amount: %3", _action, _target, _amount];
        ["logEvent", _player, "PLAYER_ACTION", _action, _details] call EDEN_fnc_auditSystem;
        true
    };
    case "logAdminAction": {
        params ["", ["_admin", objNull, [objNull]], ["_action", "", [""]], ["_target", "", [""]], ["_reason", "", [""]]];
        
        _details = format["Action: %1, Target: %2, Reason: %3", _action, _target, _reason];
        ["logEvent", _admin, "ADMIN_ACTION", _action, _details] call EDEN_fnc_auditSystem;
        true
    };
    case "logMoneyTransfer": {
        params ["", ["_sender", objNull, [objNull]], ["_receiver", objNull, [objNull]], ["_amount", 0, [0]]];
        
        _details = format["From: %1 To: %2 Amount: $%3", name _sender, name _receiver, _amount];
        ["logEvent", _sender, "MONEY_TRANSFER", "transfer", _details] call EDEN_fnc_auditSystem;
        true
    };
    case "logSecurityEvent": {
        params ["", ["_player", objNull, [objNull]], ["_eventType", "", [""]], ["_severity", "LOW", [""]], ["_description", "", [""]]];
        
        _details = format["Type: %1, Severity: %2, Description: %3", _eventType, _severity, _description];
        ["logEvent", _player, "SECURITY_EVENT", _eventType, _details] call EDEN_fnc_auditSystem;
        true
    };
    case "searchAuditLog": {
        params ["", ["_searchTerm", "", [""]], ["_category", "", [""]], ["_timeframe", 86400, [0]]];
        
        if (!isServer) exitWith { [] };
        
        _results = [];
        _cutoffTime = time - _timeframe;
        
        {
            _entry = _x;
            _matchesSearch = if (_searchTerm == "") then { true } else { 
                (_searchTerm in (_entry select 1)) || (_searchTerm in (_entry select 4))
            };
            _matchesCategory = if (_category == "") then { true } else { 
                (_entry select 2) == _category
            };
            
            if (_matchesSearch && _matchesCategory) then {
                _results pushBack _entry;
            };
        } forEach eden_auditLog;
        
        _results
    };
    case "generateReport": {
        params ["", ["_admin", objNull, [objNull]], ["_category", "", [""]], ["_timeframe", 86400, [0]]];
        
        if (isNull _admin || !(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _results = ["searchAuditLog", "", _category, _timeframe] call EDEN_fnc_auditSystem;
        
        _reportText = format["=== AUDIT REPORT ===\nCategory: %1\nTimeframe: %2 hours\nEntries: %3\n\n", 
            if (_category == "") then { "ALL" } else { _category },
            floor(_timeframe / 3600),
            count _results
        ];
        
        _maxEntries = 20;
        for "_i" from 0 to ((count _results min _maxEntries) - 1) do {
            _entry = _results select _i;
            _reportText = _reportText + format["%1 | %2 | %3\n", 
                (_entry select 0), (_entry select 2), (_entry select 3)
            ];
        };
        
        if (count _results > _maxEntries) then {
            _reportText = _reportText + format["... and %1 more entries", (count _results - _maxEntries)];
        };
        
        [_reportText] remoteExec ["EDEN_fnc_showHint", _admin];
        true
    };
    default { false };
};
