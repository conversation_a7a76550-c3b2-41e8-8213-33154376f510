/*
    File: fn_appraisalSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages property appraisal system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_propertyId", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_propertyValues", [], true];
        _player setVariable ["eden_appraisalHistory", [], true];
        true
    };
    case "requestAppraisal": {
        _owned = _player getVariable ["eden_ownedProperties", []];
        if !(_propertyId in _owned) exitWith {
            ["You don't own this property!"] call EDEN_fnc_showHint;
            false
        };
        
        _appraisalFee = 100;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _appraisalFee) exitWith {
            [format ["Not enough money! Need $%1", _appraisalFee]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _appraisalFee), true];
        
        _baseValue = 50000;
        _condition = 100;
        _marketFactor = (random 0.4) + 0.8; // 0.8 to 1.2
        
        _conditions = _player getVariable ["eden_propertyCondition", []];
        {
            if ((_x select 0) == _propertyId) then {
                _condition = _x select 1;
            };
        } forEach _conditions;
        
        _appraisedValue = floor(_baseValue * (_condition / 100) * _marketFactor);
        
        _values = _player getVariable ["eden_propertyValues", []];
        _found = false;
        {
            if ((_x select 0) == _propertyId) then {
                _x set [1, _appraisedValue];
                _values set [_forEachIndex, _x];
                _found = true;
            };
        } forEach _values;
        
        if (!_found) then {
            _values pushBack [_propertyId, _appraisedValue];
        };
        _player setVariable ["eden_propertyValues", _values, true];
        
        _history = _player getVariable ["eden_appraisalHistory", []];
        _history pushBack [_propertyId, _appraisedValue, time];
        _player setVariable ["eden_appraisalHistory", _history, true];
        
        [format ["Property appraised at $%1", _appraisedValue]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
