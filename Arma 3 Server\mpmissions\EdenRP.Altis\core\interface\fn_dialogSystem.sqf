/*
    File: fn_dialogSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages dialog system.
*/

params [["_title", "Dialog", [""]], ["_options", [], [[]]]];

if (count _options == 0) exitWith { false };

// Create dialog display
createDialog "RscDisplayEmpty";
_display = findDisplay 46;

// Create title
_titleCtrl = _display ctrlCreate ["RscText", 1000];
_titleCtrl ctrlSetPosition [0.3, 0.2, 0.4, 0.05];
_titleCtrl ctrlSetText _title;
_titleCtrl ctrlCommit 0;

// Create option buttons
for "_i" from 0 to (count _options - 1) do {
    _button = _display ctrlCreate ["RscButton", (1001 + _i)];
    _button ctrlSetPosition [0.35, (0.3 + (_i * 0.06)), 0.3, 0.05];
    _button ctrlSetText (_options select _i);
    _button ctrlAddEventHandler ["ButtonClick", format["[%1] call EDEN_fnc_handleDialogChoice;", _i]];
    _button ctrlCommit 0;
};

true
