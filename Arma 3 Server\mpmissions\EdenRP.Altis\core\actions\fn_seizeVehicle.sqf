/*
    File: fn_seizeVehicle.sqf
    Author: EdenRP Development Team
    
    Description:
    Seizes a vehicle permanently (police only).
    
    Parameters:
    0: OBJECT - Vehicle to seize
    1: OBJECT - Officer (optional, default: player)
    
    Returns:
    BOOLEAN - True if vehicle was seized successfully
*/

params [
    ["_vehicle", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_officer", player, [obj<PERSON><PERSON>]]
];

if (isNull _vehicle || isNull _officer) exitWith { false };

if (!(_officer getVariable ["eden_isPolice", false])) exitWith {
    ["Only police officers can seize vehicles!"] call EDEN_fnc_showHint;
    false
};

if (_officer distance _vehicle > 10) exitWith {
    ["Vehicle is too far away!"] call EDEN_fnc_showHint;
    false
};

if (!(_vehicle isKindOf "LandVehicle") && !(_vehicle isKindOf "Air") && !(_vehicle isKindOf "Ship")) exitWith {
    ["Invalid vehicle type!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_officer, "police_action", [_vehicle, "seize_vehicle"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Eject all passengers
{
    _x action ["GetOut", _vehicle];
} forEach crew _vehicle;

sleep 1;

// Get vehicle info
_vehicleType = typeOf _vehicle;
_vehicleOwner = _vehicle getVariable ["eden_ownerName", "Unknown"];
_vehicleValue = _vehicle getVariable ["eden_purchasePrice", 50000];

// Mark as seized
_vehicle setVariable ["eden_isSeized", true, true];
_vehicle setVariable ["eden_seizedBy", name _officer, true];
_vehicle setVariable ["eden_seizeTime", time, true];
_vehicle setVariable ["eden_seizeReason", "Police seizure", true];

// Move to police impound
_policeImpound = [3700, 13450, 0]; // Police impound coordinates
_vehicle setPos _policeImpound;
_vehicle lock 3; // Lock for everyone

// Add to seized vehicles database
_seizedVehicles = missionNamespace getVariable ["eden_seizedVehicles", []];
_seizedEntry = [
    time,
    name _officer,
    _vehicleOwner,
    _vehicleType,
    _vehicleValue,
    "Police seizure - criminal activity"
];
_seizedVehicles pushBack _seizedEntry;
missionNamespace setVariable ["eden_seizedVehicles", _seizedVehicles, true];

// Create seizure marker
_markerName = format ["seized_%1", floor(random 9999)];
_marker = createMarker [_markerName, _policeImpound];
_marker setMarkerType "mil_triangle";
_marker setMarkerText format ["Seized: %1", _vehicleType];
_marker setMarkerColor "ColorBlack";

// Update police statistics
_vehiclesSeized = _officer getVariable ["eden_vehiclesSeized", 0];
_officer setVariable ["eden_vehiclesSeized", (_vehiclesSeized + 1), true];

// Notifications
[format ["Vehicle seized: %1 (Owner: %2)", _vehicleType, _vehicleOwner]] call EDEN_fnc_showHint;

// Notify vehicle owner if online
_ownerUID = _vehicle getVariable ["eden_ownerUID", ""];
if (_ownerUID != "") then {
    {
        if (getPlayerUID _x == _ownerUID) then {
            [
                "Vehicle Seized",
                format ["Your %1 has been permanently seized by %2 due to criminal activity", _vehicleType, name _officer],
                15,
                "error"
            ] remoteExec ["EDEN_fnc_showNotification", _x];
        };
    } forEach allPlayers;
};

// Log the seizure
[format ["[EDEN] Officer %1 seized vehicle %2 (owner: %3)", name _officer, _vehicleType, _vehicleOwner], "WARN", "POLICE"] call EDEN_fnc_systemLogger;

[_officer] call EDEN_fnc_savePlayerData;

true
