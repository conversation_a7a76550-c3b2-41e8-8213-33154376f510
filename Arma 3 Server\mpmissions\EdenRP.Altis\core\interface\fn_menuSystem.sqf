/*
    File: fn_menuSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages menu system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_menuOpen", false, true];
        true
    };
    case "openMainMenu": {
        if (_player getVariable ["eden_menuOpen", false]) exitWith { false };
        
        _player setVariable ["eden_menuOpen", true, true];
        
        _options = [
            "Player Info",
            "Inventory", 
            "Job Center",
            "Bank",
            "Settings",
            "Close"
        ];
        
        ["Main Menu", _options] call EDEN_fnc_showDialog;
        true
    };
    case "closeMenu": {
        _player setVariable ["eden_menuOpen", false, true];
        closeDialog 0;
        true
    };
    case "openJobMenu": {
        _availableJobs = ["civilian", "police", "ems", "mechanic", "taxi", "trucker"];
        ["Job Center", _availableJobs] call EDEN_fnc_showDialog;
        true
    };
    default { false };
};
