/*
    File: fn_storeVehicle.sqf
    Author: EdenRP Development Team
    
    Description:
    Stores a vehicle in the player's garage.
    
    Parameters:
    0: OBJECT - Vehicle to store
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if storage was successful
*/

params [
    ["_vehicle", obj<PERSON>ull, [obj<PERSON><PERSON>]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (isNull _vehicle || isNull _player) exitWith { false };

if (_player distance _vehicle > 10) exitWith {
    ["Vehicle is too far away!"] call EDEN_fnc_showHint;
    false
};

// Check if player owns the vehicle
_vehicleOwner = _vehicle getVariable ["eden_ownerUID", ""];
_playerUID = getPlayerUID _player;

if (_vehicleOwner != _playerUID) exitWith {
    ["You don't own this vehicle!"] call EDEN_fnc_showHint;
    false
};

// Check if near garage
_nearGarages = nearestObjects [_player, ["Land_CarService_F", "Land_Garage_V1_F"], 25];
if (count _nearGarages == 0) exitWith {
    ["You must be near a garage to store vehicles!"] call EDEN_fnc_showHint;
    false
};

// Check if vehicle is damaged
if (damage _vehicle > 0.1) exitWith {
    ["Vehicle must be repaired before storing!"] call EDEN_fnc_showHint;
    false
};

// Eject all passengers
{
    _x action ["GetOut", _vehicle];
} forEach crew _vehicle;

sleep 1;

// Get vehicle data
_vehicleData = [
    typeOf _vehicle,
    damage _vehicle,
    fuel _vehicle,
    _vehicle getVariable ["eden_vehicleColor", ""],
    _vehicle getVariable ["eden_vehicleMods", []],
    _vehicle getVariable ["eden_vehicleInventory", []]
];

// Add to player's garage
_playerGarage = _player getVariable ["eden_garage", []];
_playerGarage pushBack _vehicleData;
_player setVariable ["eden_garage", _playerGarage, true];

// Delete the physical vehicle
deleteVehicle _vehicle;

[format ["Vehicle stored in garage. Total vehicles: %1", count _playerGarage]] call EDEN_fnc_showHint;

// Log storage
[format ["[EDEN] Player %1 stored vehicle %2 in garage", name _player, typeOf _vehicle], "INFO", "VEHICLE"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
