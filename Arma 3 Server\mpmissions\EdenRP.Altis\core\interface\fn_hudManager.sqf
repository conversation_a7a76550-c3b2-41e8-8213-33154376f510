/*
    File: fn_hudManager.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages HUD display system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_hudEnabled", true, true];
        _player setVariable ["eden_hudElements", ["cash", "bank", "job", "health"], true];
        true
    };
    case "updateHUD": {
        if (!(_player getVariable ["eden_hudEnabled", true])) exitWith { false };
        
        _cash = _player getVariable ["eden_cash", 0];
        _bank = _player getVariable ["eden_bankAccount", 0];
        _job = _player getVariable ["eden_job", "civilian"];
        _health = floor((1 - damage _player) * 100);
        
        _hudText = format["Cash: $%1 | Bank: $%2 | Job: %3 | Health: %4%%", _cash, _bank, _job, _health];
        
        [_hudText, 0, 0, 5, 0] spawn BIS_fnc_dynamicText;
        true
    };
    case "toggleHUD": {
        _enabled = _player getVariable ["eden_hudEnabled", true];
        _player setVariable ["eden_hudEnabled", !_enabled, true];
        
        if (!_enabled) then {
            ["HUD enabled"] call EDEN_fnc_showHint;
        } else {
            ["HUD disabled"] call EDEN_fnc_showHint;
        };
        true
    };
    default { false };
};
