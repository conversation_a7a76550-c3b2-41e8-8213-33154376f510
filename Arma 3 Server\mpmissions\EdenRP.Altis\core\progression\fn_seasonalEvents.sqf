/*
    File: fn_seasonalEvents.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages seasonal events system.
*/

params [["_action", "init", [""]]];

switch (_action) do {
    case "init": {
        if (isServer) then {
            if (isNil "eden_seasonalEvents") then {
                eden_seasonalEvents = [
                    ["summer_festival", "Summer Festival", [6, 7, 8], false],
                    ["halloween_event", "Halloween Spooktacular", [10], false],
                    ["winter_wonderland", "Winter Wonderland", [12, 1, 2], false],
                    ["spring_cleanup", "Spring Cleanup", [3, 4, 5], false]
                ];
                publicVariable "eden_seasonalEvents";
            };
            eden_activeSeasonalEvent = "";
            publicVariable "eden_activeSeasonalEvent";
        };
        true
    };
    case "checkSeasonalEvents": {
        if (isServer) then {
            _currentMonth = parseNumber (formatText ["%1", date select 1]);
            _newActiveEvent = "";
            
            {
                _eventMonths = _x select 2;
                if (_currentMonth in _eventMonths) then {
                    _newActiveEvent = _x select 0;
                };
            } forEach eden_seasonalEvents;
            
            if (_newActiveEvent != eden_activeSeasonalEvent) then {
                if (eden_activeSeasonalEvent != "") then {
                    ["endSeasonalEvent", eden_activeSeasonalEvent] call EDEN_fnc_seasonalEvents;
                };
                
                if (_newActiveEvent != "") then {
                    ["startSeasonalEvent", _newActiveEvent] call EDEN_fnc_seasonalEvents;
                };
                
                eden_activeSeasonalEvent = _newActiveEvent;
                publicVariable "eden_activeSeasonalEvent";
            };
        };
        true
    };
    case "startSeasonalEvent": {
        params ["", ["_eventID", "", [""]]];
        
        if (!isServer) exitWith { false };
        
        _eventData = [];
        {
            if ((_x select 0) == _eventID) then { _eventData = _x; };
        } forEach eden_seasonalEvents;
        
        if (count _eventData == 0) exitWith { false };
        
        _eventName = _eventData select 1;
        
        {
            [format ["🎉 %1 has begun! Special rewards and activities available!", _eventName]] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach allPlayers;
        
        switch (_eventID) do {
            case "summer_festival": {
                eden_summerFestivalActive = true;
                publicVariable "eden_summerFestivalActive";
            };
            case "halloween_event": {
                eden_halloweenEventActive = true;
                publicVariable "eden_halloweenEventActive";
            };
            case "winter_wonderland": {
                eden_winterEventActive = true;
                publicVariable "eden_winterEventActive";
            };
            case "spring_cleanup": {
                eden_springEventActive = true;
                publicVariable "eden_springEventActive";
            };
        };
        
        true
    };
    case "endSeasonalEvent": {
        params ["", ["_eventID", "", [""]]];
        
        if (!isServer) exitWith { false };
        
        _eventData = [];
        {
            if ((_x select 0) == _eventID) then { _eventData = _x; };
        } forEach eden_seasonalEvents;
        
        if (count _eventData == 0) exitWith { false };
        
        _eventName = _eventData select 1;
        
        {
            [format ["🎊 %1 has ended! Thanks for participating!", _eventName]] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach allPlayers;
        
        switch (_eventID) do {
            case "summer_festival": {
                eden_summerFestivalActive = false;
                publicVariable "eden_summerFestivalActive";
            };
            case "halloween_event": {
                eden_halloweenEventActive = false;
                publicVariable "eden_halloweenEventActive";
            };
            case "winter_wonderland": {
                eden_winterEventActive = false;
                publicVariable "eden_winterEventActive";
            };
            case "spring_cleanup": {
                eden_springEventActive = false;
                publicVariable "eden_springEventActive";
            };
        };
        
        true
    };
    case "getActiveEvent": {
        if (isNil "eden_activeSeasonalEvent") then { "" } else { eden_activeSeasonalEvent }
    };
    case "giveEventReward": {
        params ["", ["_player", objNull, [objNull]], ["_eventID", "", [""]]];
        
        if (isNull _player) exitWith { false };
        
        _multiplier = switch (_eventID) do {
            case "summer_festival": { 1.5 };
            case "halloween_event": { 2.0 };
            case "winter_wonderland": { 1.8 };
            case "spring_cleanup": { 1.3 };
            default { 1.0 };
        };
        
        _baseReward = 500;
        _eventReward = floor(_baseReward * _multiplier);
        
        [_player, "giveReward", "cash", _eventReward, "Seasonal Event"] call EDEN_fnc_rewardSystem;
        true
    };
    default { false };
};
