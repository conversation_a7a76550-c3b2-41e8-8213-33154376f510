/*
    EdenRP Hospital System
    Enhanced hospital management and medical services
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_action", "", [""]],
    ["_data", [], [[]]]
];

// Validate parameters
if (isNull _player || !isPlayer _player) exitWith {
    ["Invalid player provided to hospitalSystem", "ERROR", "MEDICAL"] call EDEN_fnc_systemLogger;
    false
};

private _result = false;
switch (toLower _action) do {
    case "checkin": {
        _result = [_player, _data] call EDEN_fnc_hospitalCheckIn;
    };
    case "treatment": {
        _result = [_player, _data] call EDEN_fnc_hospitalTreatment;
    };
    case "surgery": {
        _result = [_player, _data] call EDEN_fnc_hospitalSurgery;
    };
    case "pharmacy": {
        _result = [_player, _data] call EDEN_fnc_hospitalPharmacy;
    };
    case "emergency": {
        _result = [_player, _data] call EDEN_fnc_hospitalEmergency;
    };
    case "discharge": {
        _result = [_player] call EDEN_fnc_hospitalDischarge;
    };
    case "bill": {
        _result = [_player, _data] call EDEN_fnc_hospitalBilling;
    };
    case "records": {
        _result = [_player] call EDEN_fnc_getMedicalRecords;
    };
    default {
        [format["Unknown hospital action: %1", _action], "ERROR", "MEDICAL"] call EDEN_fnc_systemLogger;
    };
};

_result
