/*
    File: fn_intercomSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages intercom communication system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_intercomAccess", [], true];
        true
    };
    case "useIntercom": {
        params ["", "", ["_building", objNull, [objNull]], ["_message", "", [""]]];
        
        if (isNull _building || _message == "") exitWith { false };
        
        _access = _player getVariable ["eden_intercomAccess", []];
        _buildingID = str(_building);
        
        if (!(_buildingID in _access)) exitWith {
            ["No intercom access to this building"] call EDEN_fnc_showHint;
            false
        };
        
        _nearPlayers = [];
        {
            if (_x distance _building < 50) then { _nearPlayers pushBack _x; };
        } forEach allPlayers;
        
        {
            [format ["INTERCOM: %1", _message]] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach _nearPlayers;
        
        ["Intercom message sent"] call EDEN_fnc_showHint;
        true
    };
    case "grantAccess": {
        params ["", "", ["_target", objNull, [objNull]], ["_building", objNull, [objNull]]];
        
        if (isNull _target || isNull _building) exitWith { false };
        
        _access = _target getVariable ["eden_intercomAccess", []];
        _buildingID = str(_building);
        
        if (!(_buildingID in _access)) then {
            _access pushBack _buildingID;
            _target setVariable ["eden_intercomAccess", _access, true];
            [format ["Granted intercom access to %1", name _target]] call EDEN_fnc_showHint;
        };
        
        true
    };
    default { false };
};
