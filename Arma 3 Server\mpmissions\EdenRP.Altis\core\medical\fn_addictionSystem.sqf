/*
    File: fn_addictionSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages addiction and substance abuse system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_substance", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_addictionLevel", 0, true];
        _player setVariable ["eden_withdrawalLevel", 0, true];
        _player setVariable ["eden_lastUse", 0, true];
        true
    };
    case "useSubstance": {
        _addiction = _player getVariable ["eden_addictionLevel", 0];
        _addiction = (_addiction + 5) min 100;
        _player setVariable ["eden_addictionLevel", _addiction, true];
        _player setVariable ["eden_lastUse", time, true];
        
        [format ["Substance used - addiction level: %1%%", _addiction]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "withdrawal": {
        _lastUse = _player getVariable ["eden_lastUse", 0];
        if ((time - _lastUse) > 3600) then { // 1 hour
            _withdrawal = _player getVariable ["eden_withdrawalLevel", 0];
            _withdrawal = (_withdrawal + 10) min 100;
            _player setVariable ["eden_withdrawalLevel", _withdrawal, true];
            
            if (_withdrawal > 50) then {
                ["Severe withdrawal symptoms - seek help"] call EDEN_fnc_showHint;
                _player setDamage ((damage _player) + 0.1);
            };
        };
        true
    };
    default { false };
};
