/*
    EdenRP Admin Ban Function
    Enhanced ban system with comprehensive logging
*/

params [
    ["_admin", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_target", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_data", [], [[]]]
];

// Validate parameters
if (isNull _admin || isNull _target) exitWith {
    ["Invalid admin or target provided to adminBan", "ERROR", "ADMIN"] call EDEN_fnc_systemLogger;
    false
};

// Extract ban data
private _reason = if (count _data > 0) then {_data select 0} else {"No reason provided"};
private _duration = if (count _data > 1) then {_data select 1} else {0}; // 0 = permanent
private _banType = if (count _data > 2) then {_data select 2} else {"PERMANENT"};

// Get player information
private _targetUID = getPlayerUID _target;
private _targetName = name _target;
private _adminUID = getPlayerUID _admin;
private _adminName = name _admin;

// Validate ban reason
if (_reason == "" || count _reason < 5) exitWith {
    ["Ban reason must be at least 5 characters", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
    false
};

// Check if target is also an admin
private _targetAdminLevel = _target getVariable ["EDEN_AdminLevel", 0];
private _adminLevel = _admin getVariable ["EDEN_AdminLevel", 0];

if (_targetAdminLevel > 0 && _targetAdminLevel >= _adminLevel) exitWith {
    ["You cannot ban an admin of equal or higher level", "error"] remoteExec ["EDEN_fnc_showNotification", _admin];
    false
};

// Calculate ban expiration
private _banExpires = if (_duration > 0) then {
    systemTime;
    private _currentTime = systemTime;
    private _expirationTime = [
        _currentTime select 0, // year
        _currentTime select 1, // month
        _currentTime select 2, // day
        _currentTime select 3, // hour
        _currentTime select 4, // minute
        (_currentTime select 5) + (_duration * 60) // seconds + duration in minutes
    ];
    
    // Handle overflow
    if ((_expirationTime select 5) >= 60) then {
        _expirationTime set [4, (_expirationTime select 4) + floor ((_expirationTime select 5) / 60)];
        _expirationTime set [5, (_expirationTime select 5) mod 60];
    };
    
    if ((_expirationTime select 4) >= 60) then {
        _expirationTime set [3, (_expirationTime select 3) + floor ((_expirationTime select 4) / 60)];
        _expirationTime set [4, (_expirationTime select 4) mod 60];
    };
    
    if ((_expirationTime select 3) >= 24) then {
        _expirationTime set [2, (_expirationTime select 2) + floor ((_expirationTime select 3) / 24)];
        _expirationTime set [3, (_expirationTime select 3) mod 24];
    };
    
    str _expirationTime
} else {
    "NULL"
};

// Update player ban status in database
private _query = format [
    "EDEN_Players:banPlayer:%1:%2:%3",
    _reason,
    _banExpires,
    _targetUID
];

private _queryId = [_query, 1] call EDEN_fnc_asyncCall;

// Log admin action
private _logQuery = format [
    "INSERT INTO eden_admin_actions (admin_id, target_id, action, reason, timestamp, additional_data) VALUES ('%1', '%2', 'BAN', '%3', NOW(), '%4')",
    _adminUID,
    _targetUID,
    _reason,
    format["Duration: %1 minutes, Type: %2", _duration, _banType]
];

[_logQuery, 1] call EDEN_fnc_asyncCall;

// Create ban message
private _banMessage = format [
    "You have been banned from EdenRP\n\nReason: %1\nBanned by: %2\nDuration: %3\n\nIf you believe this ban is unjust, please contact the administration team.",
    _reason,
    _adminName,
    if (_duration > 0) then {format["%1 minutes", _duration]} else {"Permanent"}
];

// Notify target of ban
[_banMessage, "error"] remoteExec ["EDEN_fnc_showNotification", _target];

// Kick player after short delay
[_target, _banMessage] spawn {
    params ["_target", "_banMessage"];
    sleep 3;
    [_target, _banMessage] call EDEN_fnc_kickPlayer;
};

// Notify all admins
private _adminNotification = format [
    "ADMIN ACTION: %1 banned %2 for %3 (Duration: %4)",
    _adminName,
    _targetName,
    _reason,
    if (_duration > 0) then {format["%1 minutes", _duration]} else {"Permanent"}
];

{
    if ((_x getVariable ["EDEN_AdminLevel", 0]) >= 1) then {
        [_adminNotification, "warning"] remoteExec ["EDEN_fnc_showNotification", _x];
    };
} forEach allPlayers;

// Log to system
[format["ADMIN BAN: %1 (%2) banned %3 (%4) for '%5' (Duration: %6 minutes)", 
    _adminName, _adminUID, _targetName, _targetUID, _reason, _duration], "INFO", "ADMIN"] call EDEN_fnc_systemLogger;

// Update admin statistics
private _totalBans = _admin getVariable ["EDEN_TotalBans", 0];
_admin setVariable ["EDEN_TotalBans", _totalBans + 1, false];

// Add to global ban list for immediate effect
if (isNil "EDEN_BannedPlayers") then {
    EDEN_BannedPlayers = [];
};

EDEN_BannedPlayers pushBack [
    _targetUID,
    _targetName,
    _reason,
    _adminUID,
    _adminName,
    time,
    _banExpires,
    _banType
];

// Broadcast ban to all servers (if multi-server setup)
// This would be implemented based on your server architecture

// Success notification to admin
[format["Successfully banned %1 for: %2", _targetName, _reason], "success"] remoteExec ["EDEN_fnc_showNotification", _admin];

true
