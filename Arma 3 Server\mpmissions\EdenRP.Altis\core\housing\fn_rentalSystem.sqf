/*
    File: fn_rentalSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages property rental system.
*/

params [["_player", player, [objN<PERSON>]], ["_action", "init", [""]], ["_propertyId", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_rentedProperties", [], true];
        _player setVariable ["eden_rentalIncome", 0, true];
        true
    };
    case "rentProperty": {
        params ["", "", "", ["_monthlyRent", 1000, [0]]];
        
        _cash = _player getVariable ["eden_cash", 0];
        if (_cash < _monthlyRent) exitWith {
            [format ["Not enough money! Need $%1", _monthlyRent]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _monthlyRent), true];
        
        _rented = _player getVariable ["eden_rentedProperties", []];
        _rental = [_propertyId, _monthlyRent, time];
        _rented pushBack _rental;
        _player setVariable ["eden_rentedProperties", _rented, true];
        
        [format ["Rented property for $%1/month", _monthlyRent]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "collectRent": {
        _owned = _player getVariable ["eden_ownedProperties", []];
        if !(_propertyId in _owned) exitWith {
            ["You don't own this property!"] call EDEN_fnc_showHint;
            false
        };
        
        _rentAmount = 800;
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _rentAmount), true];
        
        _income = _player getVariable ["eden_rentalIncome", 0];
        _player setVariable ["eden_rentalIncome", (_income + _rentAmount), true];
        
        [format ["Collected $%1 rent", _rentAmount]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
