/*
    File: fn_gatherApples.sqf
    Author: EdenRP Development Team
    
    Description:
    Gathers apples from apple trees.
    
    Parameters:
    0: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if apples were gathered successfully
*/

params [["_player", player, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

// Check if near apple trees
_nearTrees = nearestObjects [_player, ["Land_Apple_01_F", "Land_Apple_02_F"], 5];
if (count _nearTrees == 0) exitWith {
    ["You must be near apple trees to gather apples!"] call EDEN_fnc_showHint;
    false
};

// Check inventory space
_currentWeight = _player getVariable ["eden_currentWeight", 0];
_maxWeight = _player getVariable ["eden_maxWeight", 50];
_appleWeight = 1; // Each apple weighs 1kg

if ((_currentWeight + _appleWeight) > _maxWeight) exitWith {
    ["Inventory is full!"] call EDEN_fnc_showHint;
    false
};

// Check if tree was recently harvested
_tree = _nearTrees select 0;
_lastHarvested = _tree getVariable ["eden_lastHarvested", 0];
if ((time - _lastHarvested) < 300) exitWith { // 5 minute cooldown
    ["This tree was recently harvested. Wait a few minutes."] call EDEN_fnc_showHint;
    false
};

// Gathering animation
[_player, "Acts_carFixingWheel"] remoteExec ["switchMove"];
["Gathering apples..."] call EDEN_fnc_showHint;

sleep 3; // Gathering time

[_player, ""] remoteExec ["switchMove"];

// Random number of apples (1-3)
_applesGathered = 1 + floor(random 3);

// Add apples to inventory
_virtualItems = _player getVariable ["eden_virtualItems", []];
_found = false;
{
    if ((_x select 0) == "apple") then {
        _x set [1, ((_x select 1) + _applesGathered)];
        _found = true;
    };
} forEach _virtualItems;

if (!_found) then {
    _virtualItems pushBack ["apple", _applesGathered];
};

_player setVariable ["eden_virtualItems", _virtualItems, true];
_player setVariable ["eden_currentWeight", (_currentWeight + (_applesGathered * _appleWeight)), true];

// Mark tree as harvested
_tree setVariable ["eden_lastHarvested", time, true];

// Add experience
_expGained = _applesGathered * 5;
_currentExp = _player getVariable ["eden_experience", 0];
_player setVariable ["eden_experience", (_currentExp + _expGained), true];

[format ["Gathered %1 apples (+%2 XP)", _applesGathered, _expGained]] call EDEN_fnc_showHint;

// Log gathering
[format ["[EDEN] Player %1 gathered %2 apples", name _player, _applesGathered], "DEBUG", "GATHERING"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
