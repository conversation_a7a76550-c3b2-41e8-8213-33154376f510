/*
    File: fn_lockpickVehicle.sqf
    Author: EdenRP Development Team
    
    Description:
    Attempts to lockpick a locked vehicle.
    
    Parameters:
    0: OBJECT - Vehicle to lockpick
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if lockpick was successful
*/

params [
    ["_vehicle", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (isNull _vehicle || isNull _player) exitWith { false };

if (_player distance _vehicle > 5) exitWith {
    ["Vehicle is too far away!"] call EDEN_fnc_showHint;
    false
};

if (!(_vehicle isKindOf "LandVehicle") && !(_vehicle isKindOf "Air")) exitWith {
    ["This vehicle cannot be lockpicked!"] call EDEN_fnc_showHint;
    false
};

if (!alive _vehicle) exitWith {
    ["Cannot lockpick destroyed vehicles!"] call EDEN_fnc_showHint;
    false
};

if (locked _vehicle == 0) exitWith {
    ["Vehicle is not locked!"] call EDEN_fnc_showHint;
    false
};

// Check if player has lockpicks
_virtualItems = _player getVariable ["eden_virtualItems", []];
_hasLockpick = false;
_lockpickIndex = -1;
{
    if ((_x select 0) == "lockpick" && (_x select 1) > 0) then {
        _hasLockpick = true;
        _lockpickIndex = _forEachIndex;
    };
} forEach _virtualItems;

if (!_hasLockpick) exitWith {
    ["You need lockpicks to break into vehicles!"] call EDEN_fnc_showHint;
    false
};

// Check if this is a police/medical vehicle
if (_vehicle getVariable ["eden_isPoliceVehicle", false] || _vehicle getVariable ["eden_isMedicalVehicle", false]) exitWith {
    ["You cannot lockpick emergency service vehicles!"] call EDEN_fnc_showHint;
    false
};

// Calculate success chance based on player level
_playerLevel = _player getVariable ["eden_playerLevel", 1];
_baseChance = 30; // 30% base chance
_levelBonus = _playerLevel * 5; // +5% per level
_successChance = (_baseChance + _levelBonus) min 85; // Max 85% chance

// Start lockpicking process
[_player, "Acts_carFixingWheel"] remoteExec ["switchMove"];
[format ["Lockpicking vehicle... (%1%% chance)", _successChance]] call EDEN_fnc_showHint;

sleep 5; // Lockpicking time

[_player, ""] remoteExec ["switchMove"];

// Determine success
_success = (random 100) < _successChance;

// Consume lockpick (always consumed on attempt)
_lockpickQuantity = (_virtualItems select _lockpickIndex) select 1;
if (_lockpickQuantity <= 1) then {
    _virtualItems deleteAt _lockpickIndex;
} else {
    (_virtualItems select _lockpickIndex) set [1, (_lockpickQuantity - 1)];
};
_player setVariable ["eden_virtualItems", _virtualItems, true];

if (_success) then {
    // Successful lockpick
    _vehicle lock 0;
    
    // Add criminal activity
    _wantedLevel = _player getVariable ["eden_wantedLevel", 0];
    _player setVariable ["eden_wantedLevel", (_wantedLevel + 1), true];
    
    // Add to criminal record
    _criminalRecord = _player getVariable ["eden_criminalRecord", []];
    _crimeRecord = [
        time,
        "System",
        "Vehicle theft attempt",
        typeOf _vehicle
    ];
    _criminalRecord pushBack _crimeRecord;
    _player setVariable ["eden_criminalRecord", _criminalRecord, true];
    
    // Add experience
    _expGained = 50;
    _currentExp = _player getVariable ["eden_experience", 0];
    _player setVariable ["eden_experience", (_currentExp + _expGained), true];
    
    // Alert nearby police
    {
        if (_x getVariable ["eden_isPolice", false] && _x distance _player < 500) then {
            [
                "Vehicle Theft Alert",
                format ["Possible vehicle theft in progress near %1", mapGridPosition _player],
                10,
                "warning"
            ] remoteExec ["EDEN_fnc_showNotification", _x];
        };
    } forEach allPlayers;
    
    ["Lockpick successful! Vehicle unlocked (+50 XP)"] call EDEN_fnc_showHint;
    
    // Log the crime
    [format ["[EDEN] Player %1 successfully lockpicked vehicle %2", name _player, typeOf _vehicle], "WARN", "CRIME"] call EDEN_fnc_systemLogger;
    
} else {
    // Failed lockpick
    ["Lockpick failed! Lockpick broken."] call EDEN_fnc_showHint;
    
    // Small chance to trigger alarm
    if (random 100 < 25) then {
        ["Vehicle alarm triggered!"] call EDEN_fnc_showHint;
        
        // Alert police with higher priority
        {
            if (_x getVariable ["eden_isPolice", false] && _x distance _player < 1000) then {
                [
                    "Vehicle Alarm",
                    format ["Vehicle alarm triggered at %1 - investigate immediately!", mapGridPosition _player],
                    15,
                    "error"
                ] remoteExec ["EDEN_fnc_showNotification", _x];
            };
        } forEach allPlayers;
    };
    
    // Log the failed attempt
    [format ["[EDEN] Player %1 failed to lockpick vehicle %2", name _player, typeOf _vehicle], "INFO", "CRIME"] call EDEN_fnc_systemLogger;
};

[_player] call EDEN_fnc_savePlayerData;

_success
