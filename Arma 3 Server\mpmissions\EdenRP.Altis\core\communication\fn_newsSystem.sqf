/*
    File: fn_newsSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages news and media system.
*/

params [["_player", player, [objN<PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_newsArticles") then {
            eden_newsArticles = [];
            publicVariable "eden_newsArticles";
        };
        _player setVariable ["eden_newsSubscriptions", [], true];
        true
    };
    case "publishArticle": {
        params ["", "", ["_headline", "", [""]], ["_content", "", [""]]];
        
        _job = _player getVariable ["eden_job", ""];
        if (_job != "journalist") exitWith {
            ["Must be a journalist to publish articles"] call EDEN_fnc_showHint;
            false
        };
        
        _article = [_headline, _content, name _player, time];
        eden_newsArticles pushBack _article;
        publicVariable "eden_newsArticles";
        
        {
            [format ["Breaking News: %1", _headline]] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach allPlayers;
        
        ["Article published"] call EDEN_fnc_showHint;
        true
    };
    case "breakingNews": {
        params ["", "", ["_message", "", [""]]];
        
        {
            [format ["BREAKING: %1", _message]] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach allPlayers;
        
        true
    };
    default { false };
};
