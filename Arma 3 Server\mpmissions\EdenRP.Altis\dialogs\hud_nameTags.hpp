/*
    File: hud_nameTags.hpp
    Author: EdenRP Development Team
    
    Description:
    HUD name tags dialog definitions for EdenRP
*/

class EdenRP_NameTag {
    idd = 3900;
    name = "EdenRP_NameTag";
    movingEnable = 0;
    enableSimulation = 0;
    duration = 999999;
    fadeIn = 0;
    fadeOut = 0;
    
    class controlsBackground {
        // No background for name tags
    };
    
    class controls {
        class NameTagBackground: RscText {
            idc = 3901;
            x = 0.45;
            y = 0.45;
            w = 0.1;
            h = 0.03;
            colorBackground[] = {0, 0, 0, 0.5};
        };
        
        class NameTagText: RscText {
            idc = 3902;
            text = "";
            x = 0.45;
            y = 0.45;
            w = 0.1;
            h = 0.03;
            colorText[] = {1, 1, 1, 1};
            sizeEx = 0.025;
            shadow = 2;
        };
        
        class HealthBar: RscText {
            idc = 3903;
            x = 0.45;
            y = 0.48;
            w = 0.1;
            h = 0.005;
            colorFrame[] = {0, 0, 0, 0.5};
            colorBar[] = {0, 1, 0, 1};
        };
        
        class DistanceText: RscText {
            idc = 3904;
            text = "";
            x = 0.45;
            y = 0.485;
            w = 0.1;
            h = 0.02;
            colorText[] = {0.8, 0.8, 0.8, 1};
            sizeEx = 0.02;
            shadow = 2;
        };
    };
};
