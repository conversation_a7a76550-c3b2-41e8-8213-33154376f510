/*
    File: fn_businessManager.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages business ownership and operations for civilians.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_businessId", 0, [0]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_ownedBusinesses", [], true];
        _player setVariable ["eden_businessExperience", 0, true];
        _player setVariable ["eden_businessLevel", 1, true];
        true
    };
    case "buyBusiness": {
        _availableBusinesses = [
            [1, "Coffee Shop", 25000, 500, "Food"],
            [2, "Gas Station", 50000, 800, "Fuel"],
            [3, "Clothing Store", 35000, 600, "Retail"],
            [4, "Electronics Store", 75000, 1200, "Electronics"],
            [5, "Restaurant", 60000, 1000, "Food"],
            [6, "Car Dealership", 100000, 2000, "Automotive"],
            [7, "Pharmacy", 45000, 700, "Medical"],
            [8, "Hardware Store", 40000, 650, "Tools"]
        ];
        
        _businessData = [];
        {
            if ((_x select 0) == _businessId) then {
                _businessData = _x;
            };
        } forEach _availableBusinesses;
        
        if (count _businessData == 0) exitWith {
            ["Business not found!"] call EDEN_fnc_showHint;
            false
        };
        
        _ownedBusinesses = _player getVariable ["eden_ownedBusinesses", []];
        _alreadyOwned = false;
        {
            if ((_x select 0) == _businessId) then {
                _alreadyOwned = true;
            };
        } forEach _ownedBusinesses;
        
        if (_alreadyOwned) exitWith {
            ["You already own this business!"] call EDEN_fnc_showHint;
            false
        };
        
        _price = _businessData select 2;
        _bankAccount = _player getVariable ["eden_bankAccount", 0];
        
        if (_bankAccount < _price) exitWith {
            [format ["Not enough money! Need $%1", _price]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_bankAccount", (_bankAccount - _price), true];
        
        _name = _businessData select 1;
        _dailyIncome = _businessData select 3;
        _type = _businessData select 4;
        
        _newBusiness = [_businessId, _name, _type, _dailyIncome, time, 100, 0]; // id, name, type, income, lastCollection, condition, employees
        _ownedBusinesses pushBack _newBusiness;
        _player setVariable ["eden_ownedBusinesses", _ownedBusinesses, true];
        
        [format ["Purchased %1 for $%2!", _name, _price]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "manageBusiness": {
        _ownedBusinesses = _player getVariable ["eden_ownedBusinesses", []];
        _businessData = [];
        _index = -1;
        
        {
            if ((_x select 0) == _businessId) then {
                _businessData = _x;
                _index = _forEachIndex;
            };
        } forEach _ownedBusinesses;
        
        if (_index == -1) exitWith {
            ["You don't own this business!"] call EDEN_fnc_showHint;
            false
        };
        
        _name = _businessData select 1;
        _condition = _businessData select 5;
        _employees = _businessData select 6;
        
        [format ["Managing %1 - Condition: %2%% - Employees: %3", _name, _condition, _employees]] call EDEN_fnc_showHint;
        true
    };
    case "collectIncome": {
        _ownedBusinesses = _player getVariable ["eden_ownedBusinesses", []];
        _totalIncome = 0;
        
        {
            _businessData = _x;
            _dailyIncome = _businessData select 3;
            _lastCollection = _businessData select 4;
            _condition = _businessData select 5;
            _employees = _businessData select 6;
            
            _daysSince = (time - _lastCollection) / 86400; // Convert to days
            if (_daysSince >= 1) then {
                _income = _dailyIncome * _daysSince;
                _income = _income * (_condition / 100); // Condition affects income
                _income = _income * (1 + (_employees * 0.1)); // Employees boost income
                
                _totalIncome = _totalIncome + _income;
                _businessData set [4, time]; // Update last collection time
            };
        } forEach _ownedBusinesses;
        
        if (_totalIncome > 0) then {
            _bankAccount = _player getVariable ["eden_bankAccount", 0];
            _player setVariable ["eden_bankAccount", (_bankAccount + _totalIncome), true];
            _player setVariable ["eden_ownedBusinesses", _ownedBusinesses, true];
            
            [format ["Collected $%1 from your businesses!", floor _totalIncome]] call EDEN_fnc_showHint;
            [_player] call EDEN_fnc_savePlayerData;
        } else {
            ["No income to collect yet."] call EDEN_fnc_showHint;
        };
        
        (_totalIncome > 0)
    };
    case "upgradeBusiness": {
        _ownedBusinesses = _player getVariable ["eden_ownedBusinesses", []];
        _businessData = [];
        _index = -1;
        
        {
            if ((_x select 0) == _businessId) then {
                _businessData = _x;
                _index = _forEachIndex;
            };
        } forEach _ownedBusinesses;
        
        if (_index == -1) exitWith {
            ["You don't own this business!"] call EDEN_fnc_showHint;
            false
        };
        
        _condition = _businessData select 5;
        _upgradeCost = 5000;
        
        if (_condition >= 100) exitWith {
            ["Business is already in perfect condition!"] call EDEN_fnc_showHint;
            false
        };
        
        _bankAccount = _player getVariable ["eden_bankAccount", 0];
        if (_bankAccount < _upgradeCost) exitWith {
            [format ["Not enough money! Need $%1", _upgradeCost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_bankAccount", (_bankAccount - _upgradeCost), true];
        _newCondition = (_condition + 20) min 100;
        _businessData set [5, _newCondition];
        
        _ownedBusinesses set [_index, _businessData];
        _player setVariable ["eden_ownedBusinesses", _ownedBusinesses, true];
        
        [format ["Business upgraded! Condition: %1%%", _newCondition]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "hireEmployee": {
        _ownedBusinesses = _player getVariable ["eden_ownedBusinesses", []];
        _businessData = [];
        _index = -1;
        
        {
            if ((_x select 0) == _businessId) then {
                _businessData = _x;
                _index = _forEachIndex;
            };
        } forEach _ownedBusinesses;
        
        if (_index == -1) exitWith {
            ["You don't own this business!"] call EDEN_fnc_showHint;
            false
        };
        
        _employees = _businessData select 6;
        _hireCost = 2000 + (_employees * 500);
        
        if (_employees >= 5) exitWith {
            ["Maximum employees reached!"] call EDEN_fnc_showHint;
            false
        };
        
        _bankAccount = _player getVariable ["eden_bankAccount", 0];
        if (_bankAccount < _hireCost) exitWith {
            [format ["Not enough money! Need $%1", _hireCost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_bankAccount", (_bankAccount - _hireCost), true];
        _businessData set [6, (_employees + 1)];
        
        _ownedBusinesses set [_index, _businessData];
        _player setVariable ["eden_ownedBusinesses", _ownedBusinesses, true];
        
        [format ["Employee hired! Total employees: %1", _employees + 1]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
