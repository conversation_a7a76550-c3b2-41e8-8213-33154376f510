/*
    File: fn_qualitySystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages item quality and grading system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_item", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_qualityGrades") then {
            eden_qualityGrades = [
                ["poor", 0.5, [0.8, 0.8, 0.8]],
                ["common", 1.0, [1, 1, 1]],
                ["uncommon", 1.5, [0.5, 1, 0.5]],
                ["rare", 2.0, [0.5, 0.5, 1]],
                ["epic", 3.0, [0.8, 0.2, 0.8]],
                ["legendary", 5.0, [1, 0.8, 0]]
            ];
            publicVariable "eden_qualityGrades";
        };
        _player setVariable ["eden_qualityItems", [], true];
        true
    };
    case "setQuality": {
        params ["", "", "", ["_quality", "common", [""]], ["_itemIndex", 0, [0]]];
        
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        if (_itemIndex >= count _virtualItems) exitWith {
            ["Invalid item index"] call EDEN_fnc_showHint;
            false
        };
        
        _qualityItems = _player getVariable ["eden_qualityItems", []];
        
        // Ensure quality array matches virtual items
        while {count _qualityItems < count _virtualItems} do {
            _qualityItems pushBack "common";
        };
        
        _qualityItems set [_itemIndex, _quality];
        _player setVariable ["eden_qualityItems", _qualityItems, true];
        
        [format ["Set %1 quality to %2", _item, _quality]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "upgradeQuality": {
        params ["", "", "", ["_itemIndex", 0, [0]]];
        
        _qualityItems = _player getVariable ["eden_qualityItems", []];
        if (_itemIndex >= count _qualityItems) exitWith {
            ["Invalid item index"] call EDEN_fnc_showHint;
            false
        };
        
        _currentQuality = _qualityItems select _itemIndex;
        _qualityOrder = ["poor", "common", "uncommon", "rare", "epic", "legendary"];
        _currentIndex = _qualityOrder find _currentQuality;
        
        if (_currentIndex == -1 || _currentIndex >= (count _qualityOrder - 1)) exitWith {
            ["Cannot upgrade this item further"] call EDEN_fnc_showHint;
            false
        };
        
        _upgradeCost = (_currentIndex + 1) * 500;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _upgradeCost) exitWith {
            [format ["Not enough money! Need $%1", _upgradeCost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _upgradeCost), true];
        
        _newQuality = _qualityOrder select (_currentIndex + 1);
        _qualityItems set [_itemIndex, _newQuality];
        _player setVariable ["eden_qualityItems", _qualityItems, true];
        
        [format ["Upgraded to %1 quality for $%2", _newQuality, _upgradeCost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "getQualityMultiplier": {
        params ["", "", "", ["_quality", "common", [""]]];
        
        _multiplier = 1.0;
        {
            if ((_x select 0) == _quality) then {
                _multiplier = _x select 1;
            };
        } forEach eden_qualityGrades;
        
        _multiplier
    };
    case "appraise": {
        params ["", "", "", ["_itemIndex", 0, [0]]];
        
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _qualityItems = _player getVariable ["eden_qualityItems", []];
        
        if (_itemIndex >= count _virtualItems) exitWith {
            ["Invalid item index"] call EDEN_fnc_showHint;
            false
        };
        
        _itemName = (_virtualItems select _itemIndex) select 0;
        _quantity = (_virtualItems select _itemIndex) select 1;
        _quality = if (_itemIndex < count _qualityItems) then {
            _qualityItems select _itemIndex
        } else {
            "common"
        };
        
        _baseValue = 100; // Default base value
        {
            if ((_x select 0) == _itemName) then {
                _baseValue = _x select 3;
            };
        } forEach eden_itemDatabase;
        
        _multiplier = [_player, "getQualityMultiplier", "", _quality] call EDEN_fnc_qualitySystem;
        _totalValue = floor(_baseValue * _multiplier * _quantity);
        
        [format ["%1x %2 (%3) - Value: $%4", _quantity, _itemName, _quality, _totalValue]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
