/*
    File: fn_seizeItems.sqf
    Author: EdenRP Development Team
    
    Description:
    Seizes illegal items from a player (police only).
    
    Parameters:
    0: OBJECT - Target player
    1: ARRAY - Items to seize (optional, seizes all illegal items if empty)
    2: OBJECT - Officer (optional, default: player)
    
    Returns:
    BOOLEAN - True if items were seized successfully
*/

params [
    ["_target", objNull, [obj<PERSON><PERSON>]],
    ["_itemsToSeize", [], [[]]],
    ["_officer", player, [obj<PERSON><PERSON>]]
];

if (isNull _target || isNull _officer) exitWith { false };

if (!(_officer getVariable ["eden_isPolice", false])) exitWith {
    ["Only police officers can seize items!"] call EDEN_fnc_showHint;
    false
};

if (_officer distance _target > 5) exitWith {
    ["Target is too far away!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_officer, "police_action", [_target, "seize_items"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Get target's inventory
_targetItems = _target getVariable ["eden_virtualItems", []];
_seizedItems = [];

// Define illegal items
_illegalItems = [
    "drugs_cocaine",
    "drugs_heroin",
    "drugs_marijuana",
    "drugs_meth",
    "lockpick",
    "zipties",
    "illegal_weapon",
    "stolen_goods",
    "counterfeit_money",
    "explosives"
];

// If no specific items specified, seize all illegal items
if (count _itemsToSeize == 0) then {
    _itemsToSeize = _illegalItems;
};

// Seize specified items
{
    _itemName = _x select 0;
    _itemQuantity = _x select 1;
    
    if (_itemName in _itemsToSeize) then {
        _seizedItems pushBack [_itemName, _itemQuantity];
        _targetItems deleteAt _forEachIndex;
    };
} forEach _targetItems;

// Update target's inventory
_target setVariable ["eden_virtualItems", _targetItems, true];

// Add seized items to evidence locker
_evidenceLocker = missionNamespace getVariable ["eden_evidenceLocker", []];
_evidenceEntry = [
    time,
    name _officer,
    name _target,
    _seizedItems,
    format ["Seized during search by %1", name _officer]
];
_evidenceLocker pushBack _evidenceEntry;
missionNamespace setVariable ["eden_evidenceLocker", _evidenceLocker, true];

// Update police statistics
_itemsSeized = _officer getVariable ["eden_itemsSeized", 0];
_officer setVariable ["eden_itemsSeized", (_itemsSeized + (count _seizedItems)), true];

// Notifications
if (count _seizedItems > 0) then {
    _seizedText = "";
    {
        _seizedText = _seizedText + format ["%1x %2, ", _x select 1, _x select 0];
    } forEach _seizedItems;
    
    [format ["Items seized: %1", _seizedText]] call EDEN_fnc_showHint;
    [format ["Officer %1 seized your items: %2", name _officer, _seizedText]] remoteExec ["EDEN_fnc_showHint", _target];
} else {
    ["No illegal items found to seize"] call EDEN_fnc_showHint;
};

// Log the seizure
[format ["[EDEN] Officer %1 seized %2 items from %3", name _officer, count _seizedItems, name _target], "INFO", "POLICE"] call EDEN_fnc_systemLogger;

[_target] call EDEN_fnc_savePlayerData;
[_officer] call EDEN_fnc_savePlayerData;

true
