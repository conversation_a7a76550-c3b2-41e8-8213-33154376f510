/*
    File: fn_startJob.sqf
    Author: EdenRP Development Team
    
    Description:
    Starts a job for the player.
    
    Parameters:
    0: STRING - Job type
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if job was started successfully
*/

params [
    ["_jobType", "", [""]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (_jobType == "" || isNull _player) exitWith { false };

// Check if player already has a job
_currentJob = _player getVariable ["eden_currentJob", ""];
if (_currentJob != "") exitWith {
    [format ["You already have a job: %1. Quit your current job first!", _currentJob]] call EDEN_fnc_showHint;
    false
};

// Job requirements
_jobRequirements = createHashMap;
_jobRequirements set ["trucker", [3, "driver"]];
_jobRequirements set ["miner", [2, ""]];
_jobRequirements set ["fisherman", [1, "fishing"]];
_jobRequirements set ["hunter", [5, "hunting"]];
_jobRequirements set ["oil_worker", [8, "oil"]];
_jobRequirements set ["diamond_miner", [15, "diamond"]];
_jobRequirements set ["taxi_driver", [2, "driver"]];
_jobRequirements set ["delivery_driver", [4, "driver"]];

_requirement = _jobRequirements getOrDefault [_jobType, [1, ""]];
_requiredLevel = _requirement select 0;
_requiredLicense = _requirement select 1;

// Check level requirement
_playerLevel = _player getVariable ["eden_playerLevel", 1];
if (_playerLevel < _requiredLevel) exitWith {
    [format ["You need level %1 for this job!", _requiredLevel]] call EDEN_fnc_showHint;
    false
};

// Check license requirement
if (_requiredLicense != "" && !(_player getVariable [format ["eden_license_%1", _requiredLicense], false])) exitWith {
    [format ["You need a %1 license for this job!", _requiredLicense]] call EDEN_fnc_showHint;
    false
};

// Set job
_player setVariable ["eden_currentJob", _jobType, true];
_player setVariable ["eden_jobStartTime", time, true];

// Job-specific initialization
switch (_jobType) do {
    case "trucker": {
        _player setVariable ["eden_truckerDeliveries", 0, true];
        ["Welcome to the trucking company! Check the job board for delivery missions."] call EDEN_fnc_showHint;
    };
    case "miner": {
        _player setVariable ["eden_miningExperience", 0, true];
        ["Welcome to the mining company! Head to the quarry to start mining."] call EDEN_fnc_showHint;
    };
    case "fisherman": {
        _player setVariable ["eden_fishCaught", 0, true];
        ["Welcome to the fishing company! Head to the docks to start fishing."] call EDEN_fnc_showHint;
    };
    case "hunter": {
        _player setVariable ["eden_animalsHunted", 0, true];
        ["Welcome to the hunting company! Head to the forest to start hunting."] call EDEN_fnc_showHint;
    };
    case "oil_worker": {
        _player setVariable ["eden_oilExtracted", 0, true];
        ["Welcome to the oil company! Head to the oil fields to start extraction."] call EDEN_fnc_showHint;
    };
    case "diamond_miner": {
        _player setVariable ["eden_diamondsFound", 0, true];
        ["Welcome to the diamond mining company! Head to the diamond mine."] call EDEN_fnc_showHint;
    };
    case "taxi_driver": {
        _player setVariable ["eden_taxiFares", 0, true];
        ["Welcome to the taxi company! Look for passengers around the city."] call EDEN_fnc_showHint;
    };
    case "delivery_driver": {
        _player setVariable ["eden_deliveriesMade", 0, true];
        ["Welcome to the delivery company! Check for packages to deliver."] call EDEN_fnc_showHint;
    };
};

// Add job uniform/equipment
switch (_jobType) do {
    case "miner": {
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _virtualItems pushBack ["pickaxe", 1];
        _virtualItems pushBack ["safety_helmet", 1];
        _player setVariable ["eden_virtualItems", _virtualItems, true];
    };
    case "fisherman": {
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _virtualItems pushBack ["fishing_rod", 1];
        _virtualItems pushBack ["bait", 10];
        _player setVariable ["eden_virtualItems", _virtualItems, true];
    };
    case "hunter": {
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _virtualItems pushBack ["hunting_rifle", 1];
        _virtualItems pushBack ["rifle_ammo", 20];
        _player setVariable ["eden_virtualItems", _virtualItems, true];
    };
};

[format ["Job started: %1", _jobType]] call EDEN_fnc_showHint;

// Log job start
[format ["[EDEN] Player %1 started job: %2", name _player, _jobType], "INFO", "JOBS"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
