/*
    File: fn_courtSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages court and legal system.
*/

params [["_player", player, [objNull]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_courtCases") then {
            eden_courtCases = [];
            publicVariable "eden_courtCases";
        };
        _player setVariable ["eden_courtAppearances", 0, true];
        true
    };
    case "fileCase": {
        params ["", "", ["_defendant", objNull, [objNull]], ["_charges", "", [""]]];
        
        if (isNull _defendant) exitWith { false };
        
        _caseId = format ["COURT_%1_%2", floor(random 10000), floor(time)];
        _case = [_caseId, name _defendant, _charges, name _player, time, "Pending"];
        
        eden_courtCases pushBack _case;
        publicVariable "eden_courtCases";
        
        [format ["Court case filed: %1 vs %2", name _defendant, _charges]] call EDEN_fnc_showHint;
        true
    };
    case "attendCourt": {
        _appearances = _player getVariable ["eden_courtAppearances", 0];
        _player setVariable ["eden_courtAppearances", (_appearances + 1), true];
        
        ["Court session attended"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "renderVerdict": {
        params ["", "", ["_caseId", "", [""]], ["_verdict", "Guilty", [""]]];
        
        _caseIndex = -1;
        {
            if ((_x select 0) == _caseId) then { _caseIndex = _forEachIndex; };
        } forEach eden_courtCases;
        
        if (_caseIndex == -1) exitWith { false };
        
        _case = eden_courtCases select _caseIndex;
        _case set [5, _verdict];
        eden_courtCases set [_caseIndex, _case];
        publicVariable "eden_courtCases";
        
        [format ["Verdict: %1", _verdict]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
