/*
    File: fn_loadingScreenContent.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages loading screen content and tips for players.
    
    Parameters:
    None
    
    Returns:
    BOOLEAN - True if loading screen was successfully set up
    
    Example:
    [] call EDEN_fnc_loadingScreenContent;
*/

// Initialize loading screen variables
if (isNil "eden_loadingTips") then {
    eden_loadingTips = [
        "Welcome to EdenRP! This is an enhanced Altis Life roleplay server.",
        "Visit the job center to find legal employment opportunities.",
        "Always carry your licenses when performing licensed activities.",
        "Police officers are here to help - cooperate with them for the best experience.",
        "Medical personnel are neutral - they will help anyone in need.",
        "Joining a gang can provide protection and new opportunities.",
        "Property ownership is available - invest in houses and businesses.",
        "The economy is player-driven - supply and demand affect prices.",
        "Quality roleplay is expected at all times.",
        "Report rule violations to administrators using the proper channels.",
        "Safe zones are marked - no violence is allowed in these areas.",
        "Vehicle insurance can save you money in the long run.",
        "Skill progression unlocks new abilities and job opportunities.",
        "The black market exists but comes with risks.",
        "Emergency services respond to calls - use them when needed.",
        "Bank robberies require planning and teamwork.",
        "Drug trafficking is illegal but profitable.",
        "Weapon licenses are required for legal firearm possession.",
        "Traffic laws are enforced - drive safely to avoid tickets.",
        "Prison sentences can be reduced through good behavior.",
        "Bounty hunting is a legitimate profession for licensed individuals.",
        "Real estate values fluctuate based on location and demand.",
        "Crafting allows you to create unique items and equipment.",
        "Reputation affects how NPCs and other players interact with you.",
        "Weather conditions can affect visibility and vehicle handling.",
        "Day/night cycles impact criminal activity and police patrols.",
        "Communication is key - use your radio and phone effectively.",
        "Backup your important data regularly to avoid losses.",
        "Server restarts occur regularly - plan your activities accordingly.",
        "Have fun and remember that everyone is here to enjoy the game!"
    ];
};

// Initialize loading screen images
if (isNil "eden_loadingImages") then {
    eden_loadingImages = [
        "images\loading_screen_1.jpg",
        "images\loading_screen_2.jpg", 
        "images\loading_screen_3.jpg",
        "images\edenrp_loading.paa"
    ];
};

// Function to get random loading tip
EDEN_fnc_getRandomTip = {
    _randomIndex = floor(random(count eden_loadingTips));
    eden_loadingTips select _randomIndex
};

// Function to get random loading image
EDEN_fnc_getRandomImage = {
    _randomIndex = floor(random(count eden_loadingImages));
    eden_loadingImages select _randomIndex
};

// Function to display loading screen
EDEN_fnc_showLoadingScreen = {
    params [
        ["_title", "EdenRP - Loading", [""]],
        ["_subtitle", "", [""]],
        ["_duration", 5, [0]]
    ];
    
    // Get random tip and image
    _tip = [] call EDEN_fnc_getRandomTip;
    _image = [] call EDEN_fnc_getRandomImage;
    
    // Create loading screen
    startLoadingScreen [_title, _subtitle];
    
    // Display tip
    _tipText = format ["<t size='0.8' color='#FFFFFF'>%1</t>", _tip];
    
    // Show loading progress
    for "_i" from 0 to 100 step 5 do {
        progressLoadingScreen (_i / 100);
        _progressText = format ["Loading... %1%%", _i];
        progressLoadingScreen (_i / 100);
        sleep 0.1;
    };
    
    // End loading screen
    endLoadingScreen;
};

// Function to show connection loading
EDEN_fnc_showConnectionLoading = {
    ["EdenRP - Connecting", "Establishing connection to server..."] call EDEN_fnc_showLoadingScreen;
};

// Function to show character loading
EDEN_fnc_showCharacterLoading = {
    ["EdenRP - Character", "Loading your character data..."] call EDEN_fnc_showLoadingScreen;
};

// Function to show world loading
EDEN_fnc_showWorldLoading = {
    ["EdenRP - World", "Loading world data and objects..."] call EDEN_fnc_showLoadingScreen;
};

// Initialize loading screen tips display
EDEN_fnc_initLoadingTips = {
    // Create tip display loop
    [] spawn {
        while {true} do {
            if (!isNull (findDisplay 46)) then {
                _tip = [] call EDEN_fnc_getRandomTip;
                _display = findDisplay 46;
                
                if (!isNull _display) then {
                    _ctrl = _display displayCtrl -1;
                    if (!isNull _ctrl) then {
                        _ctrl ctrlSetStructuredText parseText format [
                            "<t size='0.7' color='#FFFF00' align='center'>TIP: %1</t>", 
                            _tip
                        ];
                    };
                };
            };
            sleep 10; // Show new tip every 10 seconds
        };
    };
};

// Loading screen progress messages
eden_loadingMessages = [
    "Initializing EdenRP framework...",
    "Loading player database...",
    "Connecting to game servers...",
    "Loading world objects...",
    "Initializing economy system...",
    "Loading vehicle data...",
    "Setting up player permissions...",
    "Loading gang territories...",
    "Initializing police systems...",
    "Loading medical facilities...",
    "Setting up communication systems...",
    "Loading property data...",
    "Initializing crafting system...",
    "Loading mission objectives...",
    "Finalizing player setup...",
    "Welcome to EdenRP!"
];

// Function to show detailed loading progress
EDEN_fnc_showDetailedLoading = {
    params [
        ["_messages", eden_loadingMessages, [[]]]
    ];
    
    startLoadingScreen ["EdenRP", "Enhanced Altis Life Roleplay"];
    
    _totalSteps = count _messages;
    
    {
        _progress = (_forEachIndex + 1) / _totalSteps;
        progressLoadingScreen _progress;
        
        // Show current step
        _stepText = format ["Step %1/%2: %3", _forEachIndex + 1, _totalSteps, _x];
        progressLoadingScreen _progress;
        
        // Random delay to simulate loading
        sleep (0.5 + random 1);
        
    } forEach _messages;
    
    endLoadingScreen;
};

// Initialize loading screen system
[] call EDEN_fnc_initLoadingTips;

// Log successful initialization
["[EDEN] Loading screen content initialized"] call EDEN_fnc_systemLogger;

// Return success
true
