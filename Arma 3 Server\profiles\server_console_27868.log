﻿ 8:03:21 BattlEye Server: Initialized (v1.220, Arma 3 2.20.152984)
 8:03:21 Game Port: 2302, Steam Query Port: 2303
 8:03:21 Warning: Current Steam AppId: 233780 doesn't match expected value: 107410
 8:03:21 Mission EdenRP.<PERSON><PERSON> read from directory.
 8:03:22 Host identity created.
 8:03:22 Roles assigned.
 8:03:22 Reading mission ...
 8:03:27 Script core\actions\fn_robVehicle.sqf not found
 8:03:27 Mission EdenRP.<PERSON><PERSON> read from directory.
 8:03:27 Roles assigned.
 8:03:27 Reading mission ...
 8:03:28 Script core\actions\fn_robVehicle.sqf not found
 8:03:28 Mission EdenRP.<PERSON><PERSON> read from directory.
 8:03:28 Roles assigned.
 8:03:28 Reading mission ...
 8:03:28 Script core\actions\fn_robVehicle.sqf not found
 8:03:29 Mission EdenRP.<PERSON><PERSON> read from directory.
 8:03:29 Roles assigned.
 8:03:29 Reading mission ...
 8:03:29 Script core\actions\fn_robVehicle.sqf not found
 8:03:30 Mission EdenRP.<PERSON><PERSON> read from directory.
 8:03:30 Roles assigned.
 8:03:30 Reading mission ...
 8:03:30 Script core\actions\fn_robVehicle.sqf not found
 8:03:31 Mission EdenRP.<PERSON>is read from directory.
 8:03:31 Roles assigned.
 8:03:31 Reading mission ...
 8:03:31 Script core\actions\fn_robVehicle.sqf not found
 8:03:32 Mission EdenRP.Altis read from directory.
 8:03:32 Roles assigned.
 8:03:32 Reading mission ...
 8:03:32 Script core\actions\fn_robVehicle.sqf not found
 8:03:32 Mission EdenRP.Altis read from directory.
 8:03:33 Roles assigned.
 8:03:33 Reading mission ...
 8:03:33 Script core\actions\fn_robVehicle.sqf not found
 8:03:33 Mission EdenRP.Altis read from directory.
 8:03:34 Roles assigned.
 8:03:34 Reading mission ...
 8:03:34 Script core\actions\fn_robVehicle.sqf not found
 8:03:34 Mission EdenRP.Altis read from directory.
 8:03:35 Roles assigned.
 8:03:35 Reading mission ...
 8:03:35 Script core\actions\fn_robVehicle.sqf not found
 8:03:35 Mission EdenRP.Altis read from directory.
