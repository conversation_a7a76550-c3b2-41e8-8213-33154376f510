@echo off
echo Fixing extDB3 setup...

REM Copy required DLL files to main directory
copy "extDB3\extDB3_x64.dll" "." /Y
copy "extDB3\tbbmalloc_x64.dll" "." /Y

REM Copy extDB3 config to main directory
copy "extDB3\extdb3-conf.ini" "." /Y

echo extDB3 files copied successfully!
echo.
echo Files in main directory:
dir extDB3*.dll
dir tbbmalloc*.dll
dir extdb3-conf.ini

echo.
echo extDB3 setup complete!
pause
