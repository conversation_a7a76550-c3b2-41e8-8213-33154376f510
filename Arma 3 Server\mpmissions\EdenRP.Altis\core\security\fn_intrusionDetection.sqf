/*
    File: fn_intrusionDetection.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages intrusion detection system.
*/

params [["_action", "init", [""]]];

switch (_action) do {
    case "init": {
        if (isServer) then {
            if (isNil "eden_intrusionThresholds") then {
                eden_intrusionThresholds = [
                    ["failed_auth", 5, 300],
                    ["rapid_actions", 10, 60],
                    ["suspicious_movement", 3, 120],
                    ["invalid_requests", 8, 180]
                ];
                publicVariable "eden_intrusionThresholds";
            };
            if (isNil "eden_playerViolations") then {
                eden_playerViolations = [];
            };
        };
        true
    };
    case "detectIntrusion": {
        params ["", ["_player", objNull, [objNull]], ["_violationType", "", [""]]];
        
        if (!isServer || isNull _player) exitWith { false };
        
        _playerUID = getPlayerUID _player;
        _currentTime = time;
        
        // Find or create player violation record
        _playerRecord = [];
        _recordIndex = -1;
        
        {
            if ((_x select 0) == _playerUID) then {
                _playerRecord = _x;
                _recordIndex = _forEachIndex;
            };
        } forEach eden_playerViolations;
        
        if (count _playerRecord == 0) then {
            _playerRecord = [_playerUID, []];
            eden_playerViolations pushBack _playerRecord;
            _recordIndex = (count eden_playerViolations) - 1;
        };
        
        // Add violation
        _violations = _playerRecord select 1;
        _violations pushBack [_violationType, _currentTime];
        _playerRecord set [1, _violations];
        eden_playerViolations set [_recordIndex, _playerRecord];
        
        // Check thresholds
        _threshold = [];
        {
            if ((_x select 0) == _violationType) then { _threshold = _x; };
        } forEach eden_intrusionThresholds;
        
        if (count _threshold > 0) then {
            _maxCount = _threshold select 1;
            _timeWindow = _threshold select 2;
            _cutoffTime = _currentTime - _timeWindow;
            
            _recentViolations = 0;
            {
                if ((_x select 0) == _violationType && (_x select 1) > _cutoffTime) then {
                    _recentViolations = _recentViolations + 1;
                };
            } forEach _violations;
            
            if (_recentViolations >= _maxCount) then {
                ["triggerIntrusionAlert", _player, _violationType, _recentViolations] call EDEN_fnc_intrusionDetection;
            };
        };
        
        true
    };
    case "triggerIntrusionAlert": {
        params ["", ["_player", objNull, [objNull]], ["_violationType", "", [""]], ["_count", 0, [0]]];
        
        if (!isServer) exitWith { false };
        
        _alertLevel = switch (_violationType) do {
            case "failed_auth": { "HIGH" };
            case "rapid_actions": { "MEDIUM" };
            case "suspicious_movement": { "HIGH" };
            case "invalid_requests": { "MEDIUM" };
            default { "LOW" };
        };
        
        // Log security event
        ["logSecurityEvent", _player, _violationType, _alertLevel, format["Threshold exceeded: %1 violations", _count]] call EDEN_fnc_auditSystem;
        
        // Notify admins
        {
            if (getPlayerUID _x in eden_adminList) then {
                [format ["🚨 INTRUSION ALERT: %1 - %2 (%3 violations)", name _player, _violationType, _count]] remoteExec ["EDEN_fnc_showHint", _x];
            };
        } forEach allPlayers;
        
        // Take automatic action based on alert level
        switch (_alertLevel) do {
            case "HIGH": {
                // Temporarily restrict player
                _player setVariable ["eden_restricted", true, true];
                [format ["Your account has been temporarily restricted due to suspicious activity"]] remoteExec ["EDEN_fnc_showHint", _player];
                
                [] spawn {
                    sleep 300; // 5 minutes
                    _player setVariable ["eden_restricted", false, true];
                };
            };
            case "MEDIUM": {
                // Issue warning
                [format ["Warning: Suspicious activity detected. Please moderate your actions."]] remoteExec ["EDEN_fnc_showHint", _player];
            };
        };
        
        true
    };
    case "cleanupOldViolations": {
        if (!isServer) exitWith { false };
        
        _currentTime = time;
        _maxAge = 3600; // 1 hour
        
        {
            _playerRecord = _x;
            _violations = _playerRecord select 1;
            
            for "_i" from (count _violations - 1) to 0 step -1 do {
                _violation = _violations select _i;
                if ((_currentTime - (_violation select 1)) > _maxAge) then {
                    _violations deleteAt _i;
                };
            };
            
            _playerRecord set [1, _violations];
        } forEach eden_playerViolations;
        
        true
    };
    case "getPlayerViolations": {
        params ["", ["_playerUID", "", [""]]];
        
        if (!isServer) exitWith { [] };
        
        _violations = [];
        {
            if ((_x select 0) == _playerUID) then {
                _violations = _x select 1;
            };
        } forEach eden_playerViolations;
        
        _violations
    };
    case "isPlayerRestricted": {
        params ["", ["_player", objNull, [objNull]]];
        
        if (isNull _player) exitWith { false };
        
        _player getVariable ["eden_restricted", false]
    };
    default { false };
};
