/*
    File: progress.hpp
    Author: EdenRP Development Team
    
    Description:
    Progress bar dialog definitions for EdenRP
*/

class EdenRP_ProgressBar {
    idd = 3800;
    name = "EdenRP_ProgressBar";
    movingEnable = 0;
    enableSimulation = 1;
    
    class controlsBackground {
        class Background: RscText {
            idc = -1;
            x = 0.35;
            y = 0.45;
            w = 0.3;
            h = 0.1;
            colorBackground[] = {0, 0, 0, 0.8};
        };
        
        class Title: RscText {
            idc = 3801;
            text = "Processing...";
            x = 0.35;
            y = 0.45;
            w = 0.3;
            h = 0.03;
            colorText[] = {1, 1, 1, 1};
            sizeEx = 0.03;
        };
    };
    
    class controls {
        class ProgressBar: RscText {
            idc = 3802;
            x = 0.37;
            y = 0.5;
            w = 0.26;
            h = 0.02;
            colorFrame[] = {1, 1, 1, 0.5};
            colorBar[] = {0, 0.8, 0, 1};
        };
        
        class ProgressText: RscText {
            idc = 3803;
            text = "0%";
            x = 0.47;
            y = 0.52;
            w = 0.06;
            h = 0.02;
            colorText[] = {1, 1, 1, 1};
            sizeEx = 0.025;
        };
    };
};
