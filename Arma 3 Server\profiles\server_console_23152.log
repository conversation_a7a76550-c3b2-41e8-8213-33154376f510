﻿ 7:28:21 BattlEye Server: Initialized (v1.220, Arma 3 2.20.152984)
 7:28:21 Game Port: 2302, Steam Query Port: 2303
 7:28:21 Warning: Current Steam AppId: 233780 doesn't match expected value: 107410
 7:28:21 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:28:21 Host identity created.
 7:28:21 Roles assigned.
 7:28:21 Reading mission ...
 7:28:26 Script core\fn_initializePolice.sqf not found
 7:28:26 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:28:27 Roles assigned.
 7:28:27 Reading mission ...
 7:28:27 Script core\fn_initializePolice.sqf not found
 7:28:27 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:28:27 Roles assigned.
 7:28:27 Reading mission ...
 7:28:28 Script core\fn_initializePolice.sqf not found
 7:28:28 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:28:28 Roles assigned.
 7:28:28 Reading mission ...
 7:28:28 Script core\fn_initializePolice.sqf not found
 7:28:29 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:28:29 Roles assigned.
 7:28:29 Reading mission ...
 7:28:29 Script core\fn_initializePolice.sqf not found
 7:28:29 Mission EdenRP.<PERSON><PERSON> read from directory.
 7:28:29 Roles assigned.
 7:28:29 Reading mission ...
 7:28:30 Script core\fn_initializePolice.sqf not found
 7:28:30 Mission EdenRP.Altis read from directory.
 7:28:30 Roles assigned.
 7:28:30 Reading mission ...
 7:28:30 Script core\fn_initializePolice.sqf not found
 7:28:31 Mission EdenRP.Altis read from directory.
 7:28:31 Roles assigned.
 7:28:31 Reading mission ...
 7:28:31 Script core\fn_initializePolice.sqf not found
