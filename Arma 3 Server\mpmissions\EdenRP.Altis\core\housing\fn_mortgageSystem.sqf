/*
    File: fn_mortgageSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages mortgage and loan system for properties.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_propertyId", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_mortgages", [], true];
        _player setVariable ["eden_creditScore", 750, true];
        true
    };
    case "applyMortgage": {
        params ["", "", "", ["_amount", 50000, [0]], ["_term", 12, [0]]];
        
        _creditScore = _player getVariable ["eden_creditScore", 750];
        if (_creditScore < 600) exitWith {
            ["Credit score too low for mortgage!"] call EDEN_fnc_showHint;
            false
        };
        
        _downPayment = floor(_amount * 0.2);
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _downPayment) exitWith {
            [format ["Need $%1 down payment!", _downPayment]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _downPayment), true];
        
        _monthlyPayment = floor((_amount - _downPayment) / _term);
        _mortgage = [_propertyId, (_amount - _downPayment), _monthlyPayment, _term, time];
        
        _mortgages = _player getVariable ["eden_mortgages", []];
        _mortgages pushBack _mortgage;
        _player setVariable ["eden_mortgages", _mortgages, true];
        
        _owned = _player getVariable ["eden_ownedProperties", []];
        _owned pushBack _propertyId;
        _player setVariable ["eden_ownedProperties", _owned, true];
        
        [format ["Mortgage approved! Monthly payment: $%1", _monthlyPayment]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "payMortgage": {
        _mortgages = _player getVariable ["eden_mortgages", []];
        if (count _mortgages == 0) exitWith {
            ["No mortgages to pay"] call EDEN_fnc_showHint;
            false
        };
        
        _mortgage = _mortgages select 0;
        _payment = _mortgage select 2;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _payment) exitWith {
            [format ["Not enough money! Need $%1", _payment]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _payment), true];
        
        _remaining = (_mortgage select 1) - _payment;
        _mortgage set [1, _remaining];
        
        if (_remaining <= 0) then {
            _mortgages deleteAt 0;
            ["Mortgage paid off!"] call EDEN_fnc_showHint;
        } else {
            _mortgages set [0, _mortgage];
            [format ["Mortgage payment made. Remaining: $%1", _remaining]] call EDEN_fnc_showHint;
        };
        
        _player setVariable ["eden_mortgages", _mortgages, true];
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
