/*
    EdenRP Get Vehicle Configuration
    Enhanced vehicle configuration system
*/

params [["_vehicleClass", "", [""]]];

if (_vehicleClass == "") exitWith {[]};

// Vehicle configuration database
// Format: [displayName, basePrice, type, requiredLicense, factionRestriction, levelRequirement, maxSpeed, seats]
private _vehicleConfigs = [
    // Civilian Cars
    ["B_Quadbike_01_F", ["Quad Bike", 2500, "car", "driver", "any", 1, 60, 2]],
    ["C_Hatchback_01_F", ["Hatchback", 12000, "car", "driver", "any", 1, 180, 4]],
    ["C_Hatchback_01_sport_F", ["Hatchback Sport", 18000, "car", "driver", "any", 3, 200, 4]],
    ["C_Offroad_01_F", ["Offroad", 15000, "car", "driver", "any", 2, 120, 4]],
    ["C_SUV_01_F", ["SUV", 25000, "car", "driver", "any", 5, 160, 6]],
    ["C_Van_01_transport_F", ["Transport Van", 22000, "car", "truck", "any", 4, 140, 12]],
    ["C_Van_01_box_F", ["Box Van", 28000, "truck", "truck", "any", 6, 130, 3]],
    ["I_Truck_02_transport_F", ["Zamak Transport", 45000, "truck", "truck", "any", 8, 110, 17]],
    ["I_Truck_02_covered_F", ["Zamak Covered", 50000, "truck", "truck", "any", 8, 110, 17]],
    ["B_Truck_01_transport_F", ["HEMTT Transport", 85000, "truck", "truck", "any", 12, 100, 20]],
    ["B_Truck_01_box_F", ["HEMTT Box", 95000, "truck", "truck", "any", 12, 100, 3]],
    
    // Civilian Aircraft
    ["C_Heli_Light_01_civil_F", ["M-900 Civilian", 150000, "helicopter", "pilot", "any", 10, 200, 4]],
    ["B_Heli_Light_01_F", ["MH-9 Hummingbird", 180000, "helicopter", "pilot", "any", 12, 220, 4]],
    ["O_Heli_Light_02_unarmed_F", ["PO-30 Orca", 350000, "helicopter", "pilot", "any", 15, 300, 8]],
    ["I_Plane_Fighter_03_CAS_F", ["A-143 Buzzard", 750000, "plane", "pilot", "any", 20, 800, 1]],
    
    // Civilian Boats
    ["C_Boat_Civil_01_F", ["Motorboat", 8000, "boat", "boat", "any", 1, 80, 4]],
    ["C_Rubberboat", ["Assault Boat", 12000, "boat", "boat", "any", 3, 60, 6]],
    ["B_Boat_Transport_01_F", ["Speedboat", 25000, "boat", "boat", "any", 5, 120, 8]],
    ["O_Boat_Armed_01_hmg_F", ["Speedboat HMG", 450000, "boat", "boat", "any", 18, 140, 8]],
    
    // Police Vehicles
    ["C_Offroad_01_F", ["Police Offroad", 1000, "car", "", "west", 1, 120, 4]],
    ["C_SUV_01_F", ["Police SUV", 2000, "car", "", "west", 3, 160, 6]],
    ["B_MRAP_01_F", ["Hunter", 15000, "car", "", "west", 8, 140, 10]],
    ["I_MRAP_03_F", ["Strider", 18000, "car", "", "west", 10, 130, 12]],
    ["B_Heli_Light_01_F", ["Police Hummingbird", 25000, "helicopter", "", "west", 5, 220, 4]],
    ["B_Heli_Attack_01_F", ["AH-99 Blackfoot", 150000, "helicopter", "", "west", 15, 300, 2]],
    
    // Medical Vehicles
    ["C_Van_01_box_F", ["Medical Van", 1500, "car", "", "independent", 1, 130, 3]],
    ["B_Truck_01_medical_F", ["Medical HEMTT", 8000, "truck", "", "independent", 5, 100, 20]],
    ["C_Heli_Light_01_civil_F", ["Medical Helicopter", 20000, "helicopter", "", "independent", 3, 200, 4]],
    ["B_Heli_Transport_01_medevac_F", ["UH-80 Ghost Hawk", 75000, "helicopter", "", "independent", 10, 280, 16]],
    
    // Special/Donator Vehicles
    ["I_Quadbike_01_F", ["Quad Bike (Camo)", 5000, "car", "driver", "any", 1, 65, 2]],
    ["B_Heli_Light_01_stripped_F", ["MH-9 Stripped", 200000, "helicopter", "pilot", "any", 12, 230, 4]],
    ["C_Plane_Civil_01_F", ["Caesar BTT", 450000, "plane", "pilot", "any", 18, 350, 2]],
    ["B_T_LSV_01_unarmed_F", ["Prowler", 35000, "car", "driver", "any", 8, 140, 4]],
    ["O_T_LSV_02_unarmed_F", ["Qilin", 32000, "car", "driver", "any", 8, 135, 4]],
    
    // Industrial Vehicles
    ["C_Van_01_fuel_F", ["Fuel Truck", 35000, "truck", "truck", "any", 6, 120, 3]],
    ["B_Truck_01_fuel_F", ["HEMTT Fuel", 125000, "truck", "truck", "any", 15, 100, 3]],
    ["I_Truck_02_fuel_F", ["Zamak Fuel", 65000, "truck", "truck", "any", 10, 110, 3]],
    ["C_Offroad_01_repair_F", ["Repair Offroad", 18000, "car", "driver", "any", 4, 115, 4]],
    ["B_Truck_01_Repair_F", ["HEMTT Repair", 95000, "truck", "truck", "any", 12, 100, 3]],
    
    // Luxury Vehicles
    ["C_Sports_01_F", ["Comet", 85000, "car", "driver", "any", 8, 280, 2]],
    ["B_Heli_Light_01_dynamicLoadout_F", ["Armed Hummingbird", 500000, "helicopter", "pilot", "any", 20, 220, 4]],
    ["O_Heli_Transport_04_covered_F", ["Mi-290 Taru", 850000, "helicopter", "pilot", "any", 25, 250, 16]],
    
    // Motorcycles
    ["C_Quadbike_01_F", ["ATV", 3500, "car", "driver", "any", 1, 70, 2]],
    ["B_Quadbike_01_F", ["Military ATV", 4500, "car", "driver", "any", 2, 75, 2]],
    
    // Amphibious
    ["B_APC_Wheeled_01_cannon_F", ["AMV-7 Marshall", 750000, "car", "truck", "any", 25, 100, 8]],
    ["O_APC_Wheeled_02_rcws_F", ["MSE-3 Marid", 650000, "car", "truck", "any", 22, 105, 8]]
];

// Find vehicle configuration
private _config = [];
{
    if ((_x select 0) == _vehicleClass) exitWith {
        _config = _x select 1;
    };
} forEach _vehicleConfigs;

_config
