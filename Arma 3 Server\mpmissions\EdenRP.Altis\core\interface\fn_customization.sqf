/*
    File: fn_customization.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages interface customization system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_uiSettings", [
            ["hudPosition", [0.5, 0.1]],
            ["hudScale", 1.0],
            ["chatPosition", [0.0, 0.8]],
            ["minimapPosition", [0.9, 0.1]]
        ], true];
        true
    };
    case "setHUDPosition": {
        params ["", "", ["_position", [0.5, 0.1], [[]]]];
        
        _settings = _player getVariable ["eden_uiSettings", []];
        {
            if ((_x select 0) == "hudPosition") then {
                _x set [1, _position];
            };
        } forEach _settings;
        _player setVariable ["eden_uiSettings", _settings, true];
        
        ["HUD position updated"] call EDEN_fnc_showHint;
        true
    };
    case "setHUDScale": {
        params ["", "", ["_scale", 1.0, [0]]];
        
        _settings = _player getVariable ["eden_uiSettings", []];
        {
            if ((_x select 0) == "hudScale") then {
                _x set [1, _scale];
            };
        } forEach _settings;
        _player setVariable ["eden_uiSettings", _settings, true];
        
        [format ["HUD scale set to %1", _scale]] call EDEN_fnc_showHint;
        true
    };
    case "resetToDefaults": {
        _player setVariable ["eden_uiSettings", [
            ["hudPosition", [0.5, 0.1]],
            ["hudScale", 1.0],
            ["chatPosition", [0.0, 0.8]],
            ["minimapPosition", [0.9, 0.1]]
        ], true];
        
        ["UI settings reset to defaults"] call EDEN_fnc_showHint;
        true
    };
    case "saveProfile": {
        params ["", "", ["_profileName", "default", [""]]];
        
        _settings = _player getVariable ["eden_uiSettings", []];
        _profiles = _player getVariable ["eden_uiProfiles", []];
        
        _profiles pushBack [_profileName, _settings];
        _player setVariable ["eden_uiProfiles", _profiles, true];
        
        [format ["UI profile '%1' saved", _profileName]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "loadProfile": {
        params ["", "", ["_profileName", "default", [""]]];
        
        _profiles = _player getVariable ["eden_uiProfiles", []];
        _found = false;
        
        {
            if ((_x select 0) == _profileName) then {
                _player setVariable ["eden_uiSettings", (_x select 1), true];
                _found = true;
            };
        } forEach _profiles;
        
        if (_found) then {
            [format ["UI profile '%1' loaded", _profileName]] call EDEN_fnc_showHint;
        } else {
            ["Profile not found"] call EDEN_fnc_showHint;
        };
        
        _found
    };
    default { false };
};
