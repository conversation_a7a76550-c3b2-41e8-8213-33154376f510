/*
    File: fn_radarSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages police radar system for speed detection.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_radarActive", false, true];
        _player setVariable ["eden_speedViolations", 0, true];
        true
    };
    case "activateRadar": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty!"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_radarActive", true, true];
        ["Radar activated"] call EDEN_fnc_showHint;
        
        // Start radar monitoring
        [_player] spawn {
            params ["_officer"];
            while {_officer getVariable ["eden_radarActive", false]} do {
                _nearVehicles = nearestObjects [_officer, ["Car", "Truck"], 100];
                {
                    _speed = speed _x;
                    if (_speed > 80) then { // Speed limit 80 km/h
                        _driver = driver _x;
                        if (!isNull _driver && _driver != _officer) then {
                            [format ["RADAR: %1 detected speeding at %2 km/h", name _driver, floor _speed]] remoteExec ["EDEN_fnc_showHint", _officer];
                        };
                    };
                } forEach _nearVehicles;
                sleep 2;
            };
        };
        
        true
    };
    case "deactivateRadar": {
        _player setVariable ["eden_radarActive", false, true];
        ["Radar deactivated"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
