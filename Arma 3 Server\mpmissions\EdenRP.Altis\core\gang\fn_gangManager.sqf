/*
    File: fn_gangManager.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages gang system operations.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_gangName", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_gangs") then {
            eden_gangs = [];
            publicVariable "eden_gangs";
        };
        _player setVariable ["eden_gang", "", true];
        _player setVariable ["eden_gangRank", "", true];
        true
    };
    case "createGang": {
        _cost = 5000;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _gang = [_gangName, getPlayerUID _player, name _player, [getPlayerUID _player], time];
        eden_gangs pushBack _gang;
        publicVariable "eden_gangs";
        
        _player setVariable ["eden_gang", _gangName, true];
        _player setVariable ["eden_gangRank", "Leader", true];
        
        [format ["Gang '%1' created!", _gangName]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "joinGang": {
        _gangIndex = -1;
        {
            if ((_x select 0) == _gangName) then { _gangIndex = _forEachIndex; };
        } forEach eden_gangs;
        
        if (_gangIndex == -1) exitWith {
            ["Gang not found!"] call EDEN_fnc_showHint;
            false
        };
        
        _gang = eden_gangs select _gangIndex;
        _members = _gang select 3;
        _members pushBack (getPlayerUID _player);
        _gang set [3, _members];
        eden_gangs set [_gangIndex, _gang];
        publicVariable "eden_gangs";
        
        _player setVariable ["eden_gang", _gangName, true];
        _player setVariable ["eden_gangRank", "Member", true];
        
        [format ["Joined gang '%1'!", _gangName]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "leaveGang": {
        _currentGang = _player getVariable ["eden_gang", ""];
        if (_currentGang == "") exitWith {
            ["You are not in a gang!"] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_gang", "", true];
        _player setVariable ["eden_gangRank", "", true];
        
        [format ["Left gang '%1'", _currentGang]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
