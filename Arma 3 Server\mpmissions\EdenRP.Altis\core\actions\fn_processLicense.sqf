/*
    File: fn_processLicense.sqf
    Author: EdenRP Development Team
    
    Description:
    Processes license applications and renewals.
    
    Parameters:
    0: STRING - License type
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if license was processed successfully
*/

params [
    ["_licenseType", "", [""]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (_licenseType == "" || isNull _player) exitWith { false };

// Check if player already has license
_licenseVar = format ["eden_license_%1", _licenseType];
if (_player getVariable [_licenseVar, false]) exitWith {
    ["You already have this license!"] call EDEN_fnc_showHint;
    false
};

// License requirements
_requirements = createHashMap;
_requirements set ["driver", [1, 0]]; // [level, prerequisite_license]
_requirements set ["pilot", [5, "driver"]];
_requirements set ["boat", [2, ""]];
_requirements set ["gun", [10, ""]];
_requirements set ["hunting", [3, ""]];
_requirements set ["fishing", [1, ""]];
_requirements set ["mining", [2, ""]];
_requirements set ["oil", [8, "mining"]];
_requirements set ["diamond", [15, "mining"]];
_requirements set ["turtle", [12, ""]];

_requirement = _requirements getOrDefault [_licenseType, [999, ""]];
_requiredLevel = _requirement select 0;
_prerequisite = _requirement select 1;

_playerLevel = _player getVariable ["eden_playerLevel", 1];
if (_playerLevel < _requiredLevel) exitWith {
    [format ["You need level %1 for this license!", _requiredLevel]] call EDEN_fnc_showHint;
    false
};

if (_prerequisite != "" && !(_player getVariable [format ["eden_license_%1", _prerequisite], false])) exitWith {
    [format ["You need a %1 license first!", _prerequisite]] call EDEN_fnc_showHint;
    false
};

// Process the license (redirect to buy)
[_licenseType, _player] call EDEN_fnc_buyLicense
