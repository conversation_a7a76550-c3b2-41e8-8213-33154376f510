/*
    File: fn_miningAction.sqf
    Author: EdenRP Development Team
    
    Description:
    Performs mining action at resource nodes.
    
    Parameters:
    0: OBJECT - Resource node to mine
    1: OBJECT - Miner (optional, default: player)
    
    Returns:
    BOOLEAN - True if mining was successful
*/

params [
    ["_resourceNode", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_miner", player, [obj<PERSON><PERSON>]]
];

if (isNull _resourceNode || isNull _miner) exitWith { false };

if (_miner distance _resourceNode > 5) exitWith {
    ["You must be closer to the resource node!"] call EDEN_fnc_showHint;
    false
};

// Check if player has mining license
if (!(_miner getVariable ["eden_license_mining", false])) exitWith {
    ["You need a mining license to mine!"] call EDEN_fnc_showHint;
    false
};

// Check if player has pickaxe
_virtualItems = _miner getVariable ["eden_virtualItems", []];
_hasPickaxe = false;
{
    if ((_x select 0) == "pickaxe") then {
        _hasPickaxe = true;
    };
} forEach _virtualItems;

if (!_hasPickaxe) exitWith {
    ["You need a pickaxe to mine!"] call EDEN_fnc_showHint;
    false
};

// Check inventory space
_currentWeight = _miner getVariable ["eden_currentWeight", 0];
_maxWeight = _miner getVariable ["eden_maxWeight", 50];
_oreWeight = 3; // Each ore weighs 3kg

if ((_currentWeight + _oreWeight) > _maxWeight) exitWith {
    ["Inventory is full!"] call EDEN_fnc_showHint;
    false
};

// Check if node was recently mined
_lastMined = _resourceNode getVariable ["eden_lastMined", 0];
if ((time - _lastMined) < 120) exitWith { // 2 minute cooldown
    ["This resource node was recently mined. Wait a bit."] call EDEN_fnc_showHint;
    false
};

// Determine resource type based on node
_nodeType = typeOf _resourceNode;
_resourceType = switch (true) do {
    case (_nodeType in ["Land_Ore_01_F", "Land_Ore_02_F"]): { "copper" };
    case (_nodeType in ["Land_Iron_01_F", "Land_Iron_02_F"]): { "iron" };
    case (_nodeType in ["Land_Diamond_01_F"]): { "diamond" };
    case (_nodeType in ["Land_Stone_01_F", "Land_Stone_02_F"]): { "stone" };
    case (_nodeType in ["Land_Sand_01_F"]): { "sand" };
    default { "copper" }; // Default to copper
};

// Check special license requirements
switch (_resourceType) do {
    case "diamond": {
        if (!(_miner getVariable ["eden_license_diamond", false])) exitWith {
            ["You need a diamond mining license to mine diamonds!"] call EDEN_fnc_showHint;
            false
        };
    };
};

// Start mining
[_miner, "Acts_carFixingWheel"] remoteExec ["switchMove"];
["Mining..."] call EDEN_fnc_showHint;

// Mining takes time based on resource type
_miningTime = switch (_resourceType) do {
    case "diamond": { 15 };
    case "iron": { 8 };
    case "copper": { 5 };
    case "stone": { 3 };
    case "sand": { 2 };
    default { 5 };
};

sleep _miningTime;

[_miner, ""] remoteExec ["switchMove"];

// Determine mining success and quantity
_playerLevel = _miner getVariable ["eden_playerLevel", 1];
_baseChance = 80; // 80% base success rate
_levelBonus = _playerLevel * 2; // +2% per level
_successChance = (_baseChance + _levelBonus) min 95; // Max 95%

_success = (random 100) < _successChance;

if (_success) then {
    // Determine quantity based on resource rarity
    _quantity = switch (_resourceType) do {
        case "diamond": { 1 }; // Always 1 diamond
        case "iron": { 1 + floor(random 2) }; // 1-2 iron
        case "copper": { 1 + floor(random 3) }; // 1-3 copper
        case "stone": { 2 + floor(random 3) }; // 2-4 stone
        case "sand": { 3 + floor(random 3) }; // 3-5 sand
        default { 1 };
    };
    
    // Add resource to inventory
    _found = false;
    {
        if ((_x select 0) == _resourceType) then {
            _x set [1, ((_x select 1) + _quantity)];
            _found = true;
        };
    } forEach _virtualItems;
    
    if (!_found) then {
        _virtualItems pushBack [_resourceType, _quantity];
    };
    
    _miner setVariable ["eden_virtualItems", _virtualItems, true];
    _miner setVariable ["eden_currentWeight", (_currentWeight + (_oreWeight * _quantity)), true];
    
    // Mark node as mined
    _resourceNode setVariable ["eden_lastMined", time, true];
    
    // Add experience
    _expGained = switch (_resourceType) do {
        case "diamond": { 50 };
        case "iron": { 20 };
        case "copper": { 15 };
        case "stone": { 10 };
        case "sand": { 5 };
        default { 10 };
    } * _quantity;
    
    _currentExp = _miner getVariable ["eden_experience", 0];
    _miner setVariable ["eden_experience", (_currentExp + _expGained), true];
    
    // Update job stats if miner
    if ((_miner getVariable ["eden_currentJob", ""]) == "miner") then {
        _miningExp = _miner getVariable ["eden_miningExperience", 0];
        _miner setVariable ["eden_miningExperience", (_miningExp + _expGained), true];
    };
    
    // Check for level up
    _newLevel = floor((_currentExp + _expGained) / 1000) + 1;
    _currentLevel = _miner getVariable ["eden_playerLevel", 1];
    if (_newLevel > _currentLevel) then {
        _miner setVariable ["eden_playerLevel", _newLevel, true];
        [format ["Level up! You are now level %1", _newLevel], 5] call EDEN_fnc_showNotification;
    };
    
    [format ["Mined %1x %2 (+%3 XP)", _quantity, _resourceType, _expGained]] call EDEN_fnc_showHint;
    
} else {
    ["Mining failed! No resources extracted."] call EDEN_fnc_showHint;
    
    // Still mark node as attempted
    _resourceNode setVariable ["eden_lastMined", time, true];
};

// Log mining
[format ["[EDEN] Player %1 mined %2 - %3", name _miner, _resourceType, if (_success) then {"successful"} else {"failed"}], "DEBUG", "MINING"] call EDEN_fnc_systemLogger;

[_miner] call EDEN_fnc_savePlayerData;

_success
