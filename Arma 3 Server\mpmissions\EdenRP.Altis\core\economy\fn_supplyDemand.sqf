/*
    File: fn_supplyDemand.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages supply and demand economics system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_item", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_supplyDemand") then {
            eden_supplyDemand = [
                ["copper", 100, 80, 25],     // [item, supply, demand, base_price]
                ["iron", 75, 90, 35],
                ["oil", 60, 120, 45],
                ["diamonds", 30, 150, 200],
                ["salt", 200, 50, 15],
                ["fish", 150, 70, 20]
            ];
            publicVariable "eden_supplyDemand";
        };
        true
    };
    case "updateSupply": {
        params ["", "", "", ["_quantity", 1, [0]]];
        
        {
            if ((_x select 0) == _item) then {
                _currentSupply = _x select 1;
                _x set [1, (_currentSupply + _quantity)];
                eden_supplyDemand set [_forEachIndex, _x];
            };
        } forEach eden_supplyDemand;
        publicVariable "eden_supplyDemand";
        
        [_item] call EDEN_fnc_updatePrice;
        true
    };
    case "updateDemand": {
        params ["", "", "", ["_quantity", 1, [0]]];
        
        {
            if ((_x select 0) == _item) then {
                _currentDemand = _x select 2;
                _x set [2, (_currentDemand + _quantity)];
                eden_supplyDemand set [_forEachIndex, _x];
            };
        } forEach eden_supplyDemand;
        publicVariable "eden_supplyDemand";
        
        [_item] call EDEN_fnc_updatePrice;
        true
    };
    case "calculatePrice": {
        _itemData = [];
        {
            if ((_x select 0) == _item) then { _itemData = _x; };
        } forEach eden_supplyDemand;
        
        if (count _itemData == 0) exitWith { 0 };
        
        _supply = _itemData select 1;
        _demand = _itemData select 2;
        _basePrice = _itemData select 3;
        
        _ratio = if (_supply > 0) then { _demand / _supply } else { 2 };
        _newPrice = floor(_basePrice * _ratio);
        
        // Update commodity prices
        {
            if ((_x select 0) == _item) then {
                _x set [1, _newPrice];
                eden_commodityPrices set [_forEachIndex, _x];
            };
        } forEach eden_commodityPrices;
        publicVariable "eden_commodityPrices";
        
        _newPrice
    };
    case "marketShock": {
        params ["", "", "", ["_shockType", "shortage", [""]]];
        
        switch (_shockType) do {
            case "shortage": {
                {
                    _supply = _x select 1;
                    _x set [1, floor(_supply * 0.5)]; // 50% supply reduction
                    eden_supplyDemand set [_forEachIndex, _x];
                } forEach eden_supplyDemand;
                
                {
                    ["MARKET ALERT: Supply shortage affecting all commodities!"] remoteExec ["EDEN_fnc_showHint", _x];
                } forEach allPlayers;
            };
            case "surplus": {
                {
                    _supply = _x select 1;
                    _x set [1, floor(_supply * 1.5)]; // 50% supply increase
                    eden_supplyDemand set [_forEachIndex, _x];
                } forEach eden_supplyDemand;
                
                {
                    ["MARKET ALERT: Supply surplus - prices dropping!"] remoteExec ["EDEN_fnc_showHint", _x];
                } forEach allPlayers;
            };
            case "demand_spike": {
                {
                    _demand = _x select 2;
                    _x set [2, floor(_demand * 1.8)]; // 80% demand increase
                    eden_supplyDemand set [_forEachIndex, _x];
                } forEach eden_supplyDemand;
                
                {
                    ["MARKET ALERT: High demand driving prices up!"] remoteExec ["EDEN_fnc_showHint", _x];
                } forEach allPlayers;
            };
        };
        
        publicVariable "eden_supplyDemand";
        
        // Update all prices
        {
            [_x select 0] call EDEN_fnc_calculatePrice;
        } forEach eden_supplyDemand;
        
        true
    };
    default { false };
};
