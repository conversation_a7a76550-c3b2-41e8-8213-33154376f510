/*
    File: fn_sellLicense.sqf
    Author: EdenRP Development Team
    
    Description:
    Allows players to sell their licenses back for partial refund.
    
    Parameters:
    0: STRING - License type
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if license was sold successfully
*/

params [
    ["_licenseType", "", [""]],
    ["_player", player, [objNull]]
];

if (_licenseType == "" || isNull _player) exitWith { false };

// Check if player has the license
_licenseVar = format ["eden_license_%1", _licenseType];
if (!(_player getVariable [_licenseVar, false])) exitWith {
    ["You don't have this license!"] call EDEN_fnc_showHint;
    false
};

// License sell prices (50% of original price)
_licensePrices = createHashMap;
_licensePrices set ["driver", 250];
_licensePrices set ["pilot", 1250];
_licensePrices set ["boat", 500];
_licensePrices set ["gun", 2500];
_licensePrices set ["hunting", 750];
_licensePrices set ["fishing", 375];
_licensePrices set ["mining", 1000];
_licensePrices set ["oil", 1500];
_licensePrices set ["diamond", 3750];
_licensePrices set ["turtle", 1250];

_price = _licensePrices getOrDefault [_licenseType, 0];
if (_price == 0) exitWith { false };

// Remove license and give money
_player setVariable [_licenseVar, false, true];
_playerMoney = _player getVariable ["eden_cash", 0];
_player setVariable ["eden_cash", (_playerMoney + _price), true];

[format ["License sold: %1 ($%2)", _licenseType, _price]] call EDEN_fnc_showHint;
[_player] call EDEN_fnc_savePlayerData;

true
