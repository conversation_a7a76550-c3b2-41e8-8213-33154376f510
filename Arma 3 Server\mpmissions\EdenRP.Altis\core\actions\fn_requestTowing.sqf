/*
    File: fn_requestTowing.sqf
    Author: EdenRP Development Team
    
    Description:
    Requests towing service.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_vehicle", objNull, [objNull]]];

if (isNull _player) exitWith { false };

_lastRequest = _player getVariable ["eden_lastTowRequest", 0];
if ((time - _lastRequest) < 300) exitWith {
    ["Towing request cooldown active!"] call EDEN_fnc_showHint;
    false
};

_player setVariable ["eden_lastTowRequest", time, true];

{
    if (_x getVariable ["eden_isTowTruck", false]) then {
        [
            "TOWING REQUEST",
            format ["%1 needs vehicle towing at %2", name _player, mapGridPosition _player],
            15,
            "info"
        ] remoteExec ["EDEN_fnc_showNotification", _x];
    };
} forEach allPlayers;

["Towing service requested!"] call EDEN_fnc_showHint;
[format ["[EDEN] Towing request by %1", name _player], "INFO", "TOWING"] call EDEN_fnc_systemLogger;
true
