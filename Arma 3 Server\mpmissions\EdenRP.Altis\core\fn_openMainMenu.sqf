/*
    File: fn_openMainMenu.sqf
    Author: EdenRP Development Team
    
    Description:
    Opens the main player menu based on player role.
    
    Parameters:
    0: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if menu was opened successfully
*/

params [["_player", player, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

// Get player role
_playerRole = _player getVariable ["eden_playerRole", "civilian"];
_isPolice = _player getVariable ["eden_isPolice", false];
_isMedic = _player getVariable ["eden_isMedic", false];
_isAdmin = _player getVariable ["eden_isAdmin", false];

// Create main menu dialog
createDialog "EdenRP_MainMenu";

// Get the dialog
_dialog = findDisplay 3000;
if (isNull _dialog) exitWith {
    ["Failed to open main menu!"] call EDEN_fnc_showHint;
    false
};

// Set title based on role
_titleCtrl = _dialog displayCtrl 3001;
_titleText = switch (_player<PERSON>ole) do {
    case "police": { "Police Menu" };
    case "medical": { "Medical Menu" };
    case "admin": { "Admin Menu" };
    default { "Main Menu" };
};
_titleCtrl ctrlSetText _titleText;

// Get player info for display
_playerName = name _player;
_playerCash = _player getVariable ["eden_cash", 0];
_playerBank = _player getVariable ["eden_bankAccount", 0];
_playerLevel = _player getVariable ["eden_playerLevel", 1];
_playerExp = _player getVariable ["eden_experience", 0];

// Set player info
_infoCtrl = _dialog displayCtrl 3002;
_infoText = format [
    "Name: %1\nCash: $%2\nBank: $%3\nLevel: %4\nXP: %5",
    _playerName,
    _playerCash,
    _playerBank,
    _playerLevel,
    _playerExp
];
_infoCtrl ctrlSetText _infoText;

// Add menu buttons based on role
_buttonList = _dialog displayCtrl 3003;
lbClear _buttonList;

// Universal options
_buttonList lbAdd "View Inventory";
_buttonList lbAdd "View Licenses";
_buttonList lbAdd "View Statistics";
_buttonList lbAdd "Settings";

// Role-specific options
switch (_playerRole) do {
    case "civilian": {
        _buttonList lbAdd "Job Center";
        _buttonList lbAdd "Vehicle Garage";
        _buttonList lbAdd "Property Manager";
        _buttonList lbAdd "Gang Menu";
    };
    case "police": {
        _buttonList lbAdd "Police Database";
        _buttonList lbAdd "Wanted List";
        _buttonList lbAdd "Evidence Locker";
        _buttonList lbAdd "Patrol Assignment";
        _buttonList lbAdd "Police Garage";
    };
    case "medical": {
        _buttonList lbAdd "Emergency Calls";
        _buttonList lbAdd "Patient Records";
        _buttonList lbAdd "Medical Supplies";
        _buttonList lbAdd "Ambulance Garage";
    };
};

// Admin options
if (_isAdmin) then {
    _buttonList lbAdd "--- ADMIN ---";
    _buttonList lbAdd "Player Management";
    _buttonList lbAdd "Server Management";
    _buttonList lbAdd "Teleport Menu";
    _buttonList lbAdd "Spawn Menu";
};

// Set button click handler
_buttonList ctrlAddEventHandler ["LBSelChange", {
    params ["_control", "_selectedIndex"];
    
    _selectedText = _control lbText _selectedIndex;
    
    switch (_selectedText) do {
        case "View Inventory": {
            closeDialog 0;
            [] call EDEN_fnc_openInventory;
        };
        case "View Licenses": {
            closeDialog 0;
            [] call EDEN_fnc_openLicenseMenu;
        };
        case "View Statistics": {
            closeDialog 0;
            [] call EDEN_fnc_openStatsMenu;
        };
        case "Job Center": {
            closeDialog 0;
            [] call EDEN_fnc_openJobCenter;
        };
        case "Vehicle Garage": {
            closeDialog 0;
            [] call EDEN_fnc_openVehicleGarage;
        };
        case "Police Database": {
            closeDialog 0;
            [] call EDEN_fnc_openPoliceDatabase;
        };
        case "Emergency Calls": {
            closeDialog 0;
            [] call EDEN_fnc_openEmergencyCalls;
        };
        case "Player Management": {
            closeDialog 0;
            [] call EDEN_fnc_openPlayerManagement;
        };
        case "Settings": {
            closeDialog 0;
            [] call EDEN_fnc_openSettings;
        };
        default {
            [format ["Feature '%1' not yet implemented", _selectedText]] call EDEN_fnc_showHint;
        };
    };
}];

// Add close button handler
_closeBtn = _dialog displayCtrl 3004;
_closeBtn ctrlAddEventHandler ["ButtonClick", {
    closeDialog 0;
}];

// Log menu access
[format ["[EDEN] Player %1 opened main menu (%2)", name _player, _playerRole], "DEBUG", "UI"] call EDEN_fnc_systemLogger;

true
