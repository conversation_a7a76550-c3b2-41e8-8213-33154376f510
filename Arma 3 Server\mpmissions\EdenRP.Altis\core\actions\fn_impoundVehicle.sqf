/*
    File: fn_impoundVehicle.sqf
    Author: EdenRP Development Team
    
    Description:
    Impounds a vehicle (police only).
    
    Parameters:
    0: OBJECT - Vehicle to impound
    1: OBJECT - Officer (optional, default: player)
    
    Returns:
    BOOLEAN - True if impound was successful
*/

params [
    ["_vehicle", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_officer", player, [obj<PERSON><PERSON>]]
];

if (isNull _vehicle || isNull _officer) exitWith { false };

if (!(_officer getVariable ["eden_isPolice", false])) exitWith {
    ["Only police officers can impound vehicles!"] call EDEN_fnc_showHint;
    false
};

if (_officer distance _vehicle > 10) exitWith {
    ["Vehicle is too far away!"] call EDEN_fnc_showHint;
    false
};

if (!(_vehicle isKindOf "LandVehicle") && !(_vehicle isKindOf "Air") && !(_vehicle isKindOf "Ship")) exitWith {
    ["Invalid vehicle type!"] call EDEN_fnc_showHint;
    false
};

// Check if vehicle is already impounded
if (_vehicle getVariable ["eden_isImpounded", false]) exitWith {
    ["Vehicle is already impounded!"] call EDEN_fnc_showHint;
    false
};

// Eject all passengers
{
    _x action ["GetOut", _vehicle];
} forEach crew _vehicle;

sleep 1;

// Mark as impounded
_vehicle setVariable ["eden_isImpounded", true, true];
_vehicle setVariable ["eden_impoundedBy", name _officer, true];
_vehicle setVariable ["eden_impoundTime", time, true];
_vehicle lock 2; // Lock the vehicle

// Move to impound lot (example coordinates for Altis)
_impoundPosition = [3720, 13500, 0]; // Altis impound lot
_vehicle setPos _impoundPosition;

// Create impound marker
_markerName = format ["impound_%1", getPlayerUID _officer];
_marker = createMarker [_markerName, _impoundPosition];
_marker setMarkerType "mil_warning";
_marker setMarkerText format ["Impounded: %1", typeOf _vehicle];
_marker setMarkerColor "ColorRed";

// Update police statistics
_vehiclesImpounded = _officer getVariable ["eden_vehiclesImpounded", 0];
_officer setVariable ["eden_vehiclesImpounded", (_vehiclesImpounded + 1), true];

// Notifications
[format ["Vehicle impounded: %1", typeOf _vehicle]] call EDEN_fnc_showHint;

// Notify vehicle owner if online
_ownerUID = _vehicle getVariable ["eden_ownerUID", ""];
if (_ownerUID != "") then {
    {
        if (getPlayerUID _x == _ownerUID) then {
            [format ["Your %1 has been impounded by %2", typeOf _vehicle, name _officer]] remoteExec ["EDEN_fnc_showNotification", _x];
        };
    } forEach allPlayers;
};

[format ["[EDEN] Officer %1 impounded vehicle %2", name _officer, typeOf _vehicle], "INFO", "POLICE"] call EDEN_fnc_systemLogger;

[_officer] call EDEN_fnc_savePlayerData;

true
