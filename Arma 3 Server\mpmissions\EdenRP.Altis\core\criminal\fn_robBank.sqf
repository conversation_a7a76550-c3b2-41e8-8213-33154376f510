/*
    EdenRP Rob Bank Function
    Enhanced bank robbery with security systems and police response
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_data", [], [[]]]
];

// Check if player is near a bank
private _nearestBank = [_player] call EDEN_fnc_getNearestBank;
if (count _nearestBank == 0) exitWith {
    ["You are not near a bank", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

private _bankName = _nearestBank select 0;
private _bankPosition = _nearestBank select 1;
private _bankVault = _nearestBank select 2;
private _securityLevel = _nearestBank select 3;
private _lastRobbed = _nearestBank select 4;

// Check if bank was recently robbed
private _cooldownTime = 3600; // 1 hour cooldown
if (time - _lastRobbed < _cooldownTime) exitWith {
    private _remainingTime = round ((_cooldownTime - (time - _lastRobbed)) / 60);
    [format["This bank was recently robbed. Try again in %1 minutes", _remainingTime], "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Check required items
private _requiredItems = ["lockpick"];
if (_securityLevel >= 3) then {
    _requiredItems pushBack "laptop"; // High security requires hacking
};

{
    if !([_player, _x] call EDEN_fnc_hasItem) exitWith {
        [format["You need a %1 to rob this bank", _x], "error"] remoteExec ["EDEN_fnc_showNotification", _player];
        false
    };
} forEach _requiredItems;

// Check minimum gang size for high-security banks
if (_securityLevel >= 4) then {
    private _gangId = _player getVariable ["EDEN_GangID", -1];
    if (_gangId <= 0) exitWith {
        ["You need to be in a gang to rob this high-security bank", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
        false
    };
    
    private _nearbyGangMembers = [];
    {
        if (_x != _player && _x getVariable ["EDEN_GangID", -1] == _gangId && _x distance _player < 50) then {
            _nearbyGangMembers pushBack _x;
        };
    } forEach allPlayers;
    
    private _requiredMembers = _securityLevel - 1;
    if (count _nearbyGangMembers < _requiredMembers) exitWith {
        [format["You need at least %1 gang members nearby to rob this bank", _requiredMembers], "error"] remoteExec ["EDEN_fnc_showNotification", _player];
        false
    };
};

// Check police presence
private _onlinePolice = 0;
{
    if (side _x == west && _x getVariable ["EDEN_OnDuty", false]) then {
        _onlinePolice = _onlinePolice + 1;
    };
} forEach allPlayers;

private _requiredPolice = _securityLevel;
if (_onlinePolice < _requiredPolice) exitWith {
    [format["At least %1 police officers must be online to rob this bank", _requiredPolice], "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Start robbery process
[format["Starting robbery of %1...", _bankName], "info"] remoteExec ["EDEN_fnc_showNotification", _player];

// Calculate robbery time based on security level
private _robberyTime = 60 + (_securityLevel * 30); // Base 60 seconds + 30 per security level

// Trigger silent alarm after delay
private _alarmDelay = 30 + (random 30); // 30-60 seconds
[_player, _bankName, _bankPosition, _alarmDelay] spawn {
    params ["_player", "_bankName", "_bankPosition", "_alarmDelay"];
    sleep _alarmDelay;
    
    // Trigger alarm
    ["SILENT ALARM: Bank robbery in progress", "police"] remoteExec ["EDEN_fnc_showNotification", allPlayers];
    
    // Create dispatch call
    [objNull, "crime", _bankPosition, format["Bank robbery at %1", _bankName], 1] call EDEN_fnc_dispatchSystem;
    
    // Create map marker for police
    private _markerName = format ["robbery_%1", time];
    createMarker [_markerName, _bankPosition];
    _markerName setMarkerType "mil_warning";
    _markerName setMarkerColor "ColorRed";
    _markerName setMarkerText format ["BANK ROBBERY - %1", _bankName];
    _markerName setMarkerSize [1, 1];
    
    // Auto-delete marker after 30 minutes
    [_markerName] spawn {
        params ["_markerName"];
        sleep 1800;
        deleteMarker _markerName;
    };
};

// Show progress bar
[_robberyTime, format["Robbing %1", _bankName]] remoteExec ["EDEN_fnc_showProgressBar", _player];

// Set player as robbing
_player setVariable ["EDEN_IsRobbing", true, false];

// Wait for robbery time
sleep _robberyTime;

// Check if player is still alive and at location
if (!alive _player || _player distance _bankPosition > 20) exitWith {
    ["Robbery interrupted", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    _player setVariable ["EDEN_IsRobbing", false, false];
    false
};

// Calculate success chance
private _baseSuccess = 0.6; // 60% base success
private _securityPenalty = _securityLevel * 0.1; // 10% penalty per security level
private _skillBonus = 0; // TODO: Add criminal skill system
private _successChance = _baseSuccess - _securityPenalty + _skillBonus;
_successChance = [_successChance, 0.1, 0.9] call EDEN_fnc_clampValue;

// Determine success
private _success = (random 1) < _successChance;

if (_success) then {
    // Successful robbery
    private _baseReward = _bankVault;
    private _randomMultiplier = 0.8 + (random 0.4); // 80-120% of base
    private _reward = round (_baseReward * _randomMultiplier);
    
    // Add money to player
    private _currentCash = _player getVariable ["EDEN_Cash", 0];
    _player setVariable ["EDEN_Cash", _currentCash + _reward, false];
    
    // Consume lockpick
    [_player, "lockpick", 1] call EDEN_fnc_removeItem;
    
    // Add wanted level and bounty
    private _wantedLevel = _securityLevel;
    private _bounty = _reward * 0.1; // 10% of stolen amount
    [_player, _wantedLevel, _bounty, ["bank_robbery"]] call EDEN_fnc_addWantedLevel;
    
    // Log successful robbery
    private _uid = getPlayerUID _player;
    [_uid, "BANK_ROBBERY", _reward, _currentCash, _currentCash + _reward, format["Robbed %1", _bankName]] call EDEN_fnc_logTransaction;
    
    // Notify player
    [format["Successfully robbed $%1 from %2!", [_reward] call EDEN_fnc_formatMoney, _bankName], "success"] remoteExec ["EDEN_fnc_showNotification", _player];
    ["You are now wanted by police!", "warning"] remoteExec ["EDEN_fnc_showNotification", _player];
    
    // Award XP
    private _xpReward = _securityLevel * 100;
    [_player, _xpReward, "BANK_ROBBERY"] call EDEN_fnc_awardExperience;
    
    // Update bank last robbed time
    [_bankName, time] call EDEN_fnc_updateBankRobbedTime;
    
    // Update player statistics
    private _totalRobberies = _player getVariable ["EDEN_TotalRobberies", 0];
    _player setVariable ["EDEN_TotalRobberies", _totalRobberies + 1, false];
    
    // Log robbery
    [format["ROBBERY: %1 successfully robbed %2 for $%3", name _player, _bankName, _reward], "INFO", "CRIMINAL"] call EDEN_fnc_systemLogger;
    
} else {
    // Failed robbery
    ["Robbery failed - security systems activated!", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    
    // Consume lockpick anyway
    [_player, "lockpick", 1] call EDEN_fnc_removeItem;
    
    // Add smaller wanted level for attempted robbery
    [_player, 1, 1000, ["attempted_bank_robbery"]] call EDEN_fnc_addWantedLevel;
    
    // Trigger immediate police response
    ["ALARM: Failed bank robbery attempt", "police"] remoteExec ["EDEN_fnc_showNotification", allPlayers];
    
    [format["ROBBERY: %1 failed to rob %2", name _player, _bankName], "INFO", "CRIMINAL"] call EDEN_fnc_systemLogger;
};

// Reset robbing status
_player setVariable ["EDEN_IsRobbing", false, false];

// Update client money display
[_player getVariable ["EDEN_Cash", 0], _player getVariable ["EDEN_Bank", 0]] remoteExec ["EDEN_fnc_updateMoneyDisplay", _player];

_success
