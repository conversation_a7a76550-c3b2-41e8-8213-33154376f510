/*
    EdenRP Save Player Data
    Enhanced player data saving system
    
    This function saves player data to database
    with validation and error handling
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_forced", false, [false]]
];

// Validate player object
if (isNull _player || !isPlayer _player) exitWith {
    ["Invalid player object provided to savePlayerData", "ERROR", "SESSION"] call EDEN_fnc_systemLogger;
    false
};

// Only run on server
if (hasInterface) exitWith {
    ["savePlayerData called on client", "ERROR", "SESSION"] call EDEN_fnc_systemLogger;
    false
};

// Check database connection
if (!EDEN_DatabaseConnected) exitWith {
    [format["Cannot save data for %1 - database not connected", name _player], "ERROR", "SESSION"] call EDEN_fnc_systemLogger;
    false
};

// Get player UID
private _uid = getPlayerUID _player;
if (_uid == "") exitWith {
    [format["Cannot save data for %1 - invalid UID", name _player], "ERROR", "SESSION"] call EDEN_fnc_systemLogger;
    false
};

// Check if player data has been modified recently (unless forced)
if (!_forced) then {
    private _lastSave = _player getVariable ["EDEN_LastSave", 0];
    if (time - _lastSave < 30) exitWith {
        // Data was saved less than 30 seconds ago, skip
        true
    };
};

// Log save operation
[format["Saving player data for %1 (%2)", name _player, _uid], "INFO", "SESSION"] call EDEN_fnc_systemLogger;

// Collect player data with validation
private _name = name _player;
private _cash = _player getVariable ["EDEN_Cash", 0];
private _bank = _player getVariable ["EDEN_Bank", 0];
private _experience = _player getVariable ["EDEN_XP", 0];
private _level = _player getVariable ["EDEN_Level", 1];
private _reputation = _player getVariable ["EDEN_Reputation", 0];
private _playtime = _player getVariable ["EDEN_PlayTime", 0];
private _copLevel = _player getVariable ["EDEN_CopLevel", 0];
private _medicLevel = _player getVariable ["EDEN_MedicLevel", 0];
private _adminLevel = _player getVariable ["EDEN_AdminLevel", 0];
private _donatorLevel = _player getVariable ["EDEN_DonatorLevel", 0];

// Validate numeric values
_cash = [_cash, 0, *********] call EDEN_fnc_clampValue;
_bank = [_bank, 0, *********] call EDEN_fnc_clampValue;
_experience = [_experience, 0, *********] call EDEN_fnc_clampValue;
_level = [_level, 1, 100] call EDEN_fnc_clampValue;
_reputation = [_reputation, -1000, 1000] call EDEN_fnc_clampValue;
_playtime = [_playtime, 0, 999999] call EDEN_fnc_clampValue;
_copLevel = [_copLevel, 0, 10] call EDEN_fnc_clampValue;
_medicLevel = [_medicLevel, 0, 7] call EDEN_fnc_clampValue;
_adminLevel = [_adminLevel, 0, 5] call EDEN_fnc_clampValue;
_donatorLevel = [_donatorLevel, 0, 5] call EDEN_fnc_clampValue;

// Collect license data
private _copLicenses = str (_player getVariable ["EDEN_CopLicenses", []]);
private _civLicenses = str (_player getVariable ["EDEN_CivLicenses", []]);
private _medLicenses = str (_player getVariable ["EDEN_MedLicenses", []]);

// Collect gear data
private _copGear = str (_player getVariable ["EDEN_CopGear", []]);
private _medGear = str (_player getVariable ["EDEN_MedGear", []]);
private _civGear = str (_player getVariable ["EDEN_CivGear", []]);

// Collect inventory data
private _virtualInventory = str (_player getVariable ["EDEN_VirtualInventory", []]);
private _physicalInventory = str (_player getVariable ["EDEN_PhysicalInventory", []]);

// Collect skills and achievements
private _skills = str (_player getVariable ["EDEN_Skills", []]);
private _achievements = str (_player getVariable ["EDEN_Achievements", []]);

// Collect settings
private _settings = str (_player getVariable ["EDEN_Settings", []]);

// Collect criminal status
private _arrested = if (_player getVariable ["EDEN_IsArrested", false]) then {1} else {0};
private _jailTime = _player getVariable ["EDEN_JailTime", 0];
private _wantedLevel = _player getVariable ["EDEN_WantedLevel", 0];
private _bounty = _player getVariable ["EDEN_Bounty", 0];

// Collect gang data
private _gangId = _player getVariable ["EDEN_GangID", -1];
private _gangRank = _player getVariable ["EDEN_GangRank", 0];

// Collect communication data
private _phoneNumber = _player getVariable ["EDEN_PhoneNumber", ""];
private _contacts = str (_player getVariable ["EDEN_Contacts", []]);
private _messages = str (_player getVariable ["EDEN_Messages", []]);

// Calculate session playtime
private _sessionStart = _player getVariable ["EDEN_SessionStart", time];
private _sessionTime = round (time - _sessionStart);
_playtime = _playtime + _sessionTime;

// Prepare database query
private _query = format [
    "EDEN_Players:updatePlayerData:%1:%2:%3:%4:%5:%6:%7:%8:%9:%10:%11:%12:%13:%14:%15:%16:%17:%18:%19:%20:%21:%22:%23:%24:%25:%26:%27:%28:%29:%30:%31",
    _name,
    _cash,
    _bank,
    _experience,
    _level,
    _reputation,
    _playtime,
    _copLevel,
    _medicLevel,
    _adminLevel,
    _copLicenses,
    _civLicenses,
    _medLicenses,
    _copGear,
    _medGear,
    _civGear,
    _virtualInventory,
    _physicalInventory,
    _skills,
    _achievements,
    _settings,
    _arrested,
    _jailTime,
    _wantedLevel,
    _bounty,
    _gangId,
    _gangRank,
    _phoneNumber,
    _contacts,
    _messages,
    _uid
];

// Execute database query
private _queryId = [_query, 1] call EDEN_fnc_asyncCall;

// Store query info for callback
if (isNil "EDEN_PendingQueries") then {
    EDEN_PendingQueries = [];
};

EDEN_PendingQueries pushBack [
    _queryId,
    "updatePlayerData",
    [_uid, name _player],
    time
];

// Update last save time
_player setVariable ["EDEN_LastSave", time, false];
_player setVariable ["EDEN_SessionStart", time, false];

// Save player vehicles
[_player] call EDEN_fnc_savePlayerVehicles;

// Save player houses
[_player] call EDEN_fnc_savePlayerHouses;

// Log transaction if money changed
private _lastCash = _player getVariable ["EDEN_LastCash", _cash];
private _lastBank = _player getVariable ["EDEN_LastBank", _bank];

if (_lastCash != _cash || _lastBank != _bank) then {
    private _totalBefore = _lastCash + _lastBank;
    private _totalAfter = _cash + _bank;
    private _difference = _totalAfter - _totalBefore;
    
    if (_difference != 0) then {
        [_uid, "BALANCE_CHANGE", _difference, _totalBefore, _totalAfter, "Auto-save"] call EDEN_fnc_logTransaction;
    };
    
    _player setVariable ["EDEN_LastCash", _cash, false];
    _player setVariable ["EDEN_LastBank", _bank, false];
};

[format["Player data save initiated for %1 (Query ID: %2)", name _player, _queryId], "DEBUG", "SESSION"] call EDEN_fnc_systemLogger;

true
