/*
    File: fn_detectiveSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages detective operations system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_detectiveRank", "Detective", true];
        _player setVariable ["eden_casesWorked", 0, true];
        true
    };
    case "investigate": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty!"] call EDEN_fnc_showHint;
            false
        };
        
        _cases = _player getVariable ["eden_casesWorked", 0];
        _player setVariable ["eden_casesWorked", (_cases + 1), true];
        
        ["Investigation started!"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "interrogate": {
        params ["", "", ["_suspect", obj<PERSON>ull, [obj<PERSON><PERSON>]]];
        
        if (isNull _suspect) exitWith { false };
        
        ["You are being interrogated!"] remoteExec ["EDEN_fnc_showHint", _suspect];
        ["Interrogation in progress..."] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
