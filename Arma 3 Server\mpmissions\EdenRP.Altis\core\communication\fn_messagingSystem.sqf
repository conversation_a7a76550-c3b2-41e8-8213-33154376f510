/*
    File: fn_messagingSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages player messaging system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_messages", [], true];
        _player setVariable ["eden_blockedPlayers", [], true];
        true
    };
    case "sendMessage": {
        params ["", "", "", ["_message", "", [""]]];
        
        if (isNull _target || _message == "") exitWith { false };
        
        _blocked = _target getVariable ["eden_blockedPlayers", []];
        if (getPlayerUID _player in _blocked) exitWith {
            ["Player has blocked you"] call EDEN_fnc_showHint;
            false
        };
        
        _messages = _target getVariable ["eden_messages", []];
        _newMessage = [getPlayerUID _player, name _player, _message, time];
        _messages pushBack _newMessage;
        _target setVariable ["eden_messages", _messages, true];
        
        [format ["Message sent to %1", name _target]] call EDEN_fnc_showHint;
        [format ["New message from %1: %2", name _player, _message]] remoteExec ["EDEN_fnc_showHint", _target];
        true
    };
    case "blockPlayer": {
        if (isNull _target) exitWith { false };
        
        _blocked = _player getVariable ["eden_blockedPlayers", []];
        if (!(getPlayerUID _target in _blocked)) then {
            _blocked pushBack getPlayerUID _target;
            _player setVariable ["eden_blockedPlayers", _blocked, true];
            [format ["Blocked %1", name _target]] call EDEN_fnc_showHint;
        };
        true
    };
    default { false };
};
