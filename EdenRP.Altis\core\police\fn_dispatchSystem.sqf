/*
    EdenRP Police Dispatch System
    Enhanced dispatch and emergency response coordination
*/

params [
    ["_caller", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_type", "", [""]],
    ["_location", [], [[]]],
    ["_description", "", [""]],
    ["_priority", 2, [0]]
];

// Validate parameters
if (isNull _caller) exitWith {
    ["Invalid caller provided to dispatchSystem", "ERROR", "DISPATCH"] call EDEN_fnc_systemLogger;
    false
};

if (_type == "") exitWith {
    ["No call type specified", "ERROR", "DISPATCH"] call EDEN_fnc_systemLogger;
    false
};

// Get caller information
private _callerName = if (isPlayer _caller) then {name _caller} else {"Anonymous"};
private _callerUID = if (isPlayer _caller) then {getPlayerUID _caller} else {"SYSTEM"};
private _callLocation = if (count _location > 0) then {_location} else {getPosATL _caller};
private _locationName = [_callLocation] call EDEN_fnc_getLocationName;

// Generate unique call ID
if (isNil "EDEN_DispatchCallCounter") then {
    EDEN_DispatchCallCounter = 1000;
};
EDEN_DispatchCallCounter = EDEN_DispatchCallCounter + 1;
private _callID = EDEN_DispatchCallCounter;

// Validate priority
_priority = [_priority, 1, 5] call EDEN_fnc_clampValue;

// Get call type configuration
private _callConfig = [_type] call EDEN_fnc_getCallTypeConfig;
if (count _callConfig == 0) exitWith {
    [format["Invalid call type: %1", _type], "ERROR", "DISPATCH"] call EDEN_fnc_systemLogger;
    false
};

private _callName = _callConfig select 0;
private _defaultPriority = _callConfig select 1;
private _requiredUnits = _callConfig select 2;
private _estimatedTime = _callConfig select 3;

// Use default priority if not specified
if (_priority == 2) then {
    _priority = _defaultPriority;
};

// Create dispatch call record
private _callData = [
    _callID,
    _type,
    _callName,
    _callerName,
    _callerUID,
    _callLocation,
    _locationName,
    _description,
    _priority,
    _requiredUnits,
    time,
    "ACTIVE",
    [],
    _estimatedTime
];

// Add to active calls list
if (isNil "EDEN_ActiveCalls") then {
    EDEN_ActiveCalls = [];
};
EDEN_ActiveCalls pushBack _callData;

// Determine response units based on call type
private _responseUnits = [];
switch (_type) do {
    case "emergency": {
        _responseUnits = [west, independent]; // Police and Medical
    };
    case "medical": {
        _responseUnits = [independent]; // Medical only
    };
    case "fire": {
        _responseUnits = [independent]; // Medical (acting as fire)
    };
    case "police": {
        _responseUnits = [west]; // Police only
    };
    case "traffic": {
        _responseUnits = [west]; // Police only
    };
    case "crime": {
        _responseUnits = [west]; // Police only
    };
    default {
        _responseUnits = [west]; // Default to police
    };
};

// Format dispatch message
private _priorityText = switch (_priority) do {
    case 1: {"🔴 EMERGENCY"};
    case 2: {"🟠 HIGH"};
    case 3: {"🟡 MEDIUM"};
    case 4: {"🟢 LOW"};
    case 5: {"🔵 INFO"};
    default {"🟡 MEDIUM"};
};

private _dispatchMessage = format [
    "DISPATCH %1 - CALL #%2\nType: %3\nLocation: %4\nCaller: %5\nDescription: %6\nRequired Units: %7",
    _priorityText,
    _callID,
    _callName,
    _locationName,
    _callerName,
    _description,
    _requiredUnits
];

// Send to appropriate units
{
    private _side = _x;
    {
        if (side _x == _side && _x getVariable ["EDEN_OnDuty", false]) then {
            [_dispatchMessage, "police"] remoteExec ["EDEN_fnc_showNotification", _x];
            
            // Create map marker for high priority calls
            if (_priority <= 2) then {
                private _markerName = format ["dispatch_%1", _callID];
                [_markerName, _callLocation, _callName, _priority] remoteExec ["EDEN_fnc_createDispatchMarker", _x];
            };
            
            // Play alert sound
            if (_priority == 1) then {
                ["EdenRP_Alarm"] remoteExec ["playSound", _x];
            } else {
                ["EdenRP_Notification"] remoteExec ["playSound", _x];
            };
        };
    } forEach allPlayers;
} forEach _responseUnits;

// Log dispatch call
[format["DISPATCH: Call #%1 - %2 at %3 (Priority %4)", _callID, _callName, _locationName, _priority], "INFO", "DISPATCH"] call EDEN_fnc_systemLogger;

// Auto-assign nearby units for high priority calls
if (_priority <= 2) then {
    [_callID, _callLocation, _responseUnits] spawn EDEN_fnc_autoAssignUnits;
};

// Set call timeout based on priority
private _timeout = switch (_priority) do {
    case 1: {600}; // 10 minutes
    case 2: {900}; // 15 minutes
    case 3: {1200}; // 20 minutes
    case 4: {1800}; // 30 minutes
    case 5: {3600}; // 60 minutes
    default {1200};
};

[_callID, _timeout] spawn {
    params ["_callID", "_timeout"];
    sleep _timeout;
    [_callID, "TIMEOUT"] call EDEN_fnc_closeDispatchCall;
};

// Store in database for record keeping
private _query = format [
    "INSERT INTO eden_dispatch_calls (call_id, call_type, caller_id, location, description, priority, timestamp, status) VALUES (%1, '%2', '%3', '%4', '%5', %6, NOW(), 'ACTIVE')",
    _callID,
    _type,
    _callerUID,
    str _callLocation,
    _description,
    _priority
];

[_query, 1] call EDEN_fnc_asyncCall;

// Return call ID for reference
_callID
