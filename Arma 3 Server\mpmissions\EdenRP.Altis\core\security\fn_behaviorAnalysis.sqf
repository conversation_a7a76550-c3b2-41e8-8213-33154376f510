/*
    File: fn_behaviorAnalysis.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages behavior analysis system.
*/

params [["_action", "init", [""]]];

switch (_action) do {
    case "init": {
        if (isServer) then {
            if (isNil "eden_behaviorProfiles") then {
                eden_behaviorProfiles = [];
            };
            if (isNil "eden_behaviorPatterns") then {
                eden_behaviorPatterns = [
                    ["bot_like", ["repetitive_actions", "perfect_timing", "no_idle_time"]],
                    ["griefing", ["rapid_disconnects", "targeting_new_players", "destructive_actions"]],
                    ["exploiting", ["unusual_money_gains", "impossible_locations", "rapid_progression"]]
                ];
                publicVariable "eden_behaviorPatterns";
            };
        };
        true
    };
    case "analyzePlayer": {
        params ["", ["_player", objNull, [objNull]]];
        
        if (!isServer || isNull _player) exitWith { false };
        
        _playerUID = getPlayerUID _player;
        
        // Find or create behavior profile
        _profile = [];
        _profileIndex = -1;
        
        {
            if ((_x select 0) == _playerUID) then {
                _profile = _x;
                _profileIndex = _forEachIndex;
            };
        } forEach eden_behaviorProfiles;
        
        if (count _profile == 0) then {
            _profile = [_playerUID, [], time, 0]; // [UID, actions, lastUpdate, suspicionScore]
            eden_behaviorProfiles pushBack _profile;
            _profileIndex = (count eden_behaviorProfiles) - 1;
        };
        
        // Analyze recent behavior
        _actions = _profile select 1;
        _lastUpdate = _profile select 2;
        _suspicionScore = _profile select 3;
        
        // Check for suspicious patterns
        _newSuspicionScore = _suspicionScore;
        
        // Check for bot-like behavior
        if ([_actions, "bot_like"] call EDEN_fnc_behaviorAnalysis) then {
            _newSuspicionScore = _newSuspicionScore + 10;
        };
        
        // Check for griefing behavior
        if ([_actions, "griefing"] call EDEN_fnc_behaviorAnalysis) then {
            _newSuspicionScore = _newSuspicionScore + 15;
        };
        
        // Check for exploiting behavior
        if ([_actions, "exploiting"] call EDEN_fnc_behaviorAnalysis) then {
            _newSuspicionScore = _newSuspicionScore + 20;
        };
        
        // Decay suspicion score over time
        _timeDiff = time - _lastUpdate;
        _decay = _timeDiff / 3600; // Decay per hour
        _newSuspicionScore = (_newSuspicionScore - _decay) max 0;
        
        _profile set [2, time];
        _profile set [3, _newSuspicionScore];
        eden_behaviorProfiles set [_profileIndex, _profile];
        
        // Alert if suspicion score is high
        if (_newSuspicionScore > 50) then {
            ["flagSuspiciousBehavior", _player, _newSuspicionScore] call EDEN_fnc_behaviorAnalysis;
        };
        
        true
    };
    case "recordAction": {
        params ["", ["_player", objNull, [objNull]], ["_actionType", "", [""]], ["_details", "", [""]]];
        
        if (!isServer || isNull _player) exitWith { false };
        
        _playerUID = getPlayerUID _player;
        
        // Find behavior profile
        _profile = [];
        _profileIndex = -1;
        
        {
            if ((_x select 0) == _playerUID) then {
                _profile = _x;
                _profileIndex = _forEachIndex;
            };
        } forEach eden_behaviorProfiles;
        
        if (count _profile == 0) then {
            _profile = [_playerUID, [], time, 0];
            eden_behaviorProfiles pushBack _profile;
            _profileIndex = (count eden_behaviorProfiles) - 1;
        };
        
        _actions = _profile select 1;
        _actionEntry = [_actionType, time, _details];
        _actions pushBack _actionEntry;
        
        // Keep only last 100 actions
        if (count _actions > 100) then {
            _actions deleteAt 0;
        };
        
        _profile set [1, _actions];
        eden_behaviorProfiles set [_profileIndex, _profile];
        
        true
    };
    case "checkPattern": {
        params ["", ["_actions", [], [[]]], ["_patternType", "", [""]]];
        
        _pattern = [];
        {
            if ((_x select 0) == _patternType) then { _pattern = _x select 1; };
        } forEach eden_behaviorPatterns;
        
        if (count _pattern == 0) exitWith { false };
        
        _matches = 0;
        
        switch (_patternType) do {
            case "bot_like": {
                // Check for repetitive actions
                _recentActions = [];
                {
                    if ((time - (_x select 1)) < 300) then { // Last 5 minutes
                        _recentActions pushBack (_x select 0);
                    };
                } forEach _actions;
                
                if (count _recentActions > 10) then {
                    _uniqueActions = [];
                    {
                        if (!(_x in _uniqueActions)) then { _uniqueActions pushBack _x; };
                    } forEach _recentActions;
                    
                    if ((count _uniqueActions) < 3) then { _matches = _matches + 1; };
                };
            };
            case "griefing": {
                // Check for targeting new players
                _targetingActions = 0;
                {
                    if ((_x select 0) in ["attack", "steal", "harass"]) then {
                        _targetingActions = _targetingActions + 1;
                    };
                } forEach _actions;
                
                if (_targetingActions > 5) then { _matches = _matches + 1; };
            };
            case "exploiting": {
                // Check for unusual money gains
                _moneyActions = 0;
                {
                    if ((_x select 0) == "money_gain" && parseNumber(_x select 2) > 10000) then {
                        _moneyActions = _moneyActions + 1;
                    };
                } forEach _actions;
                
                if (_moneyActions > 3) then { _matches = _matches + 1; };
            };
        };
        
        _matches > 0
    };
    case "flagSuspiciousBehavior": {
        params ["", ["_player", objNull, [objNull]], ["_suspicionScore", 0, [0]]];
        
        if (!isServer) exitWith { false };
        
        _severity = switch (true) do {
            case (_suspicionScore > 80): { "CRITICAL" };
            case (_suspicionScore > 60): { "HIGH" };
            case (_suspicionScore > 40): { "MEDIUM" };
            default { "LOW" };
        };
        
        // Log the suspicious behavior
        ["logSecurityEvent", _player, "suspicious_behavior", _severity, format["Suspicion score: %1", _suspicionScore]] call EDEN_fnc_auditSystem;
        
        // Notify admins
        {
            if (getPlayerUID _x in eden_adminList) then {
                [format ["🔍 SUSPICIOUS BEHAVIOR: %1 (Score: %2)", name _player, _suspicionScore]] remoteExec ["EDEN_fnc_showHint", _x];
            };
        } forEach allPlayers;
        
        // Take action based on severity
        if (_severity in ["HIGH", "CRITICAL"]) then {
            ["detectIntrusion", _player, "suspicious_behavior"] call EDEN_fnc_intrusionDetection;
        };
        
        true
    };
    case "getSuspicionScore": {
        params ["", ["_playerUID", "", [""]]];
        
        if (!isServer) exitWith { 0 };
        
        _score = 0;
        {
            if ((_x select 0) == _playerUID) then { _score = _x select 3; };
        } forEach eden_behaviorProfiles;
        
        _score
    };
    default { false };
};
