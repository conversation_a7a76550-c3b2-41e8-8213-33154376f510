/*
    File: fn_publicAddress.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages public address system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_paSystem") then {
            eden_paSystem = [];
            publicVariable "eden_paSystem";
        };
        true
    };
    case "announcement": {
        params ["", "", ["_message", "", [""]], ["_area", "city", [""]]];
        
        _job = _player getVariable ["eden_job", ""];
        if (!(_job in ["mayor", "police", "admin"])) exitWith {
            ["Insufficient permissions"] call EDEN_fnc_showHint;
            false
        };
        
        _targetPlayers = switch (_area) do {
            case "city": { allPlayers };
            case "police": { 
                _cops = [];
                {
                    if ((_x getVariable ["eden_job", ""]) == "police") then { _cops pushBack _x; };
                } forEach allPlayers;
                _cops
            };
            default { allPlayers };
        };
        
        {
            [format ["PA SYSTEM: %1", _message]] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach _targetPlayers;
        
        ["Public announcement made"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
