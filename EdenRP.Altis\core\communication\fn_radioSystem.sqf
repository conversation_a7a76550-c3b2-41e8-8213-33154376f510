/*
    EdenRP Radio System
    Enhanced radio communication with channels and encryption
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_action", "", [""]],
    ["_data", [], [[]]]
];

// Validate parameters
if (isNull _player || !isPlayer _player) exitWith {
    ["Invalid player provided to radioSystem", "ERROR", "RADIO"] call EDEN_fnc_systemLogger;
    false
};

private _result = false;
switch (toLower _action) do {
    case "transmit": {
        _result = [_player, _data] call EDEN_fnc_radioTransmit;
    };
    case "setchannel": {
        _result = [_player, _data] call EDEN_fnc_radioSetChannel;
    };
    case "scan": {
        _result = [_player] call EDEN_fnc_radioScan;
    };
    case "encrypt": {
        _result = [_player, _data] call EDEN_fnc_radioEncrypt;
    };
    case "emergency": {
        _result = [_player, _data] call EDEN_fnc_radioEmergency;
    };
    case "getfrequencies": {
        _result = [_player] call EDEN_fnc_radioGetFrequencies;
    };
    case "jamming": {
        _result = [_player, _data] call EDEN_fnc_radioJamming;
    };
    default {
        [format["Unknown radio action: %1", _action], "ERROR", "RADIO"] call EDEN_fnc_systemLogger;
    };
};

_result
