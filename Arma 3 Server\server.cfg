// EdenRP Server Configuration
hostname = "EdenRP - Enhanced Altis Life Experience";
password = "";
passwordAdmin = "admin123";
serverCommandPassword = "server123";

maxPlayers = 6;
kickDuplicate = 1;
verifySignatures = 2;
allowedFilePatching = 1;
requiredSecureId = 2;
BattlEye = 1;
steamProtocolMaxDataSize = 1024;
steamPort = 2303;
steamQueryPort = 2304;
// Steam AppId 233780 for Arma 3 Dedicated Server
maxPing = 300;
maxDesync = 150;
maxPacketLoss = 50;
timeStampFormat = "short";
logFile = "server_console.log";
persistent = 1;

class Missions {
    class EdenRP {
        template = "EdenRP.Altis";
        difficulty = "Custom";
    };
};

// Additional Settings
disableVoN = 0;
vonCodecQuality = 10;
forceRotorLibSimulation = 0;
statisticsEnabled = 1;
