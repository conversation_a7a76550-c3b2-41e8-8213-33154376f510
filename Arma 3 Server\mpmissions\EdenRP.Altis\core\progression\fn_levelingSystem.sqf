/*
    File: fn_levelingSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages player leveling system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_jobLevels", [
            ["civilian", 1, 0],
            ["police", 1, 0],
            ["ems", 1, 0],
            ["mechanic", 1, 0]
        ], true];
        true
    };
    case "addJobExperience": {
        params ["", "", ["_job", "civilian", [""]], ["_amount", 50, [0]]];
        
        _jobLevels = _player getVariable ["eden_jobLevels", []];
        _jobIndex = -1;
        
        {
            if ((_x select 0) == _job) then { _jobIndex = _forEachIndex; };
        } forEach _jobLevels;
        
        if (_jobIndex == -1) exitWith { false };
        
        _jobData = _jobLevels select _jobIndex;
        _currentLevel = _jobData select 1;
        _currentXP = _jobData select 2;
        _newXP = _currentXP + _amount;
        
        _requiredXP = _currentLevel * 500;
        
        if (_newXP >= _requiredXP) then {
            _jobData set [1, (_currentLevel + 1)];
            _jobData set [2, (_newXP - _requiredXP)];
            
            [format ["%1 level up! Now level %2", _job, (_currentLevel + 1)]] call EDEN_fnc_showHint;
        } else {
            _jobData set [2, _newXP];
            [format ["+%1 %2 XP", _amount, _job]] call EDEN_fnc_showHint;
        };
        
        _jobLevels set [_jobIndex, _jobData];
        _player setVariable ["eden_jobLevels", _jobLevels, true];
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "getJobLevel": {
        params ["", "", ["_job", "civilian", [""]]];
        
        _jobLevels = _player getVariable ["eden_jobLevels", []];
        _level = 1;
        
        {
            if ((_x select 0) == _job) then { _level = _x select 1; };
        } forEach _jobLevels;
        
        _level
    };
    default { false };
};
