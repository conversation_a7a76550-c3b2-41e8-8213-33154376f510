/*
    File: fn_contractSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages contract system for civilian jobs and services.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_contractId", 0, [0]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_activeContracts", [], true];
        _player setVariable ["eden_completedContracts", 0, true];
        _player setVariable ["eden_contractRating", 5.0, true];
        true
    };
    case "browseContracts": {
        _availableContracts = [
            [1001, "Construction Work", "Help build a new house", 1500, 3600, "Construction"],
            [1002, "Landscaping", "Maintain city gardens", 800, 1800, "Maintenance"],
            [1003, "Security Guard", "Guard a warehouse overnight", 1200, 7200, "Security"],
            [1004, "Taxi Service", "Provide taxi services", 600, 3600, "Transport"],
            [1005, "Cleaning Service", "Clean office buildings", 500, 1800, "Cleaning"],
            [1006, "Photography", "Event photography", 1000, 2400, "Creative"],
            [1007, "Tutoring", "Teach driving lessons", 750, 1800, "Education"],
            [1008, "Catering", "Provide catering services", 900, 2700, "Food Service"]
        ];
        
        _rating = _player getVariable ["eden_contractRating", 5.0];
        _filteredContracts = [];
        
        {
            _requiredRating = 3.0;
            if ((_x select 5) in ["Security", "Creative"]) then { _requiredRating = 4.0; };
            
            if (_rating >= _requiredRating) then {
                _filteredContracts pushBack _x;
            };
        } forEach _availableContracts;
        
        _player setVariable ["eden_availableContracts", _filteredContracts, true];
        [format ["Found %1 available contracts", count _filteredContracts]] call EDEN_fnc_showHint;
        true
    };
    case "acceptContract": {
        _availableContracts = _player getVariable ["eden_availableContracts", []];
        _activeContracts = _player getVariable ["eden_activeContracts", []];
        
        if (count _activeContracts >= 2) exitWith {
            ["You can only have 2 active contracts!"] call EDEN_fnc_showHint;
            false
        };
        
        _contractData = [];
        {
            if ((_x select 0) == _contractId) then {
                _contractData = _x;
            };
        } forEach _availableContracts;
        
        if (count _contractData == 0) exitWith {
            ["Contract not found!"] call EDEN_fnc_showHint;
            false
        };
        
        _name = _contractData select 1;
        _pay = _contractData select 3;
        _duration = _contractData select 4;
        _startTime = time;
        _endTime = _startTime + _duration;
        
        _activeContract = [_contractId, _name, _pay, _startTime, _endTime, 0]; // 0 = progress
        _activeContracts pushBack _activeContract;
        _player setVariable ["eden_activeContracts", _activeContracts, true];
        
        [format ["Contract accepted: %1 for $%2", _name, _pay]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "workContract": {
        _activeContracts = _player getVariable ["eden_activeContracts", []];
        _found = false;
        
        {
            if ((_x select 0) == _contractId) then {
                _progress = _x select 5;
                _endTime = _x select 4;
                
                if (time <= _endTime) then {
                    _workAmount = 10 + (random 15);
                    _newProgress = (_progress + _workAmount) min 100;
                    _x set [5, _newProgress];
                    _found = true;
                    
                    [format ["Work progress: %1%%", floor _newProgress]] call EDEN_fnc_showHint;
                    
                    if (_newProgress >= 100) then {
                        [_player, "completeContract", _contractId] call EDEN_fnc_contractSystem;
                    };
                } else {
                    ["Contract has expired!"] call EDEN_fnc_showHint;
                };
            };
        } forEach _activeContracts;
        
        if (_found) then {
            _player setVariable ["eden_activeContracts", _activeContracts, true];
            [_player] call EDEN_fnc_savePlayerData;
        } else {
            ["Contract not found!"] call EDEN_fnc_showHint;
        };
        
        _found
    };
    case "completeContract": {
        _activeContracts = _player getVariable ["eden_activeContracts", []];
        _contractData = [];
        _index = -1;
        
        {
            if ((_x select 0) == _contractId) then {
                _contractData = _x;
                _index = _forEachIndex;
            };
        } forEach _activeContracts;
        
        if (_index == -1) exitWith {
            ["Contract not found!"] call EDEN_fnc_showHint;
            false
        };
        
        _pay = _contractData select 2;
        _progress = _contractData select 5;
        _endTime = _contractData select 4;
        
        if (_progress < 100) exitWith {
            ["Contract not completed yet!"] call EDEN_fnc_showHint;
            false
        };
        
        _finalPay = _pay;
        if (time <= _endTime) then {
            _timeBonus = _pay * 0.15;
            _finalPay = _finalPay + _timeBonus;
            ["Early completion bonus!"] call EDEN_fnc_showHint;
        };
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _finalPay), true];
        
        _rating = _player getVariable ["eden_contractRating", 5.0];
        _newRating = (_rating + 0.1) min 5.0;
        _player setVariable ["eden_contractRating", _newRating, true];
        
        _completed = _player getVariable ["eden_completedContracts", 0];
        _player setVariable ["eden_completedContracts", (_completed + 1), true];
        
        _activeContracts deleteAt _index;
        _player setVariable ["eden_activeContracts", _activeContracts, true];
        
        [format ["Contract completed! Earned $%1", floor _finalPay]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
