/*
    File: fn_quitJob.sqf
    Author: EdenRP Development Team
    
    Description:
    Quits the player's current job.
    
    Parameters:
    0: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if job was quit successfully
*/

params [["_player", player, [objNull]]];

if (isNull _player) exitWith { false };

_currentJob = _player getVariable ["eden_currentJob", ""];
if (_currentJob == "") exitWith {
    ["You don't have a job to quit!"] call EDEN_fnc_showHint;
    false
};

// Calculate final paycheck
_jobStartTime = _player getVariable ["eden_jobStartTime", time];
_hoursWorked = (time - _jobStartTime) / 3600;
_basePay = 100; // $100 per hour base pay

_finalPay = round(_hoursWorked * _basePay);

// Job-specific bonuses
switch (_currentJob) do {
    case "trucker": {
        _deliveries = _player getVariable ["eden_truckerDeliveries", 0];
        _finalPay = _finalPay + (_deliveries * 500);
        [format ["Trucking bonus: %1 deliveries x $500 = $%2", _deliveries, _deliveries * 500]] call EDEN_fnc_showHint;
    };
    case "miner": {
        _miningExp = _player getVariable ["eden_miningExperience", 0];
        _finalPay = _finalPay + (_miningExp * 10);
    };
    case "fisherman": {
        _fishCaught = _player getVariable ["eden_fishCaught", 0];
        _finalPay = _finalPay + (_fishCaught * 25);
    };
    case "hunter": {
        _animalsHunted = _player getVariable ["eden_animalsHunted", 0];
        _finalPay = _finalPay + (_animalsHunted * 100);
    };
    case "taxi_driver": {
        _fares = _player getVariable ["eden_taxiFares", 0];
        _finalPay = _finalPay + (_fares * 50);
    };
};

// Pay the player
_playerMoney = _player getVariable ["eden_cash", 0];
_player setVariable ["eden_cash", (_playerMoney + _finalPay), true];

// Clear job data
_player setVariable ["eden_currentJob", "", true];
_player setVariable ["eden_jobStartTime", 0, true];

// Clear job-specific variables
switch (_currentJob) do {
    case "trucker": {
        _player setVariable ["eden_truckerDeliveries", nil, true];
    };
    case "miner": {
        _player setVariable ["eden_miningExperience", nil, true];
    };
    case "fisherman": {
        _player setVariable ["eden_fishCaught", nil, true];
    };
    case "hunter": {
        _player setVariable ["eden_animalsHunted", nil, true];
    };
    case "oil_worker": {
        _player setVariable ["eden_oilExtracted", nil, true];
    };
    case "diamond_miner": {
        _player setVariable ["eden_diamondsFound", nil, true];
    };
    case "taxi_driver": {
        _player setVariable ["eden_taxiFares", nil, true];
    };
    case "delivery_driver": {
        _player setVariable ["eden_deliveriesMade", nil, true];
    };
};

[format ["Job quit: %1. Final paycheck: $%2 (%.1f hours worked)", _currentJob, _finalPay, _hoursWorked]] call EDEN_fnc_showHint;

// Log job quit
[format ["[EDEN] Player %1 quit job: %2 (worked %.1f hours, earned $%3)", name _player, _currentJob, _hoursWorked, _finalPay], "INFO", "JOBS"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
