/*
    File: inventory.hpp
    Author: EdenRP Development Team
    
    Description:
    Inventory dialog definitions for EdenRP
*/

class EdenRP_Inventory {
    idd = 2900;
    name = "EdenRP_Inventory";
    movingEnable = 1;
    enableSimulation = 1;
    
    class controlsBackground {
        class Background: RscText {
            idc = -1;
            x = 0.2;
            y = 0.2;
            w = 0.6;
            h = 0.6;
            colorBackground[] = {0, 0, 0, 0.8};
        };
        
        class Title: RscText {
            idc = -1;
            text = "Inventory";
            x = 0.2;
            y = 0.2;
            w = 0.6;
            h = 0.05;
            colorText[] = {1, 1, 1, 1};
            sizeEx = 0.04;
        };
    };
    
    class controls {
        class ItemList: RscListBox {
            idc = 2901;
            x = 0.25;
            y = 0.3;
            w = 0.5;
            h = 0.4;
        };
        
        class CloseButton: RscButton {
            idc = 2902;
            text = "Close";
            x = 0.7;
            y = 0.75;
            w = 0.08;
            h = 0.04;
            action = "closeDialog 0;";
        };
    };
};
