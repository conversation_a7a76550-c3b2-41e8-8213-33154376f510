/*
    File: fn_notificationSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages notification system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_notifications", [], true];
        _player setVariable ["eden_notificationSettings", ["enabled", true], true];
        true
    };
    case "showNotification": {
        params ["", "", ["_message", "", [""]], ["_type", "info", [""]], ["_duration", 5, [0]]];
        
        _settings = _player getVariable ["eden_notificationSettings", ["enabled", true]];
        if (!(_settings select 1)) exitWith { false };
        
        _color = switch (_type) do {
            case "success": { [0, 1, 0, 1] };
            case "warning": { [1, 1, 0, 1] };
            case "error": { [1, 0, 0, 1] };
            default { [1, 1, 1, 1] };
        };
        
        [_message, 0.5, 0.1, _duration, 0, 0, 0] spawn BIS_fnc_dynamicText;
        
        _notifications = _player getVariable ["eden_notifications", []];
        _notifications pushBack [_message, _type, time];
        _player setVariable ["eden_notifications", _notifications, true];
        
        true
    };
    case "clearNotifications": {
        _player setVariable ["eden_notifications", [], true];
        ["Notifications cleared"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
