/*
    File: shop.hpp
    Author: EdenRP Development Team
    
    Description:
    Shop dialog definitions for EdenRP
*/

class EdenRP_Shop {
    idd = 3600;
    name = "EdenRP_Shop";
    movingEnable = 1;
    enableSimulation = 1;
    
    class controlsBackground {
        class Background: RscText {
            idc = -1;
            x = 0.2;
            y = 0.2;
            w = 0.6;
            h = 0.6;
            colorBackground[] = {0, 0, 0, 0.8};
        };
        
        class Title: RscText {
            idc = -1;
            text = "General Store";
            x = 0.2;
            y = 0.2;
            w = 0.6;
            h = 0.05;
            colorText[] = {1, 1, 1, 1};
            sizeEx = 0.04;
        };
    };
    
    class controls {
        class ItemList: RscListBox {
            idc = 3601;
            x = 0.25;
            y = 0.3;
            w = 0.25;
            h = 0.4;
        };
        
        class InventoryList: RscListBox {
            idc = 3602;
            x = 0.55;
            y = 0.3;
            w = 0.2;
            h = 0.4;
        };
        
        class PriceText: RscText {
            idc = 3603;
            text = "Price: $0";
            x = 0.25;
            y = 0.72;
            w = 0.15;
            h = 0.04;
            colorText[] = {1, 1, 1, 1};
        };
        
        class BuyButton: RscButton {
            idc = 3604;
            text = "Buy";
            x = 0.42;
            y = 0.72;
            w = 0.08;
            h = 0.04;
            action = "[] call EDEN_fnc_buyItem;";
        };
        
        class SellButton: RscButton {
            idc = 3605;
            text = "Sell";
            x = 0.52;
            y = 0.72;
            w = 0.08;
            h = 0.04;
            action = "[] call EDEN_fnc_sellItem;";
        };
        
        class CloseButton: RscButton {
            idc = 3606;
            text = "Close";
            x = 0.7;
            y = 0.75;
            w = 0.08;
            h = 0.04;
            action = "closeDialog 0;";
        };
    };
};
