/*
    File: fn_postBail.sqf
    Author: EdenRP Development Team
    
    Description:
    Posts bail for a jailed player.
    
    Parameters:
    0: OBJECT - Jailed player
    1: OBJECT - Bail poster (optional, default: player)
    
    Returns:
    BOOLEAN - True if bail was posted successfully
*/

params [
    ["_jailedPlayer", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_bailPoster", player, [obj<PERSON><PERSON>]]
];

if (isNull _jailedPlayer || isNull _bailPoster) exitWith { false };

// Check if target player is actually in jail
if (!(_jailedPlayer getVariable ["eden_isJailed", false])) exitWith {
    [format ["%1 is not in jail!", name _jailedPlayer]] call EDEN_fnc_showHint;
    false
};

// Check if bail poster is at courthouse/police station
_nearCourthouse = false;
_courthouses = ["Land_Offices_01_V1_F", "Land_Office_01_V1_F"];
{
    if (_bailPoster distance _x < 20) then {
        _nearCourthouse = true;
    };
} forEach (nearestObjects [_bailPoster, _courthouses, 50]);

if (!_nearCourthouse) exitWith {
    ["You must be at a courthouse or police station to post bail!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_bailPoster, "legal_action", [_jailedPlayer, "bail_posting"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Calculate bail amount based on crimes and wanted level
_wantedLevel = _jailedPlayer getVariable ["eden_wantedLevel", 0];
_criminalRecord = _jailedPlayer getVariable ["eden_criminalRecord", []];
_jailTime = _jailedPlayer getVariable ["eden_jailTime", 0];

_baseBail = 1000;
_wantedMultiplier = _wantedLevel * 500;
_crimeMultiplier = (count _criminalRecord) * 200;
_timeMultiplier = _jailTime * 10;

_bailAmount = _baseBail + _wantedMultiplier + _crimeMultiplier + _timeMultiplier;

// Check for serious crimes that deny bail
_seriousCrimes = ["murder", "terrorism", "bank_robbery", "kidnapping"];
_denyBail = false;
{
    _crime = _x select 2;
    if (_crime in _seriousCrimes) then {
        _denyBail = true;
    };
} forEach _criminalRecord;

if (_denyBail) exitWith {
    ["Bail denied! Player has committed serious crimes."] call EDEN_fnc_showHint;
    false
};

// Check if bail poster has enough money
_posterMoney = _bailPoster getVariable ["eden_cash", 0];
_posterBank = _bailPoster getVariable ["eden_bankAccount", 0];
_totalMoney = _posterMoney + _posterBank;

if (_totalMoney < _bailAmount) exitWith {
    [format ["Insufficient funds! Bail amount: $%1 (You have: $%2)", _bailAmount, _totalMoney]] call EDEN_fnc_showHint;
    false
};

// Confirm bail posting
[
    format ["Post bail for %1? Amount: $%2", name _jailedPlayer, _bailAmount],
    "Bail Confirmation",
    ["Yes", "No"]
] call EDEN_fnc_showDialog;

// Wait for dialog response (simplified for this example)
sleep 1;

// Process bail payment
if (_posterMoney >= _bailAmount) then {
    _bailPoster setVariable ["eden_cash", (_posterMoney - _bailAmount), true];
} else {
    _remainingAmount = _bailAmount - _posterMoney;
    _bailPoster setVariable ["eden_cash", 0, true];
    _bailPoster setVariable ["eden_bankAccount", (_posterBank - _remainingAmount), true];
};

// Release player from jail
_jailedPlayer setVariable ["eden_isJailed", false, true];
_jailedPlayer setVariable ["eden_jailTime", 0, true];

// Move player to courthouse exit
_courthousePos = getPosATL _bailPoster;
_exitPos = [(_courthousePos select 0) + 5, (_courthousePos select 1) + 5, 0];
_jailedPlayer setPosATL _exitPos;

// Clear wanted level (bail doesn't clear crimes, just releases from jail)
_jailedPlayer setVariable ["eden_wantedLevel", 0, true];

// Add bail record
_bailRecord = _jailedPlayer getVariable ["eden_bailHistory", []];
_bailEntry = [
    time,
    name _bailPoster,
    _bailAmount,
    "Posted bail"
];
_bailRecord pushBack _bailEntry;
_jailedPlayer setVariable ["eden_bailHistory", _bailRecord, true];

// Update legal statistics
_bailsPosted = _bailPoster getVariable ["eden_bailsPosted", 0];
_bailPoster setVariable ["eden_bailsPosted", (_bailsPosted + 1), true];

_bailsReceived = _jailedPlayer getVariable ["eden_bailsReceived", 0];
_jailedPlayer setVariable ["eden_bailsReceived", (_bailsReceived + 1), true];

// Government receives bail money
_governmentFunds = missionNamespace getVariable ["eden_governmentFunds", 0];
missionNamespace setVariable ["eden_governmentFunds", (_governmentFunds + _bailAmount), true];

// Notifications
[format ["Bail posted for %1! Amount: $%2", name _jailedPlayer, _bailAmount]] call EDEN_fnc_showHint;
[format ["Your bail was posted by %1! You are free to go.", name _bailPoster]] remoteExec ["EDEN_fnc_showHint", _jailedPlayer];

// Alert police of bail posting
{
    if (_x getVariable ["eden_isPolice", false]) then {
        [
            "Bail Posted",
            format ["%1 posted bail for %2 ($%3)", name _bailPoster, name _jailedPlayer, _bailAmount],
            10,
            "info"
        ] remoteExec ["EDEN_fnc_showNotification", _x];
    };
} forEach allPlayers;

// Log bail posting
[format ["[EDEN] Bail posted: %1 posted $%2 bail for %3", name _bailPoster, _bailAmount, name _jailedPlayer], "INFO", "LEGAL"] call EDEN_fnc_systemLogger;

[_bailPoster] call EDEN_fnc_savePlayerData;
[_jailedPlayer] call EDEN_fnc_savePlayerData;

true
