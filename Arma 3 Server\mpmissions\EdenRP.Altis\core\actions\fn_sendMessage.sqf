/*
    File: fn_sendMessage.sqf
    Author: EdenRP Development Team
    
    Description:
    Sends a message to another player or group.
    
    Parameters:
    0: OBJECT - Target player
    1: STRING - Message content
    2: OBJECT - Sender (optional, default: player)
    
    Returns:
    BOOLEAN - True if message was sent successfully
*/

params [
    ["_target", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_message", "", [""]],
    ["_sender", player, [obj<PERSON><PERSON>]]
];

if (isNull _target || isNull _sender || _message == "") exitWith { false };

if (_sender == _target) exitWith {
    ["You cannot send messages to yourself!"] call EDEN_fnc_showHint;
    false
};

// Check if sender has phone
_virtualItems = _sender getVariable ["eden_virtualItems", []];
_hasPhone = false;
{
    if ((_x select 0) == "phone") then {
        _hasPhone = true;
    };
} forEach _virtualItems;

if (!_hasPhone) exitWith {
    ["You need a phone to send messages!"] call EDEN_fnc_showHint;
    false
};

// Check if target has phone
_targetItems = _target getVariable ["eden_virtualItems", []];
_targetHasPhone = false;
{
    if ((_x select 0) == "phone") then {
        _targetHasPhone = true;
    };
} forEach _targetItems;

if (!_targetHasPhone) exitWith {
    [format ["%1 doesn't have a phone to receive messages!", name _target]] call EDEN_fnc_showHint;
    false
};

// Send message
_timestamp = [time, "HH:MM"] call BIS_fnc_timeToString;
_formattedMessage = format ["[%1] SMS from %2: %3", _timestamp, name _sender, _message];

[_formattedMessage] remoteExec ["EDEN_fnc_showHint", _target];
[format ["Message sent to %1", name _target]] call EDEN_fnc_showHint;

// Add to message history
_senderMessages = _sender getVariable ["eden_sentMessages", []];
_senderMessages pushBack [time, name _target, _message, "sent"];
_sender setVariable ["eden_sentMessages", _senderMessages, true];

_targetMessages = _target getVariable ["eden_receivedMessages", []];
_targetMessages pushBack [time, name _sender, _message, "received"];
_target setVariable ["eden_receivedMessages", _targetMessages, true];

// Log message
[format ["[EDEN] SMS: %1 -> %2: %3", name _sender, name _target, _message], "INFO", "COMMUNICATION"] call EDEN_fnc_systemLogger;

[_sender] call EDEN_fnc_savePlayerData;
[_target] call EDEN_fnc_savePlayerData;

true
