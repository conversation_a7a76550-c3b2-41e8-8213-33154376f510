version=53;
class EditorData
{
	moveGridStep=1;
	angleGridStep=0.2617994;
	scaleGridStep=1;
	autoGroupingDist=10;
	toggles=1;
	class ItemIDProvider
	{
		nextID=1000;
	};
	class Camera
	{
		pos[]={14441.5,20,16803.5};
		dir[]={0,0,1};
		up[]={0,1,0};
		aside[]={1,0,0};
	};
};
binarizationWanted=0;
addons[]=
{
	"A3_Characters_F",
	"a3_map_altis"
};
class AddonsMetaData
{
	class List
	{
		items=2;
		class Item0
		{
			className="A3_Characters_F";
			name="Arma 3 Alpha - Characters and Clothing";
			author="Bohemia Interactive";
			url="https://www.arma3.com";
		};
		class Item1
		{
			className="a3_map_altis";
			name="Arma 3 - Altis";
			author="Bohemia Interactive";
			url="https://www.arma3.com";
		};
	};
};
randomSeed=15724421;
class ScenarioData
{
	author="EdenRP Development Team";
	title="EdenRP - Enhanced Altis Life";
	subtitle="Experience the next generation of Altis Life roleplay";
	overviewText="EdenRP offers an enhanced Altis Life experience with advanced roleplay systems, realistic economy, and immersive gameplay mechanics.";
	briefingName="EdenRP - Enhanced Altis Life";
	overviewTextLocked="EdenRP - Enhanced Altis Life Roleplay Server";
	saving=0;
};
class Mission
{
	class Intel
	{
		timeOfChanges=1800.0002;
		startWeather=0.30000001;
		startWind=0.1;
		startWaves=0.1;
		forecastWeather=0.30000001;
		forecastWind=0.1;
		forecastWaves=0.1;
		forecastLightnings=0.1;
		year=2035;
		month=6;
		day=24;
		hour=12;
		minute=0;
		startFogDecay=0.014;
		forecastFogDecay=0.014;
	};
	class Entities
	{
		items=3;
		class Item0
		{
			dataType="Group";
			side="West";
			class Entities
			{
				items=2;
				class Item0
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={8421.04,75.1679,25299.2};
					};
					side="West";
					flags=7;
					class Attributes
					{
						name="cop_1";
						description="EdenRP Police Officer - Whitelisted";
						isPlayable=1;
					};
					id=100;
					type="B_Soldier_F";
				};
				class Item1
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={8421.04,75.418,25290.7};
					};
					side="West";
					flags=5;
					class Attributes
					{
						name="cop_2";
						description="EdenRP Police Officer - Whitelisted";
						isPlayable=1;
					};
					id=101;
					type="B_Soldier_F";
				};
			};
			class Attributes
			{
			};
			id=98;
		};
		class Item1
		{
			dataType="Group";
			side="Independent";
			class Entities
			{
				items=2;
				class Item0
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={8431.12,76.7446,25299.5};
					};
					side="Independent";
					flags=7;
					class Attributes
					{
						name="med_1";
						description="EdenRP Medic - Whitelisted";
						isPlayable=1;
					};
					id=200;
					type="I_medic_F";
				};
				class Item1
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={8431.23,77.1758,25297};
					};
					side="Independent";
					flags=5;
					class Attributes
					{
						name="med_2";
						description="EdenRP Medic - Whitelisted";
						isPlayable=1;
					};
					id=201;
					type="I_medic_F";
				};
			};
			class Attributes
			{
			};
			id=198;
		};
		class Item2
		{
			dataType="Group";
			side="Civilian";
			class Entities
			{
				items=2;
				class Item0
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={8441.04,75.1679,25299.2};
					};
					side="Civilian";
					flags=7;
					class Attributes
					{
						name="civ_1";
						description="EdenRP Civilian";
						isPlayable=1;
					};
					id=300;
					type="C_man_1";
				};
				class Item1
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={8441.04,75.418,25290.7};
					};
					side="Civilian";
					flags=5;
					class Attributes
					{
						name="civ_2";
						description="EdenRP Civilian";
						isPlayable=1;
					};
					id=301;
					type="C_man_1";
				};
			};
			class Attributes
			{
			};
			id=298;
		};
	};
};
