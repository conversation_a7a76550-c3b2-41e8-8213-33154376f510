/*
    File: fn_durabilitySystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages item durability and wear system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_item", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_itemDurability", [], true];
        _player setVariable ["eden_repairKits", 0, true];
        true
    };
    case "setDurability": {
        params ["", "", "", ["_itemIndex", 0, [0]], ["_durability", 100, [0]]];
        
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        if (_itemIndex >= count _virtualItems) exitWith {
            ["Invalid item index"] call EDEN_fnc_showHint;
            false
        };
        
        _durabilityArray = _player getVariable ["eden_itemDurability", []];
        
        // Ensure durability array matches virtual items
        while {count _durabilityArray < count _virtualItems} do {
            _durabilityArray pushBack 100;
        };
        
        _durabilityArray set [_itemIndex, (_durability max 0 min 100)];
        _player setVariable ["eden_itemDurability", _durabilityArray, true];
        
        [format ["Set durability to %1%%", _durability]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "degradeItem": {
        params ["", "", "", ["_itemIndex", 0, [0]], ["_degradeAmount", 5, [0]]];
        
        _durabilityArray = _player getVariable ["eden_itemDurability", []];
        if (_itemIndex >= count _durabilityArray) exitWith { false };
        
        _currentDurability = _durabilityArray select _itemIndex;
        _newDurability = (_currentDurability - _degradeAmount) max 0;
        
        _durabilityArray set [_itemIndex, _newDurability];
        _player setVariable ["eden_itemDurability", _durabilityArray, true];
        
        if (_newDurability <= 0) then {
            _virtualItems = _player getVariable ["eden_virtualItems", []];
            _itemName = (_virtualItems select _itemIndex) select 0;
            
            // Remove broken item
            [_player, "removeItem", _itemName, 1] call EDEN_fnc_itemSystem;
            
            [format ["%1 broke from overuse!", _itemName]] call EDEN_fnc_showHint;
        } else {
            if (_newDurability <= 20) then {
                ["Item is in poor condition - repair soon!"] call EDEN_fnc_showHint;
            };
        };
        
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "repairItem": {
        params ["", "", "", ["_itemIndex", 0, [0]]];
        
        _repairKits = _player getVariable ["eden_repairKits", 0];
        if (_repairKits <= 0) exitWith {
            ["No repair kits available"] call EDEN_fnc_showHint;
            false
        };
        
        _durabilityArray = _player getVariable ["eden_itemDurability", []];
        if (_itemIndex >= count _durabilityArray) exitWith {
            ["Invalid item index"] call EDEN_fnc_showHint;
            false
        };
        
        _currentDurability = _durabilityArray select _itemIndex;
        if (_currentDurability >= 100) exitWith {
            ["Item is already in perfect condition"] call EDEN_fnc_showHint;
            false
        };
        
        _repairAmount = 50;
        _newDurability = (_currentDurability + _repairAmount) min 100;
        
        _durabilityArray set [_itemIndex, _newDurability];
        _player setVariable ["eden_itemDurability", _durabilityArray, true];
        _player setVariable ["eden_repairKits", (_repairKits - 1), true];
        
        [format ["Item repaired to %1%% durability", _newDurability]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "buyRepairKit": {
        _cost = 200;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _repairKits = _player getVariable ["eden_repairKits", 0];
        _player setVariable ["eden_repairKits", (_repairKits + 1), true];
        
        [format ["Purchased repair kit for $%1", _cost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "checkDurability": {
        params ["", "", "", ["_itemIndex", 0, [0]]];
        
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _durabilityArray = _player getVariable ["eden_itemDurability", []];
        
        if (_itemIndex >= count _virtualItems) exitWith {
            ["Invalid item index"] call EDEN_fnc_showHint;
            false
        };
        
        _itemName = (_virtualItems select _itemIndex) select 0;
        _durability = if (_itemIndex < count _durabilityArray) then {
            _durabilityArray select _itemIndex
        } else {
            100
        };
        
        _condition = switch (true) do {
            case (_durability >= 80): { "Excellent" };
            case (_durability >= 60): { "Good" };
            case (_durability >= 40): { "Fair" };
            case (_durability >= 20): { "Poor" };
            default { "Broken" };
        };
        
        [format ["%1: %2%% (%3)", _itemName, _durability, _condition]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
