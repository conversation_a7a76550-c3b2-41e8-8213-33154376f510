-- EdenRP Database Connection Test (No Password Setup)
-- Run this in HeidiSQL to verify everything is working
-- Updated for your no-password MySQL configuration

-- Test 1: Check if we can connect to the database
SELECT 'Connected to MySQL successfully!' AS Test1_Connection;

-- Test 2: Switch to edenrp database
USE edenrp;
SELECT 'Using edenrp database successfully!' AS Test2_Database;

-- Test 3: Show all tables (should show EdenRP tables after schema import)
SELECT 'Checking available tables...' AS Test3_Tables;
SHOW TABLES;

-- Test 4: Check if key EdenRP tables exist
SELECT
    CASE
        WHEN COUNT(*) > 0 THEN 'eden_players table exists ✓'
        ELSE 'eden_players table missing ✗'
    END AS Test4_Players_Table
FROM information_schema.tables
WHERE table_schema = 'edenrp' AND table_name = 'eden_players';

SELECT
    CASE
        WHEN COUNT(*) > 0 THEN 'eden_vehicles table exists ✓'
        ELSE 'eden_vehicles table missing ✗'
    END AS Test4_Vehicles_Table
FROM information_schema.tables
WHERE table_schema = 'edenrp' AND table_name = 'eden_vehicles';

SELECT
    CASE
        WHEN COUNT(*) > 0 THEN 'eden_gangs table exists ✓'
        ELSE 'eden_gangs table missing ✗'
    END AS Test4_Gangs_Table
FROM information_schema.tables
WHERE table_schema = 'edenrp' AND table_name = 'eden_gangs';

-- Test 5: Check table structure (only if eden_players table exists)
SELECT 'Checking eden_players table structure...' AS Test5_Structure;
DESCRIBE eden_players;

-- Test 6: Test insert/select/delete operations on eden_players
SELECT 'Testing database operations...' AS Test6_Operations;

INSERT INTO eden_players (
    player_id,
    name,
    cash,
    bank,
    experience,
    level,
    reputation,
    playtime,
    cop_level,
    medic_level,
    admin_level,
    cop_licenses,
    civ_licenses,
    med_licenses,
    active
) VALUES (
    '*****************',
    'EdenRP_TestUser',
    10000,
    50000,
    0,
    1,
    0,
    0,
    0,
    0,
    0,
    '[]',
    '[]',
    '[]',
    1
);

-- Verify the test record was inserted
SELECT 'Test player inserted successfully!' AS Test6_Insert;
SELECT * FROM eden_players WHERE name = 'EdenRP_TestUser';

-- Test update operation
UPDATE eden_players SET cash = 15000 WHERE name = 'EdenRP_TestUser';
SELECT 'Test player updated successfully!' AS Test6_Update;
SELECT cash FROM eden_players WHERE name = 'EdenRP_TestUser';

-- Clean up test data
DELETE FROM eden_players WHERE name = 'EdenRP_TestUser';
SELECT 'Test data cleaned up successfully!' AS Test6_Cleanup;

-- Test 7: Final status check
SELECT
    CONCAT('Database: ', DATABASE(), ' | User: ', USER(), ' | Time: ', NOW())
    AS Test7_Final_Status;

SELECT '🎉 All tests completed! EdenRP database is ready!' AS Final_Result;
