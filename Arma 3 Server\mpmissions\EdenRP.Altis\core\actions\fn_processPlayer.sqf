/*
    File: fn_processPlayer.sqf
    Author: EdenRP Development Team
    
    Description:
    Processes a player (police booking).
*/

params [["_suspect", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]], ["_officer", player, [obj<PERSON><PERSON>]]];

if (isNull _suspect || isNull _officer) exitWith { false };
if (!(_officer getVariable ["eden_isPolice", false])) exitWith { false };

_wantedLevel = _suspect getVariable ["eden_wantedLevel", 0];
if (_wantedLevel <= 0) exitWith {
    ["Suspect is not wanted!"] call EDEN_fnc_showHint;
    false
};

_fine = _wantedLevel * 1000;
_jailTime = _wantedLevel * 120;

_suspect setVariable ["eden_wantedLevel", 0, true];
_suspect setVariable ["eden_isJailed", true, true];
_suspect setVariable ["eden_jailTime", _jailTime, true];

_jailPos = [3688.5, 13092.5, 0.1];
_suspect setPosATL _jailPos;

[_suspect, _jailTime] spawn {
    params ["_jailedPlayer", "_time"];
    sleep _time;
    if (_jailedPlayer getVariable ["eden_isJailed", false]) then {
        _jailedPlayer setVariable ["eden_isJailed", false, true];
        _jailedPlayer setVariable ["eden_jailTime", 0, true];
        _jailedPlayer setPosATL [3695, 13095, 0.1];
        ["You have been released from jail."] remoteExec ["EDEN_fnc_showHint", _jailedPlayer];
    };
};

[format ["Processed %1 - Fine: $%2, Jail: %3min", name _suspect, _fine, round(_jailTime/60)]] call EDEN_fnc_showHint;
[format ["[EDEN] Player %1 processed by %2", name _suspect, name _officer], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
true
