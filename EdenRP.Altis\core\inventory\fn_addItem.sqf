/*
    EdenRP Add Item Function
    Enhanced item addition with weight and validation
*/

params ["_player", "_item", "_quantity", "_virtualInventory", "_physicalInventory", "_maxWeight", "_currentWeight"];

// Validate item
private _itemConfig = [_item] call EDEN_fnc_getItemConfig;
if (count _itemConfig == 0) exitWith {
    [format["Invalid item: %1", _item], "ERROR", "INVENTORY"] call EDEN_fnc_systemLogger;
    false
};

// Get item properties
private _itemWeight = _itemConfig select 2;
private _itemType = _itemConfig select 3;
private _isVirtual = _itemConfig select 4;
private _maxStack = _itemConfig select 5;

// Calculate total weight after addition
private _totalWeight = _itemWeight * _quantity;
if (_currentWeight + _totalWeight > _maxWeight) exitWith {
    ["Inventory weight limit exceeded", "WARNING", "INVENTORY"] call EDEN_fnc_systemLogger;
    ["Your inventory is too heavy!", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Choose inventory type
private _targetInventory = if (_isVirtual) then {_virtualInventory} else {_physicalInventory};

// Find existing item
private _existingIndex = -1;
{
    if ((_x select 0) == _item) exitWith {
        _existingIndex = _forEachIndex;
    };
} forEach _targetInventory;

if (_existingIndex >= 0) then {
    // Add to existing stack
    private _currentQuantity = (_targetInventory select _existingIndex) select 1;
    private _newQuantity = _currentQuantity + _quantity;
    
    // Check stack limit
    if (_newQuantity > _maxStack) then {
        private _overflow = _newQuantity - _maxStack;
        (_targetInventory select _existingIndex) set [1, _maxStack];
        
        // Add overflow as new stack
        if (_overflow > 0) then {
            _targetInventory pushBack [_item, _overflow, time, []];
        };
    } else {
        (_targetInventory select _existingIndex) set [1, _newQuantity];
    };
} else {
    // Add new item
    while {_quantity > 0} do {
        private _stackSize = if (_quantity > _maxStack) then {_maxStack} else {_quantity};
        _targetInventory pushBack [_item, _stackSize, time, []];
        _quantity = _quantity - _stackSize;
    };
};

true
