/*
    File: fn_healPlayer.sqf
    Author: EdenRP Development Team
    
    Description:
    Heals a player (medical personnel only).
    
    Parameters:
    0: OBJECT - Target player to heal
    1: OBJECT - Medic (optional, default: player)
    
    Returns:
    BOOLEAN - True if healing was successful
*/

params [
    ["_target", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_medic", player, [obj<PERSON><PERSON>]]
];

if (isNull _target || isNull _medic) exitWith { false };

if (!(_medic getVariable ["eden_isMedic", false])) exitWith {
    ["Only medical personnel can heal players!"] call EDEN_fnc_showHint;
    false
};

if (_medic distance _target > 5) exitWith {
    ["Target is too far away!"] call EDEN_fnc_showHint;
    false
};

if (!alive _target) exitWith {
    ["Cannot heal unconscious players - use revive instead!"] call EDEN_fnc_showHint;
    false
};

// Healing animation
[_medic, "AinvPknlMstpSnonWnonDnon_medic_1"] remoteExec ["switchMove"];
["Healing player..."] call EDEN_fnc_showHint;

sleep 5; // Healing time

[_medic, ""] remoteExec ["switchMove"];

// Heal the target
_target setDamage 0;
_target setVariable ["eden_hunger", 100, true];
_target setVariable ["eden_thirst", 100, true];

// Update medical statistics
_patientsHealed = _medic getVariable ["eden_patientsHealed", 0];
_medic setVariable ["eden_patientsHealed", (_patientsHealed + 1), true];

// Notifications
[format ["Successfully healed %1", name _target]] remoteExec ["EDEN_fnc_showHint", _medic];
[format ["You have been healed by %1", name _medic]] remoteExec ["EDEN_fnc_showHint", _target];

[format ["[EDEN] %1 healed %2", name _medic, name _target], "INFO", "MEDICAL"] call EDEN_fnc_systemLogger;

[_medic] call EDEN_fnc_savePlayerData;
[_target] call EDEN_fnc_savePlayerData;

true
