/*
    File: fn_transferSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages item transfer between players.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_transferHistory", [], true];
        _player setVariable ["eden_pendingTransfers", [], true];
        true
    };
    case "sendItem": {
        params ["", "", "", ["_item", "", [""]], ["_quantity", 1, [0]]];
        
        if (isNull _target) exitWith {
            ["No target player selected"] call EDEN_fnc_showHint;
            false
        };
        
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _hasItem = false;
        _playerQuantity = 0;
        
        {
            if ((_x select 0) == _item) then {
                _hasItem = true;
                _playerQuantity = _x select 1;
            };
        } forEach _virtualItems;
        
        if (!_hasItem || _playerQuantity < _quantity) exitWith {
            ["Insufficient items to transfer"] call EDEN_fnc_showHint;
            false
        };
        
        _distance = _player distance _target;
        if (_distance > 10) exitWith {
            ["Target player too far away"] call EDEN_fnc_showHint;
            false
        };
        
        // Remove from sender
        [_player, "removeItem", _item, _quantity] call EDEN_fnc_itemSystem;
        
        // Add to receiver
        [_target, "addItem", _item, _quantity] call EDEN_fnc_itemSystem;
        
        // Log transfer
        _senderHistory = _player getVariable ["eden_transferHistory", []];
        _senderHistory pushBack ["sent", _item, _quantity, getPlayerUID _target, time];
        _player setVariable ["eden_transferHistory", _senderHistory, true];
        
        _receiverHistory = _target getVariable ["eden_transferHistory", []];
        _receiverHistory pushBack ["received", _item, _quantity, getPlayerUID _player, time];
        _target setVariable ["eden_transferHistory", _receiverHistory, true];
        
        [format ["Sent %1x %2 to %3", _quantity, _item, name _target]] call EDEN_fnc_showHint;
        [format ["Received %1x %2 from %3", _quantity, _item, name _player]] remoteExec ["EDEN_fnc_showHint", _target];
        
        [_player] call EDEN_fnc_savePlayerData;
        [_target] call EDEN_fnc_savePlayerData;
        true
    };
    case "requestItem": {
        params ["", "", "", ["_item", "", [""]], ["_quantity", 1, [0]]];
        
        if (isNull _target) exitWith { false };
        
        _pending = _target getVariable ["eden_pendingTransfers", []];
        _request = [getPlayerUID _player, name _player, _item, _quantity, time];
        _pending pushBack _request;
        _target setVariable ["eden_pendingTransfers", _pending, true];
        
        [format ["Requested %1x %2 from %3", _quantity, _item, name _target]] call EDEN_fnc_showHint;
        [format ["%1 requests %2x %3", name _player, _quantity, _item]] remoteExec ["EDEN_fnc_showHint", _target];
        true
    };
    case "acceptRequest": {
        params ["", "", "", ["_requestIndex", 0, [0]]];
        
        _pending = _player getVariable ["eden_pendingTransfers", []];
        if (_requestIndex >= count _pending) exitWith {
            ["Invalid request"] call EDEN_fnc_showHint;
            false
        };
        
        _request = _pending select _requestIndex;
        _requesterUID = _request select 0;
        _item = _request select 2;
        _quantity = _request select 3;
        
        _requester = objNull;
        {
            if (getPlayerUID _x == _requesterUID) then { _requester = _x; };
        } forEach allPlayers;
        
        if (isNull _requester) exitWith {
            ["Requester not found"] call EDEN_fnc_showHint;
            false
        };
        
        [_player, "sendItem", _requester, _item, _quantity] call EDEN_fnc_transferSystem;
        
        _pending deleteAt _requestIndex;
        _player setVariable ["eden_pendingTransfers", _pending, true];
        
        ["Request accepted"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
