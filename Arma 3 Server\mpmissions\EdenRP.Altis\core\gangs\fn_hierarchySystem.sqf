/*
    File: fn_hierarchySystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages gang hierarchy system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_gangRank", "Member", true];
        _player setVariable ["eden_gangLoyalty", 50, true];
        true
    };
    case "promotePlayer": {
        _gang = _player getVariable ["eden_gang", ""];
        _rank = _player getVariable ["eden_gangRank", ""];
        
        if (_gang == "" || _rank != "Leader") exitWith {
            ["Only gang leaders can promote members!"] call EDEN_fnc_showHint;
            false
        };
        
        if (isNull _target) exitWith { false };
        
        _targetRank = _target getVariable ["eden_gangRank", "Member"];
        _newRank = switch (_targetRank) do {
            case "Member": { "Lieutenant" };
            case "Lieutenant": { "Captain" };
            default { "Member" };
        };
        
        _target setVariable ["eden_gangRank", _newRank, true];
        
        [format ["Promoted %1 to %2", name _target, _newRank]] call EDEN_fnc_showHint;
        [format ["You have been promoted to %1", _newRank]] remoteExec ["EDEN_fnc_showHint", _target];
        true
    };
    case "demotePlayer": {
        _gang = _player getVariable ["eden_gang", ""];
        _rank = _player getVariable ["eden_gangRank", ""];
        
        if (_gang == "" || _rank != "Leader") exitWith {
            ["Only gang leaders can demote members!"] call EDEN_fnc_showHint;
            false
        };
        
        if (isNull _target) exitWith { false };
        
        _target setVariable ["eden_gangRank", "Member", true];
        
        [format ["Demoted %1 to Member", name _target]] call EDEN_fnc_showHint;
        ["You have been demoted to Member"] remoteExec ["EDEN_fnc_showHint", _target];
        true
    };
    default { false };
};
