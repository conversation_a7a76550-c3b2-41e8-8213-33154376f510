/*
    File: fn_emergencyCall.sqf
    Author: EdenRP Development Team
    
    Description:
    Makes an emergency call to police or medical services.
    
    Parameters:
    0: STRING - Emergency type ("police" or "medical")
    1: STRING - Emergency description
    2: OBJECT - Caller (optional, default: player)
    
    Returns:
    BOOLEAN - True if emergency call was made successfully
*/

params [
    ["_emergencyType", "", [""]],
    ["_description", "", [""]],
    ["_caller", player, [objNull]]
];

if (_emergencyType == "" || _description == "" || isNull _caller) exitWith { false };

if (!(_emergencyType in ["police", "medical"])) exitWith {
    ["Invalid emergency type! Use 'police' or 'medical'"] call EDEN_fnc_showHint;
    false
};

// Check if caller has phone
_virtualItems = _caller getVariable ["eden_virtualItems", []];
_hasPhone = false;
{
    if ((_x select 0) == "phone") then {
        _hasPhone = true;
    };
} forEach _virtualItems;

if (!_hasPhone) exitWith {
    ["You need a phone to make emergency calls!"] call EDEN_fnc_showHint;
    false
};

// Check cooldown (prevent spam)
_lastEmergencyCall = _caller getVariable ["eden_lastEmergencyCall", 0];
if ((time - _lastEmergencyCall) < 60) exitWith { // 1 minute cooldown
    ["You can only make emergency calls once per minute!"] call EDEN_fnc_showHint;
    false
};

_caller setVariable ["eden_lastEmergencyCall", time, true];

// Find available emergency responders
_responders = [];
switch (_emergencyType) do {
    case "police": {
        {
            if (_x getVariable ["eden_isPolice", false] && alive _x) then {
                _responders pushBack _x;
            };
        } forEach allPlayers;
    };
    case "medical": {
        {
            if (_x getVariable ["eden_isMedic", false] && alive _x) then {
                _responders pushBack _x;
            };
        } forEach allPlayers;
    };
};

if (count _responders == 0) exitWith {
    [format ["No %1 personnel are currently available!", _emergencyType]] call EDEN_fnc_showHint;
    false
};

// Create emergency call
_callID = format ["EMG-%1-%2", floor(time), floor(random 9999)];
_timestamp = [time, "HH:MM:SS"] call BIS_fnc_timeToString;
_callerPos = getPosATL _caller;

_emergencyMessage = format [
    "EMERGENCY CALL - %1\nCall ID: %2\nTime: %3\nCaller: %4\nLocation: %5\nDescription: %6",
    toUpper _emergencyType,
    _callID,
    _timestamp,
    name _caller,
    mapGridPosition _caller,
    _description
];

// Send to all responders
{
    [
        format ["%1 Emergency", toUpper _emergencyType],
        _emergencyMessage,
        20,
        "error"
    ] remoteExec ["EDEN_fnc_showNotification", _x];
} forEach _responders;

// Create emergency marker
_markerName = format ["emergency_%1", _callID];
_marker = createMarker [_markerName, _callerPos];
_marker setMarkerType "mil_warning";
_marker setMarkerText format ["%1 Emergency", toUpper _emergencyType];
_marker setMarkerColor if (_emergencyType == "police") then {"ColorRed"} else {"ColorBlue"};

// Add to emergency call log
_emergencyCalls = missionNamespace getVariable ["eden_emergencyCalls", []];
_emergencyCalls pushBack [time, _callID, _emergencyType, name _caller, _callerPos, _description, false]; // false = not responded
missionNamespace setVariable ["eden_emergencyCalls", _emergencyCalls, true];

// Auto-remove marker after 30 minutes
[_markerName] spawn {
    params ["_marker"];
    sleep 1800; // 30 minutes
    deleteMarker _marker;
};

[format ["Emergency call sent to %1 %2 personnel (Call ID: %3)", count _responders, _emergencyType, _callID]] call EDEN_fnc_showHint;

// Log emergency call
[format ["[EDEN] Emergency call: %1 called %2 - %3", name _caller, _emergencyType, _description], "WARN", "EMERGENCY"] call EDEN_fnc_systemLogger;

true
