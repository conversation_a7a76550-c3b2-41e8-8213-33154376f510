/*
    File: fn_moneyLaundering.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages gang money laundering system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_amount", 0, [0]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_dirtyMoney", 0, true];
        _player setVariable ["eden_launderingOperations", 0, true];
        true
    };
    case "launderMoney": {
        _gang = _player getVariable ["eden_gang", ""];
        if (_gang == "") exitWith {
            ["You must be in a gang!"] call EDEN_fnc_showHint;
            false
        };
        
        _dirtyMoney = _player getVariable ["eden_dirtyMoney", 0];
        if (_dirtyMoney < _amount) exitWith {
            ["Not enough dirty money"] call EDEN_fnc_showHint;
            false
        };
        
        _cleanAmount = floor(_amount * 0.8); // 20% fee
        _player setVariable ["eden_dirtyMoney", (_dirtyMoney - _amount), true];
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _cleanAmount), true];
        
        _ops = _player getVariable ["eden_launderingOperations", 0];
        _player setVariable ["eden_launderingOperations", (_ops + 1), true];
        
        [format ["Laundered $%1 (received $%2)", _amount, _cleanAmount]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "addDirtyMoney": {
        _dirtyMoney = _player getVariable ["eden_dirtyMoney", 0];
        _player setVariable ["eden_dirtyMoney", (_dirtyMoney + _amount), true];
        
        [format ["Acquired $%1 dirty money", _amount]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
