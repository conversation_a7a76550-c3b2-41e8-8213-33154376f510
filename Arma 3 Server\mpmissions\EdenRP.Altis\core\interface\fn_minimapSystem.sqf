/*
    File: fn_minimapSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages minimap system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_minimapEnabled", true, true];
        _player setVariable ["eden_minimapZoom", 0.5, true];
        true
    };
    case "toggleMinimap": {
        _enabled = _player getVariable ["eden_minimapEnabled", true];
        _player setVariable ["eden_minimapEnabled", !_enabled, true];
        
        if (!_enabled) then {
            ["Minimap enabled"] call EDEN_fnc_showHint;
        } else {
            ["Minimap disabled"] call EDEN_fnc_showHint;
        };
        true
    };
    case "zoomIn": {
        _zoom = _player getVariable ["eden_minimapZoom", 0.5];
        _newZoom = (_zoom * 1.5) min 2.0;
        _player setVariable ["eden_minimapZoom", _newZoom, true];
        
        [format ["Minimap zoom: %1x", _newZoom]] call EDEN_fnc_showHint;
        true
    };
    case "zoomOut": {
        _zoom = _player getVariable ["eden_minimapZoom", 0.5];
        _newZoom = (_zoom / 1.5) max 0.1;
        _player setVariable ["eden_minimapZoom", _newZoom, true];
        
        [format ["Minimap zoom: %1x", _newZoom]] call EDEN_fnc_showHint;
        true
    };
    case "showNearbyPlayers": {
        if (!(_player getVariable ["eden_minimapEnabled", true])) exitWith { false };
        
        _nearbyPlayers = [];
        {
            if (_x != _player && _x distance _player < 500) then {
                _nearbyPlayers pushBack _x;
            };
        } forEach allPlayers;
        
        [format ["Nearby players: %1", count _nearbyPlayers]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
