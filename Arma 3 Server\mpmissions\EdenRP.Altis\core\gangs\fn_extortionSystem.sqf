/*
    File: fn_extortionSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages gang extortion system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_extortionVictims", [], true];
        _player setVariable ["eden_extortionIncome", 0, true];
        true
    };
    case "extortPlayer": {
        _gang = _player getVariable ["eden_gang", ""];
        if (_gang == "") exitWith {
            ["You must be in a gang!"] call EDEN_fnc_showHint;
            false
        };
        
        if (isNull _target) exitWith { false };
        
        _amount = 500;
        _targetCash = _target getVariable ["eden_cash", 0];
        
        if (_targetCash < _amount) exitWith {
            ["Target doesn't have enough money"] call EDEN_fnc_showHint;
            false
        };
        
        _target setVariable ["eden_cash", (_targetCash - _amount), true];
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _amount), true];
        
        _victims = _player getVariable ["eden_extortionVictims", []];
        _victims pushBack (getPlayerUID _target);
        _player setVariable ["eden_extortionVictims", _victims, true];
        
        _income = _player getVariable ["eden_extortionIncome", 0];
        _player setVariable ["eden_extortionIncome", (_income + _amount), true];
        
        [format ["Extorted $%1 from %2", _amount, name _target]] call EDEN_fnc_showHint;
        [format ["You have been extorted for $%1", _amount]] remoteExec ["EDEN_fnc_showHint", _target];
        
        [_player] call EDEN_fnc_savePlayerData;
        [_target] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
