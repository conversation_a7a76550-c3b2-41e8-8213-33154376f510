/*
    File: fn_sellItem.sqf
    Author: EdenRP Development Team
    
    Description:
    Allows players to sell items to shops.
    
    Parameters:
    0: STRING - Item class name
    1: NUMBER - Quantity (optional, default: 1)
    2: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if item was sold successfully
*/

params [
    ["_itemClass", "", [""]],
    ["_quantity", 1, [0]],
    ["_player", player, [objNull]]
];

if (_itemClass == "" || _quantity <= 0 || isNull _player) exitWith { false };

// Item sell prices (usually 60% of buy price)
_itemPrices = createHashMap;
_itemPrices set ["water_bottle", 30];
_itemPrices set ["apple", 15];
_itemPrices set ["bread", 45];
_itemPrices set ["toolkit", 300];
_itemPrices set ["first_aid_kit", 90];
_itemPrices set ["rope", 60];
_itemPrices set ["lockpick", 150];
_itemPrices set ["zipties", 45];
_itemPrices set ["phone", 600];
_itemPrices set ["radio", 450];
// Raw materials
_itemPrices set ["copper", 100];
_itemPrices set ["iron", 150];
_itemPrices set ["diamond", 2500];
_itemPrices set ["stone", 25];
_itemPrices set ["sand", 15];
_itemPrices set ["oil", 300];
// Processed materials
_itemPrices set ["copper_ingot", 250];
_itemPrices set ["iron_ingot", 400];
_itemPrices set ["processed_diamond", 5000];

_unitPrice = _itemPrices getOrDefault [_itemClass, 0];
if (_unitPrice == 0) exitWith {
    ["This item cannot be sold here!"] call EDEN_fnc_showHint;
    false
};

// Check if player has the item
_virtualItems = _player getVariable ["eden_virtualItems", []];
_playerQuantity = 0;
_itemIndex = -1;

{
    if ((_x select 0) == _itemClass) then {
        _playerQuantity = _x select 1;
        _itemIndex = _forEachIndex;
    };
} forEach _virtualItems;

if (_playerQuantity < _quantity) exitWith {
    [format ["You don't have enough %1 to sell! (Have: %2, Need: %3)", _itemClass, _playerQuantity, _quantity]] call EDEN_fnc_showHint;
    false
};

// Sell item
_totalPrice = _unitPrice * _quantity;
_playerMoney = _player getVariable ["eden_cash", 0];
_player setVariable ["eden_cash", (_playerMoney + _totalPrice), true];

// Remove from inventory
if (_playerQuantity == _quantity) then {
    _virtualItems deleteAt _itemIndex;
} else {
    (_virtualItems select _itemIndex) set [1, (_playerQuantity - _quantity)];
};

_player setVariable ["eden_virtualItems", _virtualItems, true];

// Update weight
_itemWeight = 1; // Default item weight
_totalWeight = _itemWeight * _quantity;
_currentWeight = _player getVariable ["eden_currentWeight", 0];
_player setVariable ["eden_currentWeight", (_currentWeight - _totalWeight), true];

[format ["Sold %1x %2 for $%3", _quantity, _itemClass, _totalPrice]] call EDEN_fnc_showHint;
[_player] call EDEN_fnc_savePlayerData;

true
