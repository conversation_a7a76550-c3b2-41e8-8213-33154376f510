/*
    File: fn_craftItem.sqf
    Author: EdenRP Development Team
    
    Description:
    Crafts items from raw materials.
    
    Parameters:
    0: STRING - Item to craft
    1: NUMBER - Quantity (optional, default: 1)
    2: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if crafting was successful
*/

params [
    ["_itemToCraft", "", [""]],
    ["_quantity", 1, [0]],
    ["_player", player, [objNull]]
];

if (_itemToCraft == "" || _quantity <= 0 || isNull _player) exitWith { false };

// Crafting recipes [required_materials, craft_time, required_level]
_recipes = createHashMap;
_recipes set ["toolkit", [["iron_ingot", 2, "rope", 1], 10, 5]];
_recipes set ["lockpick", [["iron_ingot", 1], 5, 3]];
_recipes set ["rope", [["processed_hemp", 3], 8, 2]];
_recipes set ["zipties", [["plastic", 2], 3, 1]];
_recipes set ["first_aid_kit", [["bandage", 3, "medicine", 1], 15, 4]];
_recipes set ["radio", [["electronics", 2, "battery", 1], 20, 6]];
_recipes set ["phone", [["electronics", 3, "battery", 1, "plastic", 1], 25, 8]];

_recipe = _recipes get _itemToCraft;
if (isNil "_recipe") exitWith {
    ["This item cannot be crafted!"] call EDEN_fnc_showHint;
    false
};

_recipe params ["_materials", "_craftTime", "_requiredLevel"];

// Check player level
_playerLevel = _player getVariable ["eden_playerLevel", 1];
if (_playerLevel < _requiredLevel) exitWith {
    [format ["You need level %1 to craft this item!", _requiredLevel]] call EDEN_fnc_showHint;
    false
};

// Check materials
_virtualItems = _player getVariable ["eden_virtualItems", []];
_canCraft = true;
_materialCount = count _materials;

for "_i" from 0 to (_materialCount - 1) step 2 do {
    _materialName = _materials select _i;
    _materialQuantity = (_materials select (_i + 1)) * _quantity;
    
    _hasQuantity = 0;
    {
        if ((_x select 0) == _materialName) then {
            _hasQuantity = _x select 1;
        };
    } forEach _virtualItems;
    
    if (_hasQuantity < _materialQuantity) then {
        [format ["You need %1x %2 to craft this!", _materialQuantity, _materialName]] call EDEN_fnc_showHint;
        _canCraft = false;
    };
};

if (!_canCraft) exitWith { false };

// Start crafting
_totalTime = _craftTime * _quantity;
[format ["Crafting %1x %2... (%3 seconds)", _quantity, _itemToCraft, _totalTime]] call EDEN_fnc_showHint;

[_player, "Acts_carFixingWheel"] remoteExec ["switchMove"];

sleep _totalTime;

[_player, ""] remoteExec ["switchMove"];

// Remove materials
for "_i" from 0 to (_materialCount - 1) step 2 do {
    _materialName = _materials select _i;
    _materialQuantity = (_materials select (_i + 1)) * _quantity;
    
    {
        if ((_x select 0) == _materialName) then {
            _newQuantity = (_x select 1) - _materialQuantity;
            if (_newQuantity <= 0) then {
                _virtualItems deleteAt _forEachIndex;
            } else {
                _x set [1, _newQuantity];
            };
        };
    } forEach _virtualItems;
};

// Add crafted item
_found = false;
{
    if ((_x select 0) == _itemToCraft) then {
        _x set [1, ((_x select 1) + _quantity)];
        _found = true;
    };
} forEach _virtualItems;

if (!_found) then {
    _virtualItems pushBack [_itemToCraft, _quantity];
};

_player setVariable ["eden_virtualItems", _virtualItems, true];

// Add experience
_expGained = _quantity * 25;
_currentExp = _player getVariable ["eden_experience", 0];
_player setVariable ["eden_experience", (_currentExp + _expGained), true];

[format ["Crafted %1x %2 (+%3 XP)", _quantity, _itemToCraft, _expGained]] call EDEN_fnc_showHint;
[_player] call EDEN_fnc_savePlayerData;

true
