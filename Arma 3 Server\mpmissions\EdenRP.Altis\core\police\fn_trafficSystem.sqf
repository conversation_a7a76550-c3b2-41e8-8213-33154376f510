/*
    File: fn_trafficSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages traffic enforcement system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON>ull]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_ticketsIssued", 0, true];
        _player setVariable ["eden_trafficStops", 0, true];
        true
    };
    case "pullOver": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty!"] call EDEN_fnc_showHint;
            false
        };
        
        if (isNull _target) exitWith {
            ["No target specified!"] call EDEN_fnc_showHint;
            false
        };
        
        _stops = _player getVariable ["eden_trafficStops", 0];
        _player setVariable ["eden_trafficStops", (_stops + 1), true];
        
        ["You have been pulled over by police!"] remoteExec ["EDEN_fnc_showHint", _target];
        [format ["Pulled over %1", name _target]] call EDEN_fnc_showHint;
        true
    };
    case "issueTicket": {
        params ["", "", "", ["_violation", "", [""]], ["_fine", 100, [0]]];
        
        if (isNull _target) exitWith { false };
        
        _cash = _target getVariable ["eden_cash", 0];
        _target setVariable ["eden_cash", (_cash - _fine) max 0, true];
        
        _tickets = _player getVariable ["eden_ticketsIssued", 0];
        _player setVariable ["eden_ticketsIssued", (_tickets + 1), true];
        
        [format ["Ticket issued: %1 - $%2", _violation, _fine]] call EDEN_fnc_showHint;
        [format ["You received a ticket: %1 - $%2", _violation, _fine]] remoteExec ["EDEN_fnc_showHint", _target];
        
        [_player] call EDEN_fnc_savePlayerData;
        [_target] call EDEN_fnc_savePlayerData;
        true
    };
    case "breathalyzer": {
        if (isNull _target) exitWith { false };
        
        _result = random 1;
        if (_result > 0.8) then {
            ["Breathalyzer: POSITIVE - Driver is intoxicated!"] call EDEN_fnc_showHint;
            [_player, "issueTicket", _target, "DUI", 1000] call EDEN_fnc_trafficSystem;
        } else {
            ["Breathalyzer: NEGATIVE - Driver is sober"] call EDEN_fnc_showHint;
        };
        true
    };
    default { false };
};
