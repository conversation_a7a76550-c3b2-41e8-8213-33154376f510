/*
    File: fn_investmentSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages investment and portfolio system.
*/

params [["_player", player, [objN<PERSON>]], ["_action", "init", [""]], ["_investment", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_investmentOptions") then {
            eden_investmentOptions = [
                ["bonds", "Government Bonds", 0.03, 30],
                ["mutual_funds", "Mutual Funds", 0.07, 15],
                ["real_estate", "Real Estate", 0.12, 10],
                ["crypto", "Cryptocurrency", 0.25, 5]
            ];
            publicVariable "eden_investmentOptions";
        };
        _player setVariable ["eden_investments", [], true];
        true
    };
    case "makeInvestment": {
        params ["", "", "", ["_amount", 1000, [0]]];
        
        _investmentData = [];
        {
            if ((_x select 0) == _investment) then { _investmentData = _x; };
        } forEach eden_investmentOptions;
        
        if (count _investmentData == 0) exitWith {
            ["Investment option not available"] call EDEN_fnc_showHint;
            false
        };
        
        _minInvestment = (_investmentData select 3) * 100;
        if (_amount < _minInvestment) exitWith {
            [format ["Minimum investment: $%1", _minInvestment]] call EDEN_fnc_showHint;
            false
        };
        
        _cash = _player getVariable ["eden_cash", 0];
        if (_cash < _amount) exitWith {
            [format ["Not enough money! Need $%1", _amount]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _amount), true];
        
        _investments = _player getVariable ["eden_investments", []];
        _newInvestment = [_investment, _amount, time, (_investmentData select 2)];
        _investments pushBack _newInvestment;
        _player setVariable ["eden_investments", _investments, true];
        
        [format ["Invested $%1 in %2", _amount, (_investmentData select 1)]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "withdrawInvestment": {
        params ["", "", "", ["_investmentIndex", 0, [0]]];
        
        _investments = _player getVariable ["eden_investments", []];
        if (_investmentIndex >= count _investments) exitWith {
            ["Invalid investment selection"] call EDEN_fnc_showHint;
            false
        };
        
        _investment = _investments select _investmentIndex;
        _principal = _investment select 1;
        _startTime = _investment select 2;
        _rate = _investment select 3;
        
        _timeHeld = (time - _startTime) / 86400; // Days
        _returns = floor(_principal * _rate * (_timeHeld / 365));
        _totalValue = _principal + _returns;
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _totalValue), true];
        
        _investments deleteAt _investmentIndex;
        _player setVariable ["eden_investments", _investments, true];
        
        [format ["Investment withdrawn: $%1 (Returns: $%2)", _totalValue, _returns]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "checkReturns": {
        _investments = _player getVariable ["eden_investments", []];
        _totalValue = 0;
        _totalReturns = 0;
        
        {
            _principal = _x select 1;
            _startTime = _x select 2;
            _rate = _x select 3;
            _timeHeld = (time - _startTime) / 86400;
            _returns = floor(_principal * _rate * (_timeHeld / 365));
            _totalValue = _totalValue + _principal + _returns;
            _totalReturns = _totalReturns + _returns;
        } forEach _investments;
        
        [format ["Portfolio Value: $%1 (Returns: $%2)", _totalValue, _totalReturns]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
