/*
    EdenRP Create Gang Function
    Enhanced gang creation with validation and features
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_data", [], [[]]]
];

// Validate player
if (isNull _player || !isPlayer _player) exitWith {
    ["Invalid player provided to createGang", "ERROR", "GANGS"] call EDEN_fnc_systemLogger;
    false
};

// Check if player is already in a gang
private _currentGangId = _player getVariable ["EDEN_GangID", -1];
if (_currentGangId > 0) exitWith {
    ["You are already in a gang", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Extract gang data
if (count _data < 2) exitWith {
    ["Invalid gang data provided", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

private _gangName = _data select 0;
private _gangTag = _data select 1;
private _gangDescription = if (count _data > 2) then {_data select 2} else {""};
private _gangColor = if (count _data > 3) then {_data select 3} else {"#FFFFFF"};

// Validate gang name
if (_gangName == "" || count _gangName < 3 || count _gangName > 32) exitWith {
    ["Gang name must be between 3 and 32 characters", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Validate gang tag
if (_gangTag == "" || count _gangTag < 2 || count _gangTag > 6) exitWith {
    ["Gang tag must be between 2 and 6 characters", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Check for inappropriate content
if ([_gangName] call EDEN_fnc_containsProfanity || [_gangTag] call EDEN_fnc_containsProfanity) exitWith {
    ["Gang name or tag contains inappropriate content", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Check if gang name or tag already exists
private _query = format ["EDEN_Gangs:getGangByName:%1", _gangName];
private _existingGang = [_query, 2] call EDEN_fnc_asyncCall;

if (count _existingGang > 0) exitWith {
    ["A gang with this name already exists", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Check creation cost
private _creationCost = 50000; // $50,000 to create a gang
private _playerCash = _player getVariable ["EDEN_Cash", 0];
private _playerBank = _player getVariable ["EDEN_Bank", 0];
private _totalFunds = _playerCash + _playerBank;

if (_totalFunds < _creationCost) exitWith {
    [format["You need $%1 to create a gang", [_creationCost] call EDEN_fnc_formatMoney], "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Check player level requirement
private _playerLevel = _player getVariable ["EDEN_Level", 1];
private _requiredLevel = 10;
if (_playerLevel < _requiredLevel) exitWith {
    [format["You must be level %1 to create a gang", _requiredLevel], "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Deduct creation cost
if (_playerBank >= _creationCost) then {
    _player setVariable ["EDEN_Bank", _playerBank - _creationCost, false];
} else {
    private _bankAmount = _playerBank;
    private _cashAmount = _creationCost - _bankAmount;
    _player setVariable ["EDEN_Bank", 0, false];
    _player setVariable ["EDEN_Cash", _playerCash - _cashAmount, false];
};

// Create gang in database
private _leaderUID = getPlayerUID _player;
private _maxMembers = EDEN_MaxGangMembers;

private _insertQuery = format [
    "EDEN_Gangs:insertGang:%1:%2:%3:%4:%5:%6:%7:%8",
    _gangName,
    _gangTag,
    _leaderUID,
    0, // bank
    _maxMembers,
    _gangColor,
    _gangDescription,
    "" // rules
];

private _queryId = [_insertQuery, 1] call EDEN_fnc_asyncCall;

// Get the new gang ID (this would normally be returned from the database)
// For now, we'll simulate it
if (isNil "EDEN_NextGangID") then {
    EDEN_NextGangID = 1;
};
private _newGangId = EDEN_NextGangID;
EDEN_NextGangID = EDEN_NextGangID + 1;

// Set player as gang leader
_player setVariable ["EDEN_GangID", _newGangId, false];
_player setVariable ["EDEN_GangRank", 5, false]; // Highest rank (leader)
_player setVariable ["EDEN_GangName", _gangName, false];

// Add player to gang members table
private _memberQuery = format [
    "EDEN_Gangs:insertGangMember:%1:%2:%3:%4",
    _newGangId,
    _leaderUID,
    5, // rank
    str ["INVITE", "KICK", "PROMOTE", "DEMOTE", "BANK_WITHDRAW", "TERRITORY"] // permissions
];

[_memberQuery, 1] call EDEN_fnc_asyncCall;

// Log gang creation
[format["GANG: %1 created gang '%2' [%3] (ID: %4)", name _player, _gangName, _gangTag, _newGangId], "INFO", "GANGS"] call EDEN_fnc_systemLogger;

// Log transaction
[_leaderUID, "GANG_CREATION", -_creationCost, _totalFunds, _totalFunds - _creationCost, format["Created gang %1", _gangName]] call EDEN_fnc_logTransaction;

// Notify player
[format["Successfully created gang '%1' [%2]", _gangName, _gangTag], "success"] remoteExec ["EDEN_fnc_showNotification", _player];
["You are now the leader of your gang", "info"] remoteExec ["EDEN_fnc_showNotification", _player];

// Update client money display
[_player getVariable ["EDEN_Cash", 0], _player getVariable ["EDEN_Bank", 0]] remoteExec ["EDEN_fnc_updateMoneyDisplay", _player];

// Award achievement
[_player, "GANG_FOUNDER"] call EDEN_fnc_unlockAchievement;

// Award XP
[_player, 500, "GANG_CREATION"] call EDEN_fnc_awardExperience;

// Add to global gang list
if (isNil "EDEN_ActiveGangs") then {
    EDEN_ActiveGangs = [];
};

EDEN_ActiveGangs pushBack [
    _newGangId,
    _gangName,
    _gangTag,
    _leaderUID,
    name _player,
    0, // bank
    0, // experience
    1, // level
    0, // reputation
    1, // member count
    time, // created
    _gangColor
];

// Broadcast gang creation to all players
{
    [format["New gang '%1' [%2] has been formed by %3", _gangName, _gangTag, name _player], "info"] remoteExec ["EDEN_fnc_showNotification", _x];
} forEach allPlayers;

true
