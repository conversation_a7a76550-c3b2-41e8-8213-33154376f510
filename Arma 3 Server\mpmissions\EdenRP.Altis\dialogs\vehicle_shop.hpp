/*
    File: vehicle_shop.hpp
    Author: EdenRP Development Team
    
    Description:
    Vehicle shop dialog definitions for EdenRP
*/

class EdenRP_VehicleShop {
    idd = 3000;
    name = "EdenRP_VehicleShop";
    movingEnable = 1;
    enableSimulation = 1;
    
    class controlsBackground {
        class Background: RscText {
            idc = -1;
            x = 0.1;
            y = 0.1;
            w = 0.8;
            h = 0.8;
            colorBackground[] = {0, 0, 0, 0.8};
        };
        
        class Title: RscText {
            idc = -1;
            text = "Vehicle Shop";
            x = 0.1;
            y = 0.1;
            w = 0.8;
            h = 0.05;
            colorText[] = {1, 1, 1, 1};
            sizeEx = 0.04;
        };
    };
    
    class controls {
        class VehicleList: RscListBox {
            idc = 3001;
            x = 0.15;
            y = 0.2;
            w = 0.3;
            h = 0.6;
        };
        
        class VehiclePreview: RscText {
            idc = 3002;
            x = 0.5;
            y = 0.2;
            w = 0.35;
            h = 0.4;
            colorBackground[] = {0.1, 0.1, 0.1, 1};
        };
        
        class PriceText: RscText {
            idc = 3003;
            text = "Price: $0";
            x = 0.5;
            y = 0.65;
            w = 0.2;
            h = 0.05;
            colorText[] = {1, 1, 1, 1};
        };
        
        class BuyButton: RscButton {
            idc = 3004;
            text = "Buy Vehicle";
            x = 0.7;
            y = 0.65;
            w = 0.15;
            h = 0.05;
            action = "[] call EDEN_fnc_buyVehicle;";
        };
        
        class CloseButton: RscButton {
            idc = 3005;
            text = "Close";
            x = 0.8;
            y = 0.85;
            w = 0.08;
            h = 0.04;
            action = "closeDialog 0;";
        };
    };
};
