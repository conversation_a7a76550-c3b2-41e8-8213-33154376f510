/*
    File: phone.hpp
    Author: EdenRP Development Team
    
    Description:
    Phone dialog definitions for EdenRP
*/

class EdenRP_Phone {
    idd = 3400;
    name = "EdenRP_Phone";
    movingEnable = 1;
    enableSimulation = 1;
    
    class controlsBackground {
        class Background: RscText {
            idc = -1;
            x = 0.35;
            y = 0.25;
            w = 0.3;
            h = 0.5;
            colorBackground[] = {0, 0, 0, 0.9};
        };
        
        class Title: RscText {
            idc = -1;
            text = "Phone";
            x = 0.35;
            y = 0.25;
            w = 0.3;
            h = 0.05;
            colorText[] = {1, 1, 1, 1};
            sizeEx = 0.04;
        };
    };
    
    class controls {
        class ContactList: RscListBox {
            idc = 3401;
            x = 0.37;
            y = 0.32;
            w = 0.26;
            h = 0.3;
        };
        
        class CallButton: RscButton {
            idc = 3402;
            text = "Call";
            x = 0.37;
            y = 0.64;
            w = 0.08;
            h = 0.04;
            action = "[] call EDEN_fnc_makeCall;";
        };
        
        class TextButton: RscButton {
            idc = 3403;
            text = "Text";
            x = 0.47;
            y = 0.64;
            w = 0.08;
            h = 0.04;
            action = "[] call EDEN_fnc_sendText;";
        };
        
        class CloseButton: RscButton {
            idc = 3404;
            text = "Close";
            x = 0.57;
            y = 0.64;
            w = 0.06;
            h = 0.04;
            action = "closeDialog 0;";
        };
    };
};
