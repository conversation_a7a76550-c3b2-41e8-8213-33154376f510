/*
    EdenRP Perform Mining Function
    Enhanced mining with realistic mechanics and skill progression
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_location", [], [[]]],
    ["_data", [], [[]]]
];

// Check if player has required tools
if !([_player, "toolkit"] call EDEN_fnc_hasItem) exitWith {
    ["You need a toolkit to mine", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Check if player is at a valid mining location
private _miningLocation = [_location] call EDEN_fnc_getNearestMiningLocation;
if (count _miningLocation == 0) exitWith {
    ["You are not at a valid mining location", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Get location data
private _locationName = _miningLocation select 0;
private _oreTypes = _miningLocation select 1;
private _difficulty = _miningLocation select 2;
private _depletion = _miningLocation select 3;

// Check if location is depleted
if (_depletion >= 100) exitWith {
    ["This mining location is depleted", "warning"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Get player mining skill
private _skills = _player getVariable ["EDEN_Skills", []];
private _miningSkill = 0;
{
    if ((_x select 0) == "mining") exitWith {
        _miningSkill = _x select 1;
    };
} forEach _skills;

// Calculate mining time based on skill and difficulty
private _baseTime = 15; // Base mining time in seconds
private _skillModifier = 1 - (_miningSkill * 0.01); // 1% reduction per skill level
private _difficultyModifier = 1 + (_difficulty * 0.2); // 20% increase per difficulty level
private _miningTime = _baseTime * _skillModifier * _difficultyModifier;
_miningTime = [_miningTime, 5, 30] call EDEN_fnc_clampValue;

// Check if player is already mining
if (_player getVariable ["EDEN_IsMining", false]) exitWith {
    ["You are already mining", "warning"] remoteExec ["EDEN_fnc_showNotification", _player];
    false
};

// Start mining process
_player setVariable ["EDEN_IsMining", true, false];
[format["Mining at %1...", _locationName], "info"] remoteExec ["EDEN_fnc_showNotification", _player];

// Show progress bar
[_miningTime, "Mining ore"] remoteExec ["EDEN_fnc_showProgressBar", _player];

// Wait for mining time
sleep _miningTime;

// Check if player is still at location and alive
if (_player distance _location > 10 || !alive _player) exitWith {
    ["Mining interrupted", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    _player setVariable ["EDEN_IsMining", false, false];
    false
};

// Calculate success chance
private _baseSuccess = 0.7; // 70% base success rate
private _skillBonus = _miningSkill * 0.005; // 0.5% per skill level
private _difficultyPenalty = _difficulty * 0.1; // 10% penalty per difficulty level
private _successChance = _baseSuccess + _skillBonus - _difficultyPenalty;
_successChance = [_successChance, 0.1, 0.95] call EDEN_fnc_clampValue;

// Determine if mining was successful
private _success = (random 1) < _successChance;

if (_success) then {
    // Successful mining - determine ore type and quantity
    private _oreType = selectRandom _oreTypes;
    private _baseQuantity = 1;
    private _skillQuantityBonus = floor (_miningSkill / 20); // Extra ore every 20 skill levels
    private _quantity = _baseQuantity + _skillQuantityBonus + (if (random 1 < 0.1) then {1} else {0}); // 10% chance for bonus ore
    
    // Add ore to inventory
    if ([_player, _oreType, _quantity] call EDEN_fnc_addItemToInventory) then {
        private _oreConfig = [_oreType] call EDEN_fnc_getItemConfig;
        private _oreName = _oreConfig select 0;
        
        [format["Successfully mined %1x %2", _quantity, _oreName], "success"] remoteExec ["EDEN_fnc_showNotification", _player];
        
        // Award XP
        private _xpReward = _difficulty * 10 + _quantity * 5;
        [_player, _xpReward, "MINING"] call EDEN_fnc_awardExperience;
        
        // Increase mining skill
        [_player, "mining", 1] call EDEN_fnc_increaseSkill;
        
        // Update location depletion
        private _depletionIncrease = 1 + (_quantity * 0.5);
        [_location, _depletionIncrease] call EDEN_fnc_updateMiningDepletion;
        
        // Log mining activity
        [format["MINING: %1 mined %2x %3 at %4", name _player, _quantity, _oreName, _locationName], "INFO", "MINING"] call EDEN_fnc_systemLogger;
        
        // Update player statistics
        private _totalMined = _player getVariable ["EDEN_TotalMined", 0];
        _player setVariable ["EDEN_TotalMined", _totalMined + _quantity, false];
        
    } else {
        ["Your inventory is full", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    };
    
} else {
    // Failed mining
    ["Mining attempt failed - no ore found", "warning"] remoteExec ["EDEN_fnc_showNotification", _player];
    
    // Still award small XP for attempt
    [_player, 2, "MINING"] call EDEN_fnc_awardExperience;
    
    // Small chance to damage toolkit
    if (random 1 < 0.05) then {
        [_player, "toolkit", 1] call EDEN_fnc_removeItem;
        ["Your toolkit was damaged and lost", "error"] remoteExec ["EDEN_fnc_showNotification", _player];
    };
};

// Consume toolkit durability (if implemented)
// [_player, "toolkit", "durability", -1] call EDEN_fnc_modifyItemProperty;

// Reset mining status
_player setVariable ["EDEN_IsMining", false, false];

// Cooldown before next mining attempt
_player setVariable ["EDEN_MiningCooldown", time + 5, false];

_success
