-- Simple EdenRP Database Test
-- This test works with your existing database setup

-- Test 1: Basic connection
SELECT 'Connected to MySQL successfully!' AS Connection_Test;

-- Test 2: Use EdenRP database
USE edenrp;
SELECT 'Using edenrp database successfully!' AS Database_Test;

-- Test 3: Count all tables
SELECT COUNT(*) AS Total_Tables FROM information_schema.tables WHERE table_schema = 'edenrp';

-- Test 4: List all EdenRP tables
SELECT table_name AS EdenRP_Tables 
FROM information_schema.tables 
WHERE table_schema = 'edenrp' 
ORDER BY table_name;

-- Test 5: Check eden_players table
SELECT COUNT(*) AS Current_Players FROM eden_players;

-- Test 6: Check eden_vehicles table  
SELECT COUNT(*) AS Current_Vehicles FROM eden_vehicles;

-- Test 7: Check eden_gangs table
SELECT COUNT(*) AS Current_Gangs FROM eden_gangs;

-- Test 8: Show eden_players structure
DESCRIBE eden_players;

-- Test 9: Test a simple insert/select/delete
INSERT INTO eden_players (player_id, name, cash, bank) 
VALUES ('TEST123', 'DatabaseTest', 5000, 15000);

SELECT 'Test record inserted!' AS Insert_Test;
SELECT * FROM eden_players WHERE player_id = 'TEST123';

-- Clean up
DELETE FROM eden_players WHERE player_id = 'TEST123';
SELECT 'Test record cleaned up!' AS Cleanup_Test;

-- Final result
SELECT '🎉 EdenRP Database is working perfectly!' AS Final_Result;
