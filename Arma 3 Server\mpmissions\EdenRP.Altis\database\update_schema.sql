-- --------------------------------------------------------
-- EdenRP Database Schema Update Script
-- Updates existing database to support new subsystems
-- Version: 2.0.0
-- Date: 2025-07-29
-- --------------------------------------------------------

USE `edenrp`;

-- Add new columns to existing eden_players table
ALTER TABLE `eden_players` 
ADD COLUMN IF NOT EXISTS `skill_points` int(11) NOT NULL DEFAULT 5,
ADD COLUMN IF NOT EXISTS `prestige_level` int(11) NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS `prestige_points` int(11) NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS `monthly_goals` text DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `weekly_challenges` text DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `job_levels` text DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `perks` text DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `friends_list` text DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `blocked_players` text DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `notification_settings` text DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `ui_settings` text DEFAULT NULL;

-- Economy Subsystem Tables
CREATE TABLE IF NOT EXISTS `eden_stock_market` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `symbol` varchar(10) NOT NULL,
  `company_name` varchar(64) NOT NULL,
  `current_price` decimal(10,2) NOT NULL,
  `previous_price` decimal(10,2) NOT NULL,
  `volume` bigint(20) NOT NULL DEFAULT 0,
  `market_cap` bigint(20) NOT NULL DEFAULT 0,
  `last_update` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `symbol` (`symbol`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `eden_player_stocks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `symbol` varchar(10) NOT NULL,
  `shares` int(11) NOT NULL,
  `purchase_price` decimal(10,2) NOT NULL,
  `purchase_date` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `symbol` (`symbol`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `eden_loans` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `amount` bigint(20) NOT NULL,
  `interest_rate` decimal(5,2) NOT NULL,
  `remaining_amount` bigint(20) NOT NULL,
  `monthly_payment` int(11) NOT NULL,
  `next_payment` timestamp NOT NULL,
  `loan_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `status` varchar(16) NOT NULL DEFAULT 'active',
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `eden_businesses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `owner_id` varchar(50) NOT NULL,
  `name` varchar(64) NOT NULL,
  `type` varchar(32) NOT NULL,
  `position` varchar(128) NOT NULL,
  `income_rate` int(11) NOT NULL DEFAULT 0,
  `upgrade_level` tinyint(4) NOT NULL DEFAULT 1,
  `last_collection` timestamp NOT NULL DEFAULT current_timestamp(),
  `purchase_date` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `owner_id` (`owner_id`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Inventory Subsystem Tables
CREATE TABLE IF NOT EXISTS `eden_containers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `owner_id` varchar(50) NOT NULL,
  `type` varchar(32) NOT NULL,
  `position` varchar(128) NOT NULL,
  `inventory` text DEFAULT NULL,
  `locked` tinyint(1) NOT NULL DEFAULT 0,
  `access_code` varchar(16) DEFAULT NULL,
  `last_accessed` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `owner_id` (`owner_id`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `eden_auctions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `seller_id` varchar(50) NOT NULL,
  `item_name` varchar(64) NOT NULL,
  `quantity` int(11) NOT NULL,
  `starting_bid` int(11) NOT NULL,
  `current_bid` int(11) NOT NULL,
  `highest_bidder` varchar(50) DEFAULT NULL,
  `end_time` timestamp NOT NULL,
  `status` varchar(16) NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `seller_id` (`seller_id`),
  KEY `status` (`status`),
  KEY `end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Communication Subsystem Tables
CREATE TABLE IF NOT EXISTS `eden_emails` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `sender_id` varchar(50) NOT NULL,
  `recipient_id` varchar(50) NOT NULL,
  `subject` varchar(128) NOT NULL,
  `body` text NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `read_status` tinyint(1) NOT NULL DEFAULT 0,
  `priority` varchar(16) NOT NULL DEFAULT 'normal',
  PRIMARY KEY (`id`),
  KEY `sender_id` (`sender_id`),
  KEY `recipient_id` (`recipient_id`),
  KEY `read_status` (`read_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `eden_social_posts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `author_id` varchar(50) NOT NULL,
  `content` text NOT NULL,
  `likes` int(11) NOT NULL DEFAULT 0,
  `shares` int(11) NOT NULL DEFAULT 0,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `visibility` varchar(16) NOT NULL DEFAULT 'public',
  PRIMARY KEY (`id`),
  KEY `author_id` (`author_id`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `eden_news_articles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `author_id` varchar(50) NOT NULL,
  `title` varchar(128) NOT NULL,
  `content` text NOT NULL,
  `category` varchar(32) NOT NULL,
  `published` tinyint(1) NOT NULL DEFAULT 0,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `author_id` (`author_id`),
  KEY `category` (`category`),
  KEY `published` (`published`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Admin Subsystem Tables
CREATE TABLE IF NOT EXISTS `eden_reports` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `reporter_id` varchar(50) NOT NULL,
  `reported_id` varchar(50) NOT NULL,
  `reason` varchar(128) NOT NULL,
  `description` text NOT NULL,
  `status` varchar(16) NOT NULL DEFAULT 'open',
  `assigned_admin` varchar(50) DEFAULT NULL,
  `resolution` text DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `resolved_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `reporter_id` (`reporter_id`),
  KEY `reported_id` (`reported_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `eden_bans` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `admin_id` varchar(50) NOT NULL,
  `reason` varchar(256) NOT NULL,
  `duration` int(11) NOT NULL,
  `ban_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `active` (`active`),
  KEY `expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `eden_warnings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `admin_id` varchar(50) NOT NULL,
  `reason` varchar(256) NOT NULL,
  `warning_date` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `warning_date` (`warning_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Progression Subsystem Tables
CREATE TABLE IF NOT EXISTS `eden_achievements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `achievement_id` varchar(64) NOT NULL,
  `progress` int(11) NOT NULL DEFAULT 0,
  `completed` tinyint(1) NOT NULL DEFAULT 0,
  `completed_at` timestamp NULL DEFAULT NULL,
  `unlocked_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `achievement_id` (`achievement_id`),
  KEY `completed` (`completed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `eden_daily_tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `task_type` varchar(32) NOT NULL,
  `task_description` varchar(128) NOT NULL,
  `target_amount` int(11) NOT NULL,
  `current_progress` int(11) NOT NULL DEFAULT 0,
  `reward_amount` int(11) NOT NULL,
  `completed` tinyint(1) NOT NULL DEFAULT 0,
  `assigned_date` date NOT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `assigned_date` (`assigned_date`),
  KEY `completed` (`completed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `eden_leaderboards` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `category` varchar(32) NOT NULL,
  `score` bigint(20) NOT NULL,
  `rank_position` int(11) NOT NULL,
  `last_update` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `category` (`category`),
  KEY `rank_position` (`rank_position`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `eden_seasonal_events` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_name` varchar(64) NOT NULL,
  `description` text NOT NULL,
  `start_date` timestamp NOT NULL,
  `end_date` timestamp NOT NULL,
  `rewards` text NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  KEY `active` (`active`),
  KEY `start_date` (`start_date`),
  KEY `end_date` (`end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Security Subsystem Tables
CREATE TABLE IF NOT EXISTS `eden_security_incidents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `incident_id` varchar(32) NOT NULL,
  `incident_type` varchar(32) NOT NULL,
  `player_id` varchar(50) DEFAULT NULL,
  `severity` varchar(16) NOT NULL,
  `description` text NOT NULL,
  `evidence` text DEFAULT NULL,
  `status` varchar(16) NOT NULL DEFAULT 'open',
  `response_log` text DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `resolved_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `incident_id` (`incident_id`),
  KEY `player_id` (`player_id`),
  KEY `incident_type` (`incident_type`),
  KEY `severity` (`severity`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `eden_forensic_evidence` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `evidence_id` varchar(32) NOT NULL,
  `evidence_type` varchar(32) NOT NULL,
  `player_id` varchar(50) DEFAULT NULL,
  `location` varchar(128) DEFAULT NULL,
  `description` text NOT NULL,
  `forensic_data` text DEFAULT NULL,
  `collected_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `preserved` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `evidence_id` (`evidence_id`),
  KEY `player_id` (`player_id`),
  KEY `evidence_type` (`evidence_type`),
  KEY `collected_at` (`collected_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `eden_player_violations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `violation_type` varchar(32) NOT NULL,
  `severity` varchar(16) NOT NULL,
  `description` text NOT NULL,
  `auto_detected` tinyint(1) NOT NULL DEFAULT 1,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `violation_type` (`violation_type`),
  KEY `severity` (`severity`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `eden_risk_assessments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `risk_score` int(11) NOT NULL,
  `risk_level` varchar(16) NOT NULL,
  `risk_factors` text NOT NULL,
  `mitigation_actions` text DEFAULT NULL,
  `assessment_date` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `risk_level` (`risk_level`),
  KEY `assessment_date` (`assessment_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

SELECT 'Database schema update completed successfully!' AS Status;
