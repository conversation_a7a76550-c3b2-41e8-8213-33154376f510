/*
    File: fn_maintenanceSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages vehicle maintenance system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_vehicle", objNull, [obj<PERSON>ull]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_vehicleMaintenance", [], true];
        _player setVariable ["eden_mechanicSkill", 0, true];
        true
    };
    case "repairVehicle": {
        if (isNull _vehicle) exitWith { false };
        
        _damageLevel = damage _vehicle;
        if (_damageLevel < 0.1) exitWith {
            ["Vehicle doesn't need repairs"] call EDEN_fnc_showHint;
            false
        };
        
        _repairCost = floor(_damageLevel * 1000);
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _repairCost) exitWith {
            [format ["Not enough money! Need $%1", _repairCost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _repairCost), true];
        
        _vehicle setDamage 0;
        _vehicle setFuel 1;
        
        _maintenance = _player getVariable ["eden_vehicleMaintenance", []];
        _record = [typeOf _vehicle, "repair", _repairCost, time];
        _maintenance pushBack _record;
        _player setVariable ["eden_vehicleMaintenance", _maintenance, true];
        
        _skill = _player getVariable ["eden_mechanicSkill", 0];
        _player setVariable ["eden_mechanicSkill", (_skill + 5), true];
        
        [format ["Vehicle repaired for $%1", _repairCost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "changeOil": {
        if (isNull _vehicle) exitWith { false };
        
        _cost = 50;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _vehicle setVariable ["eden_oilChanged", time, true];
        
        _maintenance = _player getVariable ["eden_vehicleMaintenance", []];
        _record = [typeOf _vehicle, "oil_change", _cost, time];
        _maintenance pushBack _record;
        _player setVariable ["eden_vehicleMaintenance", _maintenance, true];
        
        [format ["Oil changed for $%1", _cost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
