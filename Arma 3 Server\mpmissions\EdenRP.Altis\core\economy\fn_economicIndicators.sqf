/*
    File: fn_economicIndicators.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages economic indicators and statistics system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_economicData") then {
            eden_economicData = [
                ["gdp", 1000000],
                ["unemployment", 5.2],
                ["interest_rate", 3.5],
                ["money_supply", 500000]
            ];
            publicVariable "eden_economicData";
        };
        true
    };
    case "updateGDP": {
        _totalWealth = 0;
        {
            _cash = _x getVariable ["eden_cash", 0];
            _bank = _x getVariable ["eden_bankAccount", 0];
            _totalWealth = _totalWealth + _cash + _bank;
        } forEach allPlayers;
        
        {
            if ((_x select 0) == "gdp") then {
                _x set [1, _totalWealth];
                eden_economicData set [_forEachIndex, _x];
            };
        } forEach eden_economicData;
        publicVariable "eden_economicData";
        
        [format ["GDP Updated: $%1", _totalWealth]] call EDEN_fnc_showHint;
        true
    };
    case "updateUnemployment": {
        _totalPlayers = count allPlayers;
        _employedPlayers = 0;
        
        {
            _job = _x getVariable ["eden_job", ""];
            if (_job != "") then { _employedPlayers = _employedPlayers + 1; };
        } forEach allPlayers;
        
        _unemploymentRate = if (_totalPlayers > 0) then {
            ((_totalPlayers - _employedPlayers) / _totalPlayers) * 100
        } else { 0 };
        
        {
            if ((_x select 0) == "unemployment") then {
                _x set [1, _unemploymentRate];
                eden_economicData set [_forEachIndex, _x];
            };
        } forEach eden_economicData;
        publicVariable "eden_economicData";
        
        [format ["Unemployment Rate: %1%%", _unemploymentRate]] call EDEN_fnc_showHint;
        true
    };
    case "adjustInterestRate": {
        params ["", "", ["_newRate", 3.5, [0]]];
        
        {
            if ((_x select 0) == "interest_rate") then {
                _x set [1, _newRate];
                eden_economicData set [_forEachIndex, _x];
            };
        } forEach eden_economicData;
        publicVariable "eden_economicData";
        
        [format ["Interest Rate adjusted to %1%%", _newRate]] call EDEN_fnc_showHint;
        true
    };
    case "printMoney": {
        params ["", "", ["_amount", 100000, [0]]];
        
        {
            if ((_x select 0) == "money_supply") then {
                _currentSupply = _x select 1;
                _x set [1, (_currentSupply + _amount)];
                eden_economicData set [_forEachIndex, _x];
            };
        } forEach eden_economicData;
        publicVariable "eden_economicData";
        
        // Increase inflation due to money printing
        eden_inflationRate = eden_inflationRate + 0.005;
        publicVariable "eden_inflationRate";
        
        [format ["Money supply increased by $%1", _amount]] call EDEN_fnc_showHint;
        true
    };
    case "getIndicators": {
        _gdp = 0;
        _unemployment = 0;
        _interestRate = 0;
        _moneySupply = 0;
        
        {
            switch (_x select 0) do {
                case "gdp": { _gdp = _x select 1; };
                case "unemployment": { _unemployment = _x select 1; };
                case "interest_rate": { _interestRate = _x select 1; };
                case "money_supply": { _moneySupply = _x select 1; };
            };
        } forEach eden_economicData;
        
        [format ["Economic Indicators:\nGDP: $%1\nUnemployment: %2%%\nInterest Rate: %3%%\nMoney Supply: $%4\nInflation: %5%%", 
            _gdp, _unemployment, _interestRate, _moneySupply, floor(eden_inflationRate * 100)]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
