/*
    File: fn_compassSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages compass and navigation system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_compassEnabled", true, true];
        _player setVariable ["eden_waypoints", [], true];
        true
    };
    case "setWaypoint": {
        params ["", "", ["_position", [0,0,0], [[]]], ["_name", "Waypoint", [""]]];
        
        _waypoints = _player getVariable ["eden_waypoints", []];
        _waypoints pushBack [_name, _position];
        _player setVariable ["eden_waypoints", _waypoints, true];
        
        [format ["Waypoint set: %1", _name]] call EDEN_fnc_showHint;
        true
    };
    case "navigateToWaypoint": {
        params ["", "", ["_waypointIndex", 0, [0]]];
        
        _waypoints = _player getVariable ["eden_waypoints", []];
        if (_waypointIndex >= count _waypoints) exitWith { false };
        
        _waypoint = _waypoints select _waypointIndex;
        _position = _waypoint select 1;
        _distance = _player distance _position;
        _direction = _player getDir _position;
        
        [format ["Navigate to %1: %2m at %3°", (_waypoint select 0), floor(_distance), floor(_direction)]] call EDEN_fnc_showHint;
        true
    };
    case "showCompass": {
        if (!(_player getVariable ["eden_compassEnabled", true])) exitWith { false };
        
        _direction = getDir _player;
        _compassText = format["Heading: %1°", floor(_direction)];
        
        [_compassText, 0.5, 0.9, 2, 0] spawn BIS_fnc_dynamicText;
        true
    };
    case "clearWaypoints": {
        _player setVariable ["eden_waypoints", [], true];
        ["All waypoints cleared"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
