/*
    File: fn_forensicAnalysis.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages forensic analysis system.
*/

params [["_action", "init", [""]]];

switch (_action) do {
    case "init": {
        if (isServer) then {
            if (isNil "eden_forensicData") then {
                eden_forensicData = [];
            };
            if (isNil "eden_evidenceChain") then {
                eden_evidenceChain = [];
            };
        };
        true
    };
    case "collectEvidence": {
        params ["", ["_player", objNull, [objNull]], ["_evidenceType", "", [""]], ["_location", [0,0,0], [[]]], ["_description", "", [""]]];
        
        if (!isServer) exitWith { false };
        
        _evidenceId = format["EVD_%1_%2", floor(time), floor(random 1000)];
        _timestamp = format["%1-%2-%3 %4:%5:%6", 
            date select 0, date select 1, date select 2,
            date select 3, date select 4, floor(date select 5)
        ];
        
        _evidence = [
            _evidenceId,
            _evidenceType,
            _timestamp,
            if (isNull _player) then { "SYSTEM" } else { getPlayerUID _player },
            if (isNull _player) then { "SYSTEM" } else { name _player },
            _location,
            _description,
            []
        ];
        
        // Collect additional forensic data based on type
        _forensicData = switch (_evidenceType) do {
            case "player_data": {
                if (!isNull _player) then {
                    [
                        ["cash", _player getVariable ["eden_cash", 0]],
                        ["bank", _player getVariable ["eden_bankAccount", 0]],
                        ["level", _player getVariable ["eden_level", 1]],
                        ["job", _player getVariable ["eden_job", "civilian"]],
                        ["inventory", _player getVariable ["eden_virtualItems", []]],
                        ["position", getPos _player],
                        ["vehicle", if (vehicle _player != _player) then { typeOf vehicle _player } else { "none" }]
                    ]
                } else { [] };
            };
            case "transaction_log": {
                _recentTransactions = [];
                if (!isNull _player) then {
                    _playerUID = getPlayerUID _player;
                    // Get recent audit entries for this player
                    _auditResults = ["searchAuditLog", _playerUID, "MONEY_TRANSFER", 3600] call EDEN_fnc_auditSystem;
                    _recentTransactions = _auditResults;
                };
                [["recent_transactions", _recentTransactions]]
            };
            case "behavior_pattern": {
                if (!isNull _player) then {
                    _playerUID = getPlayerUID _player;
                    _behaviorData = ["getPlayerBaseline", _playerUID] call EDEN_fnc_anomalyDetection;
                    _suspicionScore = ["getSuspicionScore", _playerUID] call EDEN_fnc_behaviorAnalysis;
                    [
                        ["behavior_baseline", _behaviorData],
                        ["suspicion_score", _suspicionScore]
                    ]
                } else { [] };
            };
            case "system_state": {
                [
                    ["server_time", time],
                    ["player_count", count allPlayers],
                    ["active_incidents", count eden_activeIncidents],
                    ["memory_usage", diag_fps]
                ]
            };
            default { [] };
        };
        
        _evidence set [7, _forensicData];
        eden_forensicData pushBack _evidence;
        
        // Add to evidence chain
        eden_evidenceChain pushBack [_evidenceId, time, "COLLECTED", if (isNull _player) then { "SYSTEM" } else { getPlayerUID _player }];
        
        [format ["[FORENSIC] Evidence collected: %1 (ID: %2)", _evidenceType, _evidenceId], "INFO", "FORENSIC"] call EDEN_fnc_systemLogger;
        
        _evidenceId
    };
    case "analyzeEvidence": {
        params ["", ["_evidenceId", "", [""]], ["_analyst", objNull, [objNull]]];
        
        if (!isServer) exitWith { [] };
        
        // Find evidence
        _evidence = [];
        {
            if ((_x select 0) == _evidenceId) then { _evidence = _x; };
        } forEach eden_forensicData;
        
        if (count _evidence == 0) exitWith { [] };
        
        _evidenceType = _evidence select 1;
        _forensicData = _evidence select 7;
        _analysis = [];
        
        switch (_evidenceType) do {
            case "player_data": {
                // Analyze player data for anomalies
                {
                    _key = _x select 0;
                    _value = _x select 1;
                    
                    switch (_key) do {
                        case "cash": {
                            if (_value > 100000) then {
                                _analysis pushBack ["HIGH_CASH", format["Unusually high cash amount: $%1", _value]];
                            };
                        };
                        case "level": {
                            if (_value > 20) then {
                                _analysis pushBack ["HIGH_LEVEL", format["High level player: %1", _value]];
                            };
                        };
                        case "inventory": {
                            if (count _value > 50) then {
                                _analysis pushBack ["LARGE_INVENTORY", format["Large inventory: %1 items", count _value]];
                            };
                        };
                    };
                } forEach _forensicData;
            };
            case "transaction_log": {
                // Analyze transaction patterns
                _transactions = (_forensicData select 0) select 1;
                if (count _transactions > 10) then {
                    _analysis pushBack ["HIGH_TRANSACTION_VOLUME", format["High transaction volume: %1 transactions", count _transactions]];
                };
            };
            case "behavior_pattern": {
                // Analyze behavior patterns
                _suspicionScore = ((_forensicData select 1) select 1);
                if (_suspicionScore > 30) then {
                    _analysis pushBack ["SUSPICIOUS_BEHAVIOR", format["High suspicion score: %1", _suspicionScore]];
                };
            };
        };
        
        // Update evidence chain
        eden_evidenceChain pushBack [_evidenceId, time, "ANALYZED", if (isNull _analyst) then { "SYSTEM" } else { getPlayerUID _analyst }];
        
        _analysis
    };
    case "generateForensicReport": {
        params ["", ["_admin", objNull, [objNull]], ["_playerUID", "", [""]], ["_timeframe", 86400, [0]]];
        
        if (isNull _admin || !(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _cutoffTime = time - _timeframe;
        _relevantEvidence = [];
        
        {
            _evidence = _x;
            _evidenceTime = _evidence select 2;
            _evidencePlayer = _evidence select 3;
            
            if (_evidencePlayer == _playerUID || _playerUID == "") then {
                _relevantEvidence pushBack _evidence;
            };
        } forEach eden_forensicData;
        
        _reportText = format["=== FORENSIC REPORT ===\nTarget: %1\nTimeframe: %2 hours\nEvidence Items: %3\n\n", 
            if (_playerUID == "") then { "ALL PLAYERS" } else { _playerUID },
            floor(_timeframe / 3600),
            count _relevantEvidence
        ];
        
        {
            _evidence = _x;
            _evidenceId = _evidence select 0;
            _evidenceType = _evidence select 1;
            _timestamp = _evidence select 2;
            _description = _evidence select 6;
            
            _reportText = _reportText + format["%1 | %2 | %3\n%4\n\n", 
                _evidenceId, _evidenceType, _timestamp, _description
            ];
        } forEach _relevantEvidence;
        
        [_reportText] remoteExec ["EDEN_fnc_showHint", _admin];
        true
    };
    case "preserveEvidence": {
        params ["", ["_evidenceId", "", [""]]];
        
        if (!isServer) exitWith { false };
        
        // Mark evidence as preserved (cannot be deleted)
        {
            if ((_x select 0) == _evidenceId) then {
                _x pushBack "PRESERVED";
            };
        } forEach eden_forensicData;
        
        eden_evidenceChain pushBack [_evidenceId, time, "PRESERVED", "SYSTEM"];
        true
    };
    case "getEvidenceChain": {
        params ["", ["_evidenceId", "", [""]]];
        
        if (!isServer) exitWith { [] };
        
        _chain = [];
        {
            if ((_x select 0) == _evidenceId) then {
                _chain pushBack _x;
            };
        } forEach eden_evidenceChain;
        
        _chain
    };
    case "searchEvidence": {
        params ["", ["_searchTerm", "", [""]], ["_evidenceType", "", [""]]];
        
        if (!isServer) exitWith { [] };
        
        _results = [];
        
        {
            _evidence = _x;
            _matchesSearch = if (_searchTerm == "") then { true } else { 
                (_searchTerm in (_evidence select 4)) || (_searchTerm in (_evidence select 6))
            };
            _matchesType = if (_evidenceType == "") then { true } else { 
                (_evidence select 1) == _evidenceType
            };
            
            if (_matchesSearch && _matchesType) then {
                _results pushBack _evidence;
            };
        } forEach eden_forensicData;
        
        _results
    };
    default { false };
};
