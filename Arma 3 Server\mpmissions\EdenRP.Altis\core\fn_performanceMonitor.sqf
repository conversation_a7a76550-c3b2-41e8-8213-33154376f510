/*
    File: fn_performanceMonitor.sqf
    Author: EdenRP Development Team
    
    Description:
    Monitors server and client performance, providing optimization and warnings.
    
    Parameters:
    None
    
    Returns:
    BOOLEAN - True if performance monitor was successfully started
    
    Example:
    [] call EDEN_fnc_performanceMonitor;
*/

// Initialize performance monitoring variables
if (isNil "eden_performanceData") then {
    eden_performanceData = createHashMap;
    eden_performanceData set ["fps", []];
    eden_performanceData set ["playerCount", []];
    eden_performanceData set ["vehicleCount", []];
    eden_performanceData set ["objectCount", []];
    eden_performanceData set ["memoryUsage", []];
    eden_performanceData set ["networkTraffic", []];
};

// Performance thresholds
eden_performanceThresholds = createHashMap;
eden_performanceThresholds set ["minFPS", 20];
eden_performanceThresholds set ["maxPlayers", 100];
eden_performanceThresholds set ["maxVehicles", 500];
eden_performanceThresholds set ["maxObjects", 10000];
eden_performanceThresholds set ["maxMemoryMB", 4096];

// Function to get current FPS
EDEN_fnc_getCurrentFPS = {
    diag_fps
};

// Function to get player count
EDEN_fnc_getPlayerCount = {
    count allPlayers
};

// Function to get vehicle count
EDEN_fnc_getVehicleCount = {
    count vehicles
};

// Function to get object count
EDEN_fnc_getObjectCount = {
    count allMissionObjects ""
};

// Function to get memory usage (approximation)
EDEN_fnc_getMemoryUsage = {
    _objects = count allMissionObjects "";
    _players = count allPlayers;
    _vehicles = count vehicles;
    
    // Rough calculation in MB
    _memoryMB = (_objects * 0.1) + (_players * 5) + (_vehicles * 2);
    _memoryMB
};

// Function to collect performance data
EDEN_fnc_collectPerformanceData = {
    _currentTime = time;
    
    // Collect current metrics
    _fps = [] call EDEN_fnc_getCurrentFPS;
    _playerCount = [] call EDEN_fnc_getPlayerCount;
    _vehicleCount = [] call EDEN_fnc_getVehicleCount;
    _objectCount = [] call EDEN_fnc_getObjectCount;
    _memoryUsage = [] call EDEN_fnc_getMemoryUsage;
    
    // Store data with timestamp
    _dataPoint = [_currentTime, _fps, _playerCount, _vehicleCount, _objectCount, _memoryUsage];
    
    // Add to performance arrays (keep last 100 entries)
    _fpsArray = eden_performanceData get "fps";
    _fpsArray pushBack [_currentTime, _fps];
    if (count _fpsArray > 100) then { _fpsArray deleteAt 0; };
    
    _playerArray = eden_performanceData get "playerCount";
    _playerArray pushBack [_currentTime, _playerCount];
    if (count _playerArray > 100) then { _playerArray deleteAt 0; };
    
    _vehicleArray = eden_performanceData get "vehicleCount";
    _vehicleArray pushBack [_currentTime, _vehicleCount];
    if (count _vehicleArray > 100) then { _vehicleArray deleteAt 0; };
    
    _objectArray = eden_performanceData get "objectCount";
    _objectArray pushBack [_currentTime, _objectCount];
    if (count _objectArray > 100) then { _objectArray deleteAt 0; };
    
    _memoryArray = eden_performanceData get "memoryUsage";
    _memoryArray pushBack [_currentTime, _memoryUsage];
    if (count _memoryArray > 100) then { _memoryArray deleteAt 0; };
    
    // Return current data point
    _dataPoint
};

// Function to check performance thresholds
EDEN_fnc_checkPerformanceThresholds = {
    params ["_dataPoint"];
    _dataPoint params ["_time", "_fps", "_playerCount", "_vehicleCount", "_objectCount", "_memoryUsage"];
    
    _warnings = [];
    
    // Check FPS
    if (_fps < (eden_performanceThresholds get "minFPS")) then {
        _warnings pushBack format ["Low FPS detected: %1 (threshold: %2)", _fps, eden_performanceThresholds get "minFPS"];
    };
    
    // Check player count
    if (_playerCount > (eden_performanceThresholds get "maxPlayers")) then {
        _warnings pushBack format ["High player count: %1 (max: %2)", _playerCount, eden_performanceThresholds get "maxPlayers"];
    };
    
    // Check vehicle count
    if (_vehicleCount > (eden_performanceThresholds get "maxVehicles")) then {
        _warnings pushBack format ["High vehicle count: %1 (max: %2)", _vehicleCount, eden_performanceThresholds get "maxVehicles"];
    };
    
    // Check object count
    if (_objectCount > (eden_performanceThresholds get "maxObjects")) then {
        _warnings pushBack format ["High object count: %1 (max: %2)", _objectCount, eden_performanceThresholds get "maxObjects"];
    };
    
    // Check memory usage
    if (_memoryUsage > (eden_performanceThresholds get "maxMemoryMB")) then {
        _warnings pushBack format ["High memory usage: %1MB (max: %2MB)", _memoryUsage, eden_performanceThresholds get "maxMemoryMB"];
    };
    
    _warnings
};

// Function to optimize performance
EDEN_fnc_optimizePerformance = {
    // Clean up dead bodies
    {
        if (!alive _x && _x isKindOf "Man") then {
            deleteVehicle _x;
        };
    } forEach allDeadMen;
    
    // Clean up destroyed vehicles
    {
        if (!alive _x && _x isKindOf "LandVehicle") then {
            deleteVehicle _x;
        };
    } forEach vehicles;
    
    // Clean up old objects
    {
        if (_x getVariable ["eden_cleanupTime", -1] > 0 && time > (_x getVariable ["eden_cleanupTime", -1])) then {
            deleteVehicle _x;
        };
    } forEach allMissionObjects "";
    
    // Log cleanup
    ["[EDEN] Performance optimization cleanup completed"] call EDEN_fnc_systemLogger;
};

// Function to get performance report
EDEN_fnc_getPerformanceReport = {
    _dataPoint = [] call EDEN_fnc_collectPerformanceData;
    _dataPoint params ["_time", "_fps", "_playerCount", "_vehicleCount", "_objectCount", "_memoryUsage"];
    
    _report = format [
        "=== EdenRP Performance Report ===\n" +
        "Time: %1\n" +
        "FPS: %2\n" +
        "Players: %3\n" +
        "Vehicles: %4\n" +
        "Objects: %5\n" +
        "Memory Usage: %6 MB\n" +
        "================================",
        [_time, "HH:MM:SS"] call BIS_fnc_timeToString,
        _fps,
        _playerCount,
        _vehicleCount,
        _objectCount,
        _memoryUsage
    ];
    
    _report
};

// Main performance monitoring loop
EDEN_fnc_startPerformanceMonitoring = {
    [] spawn {
        while {true} do {
            // Collect performance data
            _dataPoint = [] call EDEN_fnc_collectPerformanceData;
            
            // Check for performance issues
            _warnings = [_dataPoint] call EDEN_fnc_checkPerformanceThresholds;
            
            // Log warnings
            {
                [format ["[EDEN] Performance Warning: %1", _x]] call EDEN_fnc_systemLogger;
            } forEach _warnings;
            
            // Auto-optimize if severe performance issues
            _dataPoint params ["_time", "_fps", "_playerCount", "_vehicleCount", "_objectCount", "_memoryUsage"];
            if (_fps < 15 || _objectCount > 15000) then {
                ["[EDEN] Severe performance issues detected, running auto-optimization"] call EDEN_fnc_systemLogger;
                [] call EDEN_fnc_optimizePerformance;
            };
            
            // Wait before next check
            sleep 30; // Check every 30 seconds
        };
    };
};

// Function to display performance HUD for admins
EDEN_fnc_showPerformanceHUD = {
    if (player getVariable ["eden_isAdmin", false]) then {
        [] spawn {
            while {player getVariable ["eden_showPerformanceHUD", false]} do {
                _dataPoint = [] call EDEN_fnc_collectPerformanceData;
                _dataPoint params ["_time", "_fps", "_playerCount", "_vehicleCount", "_objectCount", "_memoryUsage"];
                
                _hudText = format [
                    "<t size='0.6' color='#FFFFFF' align='left'>" +
                    "FPS: %1<br/>" +
                    "Players: %2<br/>" +
                    "Vehicles: %3<br/>" +
                    "Objects: %4<br/>" +
                    "Memory: %5 MB" +
                    "</t>",
                    round _fps,
                    _playerCount,
                    _vehicleCount,
                    _objectCount,
                    round _memoryUsage
                ];
                
                [_hudText, 0.02, 0.02, 5, 0, 0, 789] spawn BIS_fnc_dynamicText;
                sleep 5;
            };
        };
    };
};

// Initialize performance monitoring
if (isServer) then {
    [] call EDEN_fnc_startPerformanceMonitoring;
    ["[EDEN] Performance monitoring started"] call EDEN_fnc_systemLogger;
};

// Log successful initialization
["[EDEN] Performance monitor initialized"] call EDEN_fnc_systemLogger;

// Return success
true
