/*
    EdenRP Revive Player Function
    Enhanced medical revive system with realistic mechanics
*/

params [
    ["_medic", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_patient", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_method", "defibrillator", [""]],
    ["_location", "field", [""]]
];

// Validate parameters
if (isNull _medic || isNull _patient) exitWith {
    ["Invalid medic or patient provided to reviveP<PERSON>", "ERROR", "MEDICAL"] call EDEN_fnc_systemLogger;
    false
};

// Check if patient is actually unconscious/dead
if (!(_patient getVariable ["EDEN_IsUnconscious", false]) && alive _patient) exitWith {
    ["Patient is not unconscious", "warning"] remoteExec ["EDEN_fnc_showNotification", _medic];
    false
};

// Check distance
if (_medic distance _patient > 3) exitWith {
    ["You must be closer to the patient to revive them", "error"] remoteExec ["EDEN_fnc_showNotification", _medic];
    false
};

// Check if medic has required equipment
private _hasEquipment = false;
private _requiredItem = "";
private _reviveTime = 10; // Base revive time in seconds
private _successChance = 0.8; // Base success chance

switch (_method) do {
    case "defibrillator": {
        _hasEquipment = [_medic, "defibrillator"] call EDEN_fnc_hasItem;
        _requiredItem = "Defibrillator";
        _reviveTime = 8;
        _successChance = 0.9;
    };
    case "cpr": {
        _hasEquipment = true; // CPR requires no equipment
        _requiredItem = "None";
        _reviveTime = 15;
        _successChance = 0.6;
    };
    case "medkit": {
        _hasEquipment = [_medic, "medkit"] call EDEN_fnc_hasItem;
        _requiredItem = "Medical Kit";
        _reviveTime = 12;
        _successChance = 0.75;
    };
    case "bloodbag": {
        _hasEquipment = [_medic, "bloodbag"] call EDEN_fnc_hasItem;
        _requiredItem = "Blood Bag";
        _reviveTime = 10;
        _successChance = 0.85;
    };
    default {
        _hasEquipment = [_medic, "medkit"] call EDEN_fnc_hasItem;
        _requiredItem = "Medical Kit";
        _reviveTime = 12;
        _successChance = 0.75;
    };
};

if (!_hasEquipment && _method != "cpr") exitWith {
    [format["You need a %1 to perform this revival method", _requiredItem], "error"] remoteExec ["EDEN_fnc_showNotification", _medic];
    false
};

// Check medic skill level
private _medicLevel = _medic getVariable ["EDEN_MedicLevel", 0];
private _skillBonus = _medicLevel * 0.05; // 5% bonus per level
_successChance = _successChance + _skillBonus;

// Location modifiers
switch (_location) do {
    case "hospital": {
        _successChance = _successChance + 0.2; // 20% bonus in hospital
        _reviveTime = _reviveTime * 0.8; // 20% faster
    };
    case "ambulance": {
        _successChance = _successChance + 0.1; // 10% bonus in ambulance
        _reviveTime = _reviveTime * 0.9; // 10% faster
    };
    case "field": {
        // No modifiers for field treatment
    };
    case "combat": {
        _successChance = _successChance - 0.2; // 20% penalty in combat
        _reviveTime = _reviveTime * 1.3; // 30% slower
    };
};

// Check how long patient has been unconscious
private _unconsciousTime = time - (_patient getVariable ["EDEN_UnconsciousTime", time]);
private _timeModifier = 1 - (_unconsciousTime / 600); // Reduce success by time (10 minutes = 0% bonus)
_timeModifier = [_timeModifier, 0.1, 1] call EDEN_fnc_clampValue;
_successChance = _successChance * _timeModifier;

// Clamp success chance
_successChance = [_successChance, 0.1, 0.95] call EDEN_fnc_clampValue;

// Start revive process
[format["Starting revival of %1 using %2...", name _patient, _method], "info"] remoteExec ["EDEN_fnc_showNotification", _medic];
[format["%1 is attempting to revive you", name _medic], "info"] remoteExec ["EDEN_fnc_showNotification", _patient];

// Show progress bar
[_reviveTime, format["Reviving %1", name _patient]] remoteExec ["EDEN_fnc_showProgressBar", _medic];

// Disable medic movement during revive
_medic setVariable ["EDEN_IsReviving", true, false];

// Wait for revive time
sleep _reviveTime;

// Check if medic is still close and alive
if (_medic distance _patient > 3 || !alive _medic) exitWith {
    ["Revival interrupted - medic moved away or was incapacitated", "error"] remoteExec ["EDEN_fnc_showNotification", _medic];
    _medic setVariable ["EDEN_IsReviving", false, false];
    false
};

// Check success
private _success = (random 1) < _successChance;

if (_success) then {
    // Successful revival
    _patient setVariable ["EDEN_IsUnconscious", false, true];
    _patient setVariable ["EDEN_IsDead", false, true];
    
    // Restore player to conscious state
    [_patient] call EDEN_fnc_restorePlayer;
    
    // Set health based on revival method
    private _healthLevel = switch (_method) do {
        case "defibrillator": {0.8};
        case "bloodbag": {0.9};
        case "medkit": {0.7};
        case "cpr": {0.5};
        default {0.6};
    };
    
    _patient setDamage (1 - _healthLevel);
    
    // Consume medical supplies
    if (_method != "cpr") then {
        [_medic, _requiredItem, 1] call EDEN_fnc_removeItem;
    };
    
    // Create medical record
    private _treatment = format["Revival using %1", _method];
    private _cost = [_method] call EDEN_fnc_getMedicalCost;
    [getPlayerUID _patient, getPlayerUID _medic, "REVIVAL", "", _treatment, _cost] call EDEN_fnc_createMedicalRecord;
    
    // Award XP to medic
    private _xpReward = switch (_method) do {
        case "defibrillator": {100};
        case "bloodbag": {120};
        case "medkit": {80};
        case "cpr": {150}; // Higher reward for skill-based method
        default {80};
    };
    [_medic, _xpReward, "MEDICAL_REVIVAL"] call EDEN_fnc_awardExperience;
    
    // Update medic statistics
    private _revivals = _medic getVariable ["EDEN_TotalRevivals", 0];
    _medic setVariable ["EDEN_TotalRevivals", _revivals + 1, false];
    
    // Notifications
    [format["Successfully revived %1", name _patient], "success"] remoteExec ["EDEN_fnc_showNotification", _medic];
    [format["You have been revived by %1", name _medic], "success"] remoteExec ["EDEN_fnc_showNotification", _patient];
    
    // Log successful revival
    [format["MEDICAL: %1 successfully revived %2 using %3 (Success rate: %4%)", 
        name _medic, name _patient, _method, round (_successChance * 100)], "INFO", "MEDICAL"] call EDEN_fnc_systemLogger;
    
} else {
    // Failed revival
    ["Revival attempt failed", "error"] remoteExec ["EDEN_fnc_showNotification", _medic];
    [format["%1's revival attempt failed", name _medic], "error"] remoteExec ["EDEN_fnc_showNotification", _patient];
    
    // Still consume some supplies on failure
    if (_method != "cpr" && random 1 < 0.3) then {
        [_medic, _requiredItem, 1] call EDEN_fnc_removeItem;
        ["Medical supplies were consumed in the failed attempt", "warning"] remoteExec ["EDEN_fnc_showNotification", _medic];
    };
    
    // Reduce patient's remaining time
    private _currentTime = _patient getVariable ["EDEN_BleedoutTime", 300];
    _patient setVariable ["EDEN_BleedoutTime", _currentTime - 30, false]; // Lose 30 seconds
    
    [format["MEDICAL: %1 failed to revive %2 using %3 (Success rate was: %4%)", 
        name _medic, name _patient, _method, round (_successChance * 100)], "INFO", "MEDICAL"] call EDEN_fnc_systemLogger;
};

// Re-enable medic
_medic setVariable ["EDEN_IsReviving", false, false];

_success
