/*
    File: fn_developmentSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages property development system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_propertyId", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_developmentProjects", [], true];
        _player setVariable ["eden_constructionSkill", 0, true];
        true
    };
    case "startDevelopment": {
        params ["", "", "", ["_projectType", "renovation", [""]]];
        
        _owned = _player getVariable ["eden_ownedProperties", []];
        if !(_propertyId in _owned) exitWith {
            ["You don't own this property!"] call EDEN_fnc_showHint;
            false
        };
        
        _costs = [
            ["renovation", 5000, 7200],    // 2 hours
            ["expansion", 15000, 14400],   // 4 hours
            ["commercial", 25000, 21600]   // 6 hours
        ];
        
        _projectData = [];
        {
            if ((_x select 0) == _projectType) then { _projectData = _x; };
        } forEach _costs;
        
        if (count _projectData == 0) exitWith {
            ["Invalid project type"] call EDEN_fnc_showHint;
            false
        };
        
        _cost = _projectData select 1;
        _duration = _projectData select 2;
        
        _cash = _player getVariable ["eden_cash", 0];
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _projects = _player getVariable ["eden_developmentProjects", []];
        _project = [_propertyId, _projectType, time, (time + _duration), "In Progress"];
        _projects pushBack _project;
        _player setVariable ["eden_developmentProjects", _projects, true];
        
        [format ["Development project started: %1 ($%2)", _projectType, _cost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "completeDevelopment": {
        _projects = _player getVariable ["eden_developmentProjects", []];
        _completed = false;
        
        {
            if (((_x select 0) == _propertyId) && (time >= (_x select 3)) && ((_x select 4) == "In Progress")) then {
                _x set [4, "Completed"];
                _projects set [_forEachIndex, _x];
                _completed = true;
                
                _skill = _player getVariable ["eden_constructionSkill", 0];
                _player setVariable ["eden_constructionSkill", (_skill + 10), true];
            };
        } forEach _projects;
        
        if (_completed) then {
            _player setVariable ["eden_developmentProjects", _projects, true];
            ["Development project completed!"] call EDEN_fnc_showHint;
            [_player] call EDEN_fnc_savePlayerData;
            true
        } else {
            ["No completed projects found"] call EDEN_fnc_showHint;
            false
        };
    };
    default { false };
};
