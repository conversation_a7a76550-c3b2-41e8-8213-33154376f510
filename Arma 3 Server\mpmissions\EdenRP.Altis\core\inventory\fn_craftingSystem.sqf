/*
    File: fn_craftingSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages crafting and item creation system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_recipe", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_craftingRecipes") then {
            eden_craftingRecipes = [
                ["rope", [["hemp", 3]], 1, 30],
                ["bandage", [["cloth", 2]], 1, 15],
                ["lockpick", [["metal_scrap", 1], ["wire", 1]], 1, 45],
                ["explosive", [["chemicals", 2], ["timer", 1]], 1, 120]
            ];
            publicVariable "eden_craftingRecipes";
        };
        _player setVariable ["eden_craftingLevel", 1, true];
        _player setVariable ["eden_craftingXP", 0, true];
        true
    };
    case "craftItem": {
        _recipeData = [];
        {
            if ((_x select 0) == _recipe) then { _recipeData = _x; };
        } forEach eden_craftingRecipes;
        
        if (count _recipeData == 0) exitWith {
            ["Recipe not found"] call EDEN_fnc_showHint;
            false
        };
        
        _requirements = _recipeData select 1;
        _output = _recipeData select 2;
        _craftTime = _recipeData select 3;
        
        _virtualItems = _player getVariable ["eden_virtualItems", []];
        _canCraft = true;
        
        {
            _reqItem = _x select 0;
            _reqQuantity = _x select 1;
            _hasEnough = false;
            
            {
                if ((_x select 0) == _reqItem && (_x select 1) >= _reqQuantity) then {
                    _hasEnough = true;
                };
            } forEach _virtualItems;
            
            if (!_hasEnough) then { _canCraft = false; };
        } forEach _requirements;
        
        if (!_canCraft) exitWith {
            ["Missing required materials"] call EDEN_fnc_showHint;
            false
        };
        
        ["Crafting started..."] call EDEN_fnc_showHint;
        
        [{
            params ["_player", "_recipe", "_requirements", "_output"];
            
            // Remove materials
            {
                _reqItem = _x select 0;
                _reqQuantity = _x select 1;
                [_player, "removeItem", _reqItem, _reqQuantity] call EDEN_fnc_itemSystem;
            } forEach _requirements;
            
            // Add crafted item
            [_player, "addItem", _recipe, _output] call EDEN_fnc_itemSystem;
            
            // Add XP
            _xp = _player getVariable ["eden_craftingXP", 0];
            _player setVariable ["eden_craftingXP", (_xp + 10), true];
            
            _level = _player getVariable ["eden_craftingLevel", 1];
            _requiredXP = _level * 100;
            
            if (_xp >= _requiredXP) then {
                _player setVariable ["eden_craftingLevel", (_level + 1), true];
                _player setVariable ["eden_craftingXP", 0, true];
                [format ["Crafting level up! Now level %1", (_level + 1)]] call EDEN_fnc_showHint;
            };
            
            [format ["Crafted %1x %2", _output, _recipe]] call EDEN_fnc_showHint;
            [_player] call EDEN_fnc_savePlayerData;
            
        }, [_player, _recipe, _requirements, _output], _craftTime] call CBA_fnc_waitAndExecute;
        
        true
    };
    case "learnRecipe": {
        params ["", "", "", ["_newRecipe", [], [[]]]];
        
        if (count _newRecipe != 4) exitWith {
            ["Invalid recipe format"] call EDEN_fnc_showHint;
            false
        };
        
        _level = _player getVariable ["eden_craftingLevel", 1];
        if (_level < 3) exitWith {
            ["Crafting level too low to learn new recipes"] call EDEN_fnc_showHint;
            false
        };
        
        eden_craftingRecipes pushBack _newRecipe;
        publicVariable "eden_craftingRecipes";
        
        [format ["Learned new recipe: %1", (_newRecipe select 0)]] call EDEN_fnc_showHint;
        true
    };
    case "massProduction": {
        params ["", "", "", ["_quantity", 1, [0]]];
        
        _level = _player getVariable ["eden_craftingLevel", 1];
        _maxBatch = _level * 2;
        
        if (_quantity > _maxBatch) exitWith {
            [format ["Maximum batch size: %1", _maxBatch]] call EDEN_fnc_showHint;
            false
        };
        
        for "_i" from 1 to _quantity do {
            [_player, "craftItem", _recipe] call EDEN_fnc_craftingSystem;
        };
        
        true
    };
    default { false };
};
