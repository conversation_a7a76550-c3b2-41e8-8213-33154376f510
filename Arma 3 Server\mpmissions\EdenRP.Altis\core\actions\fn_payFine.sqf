/*
    File: fn_payFine.sqf
    Author: EdenRP Development Team
    
    Description:
    Pays an outstanding fine or ticket.
    
    Parameters:
    0: STRING - Ticket ID to pay
    1: OBJECT - Player (optional, default: player)
    
    Returns:
    BOOLEAN - True if fine was paid successfully
*/

params [
    ["_ticketID", "", [""]],
    ["_player", player, [obj<PERSON><PERSON>]]
];

if (_ticketID == "" || isNull _player) exitWith { false };

// Get player's tickets
_playerTickets = _player getVariable ["eden_tickets", []];
_ticketIndex = -1;
_ticketData = [];

// Find the ticket
{
    if ((_x select 0) == _ticketID) then {
        _ticketIndex = _forEachIndex;
        _ticketData = _x;
    };
} forEach _playerTickets;

if (_ticketIndex == -1) exitWith {
    ["Ticket not found!"] call EDEN_fnc_showHint;
    false
};

_ticketData params ["_id", "_issueTime", "_officer", "_violator", "_violation", "_amount", "_paid", "_dueDate"];

if (_paid) exitWith {
    ["This ticket has already been paid!"] call EDEN_fnc_showHint;
    false
};

// Check if player has enough money
_playerCash = _player getVariable ["eden_cash", 0];
_playerBank = _player getVariable ["eden_bankAccount", 0];
_totalMoney = _playerCash + _playerBank;

if (_totalMoney < _amount) exitWith {
    [format ["You need $%1 to pay this fine, but only have $%2!", _amount, _totalMoney]] call EDEN_fnc_showHint;
    false
};

// Payment processing (prefer bank over cash)
if (_playerBank >= _amount) then {
    _player setVariable ["eden_bankAccount", (_playerBank - _amount), true];
} else {
    _remainingCost = _amount - _playerBank;
    _player setVariable ["eden_bankAccount", 0, true];
    _player setVariable ["eden_cash", (_playerCash - _remainingCost), true];
};

// Mark ticket as paid
(_playerTickets select _ticketIndex) set [6, true]; // Set paid status to true
_player setVariable ["eden_tickets", _playerTickets, true];

// Reduce wanted level if applicable
_wantedLevel = _player getVariable ["eden_wantedLevel", 0];
if (_wantedLevel > 0) then {
    _player setVariable ["eden_wantedLevel", (_wantedLevel - 1), true];
};

[format ["Fine paid: $%1 for %2 (Ticket ID: %3)", _amount, _violation, _ticketID]] call EDEN_fnc_showHint;

// Log payment
[format ["[EDEN] Player %1 paid fine $%2 for ticket %3", name _player, _amount, _ticketID], "INFO", "POLICE"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
