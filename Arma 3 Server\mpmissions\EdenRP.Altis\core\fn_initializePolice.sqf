/*
    File: fn_initializePolice.sqf
    Author: EdenRP Development Team
    
    Description:
    Initializes a player as a police officer with proper equipment, variables, and settings.
    
    Parameters:
    0: OBJECT - Player object
    1: STRING - Police rank (optional, default: "Officer")
    
    Returns:
    BOOLEAN - True if initialization was successful
    
    Example:
    [player] call EDEN_fnc_initializePolice;
    [player, "Sergeant"] call EDEN_fnc_initializePolice;
*/

params [
    ["_player", player, [obj<PERSON><PERSON>]],
    ["_rank", "Officer", [""]]
];

// Validate parameters
if (isNull _player) exitWith {
    ["[EDEN] fn_initializePolice: Invalid player object"] call EDEN_fnc_systemLogger;
    false
};

// Set player role and faction
_player setVariable ["eden_playerRole", "police", true];
_player setVariable ["eden_playerFaction", "police", true];
_player setVariable ["eden_policeRank", _rank, true];

// Set police-specific flags
_player setVariable ["eden_isPolice", true, true];
_player setVariable ["eden_isMedic", false, true];
_player setVariable ["eden_isAdmin", false, true];
_player setVariable ["eden_onDuty", true, true];

// Initialize police stats
_player setVariable ["eden_arrests", 0, true];
_player setVariable ["eden_ticketsIssued", 0, true];
_player setVariable ["eden_criminalsCaught", 0, true];
_player setVariable ["eden_serviceTime", 0, true];

// Initialize equipment access based on rank
_accessLevel = switch (toLower _rank) do {
    case "chief": { 5 };
    case "captain": { 4 };
    case "lieutenant": { 3 };
    case "sergeant": { 2 };
    default { 1 }; // Officer
};
_player setVariable ["eden_policeAccessLevel", _accessLevel, true];

// Remove all gear first
removeAllWeapons _player;
removeAllItems _player;
removeAllAssignedItems _player;
removeUniform _player;
removeVest _player;
removeBackpack _player;
removeHeadgear _player;
removeGoggles _player;

// Give police uniform based on rank
switch (toLower _rank) do {
    case "chief";
    case "captain": {
        _player forceAddUniform "U_B_GEN_Commander_F";
        _player addVest "V_TacVest_blk_POLICE";
        _player addHeadgear "H_Cap_police";
    };
    case "lieutenant";
    case "sergeant": {
        _player forceAddUniform "U_B_CombatUniform_mcam_tshirt";
        _player addVest "V_TacVest_blk_POLICE";
        _player addHeadgear "H_Cap_police";
    };
    default {
        _player forceAddUniform "U_B_CombatUniform_mcam";
        _player addVest "V_TacVest_blk_POLICE";
        _player addHeadgear "H_Cap_police";
    };
};

// Add basic equipment
_player addItem "ItemMap";
_player addItem "ItemCompass";
_player addItem "ItemWatch";
_player addItem "ItemRadio";
_player addItem "ItemGPS";

// Assign items
_player assignItem "ItemMap";
_player assignItem "ItemCompass";
_player assignItem "ItemWatch";
_player assignItem "ItemRadio";
_player assignItem "ItemGPS";

// Add police-specific equipment based on access level
if (_accessLevel >= 1) then {
    _player addWeapon "hgun_P07_F";
    _player addMagazine "16Rnd_9x21_Mag";
    _player addMagazine "16Rnd_9x21_Mag";
    _player addItem "FirstAidKit";
    _player addItem "ToolKit";
};

if (_accessLevel >= 2) then {
    _player addWeapon "SMG_02_F";
    _player addMagazine "30Rnd_9x21_Mag_SMG_02";
    _player addMagazine "30Rnd_9x21_Mag_SMG_02";
    _player addItem "HandGrenade_Stone";
};

if (_accessLevel >= 3) then {
    _player addWeapon "arifle_MX_F";
    _player addMagazine "30Rnd_65x39_caseless_mag";
    _player addMagazine "30Rnd_65x39_caseless_mag";
    _player addItem "SmokeShell";
};

if (_accessLevel >= 4) then {
    _player addWeapon "srifle_EBR_F";
    _player addMagazine "20Rnd_762x51_Mag";
    _player addMagazine "20Rnd_762x51_Mag";
};

// Initialize police inventory
_player setVariable ["eden_policeEquipment", [
    ["handcuffs", 5],
    ["spike_strip", 2],
    ["evidence_bag", 10],
    ["breathalyzer", 1],
    ["radar_gun", 1]
], true];

// Set police permissions
_player setVariable ["eden_canArrest", true, true];
_player setVariable ["eden_canSearch", true, true];
_player setVariable ["eden_canSeize", true, true];
_player setVariable ["eden_canTicket", true, true];
_player setVariable ["eden_canImpound", true, true];

// Initialize patrol variables
_player setVariable ["eden_assignedPatrol", "", true];
_player setVariable ["eden_patrolPartner", objNull, true];
_player setVariable ["eden_currentCall", "", true];

// Set captive status (police are not captive)
_player setCaptive false;

// Initialize police actions
[_player] call EDEN_fnc_setupPlayerActions;

// Setup police-specific event handlers
_player addEventHandler ["Killed", {
    params ["_unit", "_killer"];
    [_unit, _killer] call EDEN_fnc_onPoliceKilled;
}];

_player addEventHandler ["Respawn", {
    params ["_unit", "_corpse"];
    [_unit, (_unit getVariable ["eden_policeRank", "Officer"])] call EDEN_fnc_initializePolice;
}];

// Setup police HUD
[] call EDEN_fnc_setupPoliceHUD;

// Create police briefing
[_player, "police"] call EDEN_fnc_briefingSystem;

// Add to police group
_policeGroup = createGroup west;
[_player] joinSilent _policeGroup;
_player setVariable ["eden_policeGroup", _policeGroup, true];

// Sync player data
[_player] call EDEN_fnc_savePlayerData;

// Welcome message
[
    format ["Welcome Officer %1!", name _player],
    format ["You are now on duty as a %1. Protect and serve!", _rank],
    5
] call EDEN_fnc_showNotification;

// Log successful initialization
[format ["[EDEN] Player %1 initialized as police officer (Rank: %2)", name _player, _rank]] call EDEN_fnc_systemLogger;

// Return success
true
