/*
    File: fn_socialMediaSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages social media system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_socialPosts") then {
            eden_socialPosts = [];
            publicVariable "eden_socialPosts";
        };
        _player setVariable ["eden_followers", [], true];
        _player setVariable ["eden_following", [], true];
        true
    };
    case "createPost": {
        params ["", "", ["_content", "", [""]]];
        
        if (_content == "") exitWith { false };
        
        _post = [getPlayerUID _player, name _player, _content, time, []];
        eden_socialPosts pushBack _post;
        publicVariable "eden_socialPosts";
        
        ["Post created"] call EDEN_fnc_showHint;
        true
    };
    case "likePost": {
        params ["", "", ["_postIndex", 0, [0]]];
        
        if (_postIndex >= count eden_socialPosts) exitWith { false };
        
        _post = eden_socialPosts select _postIndex;
        _likes = _post select 4;
        _likes pushBack getPlayerUID _player;
        _post set [4, _likes];
        eden_socialPosts set [_postIndex, _post];
        publicVariable "eden_socialPosts";
        
        ["Post liked"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
