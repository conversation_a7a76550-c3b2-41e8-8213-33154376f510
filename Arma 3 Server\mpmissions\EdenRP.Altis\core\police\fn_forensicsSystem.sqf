/*
    File: fn_forensicsSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages forensics analysis system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_forensicsLevel", 1, true];
        _player setVariable ["eden_analysisCompleted", 0, true];
        true
    };
    case "analyzeFingerprints": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty!"] call EDEN_fnc_showHint;
            false
        };
        
        _analysis = _player getVariable ["eden_analysisCompleted", 0];
        _player setVariable ["eden_analysisCompleted", (_analysis + 1), true];
        
        ["Fingerprint analysis complete!"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "analyzeDNA": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty!"] call EDEN_fnc_showHint;
            false
        };
        
        _analysis = _player getVariable ["eden_analysisCompleted", 0];
        _player setVariable ["eden_analysisCompleted", (_analysis + 1), true];
        
        ["DNA analysis complete!"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "ballistics": {
        ["Ballistics analysis complete!"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
