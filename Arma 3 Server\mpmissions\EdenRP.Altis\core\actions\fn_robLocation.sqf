/*
    File: fn_robLocation.sqf
    Author: EdenRP Development Team
    
    Description:
    Robs a location like a store or gas station.
    
    Parameters:
    0: OBJECT - Location object to rob
    1: OBJECT - <PERSON><PERSON> (optional, default: player)
    
    Returns:
    BOOLEAN - True if robbery was successful
*/

params [
    ["_location", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_robber", player, [obj<PERSON><PERSON>]]
];

if (isNull _location || isNull _robber) exitWith { false };

if (_robber distance _location > 10) exitWith {
    ["You must be closer to rob this location!"] call EDEN_fnc_showHint;
    false
};

// Check if robber has weapon
_robberWeapons = weapons _robber;
if (count _robberWeapons == 0) exitWith {
    ["You need a weapon to rob this location!"] call EDEN_fnc_showHint;
    false
};

// Check if location was recently robbed
_lastRobbed = _location getVariable ["eden_lastRobbed", 0];
if ((time - _lastRobbed) < 1800) exitWith { // 30 minute cooldown
    ["This location was recently robbed! Try again later."] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_robber, "criminal_action", [_location, "location_robbery"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Start robbery process
[_robber, "Acts_carFixingWheel"] remoteExec ["switchMove"];
["Robbing location..."] call EDEN_fnc_showHint;

sleep 8; // Robbery time

[_robber, ""] remoteExec ["switchMove"];

// Calculate stolen money based on location type
_locationType = typeOf _location;
_stolenMoney = switch (true) do {
    case (_locationType in ["Land_fs_feed_F", "Land_FuelStation_Feed_F"]): { 2000 + random 1500 }; // Gas station
    case (_locationType in ["Land_Shop_City_01_F", "Land_Shop_Town_01_F"]): { 1500 + random 1000 }; // Store
    case (_locationType in ["Land_Offices_01_V1_F"]): { 5000 + random 3000 }; // Office/Bank
    default { 500 + random 500 }; // Generic location
};

_stolenMoney = round _stolenMoney;

// Pay the robber
_robberMoney = _robber getVariable ["eden_cash", 0];
_robber setVariable ["eden_cash", (_robberMoney + _stolenMoney), true];

// Mark location as robbed
_location setVariable ["eden_lastRobbed", time, true];

// Add criminal activity
_wantedLevel = _robber getVariable ["eden_wantedLevel", 0];
_robber setVariable ["eden_wantedLevel", (_wantedLevel + 2), true];

// Add to criminal record
_criminalRecord = _robber getVariable ["eden_criminalRecord", []];
_crimeRecord = [
    time,
    "System",
    "Armed robbery of location",
    _locationType
];
_criminalRecord pushBack _crimeRecord;
_robber setVariable ["eden_criminalRecord", _criminalRecord, true];

// Add bounty
_bounty = _robber getVariable ["eden_bounty", 0];
_robber setVariable ["eden_bounty", (_bounty + 1500), true];

// Alert nearby police
{
    if (_x getVariable ["eden_isPolice", false] && _x distance _robber < 750) then {
        [
            "Location Robbery Alert",
            format ["Armed robbery at %1 - respond immediately!", mapGridPosition _robber],
            12,
            "error"
        ] remoteExec ["EDEN_fnc_showNotification", _x];
    };
} forEach allPlayers;

[format ["Location robbery successful! Stole $%1", _stolenMoney]] call EDEN_fnc_showHint;

// Log the crime
[format ["[EDEN] Player %1 robbed location %2 for $%3", name _robber, _locationType, _stolenMoney], "WARN", "CRIME"] call EDEN_fnc_systemLogger;

[_robber] call EDEN_fnc_savePlayerData;

true
