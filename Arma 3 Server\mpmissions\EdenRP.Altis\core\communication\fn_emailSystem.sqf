/*
    File: fn_emailSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages email system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_emails", [], true];
        _player setVariable ["eden_emailAddress", format["%<EMAIL>", name _player], true];
        true
    };
    case "sendEmail": {
        params ["", "", ["_recipient", "", [""]], ["_subject", "", [""]], ["_body", "", [""]]];
        
        _targetPlayer = objNull;
        {
            _emailAddr = _x getVariable ["eden_emailAddress", ""];
            if (_emailAddr == _recipient) then { _targetPlayer = _x; };
        } forEach allPlayers;
        
        if (isNull _targetPlayer) exitWith {
            ["Email address not found"] call EDEN_fnc_showHint;
            false
        };
        
        _emails = _targetPlayer getVariable ["eden_emails", []];
        _email = [_player getVariable ["eden_emailAddress", ""], _subject, _body, time, false];
        _emails pushBack _email;
        _targetPlayer setVariable ["eden_emails", _emails, true];
        
        ["Email sent"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
