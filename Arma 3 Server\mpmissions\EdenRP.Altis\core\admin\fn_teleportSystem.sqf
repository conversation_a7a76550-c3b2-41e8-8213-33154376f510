/*
    File: fn_teleportSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages admin teleportation system.
*/

params [["_admin", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON><PERSON>]]];

if (isNull _admin) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_teleportLocations") then {
            eden_teleportLocations = [
                ["Kavala", [3560, 13200, 0]],
                ["Athira", [14000, 18700, 0]],
                ["Pyrgos", [16800, 12600, 0]],
                ["Sofia", [25900, 21400, 0]],
                ["Airport", [14900, 16700, 0]]
            ];
            publicVariable "eden_teleportLocations";
        };
        true
    };
    case "teleportToLocation": {
        params ["", "", "", ["_locationName", "Kavala", [""]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _position = [0,0,0];
        {
            if ((_x select 0) == _locationName) then { _position = _x select 1; };
        } forEach eden_teleportLocations;
        
        if (_position isEqualTo [0,0,0]) exitWith {
            ["Location not found"] call EDEN_fnc_showHint;
            false
        };
        
        _admin setPos _position;
        [format ["Teleported to %1", _locationName]] call EDEN_fnc_showHint;
        true
    };
    case "teleportToCoords": {
        params ["", "", "", ["_coordinates", [0,0,0], [[]]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        _admin setPos _coordinates;
        [format ["Teleported to coordinates %1", _coordinates]] call EDEN_fnc_showHint;
        true
    };
    case "teleportPlayerToLocation": {
        params ["", "", "", ["_locationName", "Kavala", [""]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        if (isNull _target) exitWith { false };
        
        _position = [0,0,0];
        {
            if ((_x select 0) == _locationName) then { _position = _x select 1; };
        } forEach eden_teleportLocations;
        
        _target setPos _position;
        [format ["Teleported %1 to %2", name _target, _locationName]] call EDEN_fnc_showHint;
        [format ["You have been teleported to %1 by an admin", _locationName]] remoteExec ["EDEN_fnc_showHint", _target];
        true
    };
    case "addLocation": {
        params ["", "", "", ["_name", "", [""]], ["_position", [0,0,0], [[]]]];
        
        if (!(getPlayerUID _admin in eden_adminList)) exitWith { false };
        
        eden_teleportLocations pushBack [_name, _position];
        publicVariable "eden_teleportLocations";
        
        [format ["Added teleport location: %1", _name]] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
