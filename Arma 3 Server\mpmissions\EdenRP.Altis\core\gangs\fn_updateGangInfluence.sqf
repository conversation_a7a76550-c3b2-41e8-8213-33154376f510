/*
    EdenRP Gang Influence Update System
    Author: EdenRP Development Team
    Description: Updates gang influence based on reputation and activities
*/

params [["_gangId", "", [""]]];

if (_gangId == "") exitWith { 
    ["[EDEN] Invalid gang ID for influence update", "ERROR", "GANG"] call EDEN_fnc_systemLogger;
    false 
};

// Get current gang reputation
private _reputation = missionNamespace getVariable [format ["eden_gang_reputation_%1", _gangId], 0];

// Calculate base influence from reputation
private _baseInfluence = (_reputation / 100) min 50; // Max 50% base influence

// Define territory zones
private _territories = [
    ["kavala", [16019, 12722], 1000],      // Kavala city center
    ["athira", [14110, 18651], 800],       // Athira town
    ["pyrgos", [18525, 14306], 800],       // Pyrgos town
    ["sofia", [25665, 21392], 600],        // Sofia village
    ["neochori", [21398, 15835], 500],     // Neochori village
    ["zaros", [26415, 24614], 500],        // Zaros village
    ["drug_processor", [17470, 12870], 300], // Drug processing area
    ["arms_dealer", [16757, 12444], 300],  // Arms dealing area
    ["chop_shop", [16430, 16809], 400],    // Vehicle chop shop
    ["black_market", [14717, 16835], 350]  // Black market area
];

// Update influence in each territory
{
    private _territoryName = _x select 0;
    private _territoryPos = _x select 1;
    private _territoryRadius = _x select 2;
    
    // Get current influence
    private _currentInfluence = missionNamespace getVariable [format ["eden_gang_influence_%1_%2", _gangId, _territoryName], 0];
    
    // Calculate new influence based on various factors
    private _newInfluence = _currentInfluence;
    
    // Factor 1: Base reputation influence
    _newInfluence = _newInfluence + (_baseInfluence / 20); // Slow reputation-based growth
    
    // Factor 2: Gang member presence in territory
    private _gangMembers = missionNamespace getVariable [format ["eden_gang_members_%1", _gangId], []];
    private _membersInTerritory = 0;
    {
        if (alive _x && isPlayer _x) then {
            private _distance = (getPos _x) distance2D _territoryPos;
            if (_distance <= _territoryRadius) then {
                _membersInTerritory = _membersInTerritory + 1;
            };
        };
    } forEach _gangMembers;
    
    // Add influence for member presence (max +5% per update)
    if (_membersInTerritory > 0) then {
        _newInfluence = _newInfluence + (_membersInTerritory * 0.5) min 5;
    };
    
    // Factor 3: Recent gang activities in territory
    private _recentActivities = missionNamespace getVariable [format ["eden_gang_activities_%1_%2", _gangId, _territoryName], 0];
    if (_recentActivities > 0) then {
        _newInfluence = _newInfluence + (_recentActivities * 0.1);
        // Decay recent activities
        missionNamespace setVariable [format ["eden_gang_activities_%1_%2", _gangId, _territoryName], (_recentActivities * 0.9), true];
    };
    
    // Factor 4: Competing gang influence (reduces influence)
    private _competingInfluence = 0;
    private _allGangs = missionNamespace getVariable ["eden_active_gangs", []];
    {
        if (_x != _gangId) then {
            private _otherInfluence = missionNamespace getVariable [format ["eden_gang_influence_%1_%2", _x, _territoryName], 0];
            _competingInfluence = _competingInfluence + _otherInfluence;
        };
    } forEach _allGangs;
    
    // Reduce influence based on competition
    if (_competingInfluence > 50) then {
        _newInfluence = _newInfluence - ((_competingInfluence - 50) / 10);
    };
    
    // Factor 5: Natural decay over time
    _newInfluence = _newInfluence - 0.1; // Small natural decay
    
    // Cap influence between 0 and 100
    _newInfluence = (_newInfluence max 0) min 100;
    
    // Update influence if changed significantly
    if (abs(_newInfluence - _currentInfluence) > 0.1) then {
        missionNamespace setVariable [format ["eden_gang_influence_%1_%2", _gangId, _territoryName], _newInfluence, true];
        
        // Log significant changes
        if (abs(_newInfluence - _currentInfluence) > 5) then {
            [format ["[EDEN] Gang %1 influence in %2 changed from %3% to %4%", _gangId, _territoryName, round _currentInfluence, round _newInfluence], "INFO", "GANG"] call EDEN_fnc_systemLogger;
        };
    };
    
} forEach _territories;

// Calculate total gang influence score
private _totalInfluence = 0;
{
    private _territoryName = _x select 0;
    private _influence = missionNamespace getVariable [format ["eden_gang_influence_%1_%2", _gangId, _territoryName], 0];
    _totalInfluence = _totalInfluence + _influence;
} forEach _territories;

// Store total influence
missionNamespace setVariable [format ["eden_gang_total_influence_%1", _gangId], _totalInfluence, true];

// Update gang rank based on total influence
private _gangRank = switch (true) do {
    case (_totalInfluence >= 500): { "Dominant" };
    case (_totalInfluence >= 300): { "Powerful" };
    case (_totalInfluence >= 150): { "Established" };
    case (_totalInfluence >= 75): { "Growing" };
    case (_totalInfluence >= 25): { "Minor" };
    default { "Insignificant" };
};

missionNamespace setVariable [format ["eden_gang_rank_%1", _gangId], _gangRank, true];

// Check for territory control milestones
{
    private _territoryName = _x select 0;
    private _influence = missionNamespace getVariable [format ["eden_gang_influence_%1_%2", _gangId, _territoryName], 0];
    
    // Check if gang gained significant control
    if (_influence >= 75) then {
        private _wasControlling = missionNamespace getVariable [format ["eden_gang_controlling_%1_%2", _gangId, _territoryName], false];
        if (!_wasControlling) then {
            missionNamespace setVariable [format ["eden_gang_controlling_%1_%2", _gangId, _territoryName], true, true];
            
            // Notify gang members of territory control
            private _gangMembers = missionNamespace getVariable [format ["eden_gang_members_%1", _gangId], []];
            {
                if (isPlayer _x) then {
                    [format ["Your gang now controls %1! (+25 reputation)", _territoryName]] call EDEN_fnc_showNotification;
                };
            } forEach _gangMembers;
            
            // Award reputation for territory control
            ["add", _gangId, 25, format ["Territory control: %1", _territoryName]] call EDEN_fnc_reputationSystem;
        };
    } else {
        // Check if gang lost control
        private _wasControlling = missionNamespace getVariable [format ["eden_gang_controlling_%1_%2", _gangId, _territoryName], false];
        if (_wasControlling && _influence < 50) then {
            missionNamespace setVariable [format ["eden_gang_controlling_%1_%2", _gangId, _territoryName], false, true];
            
            // Notify gang members of territory loss
            private _gangMembers = missionNamespace getVariable [format ["eden_gang_members_%1", _gangId], []];
            {
                if (isPlayer _x) then {
                    [format ["Your gang lost control of %1! (-15 reputation)", _territoryName]] call EDEN_fnc_showNotification;
                };
            } forEach _gangMembers;
            
            // Apply reputation penalty for territory loss
            ["remove", _gangId, 15, format ["Territory lost: %1", _territoryName]] call EDEN_fnc_reputationSystem;
        };
    };
} forEach _territories;

[format ["[EDEN] Updated influence for gang %1 - Total: %2, Rank: %3", _gangId, round _totalInfluence, _gangRank], "INFO", "GANG"] call EDEN_fnc_systemLogger;

true
