/*
    File: fn_investigationSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages criminal investigation system for police.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_caseId", "", [""]], ["_suspect", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_activeCases") then {
            eden_activeCases = [];
            publicVariable "eden_activeCases";
        };
        _player setVariable ["eden_casesInvestigated", 0, true];
        _player setVariable ["eden_casesSolved", 0, true];
        true
    };
    case "openCase": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty to open a case!"] call EDEN_fnc_showHint;
            false
        };
        
        if (!(playerSide == west)) exitWith {
            ["Only police can open cases!"] call EDEN_fnc_showHint;
            false
        };
        
        params ["", "", "", "", ["_crimeType", "", [""]], ["_description", "", [""]]];
        
        _caseId = format ["CASE_%1_%2", floor(random 10000), floor(time)];
        _openTime = time;
        _location = getPos _player;
        _nearestCity = [_location] call EDEN_fnc_getNearestCity;
        
        _case = [
            _caseId,
            _crimeType,
            _description,
            name _player,
            _openTime,
            _location,
            _nearestCity,
            "Open",
            [],
            [],
            ""
        ];
        
        eden_activeCases pushBack _case;
        publicVariable "eden_activeCases";
        
        _investigated = _player getVariable ["eden_casesInvestigated", 0];
        _player setVariable ["eden_casesInvestigated", (_investigated + 1), true];
        
        [format ["Case opened: %1 (ID: %2)", _crimeType, _caseId]] call EDEN_fnc_showHint;
        [format ["[INVESTIGATION] %1 opened case %2 for %3", name _player, _caseId, _crimeType], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "addSuspect": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty to add suspects!"] call EDEN_fnc_showHint;
            false
        };
        
        if (isNull _suspect) exitWith {
            ["No suspect specified!"] call EDEN_fnc_showHint;
            false
        };
        
        _caseFound = false;
        _caseIndex = -1;
        
        {
            if ((_x select 0) == _caseId) then {
                _caseFound = true;
                _caseIndex = _forEachIndex;
            };
        } forEach eden_activeCases;
        
        if (!_caseFound) exitWith {
            ["Case not found!"] call EDEN_fnc_showHint;
            false
        };
        
        _case = eden_activeCases select _caseIndex;
        _suspects = _case select 8;
        _suspectData = [name _suspect, getPlayerUID _suspect, time];
        
        _suspects pushBack _suspectData;
        _case set [8, _suspects];
        eden_activeCases set [_caseIndex, _case];
        publicVariable "eden_activeCases";
        
        [format ["Added %1 as suspect to case %2", name _suspect, _caseId]] call EDEN_fnc_showHint;
        [format ["[INVESTIGATION] %1 added %2 as suspect to case %3", name _player, name _suspect, _caseId], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
        true
    };
    case "addEvidence": {
        params ["", "", "", "", ["_evidenceId", "", [""]]];
        
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty to add evidence!"] call EDEN_fnc_showHint;
            false
        };
        
        _caseFound = false;
        _caseIndex = -1;
        
        {
            if ((_x select 0) == _caseId) then {
                _caseFound = true;
                _caseIndex = _forEachIndex;
            };
        } forEach eden_activeCases;
        
        if (!_caseFound) exitWith {
            ["Case not found!"] call EDEN_fnc_showHint;
            false
        };
        
        _case = eden_activeCases select _caseIndex;
        _evidence = _case select 9;
        
        if (_evidenceId in _evidence) exitWith {
            ["Evidence already linked to this case!"] call EDEN_fnc_showHint;
            false
        };
        
        _evidence pushBack _evidenceId;
        _case set [9, _evidence];
        eden_activeCases set [_caseIndex, _case];
        publicVariable "eden_activeCases";
        
        [format ["Evidence %1 linked to case %2", _evidenceId, _caseId]] call EDEN_fnc_showHint;
        [format ["[INVESTIGATION] %1 linked evidence %2 to case %3", name _player, _evidenceId, _caseId], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
        true
    };
    case "interviewSuspect": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty to conduct interviews!"] call EDEN_fnc_showHint;
            false
        };
        
        if (isNull _suspect) exitWith {
            ["No suspect specified!"] call EDEN_fnc_showHint;
            false
        };
        
        if ((_suspect distance _player) > 10) exitWith {
            ["Suspect must be nearby for interview!"] call EDEN_fnc_showHint;
            false
        };
        
        _interviewQuestions = [
            "Where were you at the time of the incident?",
            "Do you know the victim?",
            "Can anyone verify your whereabouts?",
            "Have you been to the crime scene recently?",
            "Do you own any weapons?"
        ];
        
        _question = selectRandom _interviewQuestions;
        [format ["Interview Question: %1", _question]] call EDEN_fnc_showHint;
        [format ["You are being interviewed: %1", _question]] remoteExec ["EDEN_fnc_showHint", _suspect];
        
        // Simulate interview response
        _responses = [
            "I was at home watching TV",
            "I don't know anything about this",
            "I want a lawyer",
            "Yes, I can prove where I was",
            "No comment"
        ];
        
        _response = selectRandom _responses;
        sleep 5;
        [format ["Suspect Response: %1", _response]] call EDEN_fnc_showHint;
        
        [format ["[INVESTIGATION] %1 interviewed %2 for case %3", name _player, name _suspect, _caseId], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
        true
    };
    case "closeCase": {
        params ["", "", "", "", ["_outcome", "Solved", [""]]];
        
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty to close cases!"] call EDEN_fnc_showHint;
            false
        };
        
        _caseFound = false;
        _caseIndex = -1;
        
        {
            if ((_x select 0) == _caseId) then {
                _caseFound = true;
                _caseIndex = _forEachIndex;
            };
        } forEach eden_activeCases;
        
        if (!_caseFound) exitWith {
            ["Case not found!"] call EDEN_fnc_showHint;
            false
        };
        
        _case = eden_activeCases select _caseIndex;
        _case set [7, _outcome];
        _case set [10, format ["Closed by %1 at %2", name _player, time]];
        eden_activeCases set [_caseIndex, _case];
        publicVariable "eden_activeCases";
        
        if (_outcome == "Solved") then {
            _solved = _player getVariable ["eden_casesSolved", 0];
            _player setVariable ["eden_casesSolved", (_solved + 1), true];
        };
        
        [format ["Case %1 closed as %2", _caseId, _outcome]] call EDEN_fnc_showHint;
        [format ["[INVESTIGATION] %1 closed case %2 as %3", name _player, _caseId, _outcome], "INFO", "POLICE"] call EDEN_fnc_systemLogger;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "viewCase": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty to view cases!"] call EDEN_fnc_showHint;
            false
        };
        
        _caseFound = false;
        _case = [];
        
        {
            if ((_x select 0) == _caseId) then {
                _caseFound = true;
                _case = _x;
            };
        } forEach eden_activeCases;
        
        if (!_caseFound) exitWith {
            ["Case not found!"] call EDEN_fnc_showHint;
            false
        };
        
        _crimeType = _case select 1;
        _description = _case select 2;
        _investigator = _case select 3;
        _location = _case select 6;
        _status = _case select 7;
        _suspects = _case select 8;
        _evidence = _case select 9;
        
        _caseInfo = format ["Case: %1\nCrime: %2\nDescription: %3\nInvestigator: %4\nLocation: %5\nStatus: %6\n", 
            _caseId, _crimeType, _description, _investigator, _location, _status];
        
        _caseInfo = _caseInfo + format ["Suspects (%1):\n", count _suspects];
        {
            _suspectName = _x select 0;
            _caseInfo = _caseInfo + format ["- %1\n", _suspectName];
        } forEach _suspects;
        
        _caseInfo = _caseInfo + format ["Evidence (%1):\n", count _evidence];
        {
            _caseInfo = _caseInfo + format ["- %1\n", _x];
        } forEach _evidence;
        
        [_caseInfo] call EDEN_fnc_showHint;
        true
    };
    case "viewActiveCases": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty to view cases!"] call EDEN_fnc_showHint;
            false
        };
        
        _openCases = [];
        {
            if ((_x select 7) == "Open") then {
                _openCases pushBack _x;
            };
        } forEach eden_activeCases;
        
        if (count _openCases == 0) then {
            ["No active cases"] call EDEN_fnc_showHint;
        } else {
            _caseList = format ["Active Cases (%1):\n", count _openCases];
            {
                _id = _x select 0;
                _crime = _x select 1;
                _investigator = _x select 3;
                _caseList = _caseList + format ["- %1: %2 by %3\n", _id, _crime, _investigator];
            } forEach _openCases;
            
            [_caseList] call EDEN_fnc_showHint;
        };
        
        true
    };
    default { false };
};
