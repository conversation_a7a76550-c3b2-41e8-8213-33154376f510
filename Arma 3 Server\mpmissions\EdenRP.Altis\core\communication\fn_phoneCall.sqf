/*
    EdenRP Phone Call Function
    Enhanced phone calling with realistic features
*/

params [
    ["_caller", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_data", [], [[]]]
];

// Extract call data
if (count _data < 1) exitWith {
    ["No phone number provided", "error"] remoteExec ["EDEN_fnc_showNotification", _caller];
    false
};

private _targetNumber = _data select 0;
private _callType = if (count _data > 1) then {_data select 1} else {"voice"};

// Validate phone number format
if !([_targetNumber] call EDEN_fnc_isValidPhoneNumber) exitWith {
    ["Invalid phone number format", "error"] remoteExec ["EDEN_fnc_showNotification", _caller];
    false
};

// Check if caller is already in a call
if (_caller getVariable ["EDEN_InCall", false]) exitWith {
    ["You are already in a call", "warning"] remoteExec ["EDEN_fnc_showNotification", _caller];
    false
};

// Find target player by phone number
private _target = objNull;
{
    if ((_x getVariable ["EDEN_PhoneNumber", ""]) == _targetNumber) exitWith {
        _target = _x;
    };
} forEach allPlayers;

// Handle special numbers
switch (_targetNumber) do {
    case "911": {
        // Emergency services
        [_caller, "emergency", ["police", "medical"]] call EDEN_fnc_phoneEmergencyCall;
        true
    };
    case "311": {
        // Non-emergency police
        [_caller, "police", []] call EDEN_fnc_phoneEmergencyCall;
        true
    };
    case "411": {
        // Directory assistance
        [_caller] call EDEN_fnc_phoneDirectory;
        true
    };
    case "611": {
        // Phone company
        [_caller] call EDEN_fnc_phoneSupport;
        true
    };
    default {
        if (isNull _target) exitWith {
            ["The number you have dialed is not in service", "error"] remoteExec ["EDEN_fnc_showNotification", _caller];
            false
        };
        
        // Check if target is available
        if (_target getVariable ["EDEN_InCall", false]) exitWith {
            ["The line is busy", "warning"] remoteExec ["EDEN_fnc_showNotification", _caller];
            false
        };
        
        // Check if target's phone is on
        private _targetSettings = _target getVariable ["EDEN_PhoneSettings", []];
        private _phoneOn = true;
        {
            if ((_x select 0) == "phone_enabled") exitWith {
                _phoneOn = _x select 1;
            };
        } forEach _targetSettings;
        
        if (!_phoneOn) exitWith {
            ["The phone is turned off", "warning"] remoteExec ["EDEN_fnc_showNotification", _caller];
            false
        };
        
        // Check if caller is blocked
        private _blockedNumbers = _target getVariable ["EDEN_BlockedNumbers", []];
        private _callerNumber = _caller getVariable ["EDEN_PhoneNumber", ""];
        if (_callerNumber in _blockedNumbers) exitWith {
            ["The number you have dialed is not in service", "error"] remoteExec ["EDEN_fnc_showNotification", _caller];
            false
        };
        
        // Start call process
        [_caller, _target, _callType] call EDEN_fnc_initiateCall;
        true
    };
};
