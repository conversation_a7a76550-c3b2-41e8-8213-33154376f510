/*
    File: fn_robBank.sqf
    Author: EdenRP Development Team
    
    Description:
    Initiates a bank robbery (major criminal activity).
    
    Parameters:
    0: OBJECT - Player initiating robbery (optional, default: player)
    
    Returns:
    BOOLEAN - True if bank robbery was initiated successfully
*/

params [["_player", player, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

// Check if near bank
_nearBanks = nearestObjects [_player, ["Land_Offices_01_V1_F", "Land_MultistoryBuilding_01_F"], 25];
if (count _nearBanks == 0) exitWith {
    ["You must be inside a bank to rob it!"] call EDEN_fnc_showHint;
    false
};

// Check if bank robbery is already in progress
if (missionNamespace getVariable ["eden_bankRobberyActive", false]) exitWith {
    ["A bank robbery is already in progress!"] call EDEN_fnc_showHint;
    false
};

// Check if player has weapon
_playerWeapons = weapons _player;
if (count _playerWeapons == 0) exitWith {
    ["You need a weapon to rob the bank!"] call EDEN_fnc_showHint;
    false
};

// Check minimum gang size (need at least 2 players nearby)
_nearbyPlayers = [];
{
    if (_x != _player && alive _x && _x distance _player < 50) then {
        _nearbyPlayers pushBack _x;
    };
} forEach allPlayers;

if (count _nearbyPlayers < 1) exitWith {
    ["You need at least one accomplice nearby to rob the bank!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_player, "major_crime", ["bank_robbery"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Start bank robbery
missionNamespace setVariable ["eden_bankRobberyActive", true, true];
missionNamespace setVariable ["eden_bankRobbers", [_player] + _nearbyPlayers, true];
missionNamespace setVariable ["eden_bankRobberyStartTime", time, true];

// Create robbery marker
_bankPos = getPos (_nearBanks select 0);
_marker = createMarker ["bank_robbery", _bankPos];
_marker setMarkerType "mil_warning";
_marker setMarkerText "BANK ROBBERY IN PROGRESS";
_marker setMarkerColor "ColorRed";

// Notify all players
{
    [
        "BANK ROBBERY ALERT",
        "A bank robbery is in progress! Police respond immediately!",
        20,
        "error"
    ] remoteExec ["EDEN_fnc_showNotification", _x];
} forEach allPlayers;

// Start robbery timer (10 minutes to complete)
[_player, _bankPos] spawn {
    params ["_robber", "_position"];
    
    _robberyDuration = 600; // 10 minutes
    _startTime = time;
    
    while {time - _startTime < _robberyDuration && missionNamespace getVariable ["eden_bankRobberyActive", false]} do {
        _timeLeft = _robberyDuration - (time - _startTime);
        _minutesLeft = floor(_timeLeft / 60);
        _secondsLeft = floor(_timeLeft % 60);
        
        // Update robbers on progress
        _robbers = missionNamespace getVariable ["eden_bankRobbers", []];
        {
            if (alive _x && _x distance _position < 100) then {
                [format ["Bank robbery progress: %1:%2 remaining", _minutesLeft, if (_secondsLeft < 10) then {"0" + str _secondsLeft} else {str _secondsLeft}]] remoteExec ["EDEN_fnc_showHint", _x];
            };
        } forEach _robbers;
        
        sleep 30; // Update every 30 seconds
    };
    
    // Check if robbery completed successfully
    if (missionNamespace getVariable ["eden_bankRobberyActive", false]) then {
        // Robbery successful
        _robbers = missionNamespace getVariable ["eden_bankRobbers", []];
        _totalReward = 500000; // $500,000 total
        _rewardPerRobber = _totalReward / (count _robbers);
        
        {
            if (alive _x && _x distance _position < 100) then {
                // Pay robber
                _robberMoney = _x getVariable ["eden_cash", 0];
                _x setVariable ["eden_cash", (_robberMoney + _rewardPerRobber), true];
                
                // Add massive wanted level
                _wantedLevel = _x getVariable ["eden_wantedLevel", 0];
                _x setVariable ["eden_wantedLevel", (_wantedLevel + 10), true];
                
                // Add to criminal record
                _criminalRecord = _x getVariable ["eden_criminalRecord", []];
                _crimeRecord = [time, "System", "Bank robbery", "Major felony"];
                _criminalRecord pushBack _crimeRecord;
                _x setVariable ["eden_criminalRecord", _criminalRecord, true];
                
                // Add huge bounty
                _bounty = _x getVariable ["eden_bounty", 0];
                _x setVariable ["eden_bounty", (_bounty + 50000), true];
                
                [format ["Bank robbery successful! You earned $%1", round _rewardPerRobber]] remoteExec ["EDEN_fnc_showHint", _x];
                [_x] call EDEN_fnc_savePlayerData;
            };
        } forEach _robbers;
        
        // Global notification
        {
            [
                "BANK ROBBERY COMPLETED",
                "The bank robbery was successful! Massive manhunt initiated!",
                15,
                "error"
            ] remoteExec ["EDEN_fnc_showNotification", _x];
        } forEach allPlayers;
        
        // Log successful robbery
        [format ["[EDEN] Bank robbery completed successfully by %1 players", count _robbers], "CRITICAL", "CRIME"] call EDEN_fnc_systemLogger;
    };
    
    // Clean up
    missionNamespace setVariable ["eden_bankRobberyActive", false, true];
    missionNamespace setVariable ["eden_bankRobbers", [], true];
    deleteMarker "bank_robbery";
};

["Bank robbery initiated! You have 10 minutes to complete it!"] call EDEN_fnc_showHint;

// Log robbery start
[format ["[EDEN] Bank robbery started by %1 with %2 accomplices", name _player, count _nearbyPlayers], "CRITICAL", "CRIME"] call EDEN_fnc_systemLogger;

true
