/*
    File: fn_hangupCall.sqf
    Author: EdenRP Development Team
    
    Description:
    Hangs up the current phone call.
    
    Parameters:
    0: OBJECT - Player hanging up (optional, default: player)
    
    Returns:
    BOOLEAN - True if call was hung up successfully
*/

params [["_player", player, [objNull]]];

if (isNull _player) exitWith { false };

// Check if player is in a call
if (!(_player getVariable ["eden_inCall", false])) exitWith {
    ["You are not in a call!"] call EDEN_fnc_showHint;
    false
};

_callTarget = _player getVariable ["eden_callTarget", objNull];
_callStartTime = _player getVariable ["eden_callStartTime", time];
_callDuration = time - _callStartTime;

// End call for both parties
_player setVariable ["eden_inCall", false, true];
_player setVariable ["eden_callTarget", objNull, true];
_player setVariable ["eden_callStartTime", nil, true];

if (!isNull _callTarget) then {
    _callTarget setVariable ["eden_inCall", false, true];
    _callTarget setVariable ["eden_callTarget", objNull, true];
    _callTarget setVariable ["eden_callStartTime", nil, true];
    
    [format ["%1 hung up the call", name _player]] remoteExec ["EDEN_fnc_showHint", _callTarget];
};

// Calculate call cost (if applicable)
_callCost = round(_callDuration / 60) * 5; // $5 per minute
if (_callCost > 0) then {
    _playerMoney = _player getVariable ["eden_cash", 0];
    _player setVariable ["eden_cash", (_playerMoney - _callCost), true];
    [format ["Call ended. Duration: %.1f minutes. Cost: $%2", _callDuration/60, _callCost]] call EDEN_fnc_showHint;
} else {
    ["Call ended"] call EDEN_fnc_showHint;
};

// Log call end
[format ["[EDEN] Phone call ended: %1 hung up (duration: %.1f min)", name _player, _callDuration/60], "INFO", "COMMUNICATION"] call EDEN_fnc_systemLogger;

[_player] call EDEN_fnc_savePlayerData;

true
