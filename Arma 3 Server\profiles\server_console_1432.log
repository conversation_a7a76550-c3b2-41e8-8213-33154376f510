﻿ 9:33:13 BattlEye Server: Initialized (v1.220, Arma 3 2.20.152984)
 9:33:13 Game Port: 2302, Steam Query Port: 2303
 9:33:13 Warning: Current Steam AppId: 233780 doesn't match expected value: 107410
 9:33:13 Mission EdenRP.<PERSON><PERSON> read from directory.
 9:33:14 Host identity created.
 9:33:14 Roles assigned.
 9:33:14 Reading mission ...
 9:33:20 Script core\gangs\fn_reputationSystem.sqf not found
 9:33:20 Mission EdenRP.<PERSON><PERSON> read from directory.
 9:33:20 Roles assigned.
 9:33:20 Reading mission ...
 9:33:21 Script core\gangs\fn_reputationSystem.sqf not found
 9:33:21 Mission EdenRP.<PERSON><PERSON> read from directory.
 9:33:21 Roles assigned.
 9:33:21 Reading mission ...
 9:33:22 Script core\gangs\fn_reputationSystem.sqf not found
 9:33:22 Mission EdenRP.<PERSON><PERSON> read from directory.
 9:33:22 Roles assigned.
 9:33:22 Reading mission ...
 9:33:23 Script core\gangs\fn_reputationSystem.sqf not found
 9:33:23 Mission EdenRP.<PERSON><PERSON> read from directory.
 9:33:23 Roles assigned.
 9:33:23 Reading mission ...
 9:33:24 Script core\gangs\fn_reputationSystem.sqf not found
 9:33:24 Mission EdenRP.Altis read from directory.
 9:33:25 Roles assigned.
 9:33:25 Reading mission ...
 9:33:25 Script core\gangs\fn_reputationSystem.sqf not found
 9:33:25 Mission EdenRP.Altis read from directory.
 9:33:26 Roles assigned.
 9:33:26 Reading mission ...
 9:33:26 Script core\gangs\fn_reputationSystem.sqf not found
 9:33:26 Mission EdenRP.Altis read from directory.
 9:33:27 Roles assigned.
 9:33:27 Reading mission ...
 9:33:27 Script core\gangs\fn_reputationSystem.sqf not found
 9:33:27 Mission EdenRP.Altis read from directory.
 9:33:28 Roles assigned.
 9:33:28 Reading mission ...
 9:33:28 Script core\gangs\fn_reputationSystem.sqf not found
 9:33:28 Mission EdenRP.Altis read from directory.
 9:33:29 Roles assigned.
 9:33:29 Reading mission ...
 9:33:29 Script core\gangs\fn_reputationSystem.sqf not found
 9:33:29 Mission EdenRP.Altis read from directory.
