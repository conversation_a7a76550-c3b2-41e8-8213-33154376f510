[Default]
Version = 1
Prepared Statement Cache = true

[getPlayerVehicles]
SQL1_1 = SELECT * FROM eden_vehicles WHERE owner_id = ? AND active = 1
Number Of Inputs = 1
SQL1_INPUTS = 1

[insertVehicle]
SQL1_1 = INSERT INTO eden_vehicles (owner_id, classname, type, side, plate, color, inventory, fuel, damage, position, direction, locked, alive, active, gang_id, custom_name, modifications) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
Number Of Inputs = 17
SQL1_INPUTS = 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17

[updateVehicle]
SQL1_1 = UPDATE eden_vehicles SET color = ?, inventory = ?, fuel = ?, damage = ?, position = ?, direction = ?, locked = ?, alive = ?, impounded = ?, impound_fee = ?, insurance = ?, insurance_expires = ?, mileage = ?, last_used = NOW(), custom_name = ?, modifications = ? WHERE id = ?
Number Of Inputs = 16
SQL1_INPUTS = 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16

[deleteVehicle]
SQL1_1 = UPDATE eden_vehicles SET active = 0 WHERE id = ?
Number Of Inputs = 1
SQL1_INPUTS = 1

[impoundVehicle]
SQL1_1 = UPDATE eden_vehicles SET impounded = 1, impound_fee = ?, position = ? WHERE id = ?
Number Of Inputs = 3
SQL1_INPUTS = 1,2,3

[unimpoundVehicle]
SQL1_1 = UPDATE eden_vehicles SET impounded = 0, impound_fee = 0 WHERE id = ?
Number Of Inputs = 1
SQL1_INPUTS = 1

[getImpoundedVehicles]
SQL1_1 = SELECT * FROM eden_vehicles WHERE owner_id = ? AND impounded = 1 AND active = 1
Number Of Inputs = 1
SQL1_INPUTS = 1

[getAllImpoundedVehicles]
SQL1_1 = SELECT v.*, p.name as owner_name FROM eden_vehicles v LEFT JOIN eden_players p ON v.owner_id = p.player_id WHERE v.impounded = 1 AND v.active = 1
Number Of Inputs = 0

[transferVehicle]
SQL1_1 = UPDATE eden_vehicles SET owner_id = ? WHERE id = ? AND owner_id = ?
Number Of Inputs = 3
SQL1_INPUTS = 1,2,3

[getGangVehicles]
SQL1_1 = SELECT * FROM eden_vehicles WHERE gang_id = ? AND active = 1
Number Of Inputs = 1
SQL1_INPUTS = 1

[updateVehicleGang]
SQL1_1 = UPDATE eden_vehicles SET gang_id = ? WHERE id = ?
Number Of Inputs = 2
SQL1_INPUTS = 1,2

[getVehicleByPlate]
SQL1_1 = SELECT * FROM eden_vehicles WHERE plate = ? AND active = 1
Number Of Inputs = 1
SQL1_INPUTS = 1

[updateVehicleInsurance]
SQL1_1 = UPDATE eden_vehicles SET insurance = ?, insurance_expires = ? WHERE id = ?
Number Of Inputs = 3
SQL1_INPUTS = 1,2,3

[getExpiredInsurance]
SQL1_1 = SELECT * FROM eden_vehicles WHERE insurance = 1 AND insurance_expires < NOW() AND active = 1
Number Of Inputs = 0

[cleanupOldVehicles]
SQL1_1 = UPDATE eden_vehicles SET active = 0 WHERE last_used < DATE_SUB(NOW(), INTERVAL ? DAY) AND alive = 0
Number Of Inputs = 1
SQL1_INPUTS = 1

[getVehicleStats]
SQL1_1 = SELECT classname, COUNT(*) as count, AVG(mileage) as avg_mileage FROM eden_vehicles WHERE active = 1 GROUP BY classname ORDER BY count DESC
Number Of Inputs = 0

[searchVehicles]
SQL1_1 = SELECT v.*, p.name as owner_name FROM eden_vehicles v LEFT JOIN eden_players p ON v.owner_id = p.player_id WHERE (v.plate LIKE ? OR p.name LIKE ?) AND v.active = 1 LIMIT 50
Number Of Inputs = 2
SQL1_INPUTS = 1,2
