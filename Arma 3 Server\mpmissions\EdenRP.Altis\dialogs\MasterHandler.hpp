/*
    EdenRP Master Dialog Handler
    Enhanced UI system with modern design
*/

// Base dialog classes
class RscText {
    type = 0;
    idc = -1;
    style = 0;
    colorBackground[] = {0, 0, 0, 0};
    colorText[] = {1, 1, 1, 1};
    font = "RobotoCondensed";
    sizeEx = 0.04;
    shadow = 1;
    x = 0;
    y = 0;
    w = 0.2;
    h = 0.04;
};

class RscButton {
    type = 1;
    style = 2;
    x = 0;
    y = 0;
    w = 0.095589;
    h = 0.039216;
    text = "";
    font = "RobotoCondensed";
    sizeEx = 0.04;
    colorText[] = {1, 1, 1, 1};
    colorDisabled[] = {0.4, 0.4, 0.4, 1};
    colorBackground[] = {0.75, 0.75, 0.75, 0.8};
    colorBackgroundDisabled[] = {0, 0, 0, 0.5};
    colorBackgroundActive[] = {0.75, 0.75, 0.75, 1};
    colorFocused[] = {0.75, 0.75, 0.75, 1};
    colorShadow[] = {0.023529, 0, 0.0313725, 1};
    colorBorder[] = {0.023529, 0, 0.0313725, 1};
    soundEnter[] = {"", 0.09, 1};
    soundPush[] = {"", 0.09, 1};
    soundClick[] = {"", 0.09, 1};
    soundEscape[] = {"", 0.09, 1};
    shadow = 2;
    border = 0;
    offsetX = 0.003;
    offsetY = 0.003;
    offsetPressedX = 0.002;
    offsetPressedY = 0.002;
    borderSize = 0;
};

class RscEdit {
    type = 2;
    style = 0;
    x = 0;
    y = 0;
    h = 0.04;
    w = 0.2;
    font = "RobotoCondensed";
    sizeEx = 0.04;
    colorText[] = {0, 0, 0, 1};
    colorDisabled[] = {0, 0, 0, 0.3};
    colorBackground[] = {1, 1, 1, 1};
    colorSelection[] = {0.6, 0.6, 0.6, 1};
    autocomplete = "";
    text = "";
    size = 0.2;
    shadow = 0;
};

class RscListBox {
    type = 5;
    style = 0;
    x = 0;
    y = 0;
    w = 0.4;
    h = 0.4;
    font = "RobotoCondensed";
    sizeEx = 0.04;
    rowHeight = 0.04;
    colorText[] = {1, 1, 1, 1};
    colorDisabled[] = {1, 1, 1, 0.25};
    colorScrollbar[] = {1, 0, 0, 0};
    colorSelect[] = {0, 0, 0, 1};
    colorSelect2[] = {0, 0, 0, 1};
    colorSelectBackground[] = {0.95, 0.95, 0.95, 1};
    colorSelectBackground2[] = {1, 1, 1, 0.5};
    colorBackground[] = {0, 0, 0, 0.3};
    maxHistoryDelay = 1;
    autoScrollSpeed = -1;
    autoScrollDelay = 5;
    autoScrollRewind = 0;
    arrowEmpty = "\A3\ui_f\data\GUI\RscCommon\RscListBox\arrow_combo_ca.paa";
    arrowFull = "\A3\ui_f\data\GUI\RscCommon\RscListBox\arrow_combo_active_ca.paa";
    shadow = 0;
    colorShadow[] = {0, 0, 0, 0.5};
    border = 0;
    borderSize = 0;
    colorBorder[] = {0, 0, 0, 0.6};
    soundSelect[] = {"", 0.1, 1};
};

class RscCombo {
    type = 4;
    style = 0;
    x = 0;
    y = 0;
    w = 0.12;
    h = 0.035;
    font = "RobotoCondensed";
    sizeEx = 0.04;
    colorText[] = {0, 0, 0, 1};
    colorDisabled[] = {0, 0, 0, 0.3};
    colorBackground[] = {1, 1, 1, 1};
    colorSelectBackground[] = {1, 1, 1, 0.7};
    colorSelect[] = {0, 0, 0, 1};
    colorScrollbar[] = {1, 0, 0, 1};
    arrowEmpty = "\A3\ui_f\data\GUI\RscCommon\RscListBox\arrow_combo_ca.paa";
    arrowFull = "\A3\ui_f\data\GUI\RscCommon\RscListBox\arrow_combo_active_ca.paa";
    wholeHeight = 0.45;
    shadow = 0;
    colorShadow[] = {0, 0, 0, 0.5};
    border = 0;
    borderSize = 0;
    colorBorder[] = {0, 0, 0, 0.6};
    soundSelect[] = {"", 0.1, 1};
    soundExpand[] = {"", 0.1, 1};
    soundCollapse[] = {"", 0.1, 1};
    maxHistoryDelay = 1;
    autoScrollSpeed = -1;
    autoScrollDelay = 5;
    autoScrollRewind = 0;
};

class RscSlider {
    type = 3;
    style = 1024;
    x = 0;
    y = 0;
    w = 0.3;
    h = 0.025;
    color[] = {1, 1, 1, 0.8};
    colorActive[] = {1, 1, 1, 1};
    shadow = 0;
    colorShadow[] = {0, 0, 0, 0.5};
};

class RscFrame {
    type = 0;
    idc = -1;
    style = 64;
    shadow = 2;
    colorBackground[] = {0, 0, 0, 0};
    colorText[] = {1, 1, 1, 1};
    font = "RobotoCondensed";
    sizeEx = 0.04;
    text = "";
};

class RscBackground {
    type = 0;
    idc = -1;
    style = 512;
    shadow = 0;
    x = 0.1;
    y = 0.1;
    w = 0.8;
    h = 0.8;
    text = "";
    colorBackground[] = {0, 0, 0, 0.8};
    colorText[] = {1, 1, 1, 1};
    font = "RobotoCondensed";
    sizeEx = 0.04;
};

class RscPicture {
    type = 0;
    idc = -1;
    style = 48;
    colorBackground[] = {0, 0, 0, 0};
    colorText[] = {1, 1, 1, 1};
    font = "TahomaB";
    sizeEx = 0;
    lineSpacing = 0;
    text = "";
    fixedWidth = 0;
    shadow = 0;
    x = 0;
    y = 0;
    w = 0.2;
    h = 0.15;
};

// Enhanced dialog classes
class EDEN_RscText: RscText {
    colorText[] = {0.95, 0.95, 0.95, 1};
    font = "RobotoCondensedLight";
    shadow = 1;
};

class EDEN_RscButton: RscButton {
    colorBackground[] = {0.2, 0.4, 0.8, 0.8};
    colorBackgroundActive[] = {0.3, 0.5, 0.9, 1};
    colorFocused[] = {0.25, 0.45, 0.85, 1};
    font = "RobotoCondensed";
    sizeEx = 0.035;
};

class EDEN_RscButtonGreen: EDEN_RscButton {
    colorBackground[] = {0.2, 0.8, 0.2, 0.8};
    colorBackgroundActive[] = {0.3, 0.9, 0.3, 1};
    colorFocused[] = {0.25, 0.85, 0.25, 1};
};

class EDEN_RscButtonRed: EDEN_RscButton {
    colorBackground[] = {0.8, 0.2, 0.2, 0.8};
    colorBackgroundActive[] = {0.9, 0.3, 0.3, 1};
    colorFocused[] = {0.85, 0.25, 0.25, 1};
};

class EDEN_RscEdit: RscEdit {
    colorBackground[] = {0.1, 0.1, 0.1, 0.8};
    colorText[] = {0.95, 0.95, 0.95, 1};
    font = "RobotoCondensed";
};

class EDEN_RscListBox: RscListBox {
    colorBackground[] = {0.1, 0.1, 0.1, 0.8};
    colorText[] = {0.95, 0.95, 0.95, 1};
    colorSelectBackground[] = {0.2, 0.4, 0.8, 0.8};
    colorSelectBackground2[] = {0.25, 0.45, 0.85, 0.6};
    font = "RobotoCondensed";
};

class EDEN_RscCombo: RscCombo {
    colorBackground[] = {0.1, 0.1, 0.1, 0.8};
    colorText[] = {0.95, 0.95, 0.95, 1};
    colorSelectBackground[] = {0.2, 0.4, 0.8, 0.8};
    font = "RobotoCondensed";
};

// Include specific dialog files
#include "main_menu.hpp"
#include "inventory.hpp"
#include "vehicle_shop.hpp"
#include "gang_menu.hpp"
#include "police_menu.hpp"
#include "medical_menu.hpp"
#include "phone.hpp"
#include "atm.hpp"
#include "shop.hpp"
#include "admin_menu.hpp"
