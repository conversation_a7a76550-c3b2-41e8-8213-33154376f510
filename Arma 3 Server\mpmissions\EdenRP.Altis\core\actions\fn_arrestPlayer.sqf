/*
    File: fn_arrestPlayer.sqf
    Author: EdenRP Development Team
    
    Description:
    Arrests a player and sends them to jail.
    
    Parameters:
    0: OBJECT - Target player to arrest
    1: OBJECT - Arresting officer (optional, default: player)
    2: STRING - Arrest reason (optional)
    3: NUMBER - Jail time in minutes (optional, default: 10)
    
    Returns:
    BOOLEAN - True if arrest was successful
    
    Example:
    [cursorTarget] call EDEN_fnc_arrestPlayer;
    [_target, player, "Speeding", 5] call EDEN_fnc_arrestPlayer;
*/

params [
    ["_target", obj<PERSON>ull, [obj<PERSON>ull]],
    ["_officer", player, [objNull]],
    ["_reason", "General violation", [""]],
    ["_jailTime", 10, [0]]
];

// Validate parameters
if (isNull _target || isNull _officer) exitWith {
    ["[EDEN] fn_arrestPlayer: Invalid target or officer"] call EDEN_fnc_systemLogger;
    false
};

// Check if officer is police
if (!(_officer getVariable ["eden_isPolice", false])) exitWith {
    ["Only police officers can arrest players!"] call EDEN_fnc_showHint;
    false
};

// Check if target is already arrested
if (_target getVariable ["eden_isArrested", false]) exitWith {
    ["Player is already arrested!"] call EDEN_fnc_showHint;
    false
};

// Check distance
if (_officer distance _target > 5) exitWith {
    ["Target is too far away!"] call EDEN_fnc_showHint;
    false
};

// Security validation
if (!([_officer, "player_action", [_target, "arrest"]] call EDEN_fnc_securityValidator)) exitWith {
    false
};

// Set arrest variables
_target setVariable ["eden_isArrested", true, true];
_target setVariable ["eden_arrestedBy", name _officer, true];
_target setVariable ["eden_arrestReason", _reason, true];
_target setVariable ["eden_arrestTime", time, true];
_target setVariable ["eden_jailTime", _jailTime, true];

// Restrain the player
_target setVariable ["eden_isRestrained", true, true];
_target allowDamage false;

// Remove weapons from arrested player
{
    _target removeWeapon _x;
} forEach weapons _target;

// Add handcuff animation
[_target, "Acts_AidlPsitMstpSsurWnonDnon_loop"] remoteExec ["switchMove"];

// Teleport to jail (Altis coordinates)
_jailPosition = [1642.5, 1315.2, 0]; // Altis jail coordinates
_target setPos _jailPosition;

// Create jail cell effect
_jailMarker = createMarker [format ["jail_%1", getPlayerUID _target], _jailPosition];
_jailMarker setMarkerType "mil_warning";
_jailMarker setMarkerText format ["%1 - Jailed", name _target];
_jailMarker setMarkerColor "ColorRed";

// Start jail timer
[_target, _jailTime] spawn {
    params ["_prisoner", "_time"];
    
    _endTime = time + (_time * 60); // Convert minutes to seconds
    
    while {time < _endTime && alive _prisoner && _prisoner getVariable ["eden_isArrested", false]} do {
        _remaining = (_endTime - time) / 60;
        _minutes = floor _remaining;
        _seconds = floor ((_remaining - _minutes) * 60);
        
        _timeText = format ["Jail Time Remaining: %1:%2", 
            if (_minutes < 10) then {"0" + str _minutes} else {str _minutes},
            if (_seconds < 10) then {"0" + str _seconds} else {str _seconds}
        ];
        
        [_timeText] remoteExec ["EDEN_fnc_showHint", _prisoner];
        sleep 30; // Update every 30 seconds
    };
    
    // Release from jail
    if (alive _prisoner && _prisoner getVariable ["eden_isArrested", false]) then {
        [_prisoner] call EDEN_fnc_releasePlayer;
    };
};

// Update criminal record
_criminalRecord = _target getVariable ["eden_criminalRecord", []];
_arrestRecord = [
    time,
    name _officer,
    _reason,
    _jailTime
];
_criminalRecord pushBack _arrestRecord;
_target setVariable ["eden_criminalRecord", _criminalRecord, true];

// Increase wanted level
_wantedLevel = _target getVariable ["eden_wantedLevel", 0];
_target setVariable ["eden_wantedLevel", (_wantedLevel + 1), true];

// Update police statistics
_arrests = _officer getVariable ["eden_arrests", 0];
_officer setVariable ["eden_arrests", (_arrests + 1), true];

// Notifications
[
    "Player Arrested",
    format ["%1 has been arrested by %2 for %3 (%4 minutes)", 
        name _target, name _officer, _reason, _jailTime],
    5,
    "info"
] remoteExec ["EDEN_fnc_showNotification", _officer];

[
    "You Have Been Arrested",
    format ["You have been arrested by %1 for %2. Jail time: %3 minutes", 
        name _officer, _reason, _jailTime],
    10,
    "error"
] remoteExec ["EDEN_fnc_showNotification", _target];

// Broadcast to all police
{
    if (_x getVariable ["eden_isPolice", false] && _x != _officer) then {
        [
            "Arrest Made",
            format ["%1 arrested %2 for %3", name _officer, name _target, _reason],
            5,
            "info"
        ] remoteExec ["EDEN_fnc_showNotification", _x];
    };
} forEach allPlayers;

// Log the arrest
[format ["[EDEN] Player %1 arrested by %2 for %3 (%4 minutes)", 
    name _target, name _officer, _reason, _jailTime], "INFO", "POLICE"] call EDEN_fnc_systemLogger;

// Save player data
[_target] call EDEN_fnc_savePlayerData;
[_officer] call EDEN_fnc_savePlayerData;

// Return success
true
