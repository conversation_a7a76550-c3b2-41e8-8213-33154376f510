/*
    File: fn_initializeDatabase.sqf
    Author: EdenRP Development Team
    
    Description:
    Initializes the database connection for EdenRP
*/

// Initialize extDB3
private _result = "";

// Check if extDB3 extension is available
_result = "extDB3" callExtension "9:VERSION";
if (_result == "") then {
    ["ERROR: extDB3 extension not found!"] call EDEN_fnc_systemLogger;
    EDEN_DatabaseConnected = false;
} else {
    [format["extDB3 Version: %1", _result]] call EDEN_fnc_systemLogger;
    
    // Add database connection
    _result = "extDB3" callExtension "9:ADD_DATABASE:Database";
    if (_result != "[1]") then {
        [format["ERROR: Failed to add database connection: %1", _result]] call EDEN_fnc_systemLogger;
        EDEN_DatabaseConnected = false;
    } else {
        ["Database connection added successfully"] call EDEN_fnc_systemLogger;
        
        // Add database protocols
        _result = "extDB3" callExtension "9:ADD_DATABASE_PROTOCOL:Database:SQL_CUSTOM:eden_players:eden_players";
        _result = "extDB3" callExtension "9:ADD_DATABASE_PROTOCOL:Database:SQL_CUSTOM:eden_vehicles:eden_vehicles";
        _result = "extDB3" callExtension "9:ADD_DATABASE_PROTOCOL:Database:SQL_CUSTOM:eden_gangs:eden_gangs";
        
        // Lock database protocols
        _result = "extDB3" callExtension "9:LOCK";
        if (_result != "[1]") then {
            [format["ERROR: Failed to lock database protocols: %1", _result]] call EDEN_fnc_systemLogger;
            EDEN_DatabaseConnected = false;
        } else {
            ["Database protocols initialized and locked"] call EDEN_fnc_systemLogger;
            EDEN_DatabaseConnected = true;
        };
    };
};

EDEN_DatabaseConnected
