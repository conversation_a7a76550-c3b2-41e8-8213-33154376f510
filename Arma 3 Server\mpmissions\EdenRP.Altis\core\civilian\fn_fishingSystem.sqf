/*
    File: fn_fishingSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages fishing system for civilians.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_fishingLevel", 1, true];
        _player setVariable ["eden_fishCaught", 0, true];
        true
    };
    case "fish": {
        if (!(_player getVariable ["eden_fishingLicense", false])) exitWith {
            ["You need a fishing license!"] call EDEN_fnc_showHint;
            false
        };
        
        _level = _player getVariable ["eden_fishingLevel", 1];
        _success = (random 100) < (30 + (_level * 5));
        
        if (_success) then {
            _fishTypes = ["trout", "salmon", "bass", "tuna"];
            _fish = selectRandom _fishTypes;
            _quantity = 1 + floor(random 3);
            
            _virtualItems = _player getVariable ["eden_virtualItems", []];
            _found = false;
            {
                if ((_x select 0) == _fish) then {
                    _x set [1, ((_x select 1) + _quantity)];
                    _found = true;
                };
            } forEach _virtualItems;
            
            if (!_found) then {
                _virtualItems pushBack [_fish, _quantity];
            };
            
            _player setVariable ["eden_virtualItems", _virtualItems, true];
            
            _caught = _player getVariable ["eden_fishCaught", 0];
            _player setVariable ["eden_fishCaught", (_caught + _quantity), true];
            
            if ((_caught % 10) == 0) then {
                _newLevel = _player getVariable ["eden_fishingLevel", 1];
                _player setVariable ["eden_fishingLevel", (_newLevel + 1), true];
                [format ["Fishing level increased to %1!", _newLevel + 1]] call EDEN_fnc_showHint;
            };
            
            [format ["Caught %1x %2!", _quantity, _fish]] call EDEN_fnc_showHint;
        } else {
            ["No fish caught this time."] call EDEN_fnc_showHint;
        };
        
        [_player] call EDEN_fnc_savePlayerData;
        _success
    };
    default { false };
};
