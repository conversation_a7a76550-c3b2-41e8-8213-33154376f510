/*
    File: fn_insuranceSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages vehicle insurance system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_vehicle", objNull, [objNull]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_vehicleInsurance", [], true];
        _player setVariable ["eden_insuranceClaims", [], true];
        true
    };
    case "buyInsurance": {
        if (isNull _vehicle) exitWith { false };
        
        _vehicleType = typeOf _vehicle;
        _premium = 300;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _premium) exitWith {
            [format ["Not enough money! Need $%1", _premium]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _premium), true];
        
        _insurance = _player getVariable ["eden_vehicleInsurance", []];
        _policy = [_vehicleType, _premium, time, (time + 2592000)]; // 30 days
        _insurance pushBack _policy;
        _player setVariable ["eden_vehicleInsurance", _insurance, true];
        
        [format ["Vehicle insurance purchased for $%1", _premium]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "fileClaim": {
        if (isNull _vehicle) exitWith { false };
        
        _vehicleType = typeOf _vehicle;
        _insurance = _player getVariable ["eden_vehicleInsurance", []];
        _hasInsurance = false;
        
        {
            if (((_x select 0) == _vehicleType) && (time < (_x select 3))) then {
                _hasInsurance = true;
            };
        } forEach _insurance;
        
        if (!_hasInsurance) exitWith {
            ["No valid insurance for this vehicle!"] call EDEN_fnc_showHint;
            false
        };
        
        _damageLevel = damage _vehicle;
        _payout = floor(5000 * _damageLevel * 0.8); // 80% coverage
        
        _cash = _player getVariable ["eden_cash", 0];
        _player setVariable ["eden_cash", (_cash + _payout), true];
        
        _claims = _player getVariable ["eden_insuranceClaims", []];
        _claim = [_vehicleType, _damageLevel, _payout, time];
        _claims pushBack _claim;
        _player setVariable ["eden_insuranceClaims", _claims, true];
        
        _vehicle setDamage 0;
        
        [format ["Insurance claim paid: $%1", _payout]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
