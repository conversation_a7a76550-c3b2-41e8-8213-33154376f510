/*
    File: fn_realEstateSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages real estate market system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_propertyId", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_realEstateMarket") then {
            eden_realEstateMarket = [];
            publicVariable "eden_realEstateMarket";
        };
        true
    };
    case "listProperty": {
        params ["", "", "", ["_price", 50000, [0]]];
        
        _owned = _player getVariable ["eden_ownedProperties", []];
        if !(_propertyId in _owned) exitWith {
            ["You don't own this property!"] call EDEN_fnc_showHint;
            false
        };
        
        _listing = [_propertyId, getPlayerUID _player, name _player, _price, time];
        eden_realEstateMarket pushBack _listing;
        publicVariable "eden_realEstateMarket";
        
        [format ["Property listed for $%1", _price]] call EDEN_fnc_showHint;
        true
    };
    case "buyFromMarket": {
        _listing = [];
        _listingIndex = -1;
        
        {
            if ((_x select 0) == _propertyId) then {
                _listing = _x;
                _listingIndex = _forEachIndex;
            };
        } forEach eden_realEstateMarket;
        
        if (count _listing == 0) exitWith {
            ["Property not found on market!"] call EDEN_fnc_showHint;
            false
        };
        
        _price = _listing select 3;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _price) exitWith {
            [format ["Not enough money! Need $%1", _price]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _price), true];
        
        _owned = _player getVariable ["eden_ownedProperties", []];
        _owned pushBack _propertyId;
        _player setVariable ["eden_ownedProperties", _owned, true];
        
        eden_realEstateMarket deleteAt _listingIndex;
        publicVariable "eden_realEstateMarket";
        
        [format ["Purchased property for $%1", _price]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
