/*
    File: fn_swatSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages SWAT operations system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_swatRank", "Operator", true];
        _player setVariable ["eden_swatOperations", 0, true];
        true
    };
    case "deploySwat": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty!"] call EDEN_fnc_showHint;
            false
        };
        
        _rank = _player getVariable ["eden_policeRank", "Cadet"];
        if !(_rank in ["Lieutenant", "Captain", "Chief"]) exitWith {
            ["Insufficient rank for SWAT deployment!"] call EDEN_fnc_showHint;
            false
        };
        
        // Give SWAT equipment
        removeAllWeapons _player;
        removeAllItems _player;
        removeVest _player;
        removeHeadgear _player;
        
        _player addVest "V_PlateCarrier1_blk";
        _player addHeadgear "H_HelmetSpecB_blk";
        _player addWeapon "arifle_MX_F";
        _player addMagazine "30Rnd_65x39_caseless_mag";
        _player addMagazine "30Rnd_65x39_caseless_mag";
        _player addMagazine "30Rnd_65x39_caseless_mag";
        
        _operations = _player getVariable ["eden_swatOperations", 0];
        _player setVariable ["eden_swatOperations", (_operations + 1), true];
        
        ["SWAT gear equipped!"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "breachDoor": {
        ["Door breached!"] call EDEN_fnc_showHint;
        true
    };
    case "flashbang": {
        ["Flashbang deployed!"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
