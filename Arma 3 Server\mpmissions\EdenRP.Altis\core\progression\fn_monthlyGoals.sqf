/*
    File: fn_monthlyGoals.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages monthly goals system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_monthlyGoalTemplates") then {
            eden_monthlyGoalTemplates = [
                ["wealth_builder", "Accumulate $100,000", 100000, 2000],
                ["job_master", "Reach level 10 in any job", 10, 1500],
                ["social_networker", "Add 20 friends", 20, 1000],
                ["crime_fighter", "Arrest 25 criminals", 25, 1800],
                ["life_saver", "Revive 30 players", 30, 1600]
            ];
            publicVariable "eden_monthlyGoalTemplates";
        };
        _player setVariable ["eden_monthlyGoals", [], true];
        _player setVariable ["eden_monthlyGoalProgress", [], true];
        _player setVariable ["eden_lastMonthlyReset", 0, true];
        true
    };
    case "generateMonthlyGoals": {
        _lastReset = _player getVariable ["eden_lastMonthlyReset", 0];
        _currentMonth = parseNumber (formatText ["%1", date select 1]);
        _lastResetMonth = parseNumber (formatText ["%1", dateToNumber [2023, (_lastReset / 2592000), 1]]);
        
        if (_currentMonth == _lastResetMonth) exitWith { false };
        
        _player setVariable ["eden_monthlyGoalProgress", [], true];
        _player setVariable ["eden_lastMonthlyReset", time, true];
        
        _selectedGoals = [];
        _availableGoals = +eden_monthlyGoalTemplates;
        
        for "_i" from 1 to 2 do {
            if (count _availableGoals > 0) then {
                _randomIndex = floor(random(count _availableGoals));
                _goal = _availableGoals select _randomIndex;
                _selectedGoals pushBack [(_goal select 0), (_goal select 1), 0, (_goal select 2), (_goal select 3), false];
                _availableGoals deleteAt _randomIndex;
            };
        };
        
        _player setVariable ["eden_monthlyGoals", _selectedGoals, true];
        ["New monthly goals available!"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "updateGoalProgress": {
        params ["", "", ["_goalType", "wealth_builder", [""]], ["_amount", 1, [0]]];
        
        _goals = _player getVariable ["eden_monthlyGoals", []];
        _updated = false;
        
        {
            if ((_x select 0) == _goalType && !(_x select 5)) then {
                _currentProgress = _x select 2;
                _target = _x select 3;
                
                _newProgress = switch (_goalType) do {
                    case "wealth_builder": {
                        _cash = _player getVariable ["eden_cash", 0];
                        _bank = _player getVariable ["eden_bankAccount", 0];
                        _cash + _bank
                    };
                    case "job_master": {
                        _job = _player getVariable ["eden_job", "civilian"];
                        [_player, "getJobLevel", _job] call EDEN_fnc_levelingSystem
                    };
                    case "social_networker": {
                        count (_player getVariable ["eden_friends", []])
                    };
                    default { _currentProgress + _amount };
                };
                
                _x set [2, _newProgress];
                
                if (_newProgress >= _target) then {
                    _x set [5, true];
                    _reward = _x select 4;
                    [_player, "addExperience", _reward, "Monthly Goal"] call EDEN_fnc_experienceSystem;
                    [format ["Monthly goal completed: %1 (+%2 XP)", (_x select 1), _reward]] call EDEN_fnc_showHint;
                };
                _updated = true;
            };
        } forEach _goals;
        
        if (_updated) then {
            _player setVariable ["eden_monthlyGoals", _goals, true];
            [_player] call EDEN_fnc_savePlayerData;
        };
        
        _updated
    };
    case "showMonthlyGoals": {
        _goals = _player getVariable ["eden_monthlyGoals", []];
        
        if (count _goals == 0) exitWith {
            ["No monthly goals available. Check back next month!"] call EDEN_fnc_showHint;
            false
        };
        
        _goalText = "=== MONTHLY GOALS ===\n";
        
        {
            _status = if (_x select 5) then { "✓" } else { "○" };
            _progress = format["%1/%2", (_x select 2), (_x select 3)];
            _goalText = _goalText + format["%1 %2 [%3]\n", _status, (_x select 1), _progress];
        } forEach _goals;
        
        [_goalText] call EDEN_fnc_showHint;
        true
    };
    case "getCompletionRate": {
        _goals = _player getVariable ["eden_monthlyGoals", []];
        if (count _goals == 0) exitWith { [0, 0] };
        
        _completed = 0;
        {
            if (_x select 5) then { _completed = _completed + 1; };
        } forEach _goals;
        
        [_completed, count _goals]
    };
    default { false };
};
