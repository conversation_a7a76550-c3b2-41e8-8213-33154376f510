/*
    File: fn_allianceSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages gang alliance system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_targetGang", "", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_gangAlliances") then {
            eden_gangAlliances = [];
            publicVariable "eden_gangAlliances";
        };
        true
    };
    case "proposeAlliance": {
        _gang = _player getVariable ["eden_gang", ""];
        _rank = _player getVariable ["eden_gangRank", ""];
        
        if (_gang == "" || _rank != "Leader") exitWith {
            ["Only gang leaders can propose alliances!"] call EDEN_fnc_showHint;
            false
        };
        
        _alliance = [_gang, _targetGang, time, "Proposed"];
        eden_gangAlliances pushBack _alliance;
        publicVariable "eden_gangAlliances";
        
        [format ["Alliance proposed to %1", _targetGang]] call EDEN_fnc_showHint;
        true
    };
    case "acceptAlliance": {
        _gang = _player getVariable ["eden_gang", ""];
        _rank = _player getVariable ["eden_gangRank", ""];
        
        if (_gang == "" || _rank != "Leader") exitWith {
            ["Only gang leaders can accept alliances!"] call EDEN_fnc_showHint;
            false
        };
        
        {
            if (((_x select 1) == _gang) && (_x select 3) == "Proposed") then {
                _x set [3, "Active"];
                eden_gangAlliances set [_forEachIndex, _x];
            };
        } forEach eden_gangAlliances;
        
        publicVariable "eden_gangAlliances";
        ["Alliance accepted"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
