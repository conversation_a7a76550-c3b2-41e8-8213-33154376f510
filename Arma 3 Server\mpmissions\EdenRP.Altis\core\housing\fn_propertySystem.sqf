/*
    EdenRP Property System
    Enhanced housing and real estate management
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
    ["_action", "", [""]],
    ["_propertyId", 0, [0]],
    ["_data", [], [[]]]
];

// Validate parameters
if (isNull _player || !isPlayer _player) exitWith {
    ["Invalid player provided to propertySystem", "ERROR", "HOUSING"] call EDEN_fnc_systemLogger;
    false
};

private _result = false;
switch (toLower _action) do {
    case "purchase": {
        _result = [_player, _propertyId, _data] call EDEN_fnc_purchaseProperty;
    };
    case "sell": {
        _result = [_player, _propertyId, _data] call EDEN_fnc_sellProperty;
    };
    case "rent": {
        _result = [_player, _propertyId, _data] call EDEN_fnc_rentProperty;
    };
    case "access": {
        _result = [_player, _propertyId] call EDEN_fnc_accessProperty;
    };
    case "lock": {
        _result = [_player, _propertyId] call EDEN_fnc_lockProperty;
    };
    case "unlock": {
        _result = [_player, _propertyId] call EDEN_fnc_unlockProperty;
    };
    case "upgrade": {
        _result = [_player, _propertyId, _data] call EDEN_fnc_upgradeProperty;
    };
    case "decorate": {
        _result = [_player, _propertyId, _data] call EDEN_fnc_decorateProperty;
    };
    case "storage": {
        _result = [_player, _propertyId, _data] call EDEN_fnc_propertyStorage;
    };
    case "utilities": {
        _result = [_player, _propertyId, _data] call EDEN_fnc_propertyUtilities;
    };
    case "security": {
        _result = [_player, _propertyId, _data] call EDEN_fnc_propertySecurity;
    };
    case "maintenance": {
        _result = [_player, _propertyId, _data] call EDEN_fnc_propertyMaintenance;
    };
    case "insurance": {
        _result = [_player, _propertyId, _data] call EDEN_fnc_propertyInsurance;
    };
    case "getowned": {
        _result = [_player] call EDEN_fnc_getOwnedProperties;
    };
    case "getavailable": {
        _result = [_player] call EDEN_fnc_getAvailableProperties;
    };
    case "getinfo": {
        _result = [_propertyId] call EDEN_fnc_getPropertyInfo;
    };
    default {
        [format["Unknown property action: %1", _action], "ERROR", "HOUSING"] call EDEN_fnc_systemLogger;
    };
};

_result
