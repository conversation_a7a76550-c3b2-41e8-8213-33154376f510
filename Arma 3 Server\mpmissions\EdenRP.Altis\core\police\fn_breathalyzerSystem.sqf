/*
    File: fn_breathalyzerSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages breathalyzer testing system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_target", objNull, [obj<PERSON><PERSON>]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_breathalyzerTests", 0, true];
        true
    };
    case "administerTest": {
        if (!(_player getVariable ["eden_onDuty", false])) exitWith {
            ["You must be on duty!"] call EDEN_fnc_showHint;
            false
        };
        
        if (isNull _target) exitWith {
            ["No target specified!"] call EDEN_fnc_showHint;
            false
        };
        
        if ((_target distance _player) > 5) exitWith {
            ["Target must be nearby!"] call EDEN_fnc_showHint;
            false
        };
        
        _tests = _player getVariable ["eden_breathalyzerTests", 0];
        _player setVariable ["eden_breathalyzerTests", (_tests + 1), true];
        
        ["Administering breathalyzer test..."] call EDEN_fnc_showHint;
        ["You are being given a breathalyzer test"] remoteExec ["EDEN_fnc_showHint", _target];
        
        sleep 3;
        
        _alcoholLevel = _target getVariable ["eden_alcoholLevel", 0];
        _result = _alcoholLevel + (random 0.02); // Add some variance
        
        if (_result > 0.08) then {
            [format ["POSITIVE: BAC %1 - Subject is intoxicated!", _result toFixed 3]] call EDEN_fnc_showHint;
            [_player, "issueTicket", _target, "DUI", 1500] call EDEN_fnc_trafficSystem;
        } else {
            [format ["NEGATIVE: BAC %1 - Subject is sober", _result toFixed 3]] call EDEN_fnc_showHint;
        };
        
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "calibrateDevice": {
        ["Breathalyzer calibrated"] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
