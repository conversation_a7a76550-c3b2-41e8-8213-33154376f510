/*
    File: fn_advertisementSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages advertisement system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_advertisements") then {
            eden_advertisements = [];
            publicVariable "eden_advertisements";
        };
        true
    };
    case "createAd": {
        params ["", "", ["_message", "", [""]], ["_duration", 300, [0]]];
        
        _cost = _duration * 2;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _cost) exitWith {
            [format ["Not enough money! Need $%1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _cost), true];
        
        _ad = [_message, name _player, time, (time + _duration)];
        eden_advertisements pushBack _ad;
        publicVariable "eden_advertisements";
        
        {
            [format ["AD: %1", _message]] remoteExec ["EDEN_fnc_showHint", _x];
        } forEach allPlayers;
        
        [format ["Advertisement created for $%1", _cost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
