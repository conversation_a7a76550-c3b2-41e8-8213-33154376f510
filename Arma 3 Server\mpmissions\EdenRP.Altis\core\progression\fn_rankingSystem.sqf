/*
    File: fn_rankingSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages player ranking system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        if (isNil "eden_rankingCategories") then {
            eden_rankingCategories = [
                "level", "wealth", "playtime", "achievements", "reputation"
            ];
            publicVariable "eden_rankingCategories";
        };
        true
    };
    case "updateRankings": {
        if (isServer) then {
            _allPlayers = [];
            
            {
                _playerData = [
                    getPlayerUID _x,
                    name _x,
                    _x getVariable ["eden_level", 1],
                    (_x getVariable ["eden_cash", 0]) + (_x getVariable ["eden_bankAccount", 0]),
                    _x getVariable ["eden_playtime", 0],
                    count (_x getVariable ["eden_unlockedAchievements", []]),
                    _x getVariable ["eden_reputation", 0]
                ];
                _allPlayers pushBack _playerData;
            } forEach allPlayers;
            
            eden_playerRankings = _allPlayers;
            publicVariable "eden_playerRankings";
        };
        true
    };
    case "getRanking": {
        params ["", "", ["_category", "level", [""]]];
        
        if (isNil "eden_playerRankings") exitWith { [] };
        
        _sortedPlayers = [];
        _categoryIndex = switch (_category) do {
            case "level": { 2 };
            case "wealth": { 3 };
            case "playtime": { 4 };
            case "achievements": { 5 };
            case "reputation": { 6 };
            default { 2 };
        };
        
        _sortedPlayers = [eden_playerRankings, [], {_x select _categoryIndex}, "DESCEND"] call BIS_fnc_sortBy;
        
        _playerRank = -1;
        _playerUID = getPlayerUID _player;
        
        {
            if ((_x select 0) == _playerUID) then { _playerRank = _forEachIndex + 1; };
        } forEach _sortedPlayers;
        
        [_sortedPlayers, _playerRank]
    };
    case "showLeaderboard": {
        params ["", "", ["_category", "level", [""]]];
        
        _result = [_player, "getRanking", _category] call EDEN_fnc_rankingSystem;
        _rankings = _result select 0;
        _playerRank = _result select 1;
        
        _leaderboardText = format["=== %1 LEADERBOARD ===\n", toUpper _category];
        
        for "_i" from 0 to ((count _rankings min 10) - 1) do {
            _entry = _rankings select _i;
            _leaderboardText = _leaderboardText + format["%1. %2 - %3\n", 
                (_i + 1), (_entry select 1), (_entry select (switch (_category) do {
                    case "level": { 2 };
                    case "wealth": { 3 };
                    case "playtime": { 4 };
                    case "achievements": { 5 };
                    case "reputation": { 6 };
                    default { 2 };
                }))
            ];
        };
        
        if (_playerRank > 0) then {
            _leaderboardText = _leaderboardText + format["\nYour rank: #%1", _playerRank];
        };
        
        [_leaderboardText] call EDEN_fnc_showHint;
        true
    };
    default { false };
};
