/*
    File: fn_initializeAdmin.sqf
    Author: EdenRP Development Team
    
    Description:
    Initializes a player as an administrator with proper permissions and tools.
    
    Parameters:
    0: OBJECT - Player object
    1: NUMBER - Admin level (1-5, default: 1)
    
    Returns:
    BOOLEAN - True if initialization was successful
    
    Example:
    [player] call EDEN_fnc_initializeAdmin;
    [player, 3] call EDEN_fnc_initializeAdmin;
*/

params [
    ["_player", player, [obj<PERSON><PERSON>]],
    ["_adminLevel", 1, [0]]
];

// Validate parameters
if (isNull _player) exitWith {
    ["[EDEN] fn_initializeAdmin: Invalid player object"] call EDEN_fnc_systemLogger;
    false
};

// Clamp admin level between 1-5
_adminLevel = _adminLevel max 1 min 5;

// Set admin variables
_player setVariable ["eden_isAdmin", true, true];
_player setVariable ["eden_adminLevel", _adminLevel, true];
_player setVariable ["eden_adminMode", false, true];

// Set admin permissions based on level
switch (_adminLevel) do {
    case 1: { // Helper
        _player setVariable ["eden_canKick", false, true];
        _player setVariable ["eden_canBan", false, true];
        _player setVariable ["eden_canTeleport", true, true];
        _player setVariable ["eden_canSpectate", true, true];
        _player setVariable ["eden_canGodMode", false, true];
        _player setVariable ["eden_canSpawnItems", false, true];
        _player setVariable ["eden_canSpawnVehicles", false, true];
        _player setVariable ["eden_canManageServer", false, true];
    };
    case 2: { // Moderator
        _player setVariable ["eden_canKick", true, true];
        _player setVariable ["eden_canBan", false, true];
        _player setVariable ["eden_canTeleport", true, true];
        _player setVariable ["eden_canSpectate", true, true];
        _player setVariable ["eden_canGodMode", true, true];
        _player setVariable ["eden_canSpawnItems", true, true];
        _player setVariable ["eden_canSpawnVehicles", false, true];
        _player setVariable ["eden_canManageServer", false, true];
    };
    case 3: { // Administrator
        _player setVariable ["eden_canKick", true, true];
        _player setVariable ["eden_canBan", true, true];
        _player setVariable ["eden_canTeleport", true, true];
        _player setVariable ["eden_canSpectate", true, true];
        _player setVariable ["eden_canGodMode", true, true];
        _player setVariable ["eden_canSpawnItems", true, true];
        _player setVariable ["eden_canSpawnVehicles", true, true];
        _player setVariable ["eden_canManageServer", false, true];
    };
    case 4: { // Senior Admin
        _player setVariable ["eden_canKick", true, true];
        _player setVariable ["eden_canBan", true, true];
        _player setVariable ["eden_canTeleport", true, true];
        _player setVariable ["eden_canSpectate", true, true];
        _player setVariable ["eden_canGodMode", true, true];
        _player setVariable ["eden_canSpawnItems", true, true];
        _player setVariable ["eden_canSpawnVehicles", true, true];
        _player setVariable ["eden_canManageServer", true, true];
    };
    case 5: { // Owner/Super Admin
        _player setVariable ["eden_canKick", true, true];
        _player setVariable ["eden_canBan", true, true];
        _player setVariable ["eden_canTeleport", true, true];
        _player setVariable ["eden_canSpectate", true, true];
        _player setVariable ["eden_canGodMode", true, true];
        _player setVariable ["eden_canSpawnItems", true, true];
        _player setVariable ["eden_canSpawnVehicles", true, true];
        _player setVariable ["eden_canManageServer", true, true];
    };
};

// Initialize admin statistics
_player setVariable ["eden_adminActions", 0, true];
_player setVariable ["eden_playersKicked", 0, true];
_player setVariable ["eden_playersBanned", 0, true];
_player setVariable ["eden_adminTime", 0, true];

// Initialize admin tools
_player setVariable ["eden_adminTools", [
    "teleport_tool",
    "spectate_tool",
    "player_manager",
    "vehicle_manager",
    "item_spawner",
    "god_mode",
    "invisible_mode",
    "no_clip"
], true];

// Setup admin event handlers
_player addEventHandler ["Killed", {
    params ["_unit", "_killer"];
    if (_unit getVariable ["eden_adminMode", false]) then {
        [_unit] call EDEN_fnc_enableGodMode;
    };
}];

// Initialize admin keybinds
_player setVariable ["eden_adminKeybinds", [
    ["F1", "Admin Menu"],
    ["F2", "Teleport to Cursor"],
    ["F3", "Spectate Mode"],
    ["F4", "God Mode Toggle"],
    ["F5", "Invisible Toggle"],
    ["F6", "No Clip Toggle"],
    ["F7", "Heal All Players"],
    ["F8", "Repair All Vehicles"]
], true];

// Create admin briefing
_player createDiarySubject ["AdminTools", "Admin Tools"];
_player createDiaryRecord ["AdminTools", ["Admin Commands", format ["
<font color='#FF0000' size='14'>Admin Level %1 Commands</font><br/><br/>
<font color='#FFFF00'>Keybinds:</font><br/>
• F1 - Admin Menu<br/>
• F2 - Teleport to Cursor<br/>
• F3 - Spectate Mode<br/>
• F4 - God Mode Toggle<br/>
• F5 - Invisible Toggle<br/>
• F6 - No Clip Toggle<br/>
• F7 - Heal All Players<br/>
• F8 - Repair All Vehicles<br/><br/>
<font color='#FFFF00'>Permissions:</font><br/>
• Kick Players: %2<br/>
• Ban Players: %3<br/>
• Spawn Items: %4<br/>
• Spawn Vehicles: %5<br/>
• Server Management: %6
", 
_adminLevel,
if (_player getVariable ["eden_canKick", false]) then {"Yes"} else {"No"},
if (_player getVariable ["eden_canBan", false]) then {"Yes"} else {"No"},
if (_player getVariable ["eden_canSpawnItems", false]) then {"Yes"} else {"No"},
if (_player getVariable ["eden_canSpawnVehicles", false]) then {"Yes"} else {"No"},
if (_player getVariable ["eden_canManageServer", false]) then {"Yes"} else {"No"}
]]];

// Setup admin actions
[_player] call EDEN_fnc_setupAdminActions;

// Log admin initialization
[format ["[EDEN] Player %1 initialized as admin (Level: %2)", name _player, _adminLevel]] call EDEN_fnc_systemLogger;

// Welcome message
_rankName = switch (_adminLevel) do {
    case 1: { "Helper" };
    case 2: { "Moderator" };
    case 3: { "Administrator" };
    case 4: { "Senior Admin" };
    case 5: { "Owner" };
    default { "Admin" };
};

[
    format ["Admin Access Granted - %1", _rankName],
    format ["Welcome %1! You now have Level %2 admin permissions.", name _player, _adminLevel],
    5
] call EDEN_fnc_showNotification;

// Return success
true
