/*
    File: fn_experienceSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages player experience system.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_experience", 0, true];
        _player setVariable ["eden_level", 1, true];
        _player setVariable ["eden_skillPoints", 0, true];
        true
    };
    case "addExperience": {
        params ["", "", ["_amount", 100, [0]], ["_category", "general", [""]]];
        
        _currentXP = _player getVariable ["eden_experience", 0];
        _newXP = _currentXP + _amount;
        _player setVariable ["eden_experience", _newXP, true];
        
        _level = _player getVariable ["eden_level", 1];
        _requiredXP = _level * 1000;
        
        if (_newXP >= _requiredXP) then {
            _player setVariable ["eden_level", (_level + 1), true];
            _player setVariable ["eden_experience", (_newXP - _requiredXP), true];
            
            _skillPoints = _player getVariable ["eden_skillPoints", 0];
            _player setVariable ["eden_skillPoints", (_skillPoints + 3), true];
            
            [format ["LEVEL UP! You are now level %1 (+3 skill points)", (_level + 1)]] call EDEN_fnc_showHint;
        } else {
            [format ["+%1 XP (%2)", _amount, _category]] call EDEN_fnc_showHint;
        };
        
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "getLevel": {
        _player getVariable ["eden_level", 1]
    };
    case "getExperience": {
        _player getVariable ["eden_experience", 0]
    };
    case "getSkillPoints": {
        _player getVariable ["eden_skillPoints", 0]
    };
    default { false };
};
