/*
    EdenRP Give Starting Items
    Function to give new players starting equipment
    
    This function provides new players with basic items
    to help them get started in EdenRP
*/

params [
    ["_player", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]]
];

// Validate player
if (isNull _player || !isPlayer _player) exitWith {
    ["Invalid player provided to giveStartingItems", "ERROR", "ITEMS"] call EDEN_fnc_systemLogger;
    false
};

// Only run on server
if (hasInterface) exitWith {
    ["giveStartingItems called on client", "ERROR", "ITEMS"] call EDEN_fnc_systemLogger;
    false
};

// Define starting items based on faction
private _startingItems = [];

switch (side _player) do {
    case civilian: {
        _startingItems = [
            ["water", 2],
            ["apple", 3],
            ["toolkit", 1],
            ["cellphone", 1],
            ["map", 1],
            ["compass", 1],
            ["watch", 1]
        ];
    };
    case west: {
        _startingItems = [
            ["water", 2],
            ["apple", 3],
            ["toolkit", 1],
            ["cellphone", 1],
            ["map", 1],
            ["compass", 1],
            ["watch", 1],
            ["handcuffs", 2],
            ["ticket_book", 1]
        ];
    };
    case independent: {
        _startingItems = [
            ["water", 2],
            ["apple", 3],
            ["toolkit", 1],
            ["cellphone", 1],
            ["map", 1],
            ["compass", 1],
            ["watch", 1],
            ["medkit", 3],
            ["defibrillator", 1]
        ];
    };
};

// Add items to player's virtual inventory
private _virtualInventory = _player getVariable ["EDEN_VirtualInventory", []];

{
    private _item = _x select 0;
    private _quantity = _x select 1;
    
    // Check if item already exists in inventory
    private _existingIndex = -1;
    {
        if ((_x select 0) == _item) exitWith {
            _existingIndex = _forEachIndex;
        };
    } forEach _virtualInventory;
    
    if (_existingIndex >= 0) then {
        // Add to existing quantity
        private _currentQuantity = (_virtualInventory select _existingIndex) select 1;
        (_virtualInventory select _existingIndex) set [1, _currentQuantity + _quantity];
    } else {
        // Add new item
        _virtualInventory pushBack [_item, _quantity];
    };
} forEach _startingItems;

// Update player's inventory
_player setVariable ["EDEN_VirtualInventory", _virtualInventory, false];

// Give starting licenses based on faction
private _licenses = [];
switch (side _player) do {
    case civilian: {
        _licenses = ["driver"];
    };
    case west: {
        _licenses = ["driver", "weapon"];
    };
    case independent: {
        _licenses = ["driver", "medical"];
    };
};

// Add licenses
private _civLicenses = _player getVariable ["EDEN_CivLicenses", []];
{
    if !(_x in _civLicenses) then {
        _civLicenses pushBack _x;
    };
} forEach _licenses;
_player setVariable ["EDEN_CivLicenses", _civLicenses, false];

[format["Starting items given to %1", name _player], "INFO", "ITEMS"] call EDEN_fnc_systemLogger;

true
