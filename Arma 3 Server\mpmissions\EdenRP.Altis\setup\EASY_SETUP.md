# 🚀 EdenRP Easy Setup - Do It All For Me!

## 📋 **What This Does For You**

I've created **automated scripts** that will do 95% of the setup work for you! Here's what gets done automatically:

### ✅ **Automated Tasks:**
- Creates all required directories
- Downloads extDB3 automatically
- Installs Visual C++ Redistributable
- Copies all EdenRP files to correct locations
- Creates server.cfg, basic.cfg, and startup scripts
- Sets up extDB3 configuration
- Creates database setup scripts

### 🔧 **Manual Tasks (Only 2 things!):**
1. Install MySQL (one-click installer)
2. Run one database script

## 🎯 **Super Easy 3-Step Process**

### **Step 1: Run the Automated Setup**
1. **Right-click** on `auto_setup.bat` 
2. **Select "Run as Administrator"**
3. **Wait for it to finish** (opens browser tabs for downloads)

### **Step 2: Run the PowerShell Automation**
1. **Right-click** on `download_and_install.ps1`
2. **Select "Run with PowerShell"**
3. **If prompted about execution policy, type:** `Y` and press Enter
4. **Wait for downloads and installation to complete**

### **Step 3: Install MySQL and Setup Database**
1. **Install MySQL** from the browser tab that opened (use default settings)
2. **Open MySQL Workbench** or **MySQL Command Line**
3. **Run the script:** `setup_database.sql` (created for you)
4. **Import the schema:** Load `EdenRP.Altis\database\edenrp_schema.sql`

## 🎮 **Start Your Server**

After the 3 steps above:
1. **Double-click:** `start_server.bat` (created for you)
2. **Your EdenRP server starts automatically!**
3. **Connect in Arma 3:** Direct Connect to `127.0.0.1:2302`

## 📁 **File Locations (All Created For You)**

```
C:\Users\<USER>\OneDrive\Desktop\Arma Server Test\Arma 3 Server\
├── start_server.bat          ✅ CREATED
├── server.cfg                ✅ CREATED  
├── basic.cfg                 ✅ CREATED
├── setup_database.sql        ✅ CREATED
├── extDB3\
│   ├── extDB3_x64.dll       ✅ DOWNLOADED
│   ├── tbbmalloc_x64.dll    ✅ DOWNLOADED
│   ├── extdb3-conf.ini      ✅ CREATED
│   └── SQL_CUSTOM\          ✅ COPIED
├── @extDB3\                 ✅ DOWNLOADED
├── mpmissions\
│   └── EdenRP.Altis\        ✅ COPIED
└── profiles\                ✅ CREATED
```

## 🔧 **Troubleshooting**

### **If PowerShell script won't run:**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### **If downloads fail:**
- Check internet connection
- Run as Administrator
- Manually download from opened browser tabs

### **If MySQL connection fails:**
- Ensure MySQL is running
- Check credentials in `extdb3-conf.ini`
- Default: username=`edenrp_user`, password=`edenrp_password`

## 🎯 **Quick Start Commands**

### **Run Everything Automatically:**
```batch
# Run these in order:
1. auto_setup.bat (as Administrator)
2. download_and_install.ps1 (as Administrator)
3. Install MySQL from browser
4. Run setup_database.sql in MySQL
5. start_server.bat
```

### **One-Line Database Setup:**
```sql
-- Copy and paste this into MySQL:
CREATE DATABASE edenrp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'edenrp_user'@'localhost' IDENTIFIED BY 'edenrp_password';
GRANT ALL PRIVILEGES ON edenrp.* TO 'edenrp_user'@'localhost';
FLUSH PRIVILEGES;
```

## 🎉 **That's It!**

The automation scripts handle:
- ✅ Directory creation
- ✅ File copying
- ✅ Downloads
- ✅ Configuration
- ✅ Installation
- ✅ Setup scripts

You just need to:
1. **Run 2 scripts**
2. **Install MySQL** 
3. **Run 1 database script**
4. **Start server**

**Your EdenRP server will be running in under 10 minutes!** 🚀

## 📞 **Need Help?**

If anything goes wrong:
1. Check the console output for error messages
2. Ensure you're running as Administrator
3. Check that your Arma 3 Server path is correct
4. Verify MySQL is installed and running

The scripts will tell you exactly what's happening at each step!
