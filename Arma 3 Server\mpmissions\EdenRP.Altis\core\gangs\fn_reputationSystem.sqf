/*
    EdenRP Gang Reputation System
    Author: EdenRP Development Team
    Description: Manages gang reputation, influence, and territorial control
*/

params [
    ["_action", "", [""]],
    ["_gang", objNull, [objNull, ""]],
    ["_amount", 0, [0]],
    ["_reason", "", [""]]
];

if (_action == "") exitWith { 
    ["[EDEN] Invalid action for reputation system", "ERROR", "GANG"] call EDEN_fnc_systemLogger;
    false 
};

private _gangId = "";
if (typeName _gang == "OBJECT") then {
    _gangId = _gang getVariable ["eden_gangId", ""];
} else {
    _gangId = _gang;
};

if (_gangId == "") exitWith { 
    ["[EDEN] Invalid gang ID for reputation system", "ERROR", "GANG"] call EDEN_fnc_systemLogger;
    false 
};

switch (_action) do {
    case "add": {
        // Add reputation to gang
        if (_amount <= 0) exitWith { false };
        
        private _currentRep = missionNamespace getVariable [format ["eden_gang_reputation_%1", _gangId], 0];
        private _newRep = _currentRep + _amount;
        
        // Cap reputation at maximum
        _newRep = _newRep min 10000;
        
        missionNamespace setVariable [format ["eden_gang_reputation_%1", _gangId], _newRep, true];
        
        // Update influence based on reputation
        [_gangId] call EDEN_fnc_updateGangInfluence;
        
        // Log reputation gain
        [format ["[EDEN] Gang %1 gained %2 reputation: %3", _gangId, _amount, _reason], "INFO", "GANG"] call EDEN_fnc_systemLogger;
        
        // Notify gang members
        private _gangMembers = missionNamespace getVariable [format ["eden_gang_members_%1", _gangId], []];
        {
            if (isPlayer _x) then {
                [format ["Your gang gained %1 reputation! Reason: %2", _amount, _reason]] call EDEN_fnc_showNotification;
            };
        } forEach _gangMembers;
        
        true
    };
    
    case "remove": {
        // Remove reputation from gang
        if (_amount <= 0) exitWith { false };
        
        private _currentRep = missionNamespace getVariable [format ["eden_gang_reputation_%1", _gangId], 0];
        private _newRep = (_currentRep - _amount) max 0;
        
        missionNamespace setVariable [format ["eden_gang_reputation_%1", _gangId], _newRep, true];
        
        // Update influence based on reputation
        [_gangId] call EDEN_fnc_updateGangInfluence;
        
        // Log reputation loss
        [format ["[EDEN] Gang %1 lost %2 reputation: %3", _gangId, _amount, _reason], "INFO", "GANG"] call EDEN_fnc_systemLogger;
        
        // Notify gang members
        private _gangMembers = missionNamespace getVariable [format ["eden_gang_members_%1", _gangId], []];
        {
            if (isPlayer _x) then {
                [format ["Your gang lost %1 reputation! Reason: %2", _amount, _reason]] call EDEN_fnc_showNotification;
            };
        } forEach _gangMembers;
        
        true
    };
    
    case "get": {
        // Get current reputation
        missionNamespace getVariable [format ["eden_gang_reputation_%1", _gangId], 0]
    };
    
    case "getRank": {
        // Get reputation rank/tier
        private _reputation = missionNamespace getVariable [format ["eden_gang_reputation_%1", _gangId], 0];
        
        private _rank = switch (true) do {
            case (_reputation >= 5000): { "Legendary" };
            case (_reputation >= 2500): { "Elite" };
            case (_reputation >= 1000): { "Veteran" };
            case (_reputation >= 500): { "Established" };
            case (_reputation >= 100): { "Rising" };
            default { "Unknown" };
        };
        
        _rank
    };
    
    case "getInfluence": {
        // Get gang influence in specific area
        private _area = _amount; // Using amount parameter as area identifier
        private _influence = missionNamespace getVariable [format ["eden_gang_influence_%1_%2", _gangId, _area], 0];
        _influence
    };
    
    case "setInfluence": {
        // Set gang influence in specific area
        private _area = _reason; // Using reason parameter as area identifier
        private _influence = _amount max 0 min 100; // Cap between 0-100%
        
        missionNamespace setVariable [format ["eden_gang_influence_%1_%2", _gangId, _area], _influence, true];
        
        [format ["[EDEN] Gang %1 influence in %2 set to %3%", _gangId, _area, _influence], "INFO", "GANG"] call EDEN_fnc_systemLogger;
        true
    };
    
    case "updateInfluence": {
        // Update gang influence based on reputation and activities
        private _reputation = missionNamespace getVariable [format ["eden_gang_reputation_%1", _gangId], 0];
        private _baseInfluence = (_reputation / 100) min 50; // Base influence from reputation
        
        // Update influence in major cities
        private _cities = ["kavala", "athira", "pyrgos", "sofia"];
        {
            private _currentInfluence = missionNamespace getVariable [format ["eden_gang_influence_%1_%2", _gangId, _x], 0];
            private _newInfluence = (_currentInfluence + (_baseInfluence / 10)) min 100;
            
            missionNamespace setVariable [format ["eden_gang_influence_%1_%2", _gangId, _x], _newInfluence, true];
        } forEach _cities;
        
        true
    };
    
    case "getLeaderboard": {
        // Get gang reputation leaderboard
        private _gangs = [];
        private _gangList = missionNamespace getVariable ["eden_active_gangs", []];
        
        {
            private _rep = missionNamespace getVariable [format ["eden_gang_reputation_%1", _x], 0];
            private _gangName = missionNamespace getVariable [format ["eden_gang_name_%1", _x], "Unknown"];
            _gangs pushBack [_x, _gangName, _rep];
        } forEach _gangList;
        
        // Sort by reputation (highest first)
        _gangs sort false;
        _gangs
    };
    
    case "calculateReward": {
        // Calculate reputation reward based on activity
        private _activityType = _reason;
        private _baseReward = switch (_activityType) do {
            case "territory_capture": { 50 };
            case "successful_heist": { 75 };
            case "gang_war_victory": { 100 };
            case "drug_operation": { 25 };
            case "protection_racket": { 30 };
            case "illegal_racing": { 20 };
            case "arms_dealing": { 40 };
            default { 10 };
        };
        
        // Apply multipliers based on difficulty/risk
        private _multiplier = _amount max 1;
        private _finalReward = _baseReward * _multiplier;
        
        _finalReward
    };
    
    case "applyPenalty": {
        // Apply reputation penalty for negative actions
        private _penaltyType = _reason;
        private _penalty = switch (_penaltyType) do {
            case "member_arrested": { 10 };
            case "territory_lost": { 25 };
            case "failed_operation": { 15 };
            case "betrayal": { 50 };
            case "civilian_harm": { 30 };
            default { 5 };
        };
        
        ["remove", _gangId, _penalty, _penaltyType] call EDEN_fnc_reputationSystem;
    };
    
    case "checkPrivileges": {
        // Check what privileges gang has based on reputation
        private _reputation = missionNamespace getVariable [format ["eden_gang_reputation_%1", _gangId], 0];
        private _privileges = [];
        
        if (_reputation >= 100) then { _privileges pushBack "basic_operations" };
        if (_reputation >= 250) then { _privileges pushBack "territory_control" };
        if (_reputation >= 500) then { _privileges pushBack "advanced_weapons" };
        if (_reputation >= 1000) then { _privileges pushBack "gang_wars" };
        if (_reputation >= 2500) then { _privileges pushBack "elite_operations" };
        if (_reputation >= 5000) then { _privileges pushBack "legendary_status" };
        
        _privileges
    };
    
    default {
        [format ["[EDEN] Unknown reputation system action: %1", _action], "ERROR", "GANG"] call EDEN_fnc_systemLogger;
        false
    };
};
