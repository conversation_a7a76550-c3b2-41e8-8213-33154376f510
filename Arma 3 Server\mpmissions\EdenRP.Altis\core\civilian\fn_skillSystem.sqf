/*
    File: fn_skillSystem.sqf
    Author: EdenRP Development Team
    
    Description:
    Manages skill progression system for civilians.
*/

params [["_player", player, [obj<PERSON><PERSON>]], ["_action", "init", [""]], ["_skillType", "", [""]], ["_amount", 1, [0]]];

if (isNull _player) exitWith { false };

switch (_action) do {
    case "init": {
        _player setVariable ["eden_skills", [], true];
        _player setVariable ["eden_skillPoints", 0, true];
        
        // Initialize basic skills
        _basicSkills = [
            ["driving", 1, 0],
            ["fishing", 1, 0],
            ["farming", 1, 0],
            ["mining", 1, 0],
            ["crafting", 1, 0],
            ["trading", 1, 0],
            ["repair", 1, 0],
            ["medical", 1, 0]
        ];
        
        _player setVariable ["eden_skills", _basicSkills, true];
        true
    };
    case "addExperience": {
        _skills = _player getVariable ["eden_skills", []];
        _skillFound = false;
        
        {
            if ((_x select 0) == _skillType) then {
                _currentLevel = _x select 1;
                _currentExp = _x select 2;
                _newExp = _currentExp + _amount;
                
                _expNeeded = _currentLevel * 100; // 100 exp per level
                
                if (_newExp >= _expNeeded && _currentLevel < 10) then {
                    _newLevel = _currentLevel + 1;
                    _remainingExp = _newExp - _expNeeded;
                    
                    _x set [1, _newLevel];
                    _x set [2, _remainingExp];
                    
                    [format ["Skill level up! %1 is now level %2", _skillType, _newLevel]] call EDEN_fnc_showHint;
                    
                    // Award skill points
                    _skillPoints = _player getVariable ["eden_skillPoints", 0];
                    _player setVariable ["eden_skillPoints", (_skillPoints + 1), true];
                } else {
                    _x set [2, _newExp];
                };
                
                _skillFound = true;
            };
        } forEach _skills;
        
        if (!_skillFound) then {
            _skills pushBack [_skillType, 1, _amount];
        };
        
        _player setVariable ["eden_skills", _skills, true];
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "getSkillLevel": {
        _skills = _player getVariable ["eden_skills", []];
        _level = 1;
        
        {
            if ((_x select 0) == _skillType) then {
                _level = _x select 1;
            };
        } forEach _skills;
        
        _level
    };
    case "getSkillExperience": {
        _skills = _player getVariable ["eden_skills", []];
        _exp = 0;
        
        {
            if ((_x select 0) == _skillType) then {
                _exp = _x select 2;
            };
        } forEach _skills;
        
        _exp
    };
    case "spendSkillPoints": {
        params ["", "", "", "", ["_cost", 1, [0]]];
        
        _skillPoints = _player getVariable ["eden_skillPoints", 0];
        if (_skillPoints < _cost) exitWith {
            [format ["Not enough skill points! Need %1", _cost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_skillPoints", (_skillPoints - _cost), true];
        [format ["Spent %1 skill points", _cost]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "learnSkill": {
        _availableSkills = [
            ["lockpicking", "Lockpicking", 3],
            ["hacking", "Hacking", 5],
            ["negotiation", "Negotiation", 2],
            ["leadership", "Leadership", 4],
            ["stealth", "Stealth", 3],
            ["athletics", "Athletics", 2],
            ["cooking", "Cooking", 1],
            ["photography", "Photography", 2]
        ];
        
        _skillData = [];
        {
            if ((_x select 0) == _skillType) then {
                _skillData = _x;
            };
        } forEach _availableSkills;
        
        if (count _skillData == 0) exitWith {
            ["Skill not found!"] call EDEN_fnc_showHint;
            false
        };
        
        _skills = _player getVariable ["eden_skills", []];
        _hasSkill = false;
        {
            if ((_x select 0) == _skillType) then {
                _hasSkill = true;
            };
        } forEach _skills;
        
        if (_hasSkill) exitWith {
            ["You already know this skill!"] call EDEN_fnc_showHint;
            false
        };
        
        _cost = _skillData select 2;
        if !([_player, "spendSkillPoints", "", "", _cost] call EDEN_fnc_skillSystem) exitWith { false };
        
        _skills pushBack [_skillType, 1, 0];
        _player setVariable ["eden_skills", _skills, true];
        
        _name = _skillData select 1;
        [format ["Learned new skill: %1!", _name]] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    case "viewSkills": {
        _skills = _player getVariable ["eden_skills", []];
        _skillPoints = _player getVariable ["eden_skillPoints", 0];
        
        _skillList = format ["Skill Points: %1\n", _skillPoints];
        {
            _name = _x select 0;
            _level = _x select 1;
            _exp = _x select 2;
            _expNeeded = _level * 100;
            
            _skillList = _skillList + format ["%1: Level %2 (%3/%4 exp)\n", _name, _level, _exp, _expNeeded];
        } forEach _skills;
        
        [_skillList] call EDEN_fnc_showHint;
        true
    };
    case "getSkillBonus": {
        _level = [_player, "getSkillLevel", _skillType] call EDEN_fnc_skillSystem;
        _bonus = (_level - 1) * 0.1; // 10% bonus per level above 1
        _bonus
    };
    case "checkSkillRequirement": {
        params ["", "", "", "", ["_requiredLevel", 1, [0]]];
        
        _currentLevel = [_player, "getSkillLevel", _skillType] call EDEN_fnc_skillSystem;
        (_currentLevel >= _requiredLevel)
    };
    case "trainSkill": {
        _trainingCost = 100;
        _cash = _player getVariable ["eden_cash", 0];
        
        if (_cash < _trainingCost) exitWith {
            [format ["Not enough money for training! Need $%1", _trainingCost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_cash", (_cash - _trainingCost), true];
        
        _expGain = 25 + (random 25); // 25-50 exp
        [_player, "addExperience", _skillType, _expGain] call EDEN_fnc_skillSystem;
        
        [format ["Trained %1 skill! Gained %2 experience", _skillType, floor _expGain]] call EDEN_fnc_showHint;
        true
    };
    case "resetSkills": {
        _resetCost = 5000;
        _bankAccount = _player getVariable ["eden_bankAccount", 0];
        
        if (_bankAccount < _resetCost) exitWith {
            [format ["Not enough money for skill reset! Need $%1", _resetCost]] call EDEN_fnc_showHint;
            false
        };
        
        _player setVariable ["eden_bankAccount", (_bankAccount - _resetCost), true];
        
        // Reset all skills to level 1
        _skills = _player getVariable ["eden_skills", []];
        {
            _x set [1, 1];
            _x set [2, 0];
        } forEach _skills;
        
        _player setVariable ["eden_skills", _skills, true];
        _player setVariable ["eden_skillPoints", 10, true]; // Give some points back
        
        ["All skills reset to level 1!"] call EDEN_fnc_showHint;
        [_player] call EDEN_fnc_savePlayerData;
        true
    };
    default { false };
};
